import os


def configure_logging(app):
    import logging.config

    environment = os.environ.get("ENV", "local")
    log_level = "DEBUG" if environment in ["prod"] else "DEBUG"

    logging_conf = {
        "version": 1,
        "disable_existing_loggers": False,

        "filters": {
            'request_id': {
                '()': 'treebo_commons.request_tracing.log_filters.RequestContextFilter'
            },
            "critical_filter": {
                "()": "rackrate_service.log_filter.LogLevelFilter",
                "level": logging.CRITICAL,
            },
            "error_filter": {
                "()": "rackrate_service.log_filter.LogLevelFilter",
                "level": logging.ERROR,
            },
            "warn_filter": {
                "()": "rackrate_service.log_filter.LogLevelFilter",
                "level": logging.WARN,
            },
            "debug_filter": {
                "()": "rackrate_service.log_filter.LogLevelFilter",
                "level": logging.DEBUG,
            },
            "info_filter": {
                "()": "rackrate_service.log_filter.LogLevelFilter",
                "level": logging.INFO,
            },
        },
        "formatters": {
            "simple": {
                "format": "%(levelname)s %(message)s"
            },
            "verbose": {
                "format": "[%(request_id)s] [%(asctime)s] %(levelname)s  [%(name)s:%(lineno)s] %(message)s",
                "datefmt": "%d/%b/%Y %H:%M:%S",
            },
            "logstash": {
                "()": "logstash_formatter.LogstashFormatterV1"
            },
        },
        "handlers": {
            "null": {
                "level": "DEBUG",
                "class": "logging.NullHandler",
                "filters": ["request_id"],
            },
            "console": {
                "level": "DEBUG",
                "class": "logging.StreamHandler",
                "formatter":"logstash" if environment == "prod" else "verbose",
                "filters": ["request_id"],
            }
        },
        "loggers": {
            "rackrate_service": {
                "handlers": ["console"],
                "level": log_level,
                "propagate": True,
            },
            "sync_logger": {
                "handlers": ["console"],
                "level": log_level,
                "propagate": True,
            },
            "rate_manager": {
                "handlers": ["console"],
                "level": log_level,
            },
            "request_handler": {
                "handlers": ["console"],
                "level": log_level,
                "propagate": True,
            },
        },
        "root": {
            "handlers": ["console"],
            "level": "ERROR"
        },
    }
    logging.config.dictConfig(logging_conf)
