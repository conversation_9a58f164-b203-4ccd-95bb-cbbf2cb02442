from decimal import Decimal
from marshmallow import fields, Schema, validate, post_load

from rackrate_service.constants.catalog_constants import StandardStatusChoices


class PropertyNameSchema(Schema):
    new_name = fields.Str(
        required=True, allow_none=False, validate=validate.Length(min=1)
    )


class ChildSKUSchema(Schema):
    code = fields.String(
        required=True, allow_none=False, validate=validate.Length(min=1)
    )
    name = fields.String(
        required=True, allow_none=False, validate=validate.Length(min=1)
    )
    sku_count = fields.Integer(required=True, allow_none=True, default=0)
    saleable = fields.Boolean()


class SKUSchema(Schema):
    code = fields.String(
        required=True, allow_none=False, validate=validate.Length(min=1)
    )
    name = fields.String(
        required=True, allow_none=False, validate=validate.Length(min=1)
    )
    sku_category_code = fields.String(
        required=True, allow_none=False, validate=validate.Length(min=1)
    )
    children_sku = fields.Nested(
        ChildSKUSchema, load_from="skus", many=True, required=True, allow_none=True
    )
    saleable = fields.Boolean()
    modular = fields.Boolean(load_from="is_modular")
    default_list_price = fields.Float(required=True, default=0.0)
    default_sale_price = fields.Float(required=True, default=0.0)


class PropertySkuSchema(Schema):
    sku_code = fields.String(
        required=True, allow_none=False, validate=validate.Length(min=1)
    )
    property_code = fields.String(
        load_from="property_id",
        required=True,
        allow_none=False,
        validate=validate.Length(min=1),
    )
    status = fields.Method(deserialize="get_rr_hotel_sku_status_from_cs_status")
    saleable = fields.Boolean(load_from="is_sku_saleable", required=True)
    default_list_price = fields.Float(required=True, default=0.0)
    default_sale_price = fields.Float(required=True, default=0.0)
    description = fields.String(required=False, allow_none=True)
    extra_information = fields.Dict(required=False, allow_none=True)
    sell_separate = fields.Boolean()

    def get_rr_hotel_sku_status_from_cs_status(self, value):
        return (
            StandardStatusChoices.ACTIVE
            if value.lower() == "active"
            else StandardStatusChoices.INACTIVE
        )


class PropertySchema(Schema):
    skus = fields.Nested(PropertySkuSchema, many=True, required=True, allow_none=True)
    name = fields.Method(load_from="name", deserialize="get_hotel_name")
    code = fields.String(load_from="id", required=True)
    hx_id = fields.String(required=True)
    status = fields.Method(deserialize="get_rr_status_from_cs_status")

    def get_rr_status_from_cs_status(self, value):
        return (
            StandardStatusChoices.ACTIVE
            if value.lower() == "live"
            else StandardStatusChoices.INACTIVE
        )

    def get_hotel_name(self, name):
        return name["new_name"]


class CatalogLPropertyPayloadSchema(Schema):
    operation_type = fields.String(required=True)
    entity = fields.String(required=True)
    data = fields.Nested(PropertySchema, required=True)


class SellerSchema(Schema):
    name = fields.String(required=True)
    seller_id = fields.String(required=True)
    status = fields.String()


class CatalogLSellerPayloadSchema(Schema):
    operation_type = fields.String(required=True)
    entity = fields.String(required=True)
    data = fields.Nested(SellerSchema, required=True)


class CatalogSKUPayloadSchema(Schema):
    operation_type = fields.String(required=True)
    entity = fields.String(required=True)
    data = fields.Nested(SKUSchema, required=True)


class PricingPolicySchema(Schema):
    code = fields.String(required=True)
    name = fields.String(required=True)
    is_default = fields.Boolean(required=True)
    channel_code = fields.String(load_from="channel_id", required=True)
    sub_channel_code = fields.String(load_from="sub_channel_id", required=True)
    policy_code = fields.String(load_from="code", required=True)
    enabled = fields.Method(
        load_from="mapping_status", deserialize="load_enabled_attr_value"
    )

    def load_enabled_attr_value(self, value):
        return True if value.lower() == "active" else False


class ApplicationSchema(Schema):
    code = fields.String(load_from="id", required=True)
    name = fields.String(required=True)
    channel_id = fields.String(required=True)


class SubChannelSchema(Schema):
    code = fields.String(load_from="id", required=True)
    name = fields.String(required=True)
    channel_code = fields.String(load_from="channel_id", required=True)


class ChannelSchema(Schema):
    code = fields.String(load_from="id", required=True)
    name = fields.String(required=True)
    sub_channels = fields.Nested(SubChannelSchema, required=True, many=True)
    applications = fields.Nested(ApplicationSchema, required=True, many=True)
    pricing_policies = fields.Nested(PricingPolicySchema, required=True, many=True)


class CatalogChannelPayloadSchema(Schema):
    operation_type = fields.String(required=True)
    entity = fields.String(required=True)
    data = fields.Nested(ChannelSchema, required=True)


class SellerSkuSchema(Schema):
    uid = fields.Integer(load_from="id", required=True)
    category = fields.Integer(load_from="sku_id", required=True)
    seller_id = fields.String(required=True)
    is_saleable = fields.Boolean(load_from="is_sellable", required=True)
    is_active = fields.Boolean(required=True)
    default_list_price = fields.Float(load_from="pretax_price", required=True, default=0.0)
    default_sale_price = fields.Float(load_from="pretax_price", required=True, default=0.0)
    name = fields.String(required=True)

    @post_load
    def convert_int_to_str(self, data):
        data['uid'] = str(data['uid'])
        data['category'] = str(data['category'])
        data['default_list_price'] = Decimal(data['default_list_price'])
        data['default_sale_price'] = Decimal(data['default_sale_price'])


class CatalogSellerSkuPayloadSchema(Schema):
    operation_type = fields.String(required=True)
    entity = fields.String(required=True)
    data = fields.Nested(SellerSkuSchema, required=True)
