from rackrate_service.infrastructure.external_clients.base_client import (
    BaseExternalClient,
)
from flask import current_app as app


class OldPricingClient(BaseExternalClient):

    pricing_rackrate_xml_template = """
    <?xml version="1.0"?>
    <OTA_HotelRatePlanNotifRQ xmlns="http://www.opentravel.org/OTA/2003/05" EchoToken="{0}"
        TimeStamp="{1}" Version="1.0" MessageContentCode="8"
        xmlns:xsi="http://www.w3.org/2001/XMLSchemainstance">
        <RatePlans HotelCode="{2}">
            <RatePlan RatePlanNotifType="Overlay" RatePlanCode="{3}" RatePlanCategory="BAR" Start="{4}"
                End="{5}" CurrencyCode="INR">
                <Rates>
                    <Rate InvTypeCode="{6}" MealPlanCode="EP" Mon="1" Tue="1" Weds="1" Thur="1" Fri="1" Sat="1"
                        Sun="1">
                        <BaseByGuestAmts>
                            {7}
                        </BaseByGuestAmts>
                        <AdditionalGuestAmounts>
                            <AdditionalGuestAmount AgeQualifyingCode="10" AmountAfterTax="{8}" />
                            <AdditionalGuestAmount AgeQualifyingCode="8" AmountAfterTax="{9}" />
                        </AdditionalGuestAmounts>
                    </Rate>
                </Rates>
            </RatePlan>
        </RatePlans>
        <Source Source="CRSDashboard"></Source>
    </OTA_HotelRatePlanNotifRQ>"""

    xml_headers = {"referrer": "/", "content-type": "text/xml"}

    def get_domain(self):
        return app.config.get("OLD_PRICING_URL")

    def get_endpoint(self):
        return "pms/v1/rates/"

    @staticmethod
    def build_payload_from_data():
        return OldPricingClient.pricing_rackrate_xml_template

    @staticmethod
    def get_headers():
        return {"referrer": "/", "content-type": "text/xml"}

    def send_prices_to_old_pricing(self):
        url = self.get_domain() + self.get_endpoint()
        payload = self.build_payload_from_data()
        headers = self.get_headers()

        response = self.make_post_request(url, payload, headers=headers)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
