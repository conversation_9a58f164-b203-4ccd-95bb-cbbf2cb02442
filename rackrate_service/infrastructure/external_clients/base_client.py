import logging
from abc import abstractmethod

import requests

logger = logging.getLogger("external_client" + __name__)


class BaseExternalClient(object):
    """
        BaseExternalClient
    """

    def __init__(self, timeout=30, log_handler=None):
        self.logger = logger or log_handler
        self.timeout = timeout

    @abstractmethod
    def get_domain(self):
        """
        The domain of the external service being called. Need to be implemented by each client.
        :return: Domain of the external system
        """
        pass

    @abstractmethod
    def get_endpoint(self):
        """
        The endpoint of the external service being called. Need to be implemented by each client.
        :return: URL Endpoint of the external system
        """
        pass

    @staticmethod
    def get_headers():
        """
        The endpoint of the external service being called. Need to be implemented by each client.
        :return: URL Endpoint of the external system
        """
        return {"referrer": "/", "content-type": "application/json"}

    def make_post_request(self, url, data, headers):
        response = requests.post(url, data, headers=headers)
        return response

    def make_get_request(self, url, data=None):
        response = requests.get(url, data)
        return response
