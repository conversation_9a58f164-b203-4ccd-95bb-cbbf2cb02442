import enum
import os

from treebo_commons.service_discovery.service_registry import ServiceRegistry

from rackrate_service.infrastructure.case_insensitive_environ import CaseInsensitiveEnviron


class ServiceEndPointNames(enum.Enum):
    ROLE_MANAGER_SERVICE_URL = "role_manager_service_url"
    UPS_SERVICE_URL = "ups_service_url"
    TAX_SERVICE_URL = "tax_service_url"
    CATALOG_SERVICE_URL = "catalog_service_url"
    NOTIFICATION_SERVICE_URL = "notification_service_url"
    REALISATION_SERVICE_URL = "realisation_service_url"
    ACCOUNT_RECEIVABLE_SERVICE_URL = "account_receivable_service_url"
    TEMPLATE_SERVICE_URL = "template_service_url"
    AUTHN_SERVICE_URL = "authn_service_url"
    AUTHZ_SERVICE_URL = "authz_service_url"
    TREEBO_PLATFORM_URL = "treebo_platform_url"
    TREEBO_BACKEND_URL = "treebo_backend_url"
    COMMUNICATION_SERVICE_URL = "communication_service_url"
    POS_SERVICE_URL = "pos_service_url"
    TENANT_SERVICE_URL = "tenant_service_url"
    RATE_MANAGER_SERVICE_URL = "rate_manager_service_url"
    UNIRATE_SERVICE_URL = "unirate_service_url"
    PROWL_SERVICE_URL = "prowl_service_url"

environ = CaseInsensitiveEnviron(env=os.environ)

class ServiceRegistryClient:

    ALL_API_ENDPOINTS = ServiceRegistry.get_all_service_endpoints()

    @classmethod
    def get_role_manager_service_url(cls):
        service_name = ServiceEndPointNames.ROLE_MANAGER_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_ups_service_url(cls):
        service_name = ServiceEndPointNames.UPS_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_tax_service_url(cls):
        service_name = ServiceEndPointNames.TAX_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_catalog_service_url(cls):
        service_name = ServiceEndPointNames.CATALOG_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_notification_service_url(cls):
        service_name = ServiceEndPointNames.NOTIFICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_realisation_service_url(cls):
        service_name = ServiceEndPointNames.REALISATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_account_receivable_service_url(cls):
        service_name = ServiceEndPointNames.ACCOUNT_RECEIVABLE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_template_service_url(cls):
        service_name = ServiceEndPointNames.TEMPLATE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_authn_service_url(cls):
        service_name = ServiceEndPointNames.AUTHN_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_authz_service_url(cls):
        service_name = ServiceEndPointNames.AUTHZ_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_treebo_platform_service_url(cls):
        service_name = ServiceEndPointNames.TREEBO_PLATFORM_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_treebo_backend_service_url(cls):
        service_name = ServiceEndPointNames.TREEBO_BACKEND_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_communication_service_url(cls):
        service_name = ServiceEndPointNames.COMMUNICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_pos_service_url(cls):
        service_name = ServiceEndPointNames.POS_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_tenant_service_url(cls):
        service_name = ServiceEndPointNames.TENANT_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_rate_manager_service_url(cls):
        service_name = ServiceEndPointNames.RATE_MANAGER_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_unirate_service_url(cls):
        service_name = ServiceEndPointNames.UNIRATE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))

    @classmethod
    def get_prowl_service_url(cls):
        service_name = ServiceEndPointNames.PROWL_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, environ.get(service_name))
