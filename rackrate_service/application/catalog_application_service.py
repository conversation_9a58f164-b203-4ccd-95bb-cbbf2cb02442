import logging

from rackrate_service import SubChannel, Policy, EnabledPolicy, Channel
from rackrate_service.application.catalog_price_update_service import CatalogPriceUpdate
from rackrate_service.constants.catalog_constants import StandardStatusChoices
from rackrate_service.decorators import session_manager
from rackrate_service.domains.catalog.catalog_operations import PostSKUAddition
from rackrate_service.domains.catalog.repositories.sku_repository import (
    SKURepository,
    PropertyRepository,
    ChannelRepository,
    PropertySKURepository,
    SellerSkuRepository)
from rackrate_service.dto.catalog_dto import Converter
from rackrate_service.infrastructure.external_clients.catalog_client import (
    CatalogClient,
)
from rackrate_service.models.catalog import Property, SKU, PropertySKU
from rackrate_service.models.seller import SellerSkuModel

logger = logging.getLogger(__name__)


class CatalogApplicationService:
    def __init__(self, ):
        self.property_repository = PropertyRepository()
        self.property_sku_repository = PropertySKURepository()
        self.sku_repository = SKURepository()
        self.catalog_price_update = CatalogPriceUpdate()
        self.channel_repository = ChannelRepository()
        self.seller_sku_repository = SellerSkuRepository()

    @staticmethod
    def get_active_hotel_codes():
        properties = PropertyRepository.get_active_properties()
        return [property.code for property in properties]

    @session_manager
    def add_property(self, property_dto):
        property_obj = Property(
            code=property_dto.catalog_id,
            hx_id=property_dto.hx_id,
            name=property_dto.name,
        )
        new_property = self.property_repository.create(property_obj)
        skus = self.sku_repository.get_skus_by_codes(property_dto.sku_codes)
        self.property_repository.update_skus(property_obj, skus)
        self.catalog_price_update.new_property_bundle_prices(property_dto)

        return new_property

    @session_manager
    def update_property(self, property_dto):
        existing_property = self.property_repository.get_property_by_catalog_id(
            property_dto.catalog_id
        )
        if existing_property:
            existing_property.update(hx_id=property_dto.hx_id, name=property_dto.name)

            skus = self.sku_repository.get_skus_by_codes(property_dto.sku_codes)
            self.property_repository.update_skus(existing_property, skus)
            self.catalog_price_update.new_property_bundle_prices(property_dto)
            return existing_property

        new_property = self.add_property(property_dto)
        return new_property

    def add_bundle(self, bundle_dto):
        new_bundle = self.bundle_repository.create(bundle_dto)
        if bundle_dto.default_list_price:
            self.catalog_price_update.new_bundle_default_prices_and_affects(bundle_dto)

        return new_bundle

    def update_bundle(self, bundle_dto):
        updated_bundle = self.bundle_repository.update(bundle_dto)
        if bundle_dto.default_list_price:
            self.catalog_price_update.bundle_price_update_affects(bundle_dto)

        return updated_bundle

    def add_sku(self, sku_dto):
        new_sku = self.sku_repository.create(sku_dto)
        if sku_dto.default_list_price:
            self.catalog_price_update.new_sku_price_affects(sku_dto)

        return new_sku

    def update_sku(self, sku_dto):
        updated_sku = self.sku_repository.update(sku_dto)
        if sku_dto.default_list_price:
            self.catalog_price_update.sku_price_update_affects(sku_dto)
        if sku_dto.child_sku_codes:
            pass

        return updated_sku

    def delete_sku(self, sku_dto):
        pass

    def add_children_skus(self, composite_sku, children_skus):
        for child_sku in children_skus:
            self.sku_repository.add_children_to_sku(
                composite_sku, child_sku, child_sku.get("sku_count", -1)
            )

    def upsert_sku(self, sku_tuple):
        sku_object = Converter.get_object_from_named_tuple(SKU, sku_tuple)
        sku = self.sku_repository.get_by_code(sku_object.code)
        if sku:
            # sku content edit not allowed
            sku.name = sku_object.name
            sku.saleable = sku_object.saleable
            sku.modular = sku_object.modular
            sku.sku_category_code = sku_object.sku_category_code
            sku.default_list_price = sku_object.default_list_price
            sku.default_sale_price = sku_object.default_sale_price
        else:
            sku = SKU(
                code=sku_object.code,
                name=sku_object.name,
                modular=sku_object.modular,
                sku_category_code=sku_object.sku_category_code,
                saleable=sku_object.saleable,
                default_list_price=sku_object.default_list_price,
                default_sale_price=sku_object.default_sale_price,
            )
            if sku_tuple.children_sku:
                self.add_children_skus(sku, children_skus=sku_tuple.children_sku)
        return self.sku_repository.persist(sku)

    def upsert_seller_sku(self, seller_sku):
        seller_s = SellerSkuModel(seller_id=seller_sku['seller_id'],
                                  sku_code=seller_sku['sku_code'],
                                  is_saleable=seller_sku['is_saleable'],
                                  default_list_price=seller_sku['pretax_price'],
                                  default_sale_price=seller_sku['pretax_price'])
        return self.seller_sku_repository.persist(seller_s)

    def upsert_channel(self, channel_tuple):
        # get model objects
        channel_object = Converter.get_object_from_named_tuple(Channel, channel_tuple)
        channel = self.channel_repository.get_channel_for_update(channel_object.code)
        if channel:
            channel.name = channel_object.name
        else:
            channel = channel_object
        return self.channel_repository.persist(channel)

    def upsert_sub_channels(self, sub_channel_tuples):
        """
        No chances of duplicate sub-channels in message is possible, so use a list
        :param sub_channel_tuples:
        :return:
        """
        # get model objects
        sub_channel_objects = [
            Converter.get_object_from_named_tuple(SubChannel, sub_channel_tuple)
            for sub_channel_tuple in sub_channel_tuples
        ]
        sub_channels = []
        for sub_channel_object in sub_channel_objects:
            sub_channel = self.channel_repository.get_sub_channel_for_update(
                sub_channel_object.code
            )
            if sub_channel:
                sub_channel.name = sub_channel_object.name
                sub_channel.channel_id = sub_channel_object.channel_code
            else:
                sub_channel = SubChannel(
                    code=sub_channel_object.code,
                    name=sub_channel_object.name,
                    channel_code=sub_channel_object.channel_code,
                )
            sub_channels.append(sub_channel)
        return (
            self.channel_repository.persist_all(sub_channels) if sub_channels else None
        )

    def upsert_policies(self, pricing_policy_tuples):
        """
        We could use a list container to collect all objects and finally persist_all, but unnecessarily orm updates
        db as many times as the count of list even if the same object is present once or more, so better to iron-out
        duplicate instances, which is less costly that repeated updates of same entity with same state
        :param pricing_policy_tuples:
        :return:
        """
        # get model objects
        pricing_policy_objects = [
            Converter.get_object_from_named_tuple(Policy, single_pricing_policy_tuple)
            for single_pricing_policy_tuple in pricing_policy_tuples
        ]
        pricing_policies = dict()
        for pricing_policy_object in pricing_policy_objects:
            pricing_policy = self.channel_repository.get_pricing_policy_for_update(
                pricing_policy_object.code
            )
            if pricing_policy:
                pricing_policy.name = pricing_policy_object.name
            else:
                pricing_policy = pricing_policy_object
            if pricing_policy.code not in pricing_policies:
                pricing_policies[pricing_policy.code] = pricing_policy
        return (
            self.channel_repository.persist_all(list(pricing_policies.values()))
            if pricing_policies
            else None
        )

    def upsert_policy_mappings(self, enabled_policies_tuples):
        """
        Duplicates will always be there in db table as columns are nullable,
        is not possible to get duplicate mappings in a single channel message,
        list is good to go and is faster

        :param enabled_policies_tuples:
        :return:
        """
        # get model objects
        enabled_policies = [
            Converter.get_object_from_named_tuple(EnabledPolicy, enabled_policies_tuple)
            for enabled_policies_tuple in enabled_policies_tuples
        ]
        policies_to_be_persisted = []
        for enabled_policy in enabled_policies:
            existing_policy = self.channel_repository.get_policy_mapping(
                enabled_policy.channel_code,
                enabled_policy.sub_channel_code,
                enabled_policy.policy_code,
            )
            if existing_policy:
                existing_policy.enabled = enabled_policy.enabled
            else:
                existing_policy = enabled_policy
            policies_to_be_persisted.append(existing_policy)
        return (
            self.channel_repository.persist_all(policies_to_be_persisted)
            if policies_to_be_persisted
            else None
        )

    def upsert_property(self, property_tuple):
        property_object = Converter.get_object_from_named_tuple(
            Property, property_tuple
        )
        hotel = self.property_repository.get_by_code(property_object.code)
        if hotel:
            # property content edit not allowed
            hotel.name = property_object.name
            hotel.hx_id = property_object.hx_id
            hotel.status = property_object.status

        else:
            hotel = Property(
                code=property_object.code,
                name=property_object.name,
                hx_id=property_object.hx_id,
                status=property_object.status,
            )
        return self.property_repository.persist(hotel)

    def upsert_property_sku(self, property_sku_tuples):
        """
        SELECT FOR UPDATE doesnt work with select outer join , so did a pain select
        https://stackoverflow.com/a/46288510/1790760
        :param property_sku_tuples:
        :return:
        """
        property_sku_objects = []
        sku_codes = set()
        for property_sku_tuple in property_sku_tuples:
            property_sku_object = Converter.get_object_from_named_tuple(PropertySKU, property_sku_tuple)
            sku_codes.add(property_sku_object.sku_code)
            property_sku_objects.append(property_sku_object)
        existing_skus = self.sku_repository.get_skus_by_codes(list(sku_codes))
        existing_sku_codes = {sku.code for sku in existing_skus}
        missing_sku_codes = sku_codes - existing_sku_codes
        logger.info(f"Sku with sku_codes {missing_sku_codes} not found in sku table")
        property_sku_list = []
        created_skus = []
        for property_sku_object in property_sku_objects:
            if property_sku_object.sku_code in missing_sku_codes:
                continue
            property_sku = self.property_sku_repository.get_property_sku_by_sku_code_and_property_code(
                property_sku_object.property_code, property_sku_object.sku_code
            )
            if property_sku:
                property_sku.status = property_sku_object.status
                property_sku.saleable = property_sku_object.saleable
                if property_sku.default_list_price is None:
                    property_sku.default_list_price = property_sku_object.default_list_price
                if property_sku.default_sale_price is None:
                    property_sku.default_sale_price = property_sku_object.default_sale_price
                if property_sku_object.description is not None:
                    property_sku.description = property_sku_object.description
                if property_sku.extra_information and property_sku_object.extra_information is not None:
                    property_sku.extra_information.update(property_sku_object.extra_information)
                if property_sku_object.sell_separate is not None:
                    property_sku.sell_separate = property_sku_object.sell_separate
            else:
                property_sku = property_sku_object
                created_skus.append(property_sku)

            property_sku_list.append(property_sku)

        if not property_sku_list:
            return [], created_skus

        self.property_sku_repository.persist_all(property_sku_list)

        return property_sku_list, created_skus

    def update_pricing_for_property_skus(self, property_skus):
        for property_sku in property_skus:
            PostSKUAddition(property_sku)
        if property_skus:
            CatalogClient().notify_catalog(
                property_skus[0].property_code,
                [
                    property_sku.sku.code
                    for property_sku in property_skus
                    if property_sku.status == StandardStatusChoices.INACTIVE
                ],
            )
