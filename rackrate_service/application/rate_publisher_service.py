import logging
from collections import defaultdict

from rackrate_service.infrastructure.publishers.rate_publisher_service import RackrateEventPublisher

logger = logging.getLogger(__name__)


class RatePublisherService:
    def __init__(self):
        self.price_publisher = RackrateEventPublisher()

    def _create_price_payload(self, rates, rate_and_request_ids=[]):
        body = defaultdict(list)

        for sell_rate, request_id in rate_and_request_ids:
            property_id = sell_rate.property.uid
            sku = sell_rate.sku.uid
            stay_date = sell_rate.stay_date
            price = sell_rate.price

            body[property_id].append({
                "sku_id": sku,
                "stay_date": stay_date.isoformat(),
                "price": str(price)
            })

        for sell_rate in rates:
            property_id = sell_rate.property_code
            sku = sell_rate.sku_code
            stay_date = sell_rate.stay_date
            price = sell_rate.price
            body[property_id].append({
                "sku_id": sku,
                "stay_date": stay_date.isoformat(),
                "price": str(price)
            })

        return dict(body)

    def create_price_payload_and_publish(self, rates, rate_and_request_ids=[]):
        try:
            price = self._create_price_payload(rates, rate_and_request_ids)
            self.publish(price)
        except Exception as e:
            logger.error(f'Failed to create and send body to exchange: {e}')

    def publish(self, price_payload):
        event = 'RATE_UPDATED'
        payload = {
            'event': event,
            'data': price_payload
        }
        self.price_publisher.publish(payload)
