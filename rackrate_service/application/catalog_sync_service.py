import logging

from rackrate_service import EnabledPolicy
from rackrate_service.application.catalog_application_service import (
    CatalogApplicationService,
)
from rackrate_service.constants.catalog_constants import (
    CatalogConsumerOperationConstant,
)
from rackrate_service.decorators import session_manager
from rackrate_service.dto.catalog_dto import (
    PropertyDTO,
    SKUDTO,
    tuple_policy,
    tuple_sub_channel,
    tuple_channel,
    tuple_enabled_pricing_policy,
    Converter,
    tuple_sku,
    tuple_property,
    tuple_property_sku,
)
from rackrate_service.signals import skus_created

logger = logging.getLogger(__name__)


class CatalogSyncService(object):
    def __init__(self):
        self.catalog_application_service = CatalogApplicationService()

    @staticmethod
    def create_property_dto(validated_data):
        hx_id = validated_data.get("hx_id")
        catalog_id = validated_data.get("id")
        name = validated_data.get("name").get("new_name")
        churned = False if validated_data["status"] in ["LIVE", "SIGNED"] else True
        """
        following possible status codes/values from CS(cataloging service):
        STATUS_NEAR_CONFIRMED = 'NEAR_CONFIRMED'
        STATUS_NOT_SIGNING = 'NOT_SIGNING'
        STATUS_SIGNED = 'SIGNED'
        STATUS_DROPPED = 'DROPPED_POST_SIGNING'
        STATUS_LIVE = 'LIVE'
        STATUS_CHURNED = 'CHURNED'
        """

        sku_codes = (
            [sku_data["code"] for sku_data in validated_data["skus"]]
            if validated_data["skus"]
            else []
        )
        property_dto = PropertyDTO(
            hx_id=hx_id,
            catalog_id=catalog_id,
            name=name,
            churned=churned,
            sku_codes=sku_codes,
        )

        return property_dto

    @staticmethod
    def create_sku_dto(validated_sku_data):

        sku_name = validated_sku_data.get("name")
        sku_code = validated_sku_data.get("code")
        sku_count = validated_sku_data.get("sku_count")

        child_sku_codes = [
            sku_data.get("code") for sku_data in validated_sku_data.get("skus", [])
        ]

        sku_dto = SKUDTO(sku_name, sku_code, sku_count, child_sku_codes)
        return sku_dto

    def sync_property_data(self, validated_data):
        property_dto = CatalogSyncService.create_property_dto(
            validated_data.get("data")
        )
        if (
                validated_data.get("operation_type")
                == CatalogConsumerOperationConstant.CREATE
        ):
            try:
                self.catalog_application_service.add_property(property_dto)
            except Exception:
                logger.exception("Exception in add property")
        elif (
                validated_data.get("operation_type")
                == CatalogConsumerOperationConstant.UPDATE
        ):
            try:
                self.catalog_application_service.update_property(property_dto)
            except Exception:
                logger.exception("Exception in update property")

    def sync_sku_data(self, validated_data):
        sku_dto = CatalogSyncService.create_sku_dto(validated_data.get("data"))
        if (
                validated_data.get("operation_type")
                == CatalogConsumerOperationConstant.CREATE
        ):
            try:
                self.catalog_application_service.add_sku(sku_dto)
            except Exception as e:
                logger.exception("Exception in add property", e)
        elif (
                validated_data.get("operation_type")
                == CatalogConsumerOperationConstant.UPDATE
        ):
            try:
                self.catalog_application_service.update_sku(sku_dto)
            except Exception as e:
                logger.exception("Exception in update property", e)

        elif (
                validated_data.get("operation_type")
                == CatalogConsumerOperationConstant.DELETE
        ):
            try:
                self.catalog_application_service.delete_sku(sku_dto)
            except Exception as e:
                logger.exception("Exception in delete property", e)

    @session_manager
    def sync_property(self, property_dto):
        property_data = property_dto.get("data")
        operation = property_dto.get("operation_type")
        property_sku_data = property_data.get("skus", [])
        hotel = Converter.get_named_tuple(tuple_property, **property_data)
        hotel_skus = [
            Converter.get_named_tuple(tuple_property_sku, **property_sku)
            for property_sku in property_sku_data
        ]
        if operation != CatalogConsumerOperationConstant.DELETE:
            self.catalog_application_service.upsert_property(hotel)
            saved_property_skus, created_property_skus = self.catalog_application_service.upsert_property_sku(
                hotel_skus)
            self.catalog_application_service.update_pricing_for_property_skus(saved_property_skus)
            if created_property_skus:
                sku_codes = [s.sku_code for s in created_property_skus]
                skus_created.send(CatalogApplicationService, property_code=hotel.code, sku_codes=sku_codes)

    @session_manager
    def sync_seller(self, seller_dto):
        operation = seller_dto.get("operation_type")
        if operation == CatalogConsumerOperationConstant.DELETE:
            raise RuntimeError('Seller cannot be deleted')

        property_data = seller_dto.get("data")
        property_sku_data = property_data.get("skus", [])
        hotel = Converter.get_named_tuple(tuple_property, **property_data)
        hotel_skus = [
            Converter.get_named_tuple(tuple_property_sku, **property_sku)
            for property_sku in property_sku_data
        ]
        self.catalog_application_service.upsert_property(hotel)
        saved_property_skus, created_property_skus = self.catalog_application_service.upsert_property_sku(hotel_skus)
        self.catalog_application_service.update_pricing_for_property_skus(saved_property_skus)
        if created_property_skus:
            sku_codes = [s.sku_code for s in created_property_skus]
            skus_created.send(CatalogApplicationService, property_code=hotel.code, sku_codes=sku_codes)

    @session_manager
    def sync_sku(self, sku_dto):
        sku_data = sku_dto.get("data")
        operation = sku_dto.get("operation_type")
        sku = Converter.get_named_tuple(tuple_sku, **sku_data)
        if operation != CatalogConsumerOperationConstant.DELETE:
            self.catalog_application_service.upsert_sku(sku)

    @staticmethod
    def get_enabled_policies(sub_channels_data):
        enabled_policies = []
        for unit_sub_channel in sub_channels_data:
            channel_id = unit_sub_channel.get("channel_id", None)
            sub_channel_id = unit_sub_channel.get("code", None)
            pricing_policies = unit_sub_channel.get("pricing_policies", [])
            if pricing_policies:
                for pricing_policy in pricing_policies:
                    pricing_policy_code = pricing_policy.get("code", None)
                    enabled_policies.append(
                        EnabledPolicy(
                            channel_code=channel_id,
                            sub_channel_code=sub_channel_id,
                            policy_code=pricing_policy_code,
                            enabled=True,
                        )
                    )
        return enabled_policies

    @session_manager
    def sync_channel_data(self, channel_info_dto):
        """
        https://stackoverflow.com/questions/1051182/what-is-data-transfer-object
        https://martinfowler.com/bliki/LocalDTO.html
        :param channel_info_dto:
        :return:
        """
        channel_data = channel_info_dto.get("data")
        operation = channel_info_dto.get("operation_type")
        channel = Converter.get_named_tuple(tuple_channel, **channel_data)
        pricing_policy_data = channel_data.get("pricing_policies", [])
        pricing_policies = [
            Converter.get_named_tuple(tuple_policy, **single_pricing_policy)
            for single_pricing_policy in pricing_policy_data
        ]
        sub_channels_data = channel_data.get("sub_channels", [])
        sub_channels = [
            Converter.get_named_tuple(tuple_sub_channel, **sub_channel)
            for sub_channel in sub_channels_data
        ]
        enabled_policies = [
            Converter.get_named_tuple(tuple_enabled_pricing_policy, **policy)
            for policy in pricing_policy_data
        ]

        if operation != CatalogConsumerOperationConstant.DELETE:
            self.catalog_application_service.upsert_policies(pricing_policies)
            self.catalog_application_service.upsert_channel(channel)
            self.catalog_application_service.upsert_sub_channels(sub_channels)
            self.catalog_application_service.upsert_policy_mappings(enabled_policies)

    @session_manager
    def sync_seller_sku(self, seller_sku_dto):
        operation = seller_sku_dto.get("operation_type")
        if operation != CatalogConsumerOperationConstant.DELETE:
            self.catalog_application_service.upsert_seller_sku(seller_sku_dto)
