import logging

logger = logging.getLogger(__name__)


class CatalogPriceUpdate:
    # eg: for sku
    # ripple affect to property skus: update prices of property_skus
    # update default prices of corresponding bundles
    # for overriding bundle prices, we need the price diff
    # ripple affect to bundle overrides in authoring tables with diff
    # if those instances are also in rates table.. updated
    # find out bundle prices which needs to be updated, if any
    # update them
    def sku_price_update_affects(self, sku_dto):
        logger.info("called sku_price_update_affects")
        pass

    def new_sku_price_affects(self, sku_dto):
        logger.info("called new_sku_price_affects")
        pass

    def bundle_price_update_affects(self, bundle_dto):
        logger.info("bundle_price_update_affects")
        pass

    def new_bundle_default_prices_and_affects(self, bundle_dto):
        logger.info("called new_bundle_default_prices_and_affects")
        pass

    def bundle_addition_to_property_affects(self, property_dto):
        logger.info("called bundle_addition_to_property_affects")
        pass

    def new_property_bundle_prices(self, property_dto):
        logging.info("called new_property_bundle_prices")
        pass
