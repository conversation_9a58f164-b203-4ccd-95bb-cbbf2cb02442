from .settings import Config
import os

class ProdConfig(Config):
    """Production configuration."""

    ENV = "prod"
    DEBUG = False
    DEBUG_TB_ENABLED = False  # Disable Debug toolbar
    # SQLALCHEMY_POOL_SIZE = 50
    # SQLALCHEMY_MAX_OVERFLOW = 100
    CATALOG_SERVICE_ENDPOINT = os.environ.get(
        "CATALOG_SERVICE_ENDPOINT", "http://catalog.treebo.com"
    )
    RACKRATE_SERVICE_ENDPOINT = os.environ.get(
        "RACKRATE_SERVICE_ENDPOINT", "http://rackrate.treebo.com"
    )
