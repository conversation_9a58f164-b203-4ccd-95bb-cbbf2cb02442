import calendar
import datetime
from itertools import cycle

import dateutil
import pytz
from datetime import date, timedelta

date_string_format = "%Y-%m-%d"
ASIA_KOLKATA_TIME_ZONE = 'Asia/Kolkata'


def current_date():
    return datetime.date.today()


def get_current_date_india():
    return datetime.datetime.now(tz=pytz.timezone(ASIA_KOLKATA_TIME_ZONE)).date()


def convert_date_to_datetime(date):
    if isinstance(date, datetime.date):
        return datetime.datetime.combine(date, datetime.datetime.min.time())
    else:
        return date


def current_date_day():
    return datetime.datetime.now().date()


def utcnow():
    return datetime.datetime.now(pytz.utc)


def current_datetime():
    return datetime.datetime.now(pytz.utc)


def current_datetime_without_tz():
    return datetime.datetime.now()


def to_date(date_time):
    if isinstance(date_time, datetime.datetime):
        return date_time.date()
    else:
        return date_time


def subtract_days(date_: date, days):
    return date_ - timedelta(days=days)


def add_days(date_: date, days):
    return date_ + timedelta(days=days)


def date_to_ymd_str(date_time):
    return date_time.strftime("%Y-%m-%d")


def date_range_inclusive(start: date, end: date):
    return [start + timedelta(i) for i in range((end - start).days + 1)]


def date_range_start_inclusive(start: date, end: date):
    return [start + timedelta(i) for i in range((end - start).days)]


def date_range_exclusive(start: date, end: date):
    return [start + timedelta(i) for i in range(1, (end - start).days - 1)]


def ymd_str_to_date(date_str):
    return datetime.datetime.strptime(date_str, "%Y-%m-%d")


def datetime_str_to_datetime(datetime_str):
    return datetime.datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S.%f")


def from_iso_time_string(datetime_str):
    if isinstance(datetime_str, str):
        return dateutil.parser.parse(datetime_str)
    return datetime_str


def validate_time_format(time: str):
    return datetime.datetime.strptime(time, "%H:%M:%S")


def validate_date_format(date: str):
    return datetime.datetime.strptime(date, "%Y-%m-%d").date()


def isoformat_datetime(date_: datetime):
    if isinstance(date_, datetime.datetime):
        return date_.replace(microsecond=0).isoformat()
    else:
        return date_.isoformat()


def get_datetime_list_from_start_and_end_date(start_date, end_date):
    return [start_date + timedelta(days=d) for d in range((end_date - start_date).days)]


def get_date_list_from_start_and_end_date(start_date, end_date):
    return [
        start_date.date() + timedelta(days=d)
        for d in range((end_date.date() - start_date.date()).days)
    ]


def weekday_name(weekday_number):
    weekday_number_to_name = {0: 'mon', 1: 'tue', 2: 'wed', 3: 'thu', 4: 'fri', 5: 'sat', 6: 'sun'}
    return weekday_number_to_name.get(weekday_number)


def split_date_range(start_date, end_date, exclude_dates):
    date_ranges = []
    start, end = None, None

    for date in date_range_inclusive(start_date, end_date):
        if date not in exclude_dates:
            if start is None and end is None:
                start, end = date, date
            else:
                end = end + datetime.timedelta(days=1)

        else:
            if start is not None:
                date_ranges.append((start, end))
                start, end = None, None

    if start is not None:
        date_ranges.append((start, end))

    return date_ranges


def map_source_range_to_target_range_with_cycle(source_range, target_range):
    dates_for_new_inclusion_rates = date_range_inclusive(source_range[0], source_range[1])
    dates_to_link = date_range_inclusive(target_range[0], target_range[1])

    date_linkage = {date_tup[0]: date_tup[1] for date_tup in
                    list(zip(dates_for_new_inclusion_rates, cycle(dates_to_link)))}
    return date_linkage


def between(start_date, end_date):
    delta = end_date - start_date
    return [start_date + timedelta(days=i) for i in range(delta.days + 1)]


def get_lowercase_weekday_name(applicable_date):
    return calendar.day_name[applicable_date.weekday()].lower()
