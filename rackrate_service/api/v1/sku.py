from flask import Blueprint, request

from rackrate_service import PropertyRepository, PropertySKU, SKURepository
from rackrate_service.decorators import json_response

bp = Blueprint("sku_bp", __name__, url_prefix="/v1/database")


@bp.route('/get_all_sku_codes', methods=['GET'])
@json_response()
def get_all_property_sku_for_property_code():
    response = []
    property_code = request.args.get('property_code', None)
    all_sku_details = PropertySKU.query.filter(PropertySKU.property_code == property_code)
    sku_code_list = list(sku_details.sku_code for sku_details in all_sku_details)
    sku_name_details = SKURepository().get_skus_by_codes(sku_code_list)
    for sku in sku_name_details:
        response.append(
            {
                'code': sku.code,
                'name': sku.name,
            }
        )
    return dict(sku_codes=response), 200

