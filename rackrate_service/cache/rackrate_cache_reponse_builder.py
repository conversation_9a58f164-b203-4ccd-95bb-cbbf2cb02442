import json

from rackrate_service import dateutils
from rackrate_service.cache.cache_key import KeyConverter
from rackrate_service.constants.rate_constants import AnyPolicy
from rackrate_service.dto.rackrate_response_building_dto import (
    RackRateAPIData,
    HotelPrice,
    SKUPrice,
    PolicyPrice,
    PriceData,
    PriceForDate,
)
from rackrate_service.exception import PolicyNotFoundInCacheException


class CacheAPIResponseBuilder:
    def __init__(
        self, cache_values, request_object, guardrail_and_sku_detail_cache_values
    ):
        self.cache_values = cache_values
        self.request_object = request_object
        self.guardrail_and_sku_detail_cache_values = (
            guardrail_and_sku_detail_cache_values
        )
        self.stay_start = request_object.stay_start
        self.stay_end = request_object.stay_end
        self.channel_code = request_object.channel_code
        self.sub_channel_code = request_object.sub_channel_code
        self.date_list = dateutils.get_date_list_from_start_and_end_date(
            self.stay_start, self.stay_end
        )
        self.errors = []
        if hasattr(request_object, "policy_list"):
            self.policy_list = request_object.policy_list
        else:
            # todo: remove db call from here. should also be cached
            # self.policy_list = [
            #     enabled_policy.policy_code
            #     for enabled_policy in EnabledPolicyRepository.get_enabled_policies_for_request(
            #         self.channel_code, self.sub_channel_code
            #     )
            # ]
            self.policy_list = [AnyPolicy]

    def build_response_from_cache(self):
        return self.make_rackrate_api_data(), self.errors

    def make_rackrate_api_data(self):
        hotel_prices = self.make_hotel_prices()
        rackrate_api_data = RackRateAPIData(
            self.stay_start, self.stay_end, hotel_prices
        )
        return rackrate_api_data

    def make_hotel_prices(self):
        hotel_prices = []
        for hotel_payload in self.request_object.hotels:
            property_code = hotel_payload.property_code
            skus = hotel_payload.skus
            sku_prices = self.make_sku_prices(skus, property_code)
            hotel_price = HotelPrice(property_code, sku_prices)
            hotel_prices.append(hotel_price)
        return hotel_prices

    def make_sku_prices(self, skus, property_code):
        sku_prices = []
        property_cache_key = KeyConverter.make_property_key(
            property_code, self.channel_code, self.sub_channel_code
        )
        property_cache_value = self.guardrail_and_sku_detail_cache_values[
            property_cache_key
        ]
        for sku in skus:
            sku_code = sku.sku_code
            policy_prices = self.make_policy_prices(sku_code, property_code)

            try:
                guardrails_data = property_cache_value[sku_code]["guardrails"]
                sku_details = property_cache_value[sku_code]["sku_details"]
            except (KeyError, TypeError):
                # catching type error because property_cache_value can be None sometimes
                self.errors.append((property_code, sku_code))
                continue

            sku_price = SKUPrice(sku_details, guardrails_data, policy_prices)
            sku_prices.append(sku_price)
        return sku_prices

    def make_policy_prices(self, sku_code, property_code):
        policy_prices = []
        for policy_code in self.policy_list:
            price_per_date = self.make_price_per_date(
                policy_code, sku_code, property_code
            )
            if price_per_date:
                policy_price = PolicyPrice(policy_code, price_per_date)
                policy_prices.append(policy_price)
        return policy_prices

    def make_price_per_date(self, policy_code, sku_code, property_code):
        price_per_date = []
        for stay_date in self.date_list:
            key = KeyConverter.make_rates_key(
                property_code, stay_date, self.channel_code, self.sub_channel_code
            )

            try:
                cached_sku_data = self.cache_values[key]["skus"][sku_code]
            except KeyError:
                continue

            try:
                policy_price = cached_sku_data["prices"][policy_code]
            except KeyError:
                self.errors.append("No prices found for policy: %s" % policy_code)
                continue

            rate_id = cached_sku_data["rate_id"][policy_code]
            prices = PriceData(**policy_price)
            price_for_date = PriceForDate(stay_date, prices, rate_id)
            price_per_date.append(price_for_date)
        return price_per_date
