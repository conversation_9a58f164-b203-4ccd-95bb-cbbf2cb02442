from rackrate_service.constants.rate_constants import (
    PriceTypeChoices,
    AnyChannel,
    AnySubChannel,
    AnyPolicy,
)
from rackrate_service.decorators import timer
import logging

from rackrate_service.exception import RackrateNoPriceFoundAPIException
from rackrate_service.rackrate_search_tracer import current_search_tracer

logger = logging.getLogger(__name__)


class RackrateNestedDictAggregate:
    def __init__(self, nested_dict):
        self.nested_dict = nested_dict

    def get_global_min_max(self, property_code, sku_code):
        return self.nested_dict[property_code][sku_code].get_global_min_max()

    def get_channel_min_max(self, property_code, sku_code):
        return self.nested_dict[property_code][sku_code].get_channel_min_max()

    def check_if_property_sku_exists(self, property_code, sku_code):
        if self.nested_dict[property_code][sku_code].exists:
            return True
        return False

    def get_list_price_and_override_flag(
        self, property_code, sku_code, date, policy_code, errors
    ):
        list_price, list_override_found, list_price_override_id = self.nested_dict[
            property_code
        ][sku_code].get_price(
            str(date), PriceTypeChoices.LIST_PRICE, policy_code, self.nested_dict
        )
        if list_price is None:
            errors.append("No price found for %s-%s" % (property_code, sku_code))
            list_price, list_override_found, list_price_override_id = None, None, None
        return list_price, list_override_found, list_price_override_id

    def get_sale_price_and_override_flag(
        self, property_code, sku_code, date, policy_code
    ):
        sale_price, sale_override_found, sale_price_override_id = self.nested_dict[
            property_code
        ][sku_code].get_price(
            str(date), PriceTypeChoices.SALE_PRICE, policy_code, self.nested_dict
        )
        if sale_price is None:
            sale_price, sale_override_found, sale_price_override_id = None, None, None
        return sale_price, sale_override_found, sale_price_override_id

    def check_modular(self, property_code, sku_code):
        return self.nested_dict[property_code][sku_code].modular

    def get_sku_name(self, property_code, sku_code):
        return self.nested_dict[property_code][sku_code].sku_name

    def get_property_sku_id(self, property_code, sku_code):
        return self.nested_dict[property_code][sku_code].property_sku_id

    def get_m_and_c_dict(self, property_code, sku_code, policy_code):
        return self.nested_dict[property_code][sku_code].get_multiplier_and_constant(
            policy_code
        )

    def get_children_counts(self, property_code, sku_code):
        return (
            self.nested_dict[property_code][sku_code].children_counts
            if self.nested_dict[property_code][sku_code].children_counts
            else []
        )

    @timer(logger)
    def populate_default_rates_into_nested_dict(self, default_entries):
        for default_entry in default_entries:
            hotel_sku_aggregate = self.nested_dict[default_entry.property_code][
                default_entry.sku_code
            ]

            hotel_sku_aggregate_price_per_date = hotel_sku_aggregate.price_per_date

            rate_aggregate = hotel_sku_aggregate_price_per_date[
                str(default_entry.stay_date)
            ][default_entry.price_type]
            rate_aggregate.default_rate_entries.append(default_entry)

    @timer(logger)
    def populate_override_rates_into_nested_dict(self, override_entries):
        for override_entry in override_entries:
            hotel_sku_aggregate = self.nested_dict[override_entry.property_code][
                override_entry.sku_code
            ]

            hotel_sku_aggregate_price_per_date = hotel_sku_aggregate.price_per_date

            rate_aggregate = hotel_sku_aggregate_price_per_date[
                str(override_entry.stay_date)
            ][override_entry.price_type]
            rate_aggregate.override_rate_entries.append(override_entry)

    @timer(logger)
    def populate_guardrails_into_nested_dict(self, all_guardrails):
        for guardrail_entry in all_guardrails:
            if (
                guardrail_entry.channel_code == AnyChannel
                and guardrail_entry.sub_channel_code == AnySubChannel
            ):
                self.nested_dict[guardrail_entry.property_code][
                    guardrail_entry.sku_code
                ].global_guardrail_entries.append(guardrail_entry)
            else:
                self.nested_dict[guardrail_entry.property_code][
                    guardrail_entry.sku_code
                ].channel_guardrail_entries.append(guardrail_entry)

    @timer(logger)
    def populate_rate_plans_into_nested_dict(self, all_rate_plans):
        for rate_plan_entry in all_rate_plans:
            if (
                rate_plan_entry.channel_code == AnyChannel
                and rate_plan_entry.sub_channel_code == AnySubChannel
                and rate_plan_entry.policy_code == AnyPolicy
            ):
                self.nested_dict[rate_plan_entry.property_code][
                    rate_plan_entry.sku_code
                ].default_rate_plan_entries.append(rate_plan_entry)
            else:
                self.nested_dict[rate_plan_entry.property_code][
                    rate_plan_entry.sku_code
                ].linked_rate_plan_entries.append(rate_plan_entry)

    @timer(logger)
    def populate_property_sku_into_nested_dict(self, all_property_skus, all_sku_codes):
        nested_dict = self.nested_dict
        for property_sku in all_property_skus:
            property_code = property_sku.property_code
            sku_code = property_sku.sku_code
            sku = property_sku.sku

            hotel_sku_price_aggregate = nested_dict[property_code][sku_code]

            hotel_sku_price_aggregate.exists = True
            hotel_sku_price_aggregate.modular = sku.modular
            hotel_sku_price_aggregate.sku_name = sku.name
            hotel_sku_price_aggregate.property_sku_id = property_sku.id
            hotel_sku_price_aggregate.is_inferred = sku_code not in all_sku_codes
            hotel_sku_price_aggregate.children_counts = sku.children_counts
            hotel_sku_price_aggregate.default_list_price = property_sku.default_list_price
            hotel_sku_price_aggregate.default_sale_price = property_sku.default_sale_price
            hotel_sku_price_aggregate.property_code = property_code
            hotel_sku_price_aggregate.sku_code = sku_code
