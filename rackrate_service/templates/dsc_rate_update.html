<html>
  <head>
  </head>
  <body>
  {% if room_details or room_prices %}
  <h1> Query Results</h1>
  <h2> First query data</h2>
  <form action="" method="POST">
  <table border="1">
        <tr>
            <td><b>cs_id</b></td>
            <td><b>has_acacia</b></td>
            <td><b>has_oak</b></td>
            <td><b>has_maple</b></td>
            <td><b>has_mahogany</b></td>
            <td><b>wifi_price</b></td>
            <td><b>breakfast_price</b></td>
        </tr>
        {% for object in room_details %}
        <tr>
            <td>{{ object[0] }}</td>
            <td>{{ object[1] }}</td>
            <td>{{ object[2] }}</td>
            <td>{{ object[3] }}</td>
            <td>{{ object[4] }}</td>
            <td>{{ object[5] }}</td>
            <td>{{ object[6] }}</td>
        </tr>
        {%  endfor %}
    </table>
  <h2> Second query data</h2>
  <table border="1">
        <tr>
            <td><b>applicaility</b></td>
            <td><b>acacia</b></td>
            <td><b>oak</b></td>
            <td><b>maple</b></td>
            <td><b>mahogany</b></td>
            <td><b>base_room</b></td>
            <td><b>is_enable</b></td>
        </tr>
        {% for object in room_prices %}
            <td><input type="text", name="catalog_id" value={{ object[0]}} readonly /></td>
            <td><input type="text", name="acacia" value={{ object[1]}} /></td>
            <td><input type="text", name="oak" value={{ object[2]}} /></td>
            <td><input type="text", name="maple" value={{ object[3]}} /></td>
            <td><input type="text", name="mahogany" value={{ object[4]}} /></td>
            <td><input type="text", name="base_room" value={{ object[5]}}  readonly /></td>
            <td>{{ object[6] }}</td>
        </tr>
        {%  endfor %}
    </table>
      {% if comment %}
      <h2> {{comment}} </h2>
      {% endif %}
  <input type="submit" value="Submit"/>
  </form>
  {% else %}
       <form action="" method="GET">
      <p>catalog_id <input type = "text" name = "catalog_id" /></p>
      <input onclick="window.location.href = '/rate_wow';" type="submit" value="Submit request" />
    </form>
  {% endif %}
  </body>
</html>
