from rackrate_service.constants.rate_constants import AnyChannel, AnySubChannel
from rackrate_service.models.catalog import PropertySKU
from rackrate_service.models.rate_plan import LinkedRatePlan

nrp_list_m = 0.925
nrp_list_c = 0

nrp_sale_m = 0.925
nrp_sale_c = 0


class NRPDataSync:
    def sync_nrp_m_and_c(self):
        # linked_rate_plans = LinkedRatePlan.query.filter(LinkedRatePlan.policy_code == "nrp")
        # for linked_rate_plan in linked_rate_plans:
        #     linked_rate_plan.
        property_skus = PropertySKU.query.all()
        for property_sku in property_skus:

            nrp_lrp = LinkedRatePlan(
                property_code=property_sku.property_code,
                sku_code=property_sku.sku_code,
                channel_code="direct",
                sub_channel_code=AnySubChannel,
                policy_code="nrp",
                list_price_multiplier=nrp_list_m,
                list_price_constant=nrp_list_c,
                sale_price_multiplier=nrp_sale_m,
                sale_price_constant=nrp_sale_c,
            )
            nrp_lrp.save()
