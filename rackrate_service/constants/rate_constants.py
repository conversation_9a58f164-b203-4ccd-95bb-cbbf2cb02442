import enum


class DefaultSKUPrice:
    PRICE = 10000
    LIST_PRICE = 10000
    SALE_PRICE = 10000


class DefaultGuardrail:
    DEFAULT_MIN = 890
    DEFAULT_MAX = 20000


class OverrideSourceChoices(enum.Enum):
    DATASCIENCE = "DS"
    DASHBOARD = "DASHBOARD"
    HAWKEYE = "HAWKEYE"

    @staticmethod
    def get_from_string(string):
        if string == "DS":
            return OverrideSourceChoices.DATASCIENCE

        if string == "DASHBOARD":
            return OverrideSourceChoices.DASHBOARD

        if string == "HAWKEYE":
            return OverrideSourceChoices.HAWKEYE

    @staticmethod
    def all():
        return ["DS", "DASHBOARD", "HAWKEYE"]


class PriceTypeChoices(enum.Enum):
    LIST_PRICE = "LP"
    SALE_PRICE = "SP"


class RateType:
    DEFAULT = "DEFAULT"
    OVERRIDE = "OVERRIDE"


PU_ID_OFFSET = 400000


DefaultPolicy = "rp"

AnyPolicy = "ANY"
AnySubChannel = "ANY"
AnyChannel = "ANY"
