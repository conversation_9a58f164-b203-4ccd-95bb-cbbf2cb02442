from ths_common.constants.booking_constants import BookingStatus


class ACQTypes:
    BLACKLISTED_PHONE_NUMBER_ACQ = "blacklisted_phone_number_acq"
    INVALID_PHONE_NUMBER_ACQ = "invalid_phone_number_acq"
    RATEPLAN_ACQ = "rateplan_acq"


BOOKING_STATUS_FOR_ACQ = [
    BookingStatus.RESERVED.value,
    BookingStatus.TEMPORARY.value,
    BookingStatus.CONFIRMED.value,
]


THRESHOLD_OF_UNPAID_CRITERIA = 250
APPLICATION_NAME = "acq"


class ACQFrequency:
    HOURLY = "hourly"
    HALF_HOURLY = "half_hourly"


class ACQInEligibilityMessages:
    SLOT_NOT_MATCHING_FOR_REMINDER_DISPATCH = (
        "In eligible for reminder dispatch on current slot"
    )
    REMINDER_SKIPPED_AS_NO_PAYMENT_IS_NEEDED = (
        "Reminder skipped as no payment is needed per configured policy"
    )
    BOOKING_STATUS_NOT_MATCHING_FOR_ACQ = "In eligible booking status for ACQ"
    BOOKING_HAS_GUARANTEE = "Booking has a guarantee"
    BOOKING_IS_WHITELISTED = "Booking is white listed"
    BOOKING_GOT_FULL_PAYMENT = "Booking got full payment"
