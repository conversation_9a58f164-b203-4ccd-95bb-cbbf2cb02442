from core.entity.acq_booking import ACQBookingEntity
from core.models import ACQBooking
from core.value_objects.acq_xray import ACQXRay

from object_registry import register_instance
from prometheus.infrastructure.database.base_repository import BaseRepository


@register_instance()
class ACQBookingRepository(BaseRepository):
    @staticmethod
    def to_domain_entity(db_model: ACQBooking):
        return ACQBookingEntity(
            booking_id=db_model.booking_id,
            acq_type=db_model.acq_type,
            acq_items=[ACQXRay.from_json(item) for item in db_model.acq_items]
            if db_model.acq_items
            else [],
        )

    @staticmethod
    def to_db_model(domain_entity: ACQBookingEntity) -> ACQBooking:
        # noinspection PyArgumentList
        return ACQBooking(
            booking_id=domain_entity.booking_id,
            acq_type=domain_entity.acq_type,
            acq_items=[item.to_json() for item in domain_entity.acq_items]
            if domain_entity.acq_items
            else None,
        )

    def save(self, entity: ACQBookingEntity):
        self._save(self.to_db_model(entity))

    def update(self, entity: ACQBookingEntity):
        self._update(self.to_db_model(entity))

    def find(self, booking_id):
        return self.to_domain_entity(self.query(ACQBooking).get(booking_id))

    def fetch(self, booking_ids=None, acq_type=None):
        query = self.query(ACQBooking).filter(ACQBooking.deleted.is_(False))
        if booking_ids:
            query = query.filter(ACQBooking.booking_id.in_(booking_ids))
        if acq_type:
            query = query.filter(ACQBooking.acq_type == acq_type)
        return [self.to_domain_entity(item) for item in query.all()]

    def create_or_update_in_bulk(self, entities: [ACQBookingEntity]):
        to_create, to_update = [], []

        for entity in entities:
            if entity.is_new:
                to_create.append(self.to_db_model(entity).mapping_dict())
            elif entity.is_dirty:
                to_update.append(self.to_db_model(entity).mapping_dict())

        if to_create:
            self._bulk_insert_mappings(ACQBooking, to_create)
        if to_update:
            self._bulk_update_mappings(ACQBooking, to_update)

        self.flush_session()

    def to_aggregate(self, **kwargs):
        pass

    def from_aggregate(self, aggregate=None):
        pass
