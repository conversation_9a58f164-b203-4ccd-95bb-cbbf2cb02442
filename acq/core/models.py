from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>loat, <PERSON><PERSON><PERSON>, Inte<PERSON>, String, Time
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from shared_kernel.infrastructure.database.orm_base import DeleteMixin, TimeStampMixin


class ACQScheduleTypes(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "acq_schedule_types"

    schedule_type = Column(String(60), name="schedule_type", primary_key=True)
    description = Column(String(255), name="description")
    schedules = Column('schedules', JSONB)
    ivr_slots = Column('ivr_slots', JSONB)


class RatePlanACQPaymentPolicyConfig(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "acq_rate_plan_acq_payment_policy_config"

    id = Column(Integer(), name="id", primary_key=True, autoincrement=True)
    config_type = Column(String(60), name="config_type", nullable=False)
    lower_bound_for_reminder = Column(Integer(), name="lower_bound_for_reminder")
    upper_bound_for_reminder = Column(Integer(), name="upper_bound_for_reminder")
    use_on_occupancy_breach = Column(Boolean(), name="use_on_occupancy_breach")
    schedule_type = Column(
        String(60),
        ForeignKey("acq_schedule_types.schedule_type"),
        name="schedule_type",
    )

    schedule = relationship("ACQScheduleTypes", lazy="selectin")


class ConditionalACQConfig(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "acq_conditional_acq_config"

    id = Column(Integer(), name="id", primary_key=True, autoincrement=True)
    acq_type = Column(String(60), name="acq_type", nullable=False)
    l_abw = Column(Integer(), name="l_abw")
    u_abw = Column(Integer(), name="u_abw")
    payment_percentage_needed = Column(Float(), name="payment_percentage_needed")
    schedule_type = Column(
        String(60),
        ForeignKey("acq_schedule_types.schedule_type"),
        name="schedule_type",
    )

    schedule = relationship("ACQScheduleTypes", lazy="selectin")


class ACQReminderThreshold(Base, TimeStampMixin):
    __tablename__ = "acq_reminder_threshold"

    id = Column(Integer(), name="id", primary_key=True, autoincrement=True)
    l_abw = Column(Integer(), name="l_abw")
    u_abw = Column(Integer(), name="u_abw")
    min_time = Column(Time(), name="min_time")
    max_time = Column(Time(), name="max_time")
    threshold = Column(Integer(), name="threshold")
    acq_type = Column(String(60), name="acq_type", nullable=False)


class ACQBlackListedPhoneNumbers(Base, TimeStampMixin):
    __tablename__ = "acq_blacklisted_phone_numbers"

    phone_number = Column(String(20), name="phone_number", primary_key=True)
    country_code = Column(String(10), name="country_code", primary_key=True)
    active = Column(Boolean(), name="active", default=True)


class ACQWhiteListedPhoneNumbers(Base, TimeStampMixin):
    __tablename__ = "acq_whitelisted_phone_numbers"

    phone_number = Column(String(20), name="phone_number", primary_key=True)
    country_code = Column(String(10), name="country_code", primary_key=True)
    active = Column(Boolean(), name="active", default=True)


class ACQBooking(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "acq_booking"

    booking_id = Column(String(60), name="booking_id", primary_key=True)
    acq_type = Column(String(60), name="acq_status", primary_key=True)
    acq_items = Column(JSONB, name="acq_items")

    def mapping_dict(self):
        return {
            "booking_id": self.booking_id,
            "acq_type": self.acq_type,
            "acq_items": self.acq_items,
        }
