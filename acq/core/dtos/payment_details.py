from dataclasses import dataclass
from datetime import date
from typing import Any

from core.constants import ACQTypes
from core.dtos.conditional_acq_policy_dto import ConditionalACQConfigDto
from treebo_commons.money import Money

from prometheus.domain.booking.dtos.rate_plan_dtos import RatePlanPaymentPolicies

MIS_THRESHOLD_PAYMENT = 250


@dataclass
class PaymentDetails:
    acq_type: str
    payment_date: date
    pre_fee_payment_needed: Money
    post_fee_payment_needed: Money
    payment_policy: Any
    is_eligible_for_ivr: bool = False

    POLICY_CLASSES = {
        ACQTypes.RATEPLAN_ACQ: RatePlanPaymentPolicies,
        ACQTypes.BLACKLISTED_PHONE_NUMBER_ACQ: ConditionalACQConfigDto,
        ACQTypes.INVALID_PHONE_NUMBER_ACQ: ConditionalACQConfigDto,
    }

    @property
    def fees(self):
        return self.post_fee_payment_needed - self.pre_fee_payment_needed

    @property
    def needed_payment_to_keep_booking(self):
        return self.post_fee_payment_needed or self.pre_fee_payment_needed

    def is_payment_needed(self):
        return (
            self.post_fee_payment_needed
            and self.post_fee_payment_needed >= MIS_THRESHOLD_PAYMENT
        )

    @staticmethod
    def from_json(data):
        acq_type = data["acq_type"]
        pre_fee_payment_needed = data.get("pre_fee_payment_needed")
        post_fee_payment_needed = data.get("post_fee_payment_needed")
        payment_policy = data.get("payment_policy")
        if payment_policy:
            payment_policy = PaymentDetails.POLICY_CLASSES[acq_type].from_json(
                payment_policy
            )

        return PaymentDetails(
            acq_type=acq_type,
            payment_date=date.fromisoformat(data["payment_date"]),
            pre_fee_payment_needed=Money(pre_fee_payment_needed)
            if pre_fee_payment_needed
            else None,
            payment_policy=payment_policy,
            is_eligible_for_ivr=data.get("is_eligible_for_ivr", False),
            post_fee_payment_needed=Money(post_fee_payment_needed)
            if post_fee_payment_needed
            else None,
        )

    def to_json(self):
        payment_date = self.payment_date.isoformat()
        payment_policy = self.payment_policy.to_json() if self.payment_policy else None
        return {
            "acq_type": self.acq_type,
            "payment_date": payment_date,
            "pre_fee_payment_needed": str(self.pre_fee_payment_needed)
            if self.pre_fee_payment_needed
            else None,
            "post_fee_payment_needed": str(self.post_fee_payment_needed)
            if self.post_fee_payment_needed
            else None,
            "payment_policy": payment_policy,
            "is_eligible_for_ivr": self.is_eligible_for_ivr,
            "needed_payment_to_keep_booking": str(self.needed_payment_to_keep_booking),
        }
