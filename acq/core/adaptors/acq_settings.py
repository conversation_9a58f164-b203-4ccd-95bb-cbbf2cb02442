from core.entity.settings import (
    ACQReminderThresholdEntity,
    ACQSchedulerType,
    ConditionalACQConfigEntity,
    RatePlanACQPaymentPolicyEntity,
)
from core.value_objects.data_class import ACQSlot


class RatePlanACQPaymentPolicySettingsAdaptor:
    @classmethod
    def to_domain_entity(cls, db_model):
        scheduler_type = None
        if db_model.schedule:
            scheduler_type = ACQSchedulerType(
                scheduler_type=db_model.schedule.schedule_type,
                description=db_model.schedule.description,
                schedules=[
                    ACQSlot.from_string(item)
                    for item in db_model.schedule.schedules or []
                ],
                ivr_slots=[
                    ACQSlot.from_string(item)
                    for item in db_model.schedule.ivr_slots or []
                ],
            )
        return RatePlanACQPaymentPolicyEntity(
            config_type=db_model.config_type,
            lower_bound_for_reminder=db_model.lower_bound_for_reminder,
            upper_bound_for_reminder=db_model.upper_bound_for_reminder,
            use_on_occupancy_breach=db_model.use_on_occupancy_breach,
            scheduler_type=scheduler_type,
        )


class ConditionalACQConfigSettingsAdaptor:
    @classmethod
    def to_domain_entity(cls, db_model):
        scheduler_type = None
        if db_model.schedule:
            scheduler_type = ACQSchedulerType(
                scheduler_type=db_model.schedule.schedule_type,
                description=db_model.schedule.description,
                schedules=[
                    ACQSlot.from_string(item)
                    for item in db_model.schedule.schedules or []
                ],
                ivr_slots=[
                    ACQSlot.from_string(item)
                    for item in db_model.schedule.ivr_slots or []
                ],
            )
        return ConditionalACQConfigEntity(
            acq_type=db_model.acq_type,
            l_abw=db_model.l_abw,
            u_abw=db_model.u_abw,
            payment_percentage_needed=db_model.payment_percentage_needed,
            scheduler_type=scheduler_type,
        )


class ACQReminderThresholdSettingsAdaptor:
    @classmethod
    def to_domain_entity(cls, db_model):
        return ACQReminderThresholdEntity(
            l_abw=db_model.l_abw,
            u_abw=db_model.u_abw,
            acq_type=db_model.acq_type,
            threshold=db_model.threshold,
            min_time=db_model.min_time,
            max_time=db_model.max_time,
        )
