import json
from collections import defaultdict

from acq_s3_util import AcqS3StoreUtil
from core.dtos.acq_request_dto import ACQBookingStatus, ACQPayloadDTO

from ths_common.utils.id_generator_utils import random_id_generator


def lambda_handler(event, context):
    print(f"Event: \n{event}")

    results = defaultdict(list)

    for result in extract_results_to_write(event):
        acq_uuid = result.get("acq_uuid")
        if not acq_uuid or is_pl_generation_event(acq_uuid):
            continue

        if cancellation_error := result.get("cancellation_error"):
            result = update_payload_with_error(result, cancellation_error)

        results[acq_uuid].append(result)

    if not results:
        return False

    batch_uu_id = random_id_generator()

    for acq_uuid, acq_results in results.items():
        AcqS3StoreUtil.store_results(
            result_batch_id=batch_uu_id,
            s3_path_to_acq_store=acq_uuid,
            bookings=acq_results,
        )

    return True


def is_pl_generation_event(acq_uuid):
    # TODO: handle this better
    return "PLGen" in acq_uuid


def extract_results_to_write(event):
    records = event.get("Records", [])
    for record in records:
        body = json.loads(record.get("body"))
        source = record.get("eventSource")

        if body and source == "aws:sqs":
            yield body


def update_payload_with_error(result, cancellation_error):
    """
    Updates the ACQ payload with cancellation error details.
    """
    try:
        acq_payload = ACQPayloadDTO.from_json(result)
        acq_payload.update_status(ACQBookingStatus.CANCELLATION_FAILED)
        acq_payload.record_remark(cancellation_error)
        return acq_payload.to_json()
    except Exception as e:
        print(f"Error updating payload with cancellation error: {e}")
        return result
