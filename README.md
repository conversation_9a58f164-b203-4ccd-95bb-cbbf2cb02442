# README #

This README file contains details about the new b2b (corporate-bookings) utility/api and various aspects of the dev/testing/deployment life-cycle.

### What is this repository for? ###

* Quick summary
This repository is for new B2B code built after re-architecturing. The design artefacts from the re-architecting exercise can be found here - [B2B Re-architecture: Design Artefacts
](https://treebo.atlassian.net/wiki/display/EN/B2B)

* Version: 0.1.0

### How do I get set up? ###

Run the following steps from the project root

Configuration
```bash
virtualenv env
source env/bin/activate
```
 Dependencies
```bash
python setup.py install
setup
```

Database configuration
```psql
createdb b2b
python b2b/manage.py migrate
```
How to run tests
```bash

```
Running instructions
```
python b2b/manage.py runserver --settigns=webapp.config.local
```

If during commit, you see the issue regarding locale `en_US.UTF-8` run the following:
```bash
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
```

### Contribution guidelines ###

* Code
    * Please adhere to PEP-8 standards for naming conventions
    * Run pylint regularly on code to ensure compatibility
    *

* Writing tests
    * Create package and files in parallel to the main src code
    * Name your tests starting with `test_`
    * **Do not write** tests that require database to be initialized
    * A testing unit should focus on one tiny bit of functionality and prove it correct. Good way to achieve this is to try hard to make only one assert per unit test
    * Each test should be fully independent.
    * Run unit tests before pushing code. Fix all tests if they are breaking before push.
    * Use descriptive names for tests. BDD derived test names are often helpful. E.g. even a name like `test_should_store_awaiting_hx_confirmation_before_confirm_api_call()` is acceptable.


* Code review
    * All pull requests should be accompanied by unit-tests which are passing and covering most of the newly written code.
    * New branch should be forked for every new feature, and it should be forked from and merged into develop branch

* Other guidelines
    * TODO: <link to coding guidelines>

### Who do I talk to? ###

* B2B Team
    * [Shreyas Kulkarni](mailto:<EMAIL>)
    * [Thulasi Ram](mailto:<EMAIL>)
    * [Prashant Kumar](mailto:<EMAIL>)
    * [Mayank Goel](mailto:<EMAIL>)
    * [Varun Achar](mailto:<EMAIL>)
