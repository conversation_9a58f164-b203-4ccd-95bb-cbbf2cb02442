import pytest
from tests.mockers import mock_role_manager


@pytest.fixture
def dow_prices_date_range_rate_update_request(request):
    start_date = request.param.get('start_date', '2020-12-10') if hasattr(request, 'param') else '2020-12-10'
    end_date = request.param.get('end_date', '2020-12-18') if hasattr(request, 'param') else '2020-12-18'
    return {
        "start_date": start_date,
        "end_date": end_date,
        "dow_prices": {
            "mon": "1000",
            "tue": "1001",
            "wed": "1002",
            "thu": "1003",
            "fri": "1004",
            "sat": "1005",
            "sun": "1006"
        },
        "property_id": "123",
        "room_types_with_occupancy": [{
            "room_type_id": "RT01",
            "adult_count": 1
        }]
    }


@pytest.fixture
def flat_price_date_range_rate_update_request(request):
    start_date = request.param.get('start_date', '2020-12-10') if hasattr(request, 'param') else '2020-12-10'
    end_date = request.param.get('end_date', '2020-12-18') if hasattr(request, 'param') else '2020-12-18'
    return {
        "start_date": start_date,
        "end_date": end_date,
        "price": "2000",
        "property_id": "123",
        "room_types_with_occupancy": [{
            "room_type_id": "RT02",
            "adult_count": 1
        }],
        "action_type": "create rate"
    }


@pytest.fixture
def copy_price_date_range_rate_update_request():
    return {
        "property_id": "123",
        "start_date": "2021-02-10",
        "end_date": "2021-02-20",
        "date_range_for_price_copy": {
            "start_date": "2020-12-10",
            "end_date": "2020-12-18",
        },
        "room_types_with_occupancy": [{
            "room_type_id": "RT02",
            "adult_count": 1
        }]
    }


@pytest.fixture
def other_date_range_linkage_update_request_payload():
    return {
        "start_date": "2021-12-20",
        "end_date": "2021-12-28",
        "property_id": "123",
        "room_types_with_occupancy": [{
            "room_type_id": "RT01",
            "adult_count": 1
        }],
        "linkage": {
            "linkage_equation": {
                "fixed_change": "20",
                "percentage_change": 80
            },
            "linkage_type": "other_date_range",
            "other_date_range_linkage_detail": {
                "start_date": "2021-11-20",
                "end_date": "2021-11-28",
            }
        }
    }


@pytest.fixture
def other_date_range_linkage_update_request_with_no_end_date_payload():
    return {
        "start_date": "2021-11-20",
        "property_id": "123",
        "room_types_with_occupancy": [{
            "room_type_id": "RT01",
            "adult_count": 1
        }],
        "linkage": {
            "linkage_equation": {
                "fixed_change": "20",
                "percentage_change": 80
            },
            "linkage_type": "other_date_range",
            "other_date_range_linkage_detail": {
                "start_date": "2021-10-20",
                "end_date": "2021-10-28",
            }
        }
    }


@pytest.fixture
def other_room_linked_rate_update_request(request, client, flat_price_date_range_rate_update_request):
    start_date = request.param.get('start_date', '2020-12-10') if hasattr(request, 'param') else '2020-12-10'
    end_date = request.param.get('end_date', '2020-12-18') if hasattr(request, 'param') else '2020-12-18'

    flat_price_date_range_rate_update_request['start_date'] = start_date
    flat_price_date_range_rate_update_request['end_date'] = end_date

    # First update the rates to which the rates in this fixture needs to be linked to
    headers = {
        "X-User-Type": "fdm",
        "X-User": "batman"
    }
    with mock_role_manager():
        response = client.post('/rate-manager/v1/room-base-rates', json=flat_price_date_range_rate_update_request,
                               headers=headers)
    assert response.status_code == 200

    return {
        "start_date": start_date,
        "end_date": end_date,
        "property_id": "123",
        "room_types_with_occupancy": [{
            "room_type_id": "RT01",
            "adult_count": 1
        }],
        "linkage": {
            "linkage_equation": {
                "fixed_change": "20",
                "percentage_change": 80
            },
            "linkage_type": "other_room_category",
            "other_room_category_linkage_detail": {
                "room_type_id": "RT02",
                "adult_count": 1
            }
        }
    }
