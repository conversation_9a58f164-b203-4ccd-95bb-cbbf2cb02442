from tests.mockers import mock_role_manager


def test_create_package_creates_new_package_in_db(client, package_request, package_repo):
    headers = {
        "X-User-Type": "fdm",
        "X-User": "batman"
    }
    with mock_role_manager():
        response = client.post('/rate-manager/v1/packages', json=package_request, headers=headers)
    assert response.status_code == 200

    packages = package_repo.load_packages(property_id=package_request['property_id'])

    assert len(packages) == 1
    assert packages[0].package_name == package_request['package_name']

    # assert inclusions
    assert len(packages[0].inclusions) == 1
    assert packages[0].inclusions[0].sku_id == package_request['inclusions'][0]['sku_id']

    # assert offering
    assert packages[0].inclusions[0].offering.offering_type.value == package_request['inclusions'][0]['offering'][
        'offering_type']
    assert packages[0].inclusions[0].offering.offered_quantity == package_request['inclusions'][0]['offering'][
        'offered_quantity']

    # assert frequency
    assert packages[0].inclusions[0].frequency.frequency_type.value == package_request['inclusions'][0]['frequency'][
        'frequency_type']
    assert packages[0].inclusions[0].frequency.count == package_request['inclusions'][0]['frequency']['count']
    assert packages[0].inclusions[0].frequency.day_of_serving.value == package_request['inclusions'][0]['frequency'][
        'day_of_serving']


def test_list_of_packages_from_db(client, package_request, package_repo):
    headers = {
        "X-User-Type": "fdm",
        "X-User": "batman"
    }
    with mock_role_manager():
        response = client.post('/rate-manager/v1/packages', json=package_request, headers=headers)
    assert response.status_code == 200

    with mock_role_manager():
        response = client.get('/rate-manager/v1/packages', query_string={'property_id': package_request['property_id']},
                              headers=headers)
    assert response.status_code == 200

    packages = response.json['data']['packages']

    assert len(packages) == 1
    assert packages[0]['package_name'] == package_request['package_name']

    # assert inclusions
    inclusions = packages[0]['inclusions']
    assert len(inclusions) == 1
    assert inclusions[0]['sku_id'] == package_request['inclusions'][0]['sku_id']

    # assert offering
    assert inclusions[0]['offering']['offering_type'] == package_request['inclusions'][0]['offering']['offering_type']
    assert inclusions[0]['offering']['offered_quantity'] == package_request['inclusions'][0]['offering'][
        'offered_quantity']

    # assert frequency
    assert inclusions[0]['frequency']['frequency_type'] == package_request['inclusions'][0]['frequency'][
        'frequency_type']
    assert inclusions[0]['frequency']['count'] == package_request['inclusions'][0]['frequency']['count']
    assert inclusions[0]['frequency']['day_of_serving'] == package_request['inclusions'][0]['frequency'][
        'day_of_serving']


def test_get_package_for_package_id(client, package_request, package_repo):
    headers = {
        "X-User-Type": "fdm",
        "X-User": "batman"
    }
    with mock_role_manager():
        response = client.post('/rate-manager/v1/packages', json=package_request, headers=headers)
    assert response.status_code == 200
    package_id = response.json['data']['package']['package_id']
    with mock_role_manager():
        response = client.get('/rate-manager/v1/packages/{package_id}'.format(package_id=package_id), headers=headers)
    assert response.status_code == 200

    package = response.json['data']['package']

    assert package['package_name'] == package_request['package_name']
    assert package['package_id'] == package_id

    # assert inclusions
    inclusions = package['inclusions']
    assert len(inclusions) == 1
    assert inclusions[0]['sku_id'] == package_request['inclusions'][0]['sku_id']

    # assert offering
    assert inclusions[0]['offering']['offering_type'] == package_request['inclusions'][0]['offering']['offering_type']
    assert inclusions[0]['offering']['offered_quantity'] == package_request['inclusions'][0]['offering'][
        'offered_quantity']

    # assert frequency
    assert inclusions[0]['frequency']['frequency_type'] == package_request['inclusions'][0]['frequency'][
        'frequency_type']
    assert inclusions[0]['frequency']['count'] == package_request['inclusions'][0]['frequency']['count']
    assert inclusions[0]['frequency']['day_of_serving'] == package_request['inclusions'][0]['frequency'][
        'day_of_serving']


def test_for_forbidden_user_for_create_package(client, package_request):
    with mock_role_manager():
        response = client.post('/rate-manager/v1/packages', json=package_request, headers=None)
    assert response.status_code == 403
    assert response.json["errors"][0]["message"] == "Creating new package not allowed"
