version: "2.1"

services:
  b2b_app_server:
    extends:
      file: prod-base-docker-compose.yml
      service: b2b_base_service
    volumes:
      - "/var/www/static:/usr/src/b2b_app/static"  
    container_name: "b2b_app_server"
    restart: always
    ports:
      - "${HOST_PORT}:8001"
    entrypoint: /usr/src/b2b_app/scripts/gunicorn_start
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
