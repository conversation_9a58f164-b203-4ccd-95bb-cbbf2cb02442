version: "2.1"

services:
  work_queue_consumer:
    extends:
      file: base-docker-compose.yml
      service: b2b_base_service
    restart: always
    command: "python '${WORKER_SCRIPT_PACKAGE}/wq_consumer.py' -u '${RABBITMQ_URL}' '-b=${HOST}'"

  dead_letter_queue_consumer:
    extends:
      file: base-docker-compose.yml
      service: b2b_base_service
    restart: always
    command: "python '${WORKER_SCRIPT_PACKAGE}/dlq_consumer.py' -u '${RABBITMQ_URL}' '-b=${HOST}'"
    container_name: "dead_letter_queue_consumer"

  task_queue_consumer:
    extends:
      file: base-docker-compose.yml
      service: b2b_base_service
    restart: always
    command: "python '${WORKER_SCRIPT_PACKAGE}/task_consumer.py' -u '${RABBITMQ_URL}'  '-b=${HOST}'"
    
  gift_card_order_email_consumer:
    extends:
      file: base-docker-compose.yml
      service: b2b_base_service
    restart: always
    command: "python '${WORKER_SCRIPT_PACKAGE}/gift_card_order_email_consumer.py' -u '${RABBITMQ_URL}' '-b=${HOST}'"
    container_name: "gift_card_order_email_consumer"

  hotel_sync_consumer:
    extends:
      file: base-docker-compose.yml
      service: b2b_base_service
    restart: always
    command: "python '${WORKER_SCRIPT_PACKAGE}/catalog_service_hotel_sync_consumer.py' -u '${RABBITMQ_URL}' '-b=${HOST}'"
    container_name: "hotel_sync_consumer"


  # NOTE: Disabled Auto TCR
  #  crrc_consumer:
  #    extends:
  #      file: base-docker-compose.yml
  #      service: b2b_base_service
  #    restart: always
  #    command: "python '${WORKER_SCRIPT_PACKAGE}/crrc_consumer.py' -u '${RABBITMQ_URL}' '-b=${HOST}'"
  #    container_name: "crrc_consumer"

  payment_consumer:
    extends:
      file: base-docker-compose.yml
      service: b2b_base_service
    restart: always
    command: "python '${WORKER_SCRIPT_PACKAGE}/payment_consumer.py' -u '${RABBITMQ_URL}' '-b=${HOST}'"
    container_name: "payment_consumer"

  crs_event_consumer:
    extends:
      file: base-docker-compose.yml
      service: b2b_base_service
    restart: always
    command: "python '${WORKER_SCRIPT_PACKAGE}/crs_event_listener.py' -u '${RABBITMQ_URL}' '-b=${HOST}'"
    container_name: "b2b_crs_event_listener"


  company_profile_event_consumer:
    extends:
      file: base-docker-compose.yml
      service: b2b_base_service
    restart: always
    command: "python '${WORKER_SCRIPT_PACKAGE}/company_profile_consumer.py' -u '${RABBITMQ_URL}' '-b=${HOST}'"
    container_name: "b2b_company_profile_event_listener"


  company_profile_event_publisher:
    extends:
      file: base-docker-compose.yml
      service: b2b_base_service
    restart: always
    command: "python manage.py start_integration_event_worker"
    container_name: "b2b_company_profile_event_publisher"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}


