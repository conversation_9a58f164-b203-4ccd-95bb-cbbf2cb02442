"""empty message

Revision ID: 6c0d2204615c
Revises: 78f0660617ce
Create Date: 2018-10-09 13:00:14.060754

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "6c0d2204615c"
down_revision = "78f0660617ce"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("sku", sa.Column("parent_sku_code", sa.String(), nullable=True))
    op.drop_constraint("sku_parent_sku_id_fkey", "sku", type_="foreignkey")
    op.create_foreign_key(None, "sku", "sku", ["parent_sku_code"], ["code"])
    op.drop_column("sku", "parent_sku_id")
    op.alter_column(
        "sku_bundle", "child_sku_code", existing_type=sa.VARCHAR(), nullable=False
    )
    op.alter_column(
        "sku_bundle", "composite_sku_code", existing_type=sa.VARCHAR(), nullable=False
    )
    op.drop_constraint(
        "_unique_parent_child_sku_combination", "sku_bundle", type_="unique"
    )
    op.create_unique_constraint(
        "_unique_parent_child_sku_combination",
        "sku_bundle",
        ["composite_sku_code", "child_sku_code"],
    )
    op.drop_constraint(
        "sku_bundle_composite_sku_id_fkey", "sku_bundle", type_="foreignkey"
    )
    op.drop_constraint("sku_bundle_child_sku_id_fkey", "sku_bundle", type_="foreignkey")
    op.drop_column("sku_bundle", "child_sku_id")
    op.drop_column("sku_bundle", "composite_sku_id")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "sku_bundle",
        sa.Column(
            "composite_sku_id", sa.INTEGER(), autoincrement=False, nullable=False
        ),
    )
    op.add_column(
        "sku_bundle",
        sa.Column("child_sku_id", sa.INTEGER(), autoincrement=False, nullable=False),
    )
    op.create_foreign_key(
        "sku_bundle_child_sku_id_fkey",
        "sku_bundle",
        "sku",
        ["child_sku_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "sku_bundle_composite_sku_id_fkey",
        "sku_bundle",
        "sku",
        ["composite_sku_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(
        "_unique_parent_child_sku_combination", "sku_bundle", type_="unique"
    )
    op.create_unique_constraint(
        "_unique_parent_child_sku_combination",
        "sku_bundle",
        ["composite_sku_id", "child_sku_id"],
    )
    op.alter_column(
        "sku_bundle", "composite_sku_code", existing_type=sa.VARCHAR(), nullable=True
    )
    op.alter_column(
        "sku_bundle", "child_sku_code", existing_type=sa.VARCHAR(), nullable=True
    )
    op.add_column(
        "sku",
        sa.Column("parent_sku_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.drop_constraint(None, "sku", type_="foreignkey")
    op.create_foreign_key(
        "sku_parent_sku_id_fkey", "sku", "sku", ["parent_sku_id"], ["id"]
    )
    op.drop_column("sku", "parent_sku_code")
    # ### end Alembic commands ###
