"""empty message

Revision ID: 2edfe373df9a
Revises: b9d409522434
Create Date: 2018-08-24 00:18:32.416607

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '2edfe373df9a'
down_revision = 'b9d409522434'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('charge', sa.Column('charge_split_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('charge', 'charge_split_type')
    # ### end Alembic commands ###
