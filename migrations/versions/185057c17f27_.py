"""empty message

Revision ID: 185057c17f27
Revises: ce659f3e5309
Create Date: 2018-05-20 22:41:22.575503

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '185057c17f27'
down_revision = 'ce659f3e5309'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('booking_action',
    sa.<PERSON>umn('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.<PERSON>an(), nullable=True),
    sa.Column('action_id', sa.String(), nullable=False),
    sa.Column('action_type', sa.String(), nullable=True),
    sa.Column('booking_id', sa.String(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('entity', sa.String(), nullable=True),
    sa.Column('payload', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('action_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('booking_action')
    # ### end Alembic commands ###
