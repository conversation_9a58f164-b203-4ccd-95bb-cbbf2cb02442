"""empty message

Revision ID: eba6cd1aeb9f
Revises: 59ba97939651
Create Date: 2019-04-11 19:31:49.438005

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'eba6cd1aeb9f'
down_revision = '59ba97939651'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('charge', sa.Column('item_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('charge', 'item_id')
    # ### end Alembic commands ###
