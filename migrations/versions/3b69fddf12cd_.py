"""empty message

Revision ID: 3b69fddf12cd
Revises: 63ef322619f3
Create Date: 2018-05-25 01:12:41.302677

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '3b69fddf12cd'
down_revision = '63ef322619f3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('addon', sa.Column('bill_to_type', sa.String(), nullable=True))
    op.add_column('addon', sa.Column('charge_type', sa.String(), nullable=True))
    op.add_column('payment', sa.Column('comment', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('payment', 'comment')
    op.drop_column('addon', 'charge_type')
    op.drop_column('addon', 'bill_to_type')
    # ### end Alembic commands ###
