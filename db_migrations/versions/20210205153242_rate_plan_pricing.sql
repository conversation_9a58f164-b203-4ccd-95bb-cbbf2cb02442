-- revision: '20210205153242_rate_plan_pricing'
-- down_revision: '20210121161512_room_base_rate_linkage'

-- upgrade
CREATE TABLE rate_plan_rate(
    rate_id CHARACTER VARYING PRIMARY KEY,
    rate_plan_id CHARACTER VARYING NOT NULL,
    property_id CHARACTER VARYING NOT NULL,
    room_type_id CHARACTER VARYING NOT NULL,
    adult_count INTEGER NOT NULL,
    price NUMERIC(15,4) NOT NULL,
    stay_date DATE NOT NULL,
    linkage JSONB,
    parent_room_base_rate_id CHARACTER VARYING REFERENCES room_base_rate (room_base_rate_id),
    parent_rate_id CHARACTER VARYING REFERENCES rate_plan_rate (rate_id),
    parent_inclusion_base_rate_ids character varying[],
    persistent_override boolean DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now(),
    modified_at timestamp with time zone DEFAULT now()
);

ALTER TABLE ONLY rate_plan_rate
    ADD CONSTRAINT rate_plan_rate_uc UNIQUE (property_id, rate_plan_id, room_type_id,
                                           adult_count, stay_date);

CREATE TABLE rate_plan_rate_linkage(
    id SERIAL PRIMARY KEY,
    rate_plan_id CHARACTER VARYING NOT NULL,
    property_id CHARACTER VARYING NOT NULL,
    room_type_id CHARACTER VARYING NOT NULL,
    adult_count INTEGER NOT NULL,
    linked_room_type_id  CHARACTER VARYING NOT NULL,
    linked_adult_count INTEGER NOT NULL,
    linked_rate_plan_id CHARACTER VARYING NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    modified_at timestamp with time zone DEFAULT now()
);

ALTER TABLE ONLY rate_plan_rate_linkage
   ADD CONSTRAINT rate_plan_rate_linkage_uc UNIQUE (rate_plan_id, property_id, room_type_id,
                                        adult_count, linked_room_type_id,linked_adult_count,
                                        linked_rate_plan_id);

CREATE TABLE running_rate_plan_rate (
    running_rate_id CHARACTER VARYING PRIMARY KEY,
    rate_plan_id CHARACTER VARYING NOT NULL,
    property_id  CHARACTER VARYING NOT NULL,
    room_type_id CHARACTER VARYING NOT NULL,
    adult_count INTEGER NOT NULL,
    start_date DATE NOT NULL,
    price NUMERIC(15,4) NOT NULL,
    dow_prices JSONB,
    linkage JSONB,
    persistent_override boolean DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now(),
    modified_at timestamp with time zone DEFAULT now(),
    deleted boolean NOT NULL
);
-- downgrade
DROP TABLE running_rate_plan_rate;
ALTER TABLE rate_plan_rate DROP CONSTRAINT rate_plan_rate_uc;
DROP TABLE rate_plan_rate;
ALTER TABLE rate_plan_rate_linkage DROP CONSTRAINT rate_plan_rate_linkage_uc;
DROP TABLE rate_plan_rate_linkage;