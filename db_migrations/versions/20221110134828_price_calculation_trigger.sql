-- revision: '20221110134828_price_calculation_trigger'
-- down_revision: '20221109184533_base_room_price_trigger_id_and_is_skipped'

-- upgrade

--
-- Name: price_calculation_trigger; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE price_calculation_trigger (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    id character varying NOT NULL,
    source character varying(255) NOT NULL,
    price_update_config_id character varying,
    user_email character varying(255),
    abw_start integer,
    abw_end integer,
    hotel_ids character varying
);

ALTER TABLE ONLY price_calculation_trigger
    ADD CONSTRAINT price_calculation_trigger_pkey PRIMARY KEY (id);

-- downgrade

DROP TABLE price_calculation_trigger;
