-- revision: '20221109184533_base_room_price_trigger_id_and_is_skipped'
-- down_revision: '20221102120035_rack_rate_to_hotel_room_type'

-- upgrade
ALTER TABLE base_room_price
    ADD COLUMN price_trigger_id character varying,
    ADD COLUMN sku_code character varying,
    ADD COLUMN is_skipped boolean DEFAULT FALSE NOT NULL,
    ADD COLUMN modified_at timestamp with time zone DEFAULT now() NOT NULL;
CREATE INDEX ix_price_trigger_id_hotel_date ON base_room_price (price_trigger_id, hotel_id, target_date);
CREATE INDEX ix_price_date_skipped_hotel ON base_room_price (target_date, is_skipped, hotel_id);

-- downgrade
DROP INDEX ix_price_trigger_id_hotel_date;
DROP INDEX ix_price_date_skipped_hotel;
ALTER TABLE base_room_price DROP COLUMN price_trigger_id, DROP COLUMN sku_code, DROP COLUMN is_skipped, DROP COLUMN modified_at;
