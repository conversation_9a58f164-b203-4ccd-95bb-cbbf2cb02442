class RateManagerException(Exception):
    error_code = "0001"
    message = "Something went wrong"

    def __init__(self, description=None, extra_payload=None):
        self.description = description
        self.extra_payload = extra_payload

    def __str__(self):
        return "exception: error_code=%s message=%s description=%s" % (
            self.error_code,
            self.message,
            self.description,
        )

    def with_description(self, description):
        self.description = description
        return self

    @property
    def code(self):
        return "0401" + self.error_code


class CyclicLinkageFound(RateManagerException):
    error_code = "0002"
    message = "Cyclic Linkage found while creating a linked rate"


class ResourceNotFound(RateManagerException):
    error_code = "0003"
    message = "Resource not found"

    def __init__(
        self, resource_name=None, resource_id=None, description=None, extra_payload=None
    ):
        if resource_name and resource_id:
            self.message = f"{resource_name} not found: {resource_id}"
        super(ResourceNotFound, self).__init__(
            description=description, extra_payload=extra_payload
        )


class ValidationException(RateManagerException):
    error_code = "0003"
    message = "Validation Exception"

    def __init__(self, message=None, description=None, extra_payload=None):
        self.message = message if message else self.message
        super(ValidationException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class DownstreamSystemFailure(RateManagerException):
    error_code = "0004"
    message = "Downstream system failed."

    def __init__(self, message=None, description=None, extra_payload=None):
        self.message = message
        super(DownstreamSystemFailure, self).__init__(
            description=description, extra_payload=extra_payload
        )


class AuthorizationError(RateManagerException):
    error_code = "0005"
    message = "You're not authorized to perform this operation"

    def __init__(self, description=None, extra_payload=None):
        super(AuthorizationError, self).__init__(
            description=description, extra_payload=extra_payload
        )


class PolicyAuthException(AuthorizationError):
    error_code = "0006"

    def __init__(self, error=None, message=None, description=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error_code
            self.message = message if message else self.message
        super(PolicyAuthException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class HotelIdHeaderInformationIncorrect(RateManagerException):
    error_code = "0007"
    message = (
        "You're not authorised to perform this operation. Please login and try again."
    )

    def __init__(self, message=None):
        self.message = message if message else self.message
        super(HotelIdHeaderInformationIncorrect, self).__init__(
            description="X-Hotel-Id header is incorrect to serve the request"
        )
