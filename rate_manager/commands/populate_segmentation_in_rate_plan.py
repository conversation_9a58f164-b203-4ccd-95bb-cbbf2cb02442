import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from core.common.globals import global_context
from object_registry import inject
from rackrate_service.decorators import session_manager
from rate_manager.domain.value_objects.segment import Segment, SegmentValue
from rate_manager.infrastructure.repositories.rate_plan_repo import RatePlanRepo


def chunks(l, n):
    """Yield successive n-sized chunks from l."""
    for i in range(0, len(l), n):
        yield l[i : i + n]


MAPPINGS_PV = {
    'ACO': 'PL',
    'ACOFF': 'PL',
    'ADVFOC': 'PD',
    'BGPG1': 'OS',
    'BGPG1BB': 'OS',
    'BGPG2': 'OS',
    'BGPG3': 'OS',
    'CD1': 'CI',
    'CD2': 'CI',
    'CNFBB': 'TZ',
    'COMP': 'TZ',
    'COR100': 'CI',
    'DIRUP': 'TD',
    'FLDM1': 'TF',
    'FLDM10': 'TF',
    'FLMRA1': 'TF',
    'FLMRA3': 'TG',
    'FLMRA4': 'TH',
    'FLMRB1': 'TF',
    'FLMRB3': 'TG',
    'FLMRB4': 'TH',
    'FLMSUM': 'TH',
    'FLMSUP': 'TH',
    'FLPKI': 'TH',
    'FLRA1': 'TE',
    'FLRA3': 'TF',
    'FLRA3L': 'TH',
    'FLRA3M': 'TH',
    'FLRA3S': 'TG',
    'FLRA4': 'TG',
    'FLRA4L': 'TH',
    'FLRA4M': 'TH',
    'FLRA4S': 'TG',
    'FLRAF': 'TE',
    'FLRAFB': 'TE',
    'FLRB1': 'TE',
    'FLRB3': 'TF',
    'FLRB3L': 'TH',
    'FLRB3M': 'TH',
    'FLRB3S': 'TG',
    'FLRB4': 'TG',
    'FLRB4L': 'TH',
    'FLRB4M': 'TH',
    'FLRB4S': 'TG',
    'FLSBB': 'TH',
    'FLSUM': 'TE',
    'FLSUP': 'TH',
    'FMRA3L': 'TH',
    'FMRA3M': 'TH',
    'FMRA3S': 'TH',
    'FMRA4L': 'TH',
    'FMRA4M': 'TH',
    'FMRA4S': 'TH',
    'FMRB3L': 'TH',
    'FMRB3M': 'TH',
    'FMRB3S': 'TH',
    'FMRB4M': 'TH',
    'FMRB4S': 'TH',
    'GB2': 'OQ',
    'GP6': 'OQ',
    'GRA1': 'PA',
    'GRA3': 'PB',
    'GRA3L': 'PD',
    'GRA3M': 'PD',
    'GRA3S': 'PC',
    'GRA4': 'PC',
    'GRA4L': 'PD',
    'GRA4M': 'PD',
    'GRA4S': 'PC',
    'GRB1': 'PA',
    'GRB3': 'PB',
    'GRB3M': 'PD',
    'GRB3S': 'PC',
    'GRB4': 'PC',
    'GRB4M': 'PD',
    'GRB4S': 'PC',
    'GSE1_BB': 'ST',
    'GSE1_RO': 'OT',
    'HS1BRN': 'PK',
    'HS3BRN': 'PL',
    'HSE': 'TZ',
    'ITN1': 'CK',
    'LONGSTAY': 'TD',
    'MODBB': 'OZ',
    'MODIF': 'OZ',
    'Owners': 'OZ',
    'Owners-double': 'TZ',
    'Owners-Quad': 'TZ',
    'Owners-single': 'TZ',
    'Owners-triple': 'TZ',
    'PKG': 'TD',
    'PKG1': 'TG',
    'PKG2': 'TD',
    'RA1': 'TA',
    'RA3': 'TB',
    'RA3L': 'TD',
    'RA3M': 'TD',
    'RA3S': 'TC',
    'RA4': 'TC',
    'RA4L': 'TD',
    'RA4M': 'TD',
    'RA4S': 'TC',
    'RAC': 'TA',
    'RAFB': 'TA',
    'RB1': 'TA',
    'RB1D10': 'CI',
    'RB1D11': 'CI',
    'RB1D12': 'CI',
    'RB1D13': 'CI',
    'RB1D14': 'CI',
    'RB1D15': 'CJ',
    'RB1D16': 'CJ',
    'RB1D17': 'CI',
    'RB1D6': 'CI',
    'RB1D7': 'CI',
    'RB1D8': 'CI',
    'RB1D9': 'CI',
    'RB3': 'TB',
    'RB3L': 'TD',
    'RB3M': 'TD',
    'RB3S': 'TC',
    'RB4': 'TC',
    'RB4M': 'TD',
    'RB4S': 'TC',
    'RC1': 'PA',
    'RC3': 'PB',
    'RC3M': 'PD',
    'RC3S': 'PC',
    'RC4': 'PC',
    'RC4M': 'PD',
    'RC4S': 'PC',
    'RDMDA': 'TB',
    'RLGENE': 'TZ',
    'ROOMONLY': 'TD',
    'Stay4P3': 'TD',
    'STAYPPub': 'TD',
    'SUMBB': 'TD',
    'SUPBB': 'TD',
    'SUPERS': 'TD',
    'TGLL1': 'OT',
    'TGLL1BB': 'OT',
    'TGLL2': 'OT',
    'TGLL2BB': 'OT',
    'TGLL3': 'OT',
    'TGLL3BB': 'OT',
    'VSP': 'TD'
}
MAPPINGS_NO_5 = {
    'ACO': 'PL',
    'ACOFF': 'PL',
    'ADVFOC': 'PD',
    'BG': 'OS',
    'BGPG1': 'OS',
    'BGPG1BB': 'OS',
    'BGPG2': 'OS',
    'BGPG2BB': 'OS',
    'BGPG3': 'OS',
    'BGPG3BB': 'OS',
    'CD1': 'CI',
    'CD2': 'CI',
    'CNFBB': 'TZ',
    'COMP': 'TZ',
    'COMPBB': 'TZ',
    'COR100': 'CI',
    'CZ3Z0L': 'CI',
    'CZIU00': 'CI',
    'CZJ602': 'CI',
    'DIRUP': 'TD',
    'FLA': 'TD',
    'FLASH': 'TD',
    'FLASHX': '',
    'FLDM1': 'TF',
    'FLDM10': 'TF',
    'FLFLSH': 'TH',
    'FLMRB3': 'TG',
    'FLMSUM': 'TH',
    'FLRA1': 'TE',
    'FLRA3L': 'TH',
    'FLRA3M': 'TH',
    'FLRAF': 'TE',
    'FLRAFB': 'TE',
    'FLRB1': 'TE',
    'FLRB3': 'TF',
    'FLRB3L': 'TH',
    'FLRB3M': 'TH',
    'FLRB3S': 'TG',
    'FLRB4': 'TG',
    'FLRB4M': 'TH',
    'FLRB4S': 'TG',
    'FLSBB': 'TH',
    'FLSUM': 'TE',
    'FMRA3L': 'TH',
    'FMRA3M': 'TH',
    'FMRB3L': 'TH',
    'FMRB3M': 'TH',
    'FMRB3S': 'TH',
    'FMRB4M': 'TH',
    'FMRB4S': 'TH',
    'GB2': 'OQ',
    'GP6': 'OQ',
    'GRA1': 'PA',
    'GRA3': 'PB',
    'GRA3L': 'PD',
    'GRA3M': 'PD',
    'GRA3S': 'PC',
    'GRA4': 'PC',
    'GRA4L': 'PD',
    'GRA4M': 'PD',
    'GRA4S': 'PC',
    'GRB1': 'PA',
    'GRB3': 'PB',
    'GRB3M': 'PD',
    'GRB3S': 'PC',
    'GRB4': 'PC',
    'GRB4M': 'PD',
    'GRB4S': 'PC',
    'GSE1_BB': 'ST',
    'GSE1_RO': 'ST',
    'HS1BRN': 'PK',
    'HS3BRN': 'PL',
    'HSE': 'TD',
    'ITN1': 'CK',
    'MODBB': 'TZ',
    'MODIF': 'TZ',
    'PKG': 'TD',
    'PKG1': 'TG',
    'PKG2': 'TD',
    'PKGTH1': '',
    'PKGTH2': '',
    'RA1': 'TA',
    'RA3L': 'TD',
    'RA3M': 'TD',
    'RA3S': 'TC',
    'RA4L': 'TD',
    'RA4M': 'TD',
    'RA4S': 'TC',
    'RAC': 'TA',
    'RAFB': 'TA',
    'RB1': 'TA',
    'RB1D10': 'CI',
    'RB1D11': 'CI',
    'RB1D12': 'CI',
    'RB1D13': 'CI',
    'RB1D14': 'CI',
    'RB1D15': 'CJ',
    'RB1D16': 'CJ',
    'RB1D17': 'CI',
    'RB1D20': 'CI',
    'RB1D22': 'CI',
    'RB1D25': 'CI',
    'RB1D6': 'CI',
    'RB1D7': 'CI',
    'RB1D8': 'CI',
    'RB1D9': 'CI',
    'RB3': 'TB',
    'RB3L': 'TD',
    'RB3M': 'TD',
    'RB3S': 'TC',
    'RB4': 'TC',
    'RB4M': 'TD',
    'RB4S': 'TC',
    'RC3': 'PB',
    'RC3M': 'PD',
    'RC3S': 'PC',
    'RC4M': 'PD',
    'RC4S': 'PC',
    'RDMDA': 'TB',
    'RLGENE': 'TZ',
    'ROOMONLY': 'TD',
    'SUMBB': 'TD',
    'SUPBB': 'TD',
    'SUPERS': 'TD',
    'TGLL1': 'OT',
    'TGLL1BB': 'OT',
    'TGLL2': 'OT',
    'TGLL2BB': 'OT',
    'TGLL3': 'OT',
    'TGLL3BB': 'OT',
    'VSP': 'TD',
}
SEGMENT_MAPPING = {
    'TA': {
        'name': 'Public Direct Unrestricted',
        'group_name': 'Transient Public Direct',
    },
    'TB': {'name': 'Public Direct Saver', 'group_name': 'Transient Public Direct'},
    'TC': {
        'name': 'Public Direct Advance Saver',
        'group_name': 'Transient Public Direct',
    },
    'TD': {'name': 'Public Direct Promotions', 'group_name': 'Transient Public Direct'},
    'TE': {
        'name': 'Public Indirect Unrestricted',
        'group_name': 'Transient Public Indirect',
    },
    'TF': {'name': 'Public Indirect Saver', 'group_name': 'Transient Public Indirect'},
    'TG': {
        'name': 'Public Indirect Advance Saver',
        'group_name': 'Transient Public Indirect',
    },
    'TH': {
        'name': 'Public Indirect Promotions',
        'group_name': 'Transient Public Indirect',
    },
    'PA': {
        'name': 'Loyalty card Unrestricted',
        'group_name': 'Preferred Public - Subscribers',
    },
    'PB': {
        'name': 'Loyalty card Saver',
        'group_name': 'Preferred Public - Subscribers',
    },
    'PC': {
        'name': 'Loyalty card Advance Saver',
        'group_name': 'Preferred Public - Subscribers',
    },
    'PD': {
        'name': 'Loyalty card Promotions',
        'group_name': 'Preferred Public - Subscribers',
    },
    'PI': {
        'name': 'Partnerships (Strategic contribution)',
        'group_name': 'Preferred Negotiated - Partnerships',
    },
    'PJ': {
        'name': 'Privileged Partners (High contribution)',
        'group_name': 'Preferred Negotiated - Partnerships',
    },
    'PK': {
        'name': 'Privileged Partners (Medium contribution)',
        'group_name': 'Preferred Negotiated - Partnerships',
    },
    'PL': {
        'name': 'Privileged Partners/ Carte Bienvenue (Low contribution)',
        'group_name': 'Preferred Negotiated - Partnerships',
    },
    'CI': {
        'name': 'Corporate Contract Dynamic & Static (Strategic Contribution)',
        'group_name': 'Contracted',
    },
    'CJ': {
        'name': 'Corporate Contract Dynamic & Static (High contribution)',
        'group_name': 'Contracted',
    },
    'CK': {
        'name': 'Wholesale Contract Static (Medium contribution)',
        'group_name': 'Contracted',
    },
    'CL': {
        'name': 'Wholesale Contract Static (Low contribution)',
        'group_name': 'Contracted',
    },
    'OQ': {'name': 'One Shot Small Meetings', 'group_name': 'MICE One-Off'},
    'OR': {'name': 'One Shot Large Meetings', 'group_name': 'MICE One-Off'},
    'OS': {'name': 'One Shot Business Groups', 'group_name': 'MICE One-Off'},
    'OT': {'name': 'One Shot Leisure Groups', 'group_name': 'Leisure Groups - One-Off'},
    'SQ': {'name': 'Series Small Meetings', 'group_name': 'MICE Series'},
    'SR': {'name': 'Series Large Meetings', 'group_name': 'MICE Series'},
    'SS': {'name': 'Series Busienss Groups', 'group_name': 'MICE Series'},
    'ST': {'name': 'Series Leisure Groups', 'group_name': 'Series Leisure Groups'},
    'CQ': {'name': 'Contracted Small Meetings', 'group_name': 'MICE Contracted'},
    'CR': {'name': 'Contracted Business Groups', 'group_name': 'MICE Contracted'},
    'CS': {'name': 'Contracted Small Meetings', 'group_name': 'MICE Contracted'},
    'CT': {
        'name': 'Contracted Leisure Groups',
        'group_name': 'Contracted Leisure Groups',
    },
    'OY': {'name': 'Airline Lay Over', 'group_name': 'Crew'},
    'SY': {'name': 'Airline Crew', 'group_name': 'Crew'},
    'CY': {'name': 'Airline Others', 'group_name': 'Crew'},
    'CZ': {'name': 'Others Contracted', 'group_name': 'Other Rates'},
    'PZ': {'name': 'Others Preferred', 'group_name': 'Other Rates'},
    'TZ': {'name': 'Complimentary & DayUse', 'group_name': 'Other Rates'},
    'BA': {'name': 'Restaurant Ext Bar', 'group_name': 'RE'},
    'BR': {'name': 'Restaurant Ext BKF', 'group_name': 'RE'},
    'LU': {'name': 'Restaurant Ext Lunch', 'group_name': 'RE'},
    'DI': {'name': 'Restaurant Ext Dinner', 'group_name': 'RE'},
    'EX': {'name': 'Restaurant Ext', 'group_name': 'RE'},
    'OD': {'name': 'One Day Meeting', 'group_name': 'ME'},
    'AF': {'name': 'Restaurant SM Profess', 'group_name': 'BQ'},
    'BQ': {'name': 'Restaurant SM', 'group_name': 'BQ'},
    'CO': {'name': 'Restaurant SM Cocktail', 'group_name': 'BQ'},
    'FA': {'name': 'Restaurant SM Family', 'group_name': 'BQ'},
    'SL': {'name': 'Restaurant SM Stop Lunches', 'group_name': 'BQ'},
    'NH': {'name': 'Treatment without room', 'group_name': 'CU'},
    'OZ': {'name': 'One-off No Planning', 'group_name': 'Other One-Off'},
}

reports = {
    "rate_plan_without_mappings": [],
    "rate_plan_with_invalid_segments": [],
    "rate_plan_which_are_not_migrated": [],
}
CHUNK_SIZE = 50


@click.command('add_segmentation_details_in_rate_plan')
@inject(rate_plan_repo=RatePlanRepo)
@click.option('--hotel_ids', help="',' separated hotel ids", default='9901871,9907946')
@click.option('--tenant_id', help="Tenant ID for which this command should be run.",
              default=TenantClient.get_default_tenant())
@with_appcontext
def add_segmentation_details_in_rate_plan(rate_plan_repo: RatePlanRepo, hotel_ids=None,
                                          tenant_id=TenantClient.get_default_tenant()):
    click.echo(f"Tenant ID: {tenant_id}")
    global_context.set_tenant_id(tenant_id=tenant_id)
    hotel_ids = hotel_ids.split(',') if hotel_ids else None
    rate_plan_ids = rate_plan_repo.load_all_rate_plan_having_no_segments(hotel_ids=hotel_ids)
    total = len(rate_plan_ids)
    processed = 0
    click.echo(f"{total} bookings found with empty segmentations")
    for ids in chunks(rate_plan_ids, CHUNK_SIZE):
        processed += populate_segments(ids, rate_plan_repo)
        click.echo(f"{processed} out of {total} bookings")
    for _type, report in reports.items():
        click.echo("------------------------")
        click.echo(_type)
        for item in report:
            click.echo(item)


@session_manager
def populate_segments(rate_plan_ids, rate_plan_repo: RatePlanRepo):
    click.echo(f"processing chunk size of {len(rate_plan_ids)}")
    rate_plans = rate_plan_repo.load_all_for_update(rate_plan_ids)
    rate_plan_to_update = []
    for rate_plan in rate_plans:
        segments = get_segmentation(rate_plan.short_code, rate_plan.rate_plan_id, rate_plan.property_id)
        hotel_name = "NO5" if rate_plan.property_id == "9901871" else "PV"
        if segments:
            rate_plan.update_segments(segments)
            rate_plan_to_update.append(rate_plan)
        else:
            reports['rate_plan_which_are_not_migrated'].append(
                "{} - {}- {}".format(hotel_name, rate_plan.short_code, rate_plan.rate_plan_id)
            )
    click.echo(
        f"Found mapping for {len(rate_plan_to_update)} out of {len(rate_plans)}. Updating those.."
    )
    rate_plan_repo.update_all(rate_plan_to_update)
    return len(rate_plan_to_update)


def get_segmentation(rate_plan_code, rate_plan_id, hotel_id):
    market_segment_code = (
        MAPPINGS_NO_5.get(rate_plan_code)
        if hotel_id == "9901871"
        else MAPPINGS_PV.get(rate_plan_code)
    )
    hotel_name = "NO5" if hotel_id == "9901871" else "PV"
    if market_segment_code:
        segment = SEGMENT_MAPPING.get(market_segment_code)
        if segment:
            market_segment = Segment(
                name="Market Segment",
                group_name=segment.get("group_name"),
                value=SegmentValue(code=market_segment_code, name=segment.get("name")),
            )
            return [market_segment]
        else:
            reports["rate_plan_with_invalid_segments"].append(
                "{} : {} : {} : {}".format(
                    hotel_name, rate_plan_id, rate_plan_code, market_segment_code
                )
            )
    else:
        reports["rate_plan_without_mappings"].append(
            "{} : {} : {}".format(hotel_name, rate_plan_id, rate_plan_code)
        )
    return None
