from rate_manager.domain.entities.aggregate_roots.rate_plan_company_profile import RatePlanCompanyProfile


class RatePlanCompanyProfileFactory:
    @staticmethod
    def create_new(rate_plan_id: str, company_profile_id: str = None, superhero_company_code: str = None):
        return RatePlanCompanyProfile(rate_plan_id=rate_plan_id, company_profile_id=company_profile_id,
                                      superhero_company_code=superhero_company_code)
