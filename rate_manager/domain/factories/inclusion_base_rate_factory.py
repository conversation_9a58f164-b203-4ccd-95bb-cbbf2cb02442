import datetime
import typing
from decimal import Decimal

from treebo_commons.utils import dateutils

from rackrate_service.dateutils import weekday_name
from rate_manager.application.dtos.target_base_rates_with_rack_rate import BaseRatesAndRackRate
from rate_manager.constants.rate_manager_constants import LinkageType
from rate_manager.domain.domain_events.domain_event_registry import register_event
from rate_manager.domain.domain_events.inclusion_base_rate.inclusion_base_rate_created import InclusionBaseRateCreated
from rate_manager.domain.entities.aggregate_roots.inclusion_base_rate import InclusionBaseRate
from rate_manager.domain.value_objects.day_of_week_prices import DayOfWeekPricesVO
from rate_manager.domain.entities.aggregate_roots.inclusion_rack_rate import InclusionRackRate
from rate_manager.domain.value_objects.item_ids.inclusion_item_id import InclusionItemId
from rate_manager.utils.id_generator import generate_unique_random_id
from rate_manager.domain.value_objects.rate_linkage import RateLinkageVO


class InclusionBaseRateFactory:
    @staticmethod
    def create_base_rate_without_linkage(
            property_id, item_id: InclusionItemId, start_date: datetime.date, end_date: datetime.date,
            price: Decimal = None, dow_prices: DayOfWeekPricesVO = None,
            exclude_dates: typing.Set[datetime.date] = None,
            only_dates: typing.Set[datetime.date] = None, persistent_override: bool = None,
            target_source_date_mapping: typing.Dict[datetime.date, datetime.date] = None,
            source_base_rates: [InclusionBaseRate] = None):
        if not end_date:
            raise ValueError("Can't create InclusionBaseRate without end date")

        inclusion_base_rates = []
        source_inclusions_group_by_stay_date = {}
        if source_base_rates:
            source_inclusions_group_by_stay_date = {base_rate.stay_date: base_rate for base_rate in
                                                    source_base_rates}

        for date in dateutils.date_range(start_date, end_date, end_inclusive=True):
            if exclude_dates and date in exclude_dates:
                continue

            if only_dates and date not in only_dates:
                continue

            if target_source_date_mapping:
                source_base_rate = source_inclusions_group_by_stay_date.get(target_source_date_mapping[date])
                price = source_base_rate.price

            if price is None:
                inclusion_base_rates.append(
                    InclusionBaseRate(inclusion_base_rate_id=generate_unique_random_id('IBR'), property_id=property_id,
                                      sku_id=item_id.sku_id, price=getattr(dow_prices, weekday_name(date.weekday())),
                                      stay_date=date, linkage=None, persistent_override=persistent_override))
            else:
                inclusion_base_rates.append(
                    InclusionBaseRate(inclusion_base_rate_id=generate_unique_random_id('IBR'), property_id=property_id,
                                      sku_id=item_id.sku_id, price=price, stay_date=date, linkage=None,
                                      persistent_override=persistent_override))

        # if inclusion_base_rates:
        #     register_event(InclusionBaseRateCreated(property_id, item_id, inclusion_base_rates))
        return inclusion_base_rates

    @staticmethod
    def create_base_rate_with_linkage(property_id, item_id: InclusionItemId, start_date: datetime.date,
                                      end_date: datetime.date,
                                      linkage: RateLinkageVO, target_inclusion_base_rates: [InclusionBaseRate],
                                      date_linkage: typing.Dict[datetime.date, datetime.date] = None,
                                      exclude_dates: typing.Set[datetime.date] = None,
                                      only_dates: typing.Set[datetime.date] = None):
        if not end_date:
            raise ValueError("Can't create InclusionBaseRate without end date")

        target_inclusion_group_by_stay_date = {base_rate.stay_date: base_rate for base_rate in
                                               target_inclusion_base_rates}
        inclusion_base_rates = []
        for date in dateutils.date_range(start_date, end_date, end_inclusive=True):
            if exclude_dates and date in exclude_dates:
                continue

            if only_dates and date not in only_dates:
                continue

            if linkage.linkage_type == LinkageType.OTHER_DATE_RANGE:
                target_date = date_linkage.get(date)
            else:
                target_date = date

            target_inclusion_base_rate = target_inclusion_group_by_stay_date.get(target_date)

            price = linkage.linkage_equation.derive_output_price(input_price=target_inclusion_base_rate.price)

            inclusion_base_rates.append(
                InclusionBaseRate(inclusion_base_rate_id=generate_unique_random_id('IBR'), property_id=property_id,
                                  sku_id=item_id.sku_id, price=price, stay_date=date, linkage=linkage,
                                  parent_inclusion_base_rate_id=target_inclusion_base_rate.inclusion_base_rate_id if
                                  target_inclusion_base_rate else None))

        # if inclusion_base_rates:
        #     register_event(InclusionBaseRateCreated(property_id, item_id, inclusion_base_rates))
        return inclusion_base_rates

    @staticmethod
    def create_virtual_base_rates_from_rack_rate(property_id, item_id: InclusionItemId, dates,
                                                 rack_rate: InclusionRackRate):
        if not rack_rate:
            raise ValueError("Rack Rate not configured for [SKU ID: {0}]".format(item_id.sku_id))

        return [InclusionBaseRate(inclusion_base_rate_id=None, property_id=property_id, sku_id=item_id.sku_id,
                                  price=rack_rate.price, stay_date=date) for date in dates]

    @staticmethod
    def create_base_rates_from_rack_rate(property_id, item_id: InclusionItemId, dates,
                                         rack_rate: InclusionRackRate, linkage: RateLinkageVO):
        if not rack_rate:
            raise ValueError("Rack Rate not configured for [SKU ID: {0}]".format(item_id.sku_id))

        return [InclusionBaseRate(inclusion_base_rate_id=generate_unique_random_id('IBR'), property_id=property_id,
                                  sku_id=item_id.sku_id, price=rack_rate.price, stay_date=date,
                                  linkage=linkage, parent_rack_rate_id=rack_rate.inclusion_rack_rate_id)
                for date in dates]
