from rate_manager.domain.value_objects.inclusion import InclusionVO
from rate_manager.utils.id_generator import generate_random_id
from rate_manager.domain.entities.aggregate_roots.package import Package


class PackageFactory:
    @staticmethod
    def create_new(property_id, package_name, inclusions):
        return Package(
            package_id=generate_random_id('PKG'),
            property_id=property_id,
            package_name=package_name,
            inclusions=[InclusionVO.from_json(inclusion) for inclusion in inclusions]
        )
