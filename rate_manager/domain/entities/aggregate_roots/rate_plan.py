import operator

from datetime import datetime
from decimal import Decimal
from typing import List

from rate_manager.application.dtos.pricing.pricing import PriceRequestDTO
from rate_manager.domain.value_objects.occupancy import OccupancyVO
from rate_manager.domain.value_objects.policies import PoliciesVO
from rate_manager.domain.value_objects.restrictions import RestrictionsVO
from rate_manager.domain.value_objects.channel_mapping import ChannelMappingVO
from rate_manager.domain.value_objects.room_type_occupancy_mapping import RoomTypeOccupancyMappingVO
from rate_manager.domain.entities.non_room_night_inclusion_rate import NonRoomNightInclusionRate
from rate_manager.constants.rate_manager_constants import UnitOfChildCharge
from rate_manager.domain.value_objects.segment import Segment


class RatePlan(object):

    def __init__(self,
                 rate_plan_id: str,
                 property_id: str,
                 package_id: str,
                 name: str,
                 room_type_occupancy_mappings: List[RoomTypeOccupancyMappingVO],
                 is_active: bool = True,
                 is_flexi: bool = False,
                 policies: PoliciesVO = None,
                 short_code: str = None,
                 description: str = None,
                 created_at: datetime = None,
                 restrictions: RestrictionsVO = None,
                 channel_mappings: List[ChannelMappingVO] = None,
                 non_room_night_inclusion_rates: List[NonRoomNightInclusionRate] = None,
                 segments: List[Segment] = None,
                 extra_information: dict = None,
                 sell_start_date: datetime.date = None,
                 sell_end_date: datetime.date = None,
                 commission_type: str = None,
                 commission_percent: Decimal = None,
                 print_rate: bool = True,
                 suppress_rate: bool = False,
                 ):

        self.rate_plan_id = rate_plan_id
        self.property_id = property_id
        self._package_id = package_id
        self._is_active = is_active
        self.name = name
        self.short_code = short_code
        self.description = description
        if policies and policies.cancellation_policies:
            cancellations = policies.cancellation_policies
            cancellations.sort(key=operator.attrgetter('cancellation_duration_before_checkin_start'))
            for cancellation in range(0, len(cancellations)):
                if not (cancellations[cancellation].cancellation_duration_before_checkin_start and
                        cancellations[cancellation].cancellation_duration_before_checkin_end):
                    continue
                if bool(cancellations[cancellation].cancellation_duration_before_checkin_start) ^ bool(
                        cancellations[cancellation].cancellation_duration_before_checkin_end):
                    raise ValueError("cancellation_duration_before_checkin_start and "
                                     "cancellation_duration_before_checkin_end both should be present.")
                if cancellations[cancellation].cancellation_duration_before_checkin_start > \
                        cancellations[cancellation].cancellation_duration_before_checkin_end:
                    raise ValueError("Checkin_start cannot be greater than checkin_end")

                if cancellation > 0 and (cancellations[cancellation].cancellation_duration_before_checkin_start <
                                         cancellations[cancellation - 1].cancellation_duration_before_checkin_end):
                    raise ValueError("Cannot allow overlapping of layering days")
        self._policies = policies
        self.restrictions = restrictions
        self._channel_mappings = channel_mappings if channel_mappings is not None else []
        self.room_type_occupancy_mappings = room_type_occupancy_mappings
        self.non_room_night_inclusion_rates = non_room_night_inclusion_rates
        self.created_at = created_at
        self.is_flexi = is_flexi
        self.segments = segments if segments is not None else []
        self.extra_information = extra_information
        self.sell_start_date = sell_start_date
        self.sell_end_date = sell_end_date
        self.commission_type = commission_type
        self.commission_percent = commission_percent
        self.print_rate = print_rate
        self.suppress_rate = suppress_rate


    @property
    def package_id(self):
        return self._package_id

    @property
    def channel_mappings(self):
        return self._channel_mappings

    @channel_mappings.setter
    def channel_mappings(self, value):
        self._channel_mappings = value

    @property
    def is_active(self):
        return self._is_active

    @is_active.setter
    def is_active(self, value):
        self._is_active = value
        if not self._is_active:
            for channel_mapping in self._channel_mappings:
                channel_mapping.active = False

    @property
    def policies(self):
        return self._policies

    @policies.setter
    def policies(self, value):
        if hasattr(value, 'child_policy'):
            self._policies.child_policy = value.child_policy
        if hasattr(value, 'payment_policies'):
            self._policies.payment_policies = value.payment_policies
        if hasattr(value, 'cancellation_policies'):
            self._policies.cancellation_policies = value.cancellation_policies
        if hasattr(value, 'custom_policies'):
            self._policies.custom_policies = value.custom_policies
        if hasattr(value, 'reward_policy'):
            self._policies.reward_policy = value.reward_policy

    def is_room_type_occupancy_mapped(self, room_type_id, occupancy):
        room_type_occupancy_mapping = RoomTypeOccupancyMappingVO(room_type_id, occupancy)
        return room_type_occupancy_mapping in self.room_type_occupancy_mappings

    def is_valid_restriction(self, booking_date, stay_start, stay_end):
        if not self.restrictions:
            return True
        los = (stay_end - stay_start).days + 1
        bw = (stay_start - booking_date).days
        if (self.restrictions.minimum_los and los < self.restrictions.minimum_los or
                self.restrictions.maximum_los and los > self.restrictions.maximum_los or
                self.restrictions.minimum_abw and bw < self.restrictions.minimum_abw or
                self.restrictions.maximum_abw and bw > self.restrictions.maximum_abw):
            return False
        return True

    def is_boundary_within_sell_dates(self, start, end=None):
        end = end if end else start
        assert start <= end
        if self.sell_start_date and self.sell_start_date > start:
            return False
        if self.sell_end_date and self.sell_end_date < end:
            return False
        return True

    def are_children_allowed(self):
        if self.policies and self.policies.child_policy and self.policies.child_policy.child_allowed is False:
            return False
        return True

    def get_children_price(self, child_count, adult_price):
        children_price = 0
        if self.policies and self.policies.child_policy:
            charge_per_child = self.policies.child_policy.charge_per_child
            unit_of_charge = self.policies.child_policy.unit_of_charge

            if unit_of_charge == UnitOfChildCharge.FIXED_VALUE.value:
                children_price = charge_per_child * child_count
            elif unit_of_charge == UnitOfChildCharge.PERCENTAGE_OF_ADULT_SINGLE_OCCUPANCY_PRICE.value:
                children_price = ((charge_per_child * adult_price) / 100) * child_count
        return children_price

    def supports_price_request(self, price_request: PriceRequestDTO, room_type_occupancy_stay):
        if not self.is_valid_restriction(
                price_request.booking_date,
                room_type_occupancy_stay['stay_start'],
                room_type_occupancy_stay['stay_end']
        ):
            return False

        if not self.is_valid_for_stay_dates(
            room_type_occupancy_stay["stay_start"], room_type_occupancy_stay["stay_end"]
        ):
            return False

        occupancy = room_type_occupancy_stay['occupancy']
        if not isinstance(occupancy, OccupancyVO):
            occupancy = OccupancyVO(adult_count=int(occupancy['adults']), child_count=int(occupancy.get('children', 0)))

        if occupancy.child_count >= 1 and not self.are_children_allowed():
            return False

        if not self.is_room_type_occupancy_mapped(room_type_occupancy_stay['room_type_id'], OccupancyVO(
                adult_count=occupancy.adult_count)):
            return False

        return True

    def update_room_type_occupancy_mappings(self, room_type_occupancy_mappings: List[RoomTypeOccupancyMappingVO]):
        self.room_type_occupancy_mappings = room_type_occupancy_mappings

    def update_segments(self, segments: List[Segment]):
        self.segments = segments

    def is_valid_for_stay_dates(self, stay_start, stay_end):
        if not (self.sell_start_date or self.sell_end_date):
            return True
        elif self.sell_start_date and self.sell_end_date:
            return self.sell_start_date <= stay_start <= stay_end <= self.sell_end_date
        elif self.sell_start_date:
            return self.sell_start_date <= stay_start
        elif self.sell_end_date:
            return self.sell_end_date >= stay_end
