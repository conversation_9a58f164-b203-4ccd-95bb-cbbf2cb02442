import typing

from rackrate_service.dateutils import map_source_range_to_target_range_with_cycle
from rate_manager.application.dtos.target_base_rates_with_rack_rate import BaseRatesAndRackRate
from rate_manager.constants.rate_manager_constants import LinkageType
from rate_manager.domain.entities.aggregate_roots.inclusion_base_rate import InclusionBaseRate
from rate_manager.domain.entities.aggregate_roots.room_base_rate import RoomBaseRate


class BaseRateDomainService:
    @staticmethod
    def find_active_running_rates_with_linkages(running_room_base_rates, start_date, end_date):
        date_slot = (start_date, end_date)
        date_range_wise_running_base_rate_with_linkage = dict()
        # TODO: Ensure running room base rates is sorted in reverse order by created_at
        # Last created running base rates overrides the running base rate created before it, for any subset of date
        # range
        # If any of the effectively active running base rate has got linkage, that linkage should get automatically
        # applied to the rate newly created on that specific date, after running base rate is created
        # This is needed because when running base rate is created, we don't create new rates, but only update the
        # rates already created

        # Also, when this running base rate linkage is attached to newly created rates, it'll be attached in
        # overridden form
        for base_rate in running_room_base_rates:
            if base_rate.start_date < date_slot[1]:
                if base_rate.linkage:
                    # Pick this running rate only if there is a linkage
                    date_range_wise_running_base_rate_with_linkage[(base_rate.start_date, date_slot[1])] = base_rate

                date_slot = (start_date, base_rate.start_date)

                if date_slot[1] < date_slot[0]:
                    break
        return date_range_wise_running_base_rate_with_linkage

    @staticmethod
    def create_base_rate_with_linkage(
            existing_base_rates, property_id, item_id, start_date, linkage,
            target_base_rates: typing.Union[typing.List[RoomBaseRate], typing.List[InclusionBaseRate]],
            end_date, base_rate_factory, only_dates=None):

        return base_rate_factory.create_base_rate_with_linkage(
            property_id, item_id, start_date, end_date, linkage, target_base_rates,
            date_linkage=BaseRateDomainService._generate_date_linkage(linkage, existing_base_rates,
                                                                      start_date=start_date, end_date=end_date),
            exclude_dates={base_rate.stay_date for base_rate in existing_base_rates} if existing_base_rates else None,
            only_dates=only_dates)

    @staticmethod
    def update_base_rate_with_linkage(
            existing_base_rates: typing.Union[typing.List[RoomBaseRate], typing.List[InclusionBaseRate]], linkage,
            target_base_rates: typing.Union[typing.List[RoomBaseRate], typing.List[InclusionBaseRate]],
            room_type_name=None, sku_name=None, linked_room_type_name=None, linked_sku_name=None,persistent_override=None
    ):
        if not existing_base_rates:
            return

        date_linkage = BaseRateDomainService._generate_date_linkage(linkage, existing_base_rates)

        target_base_rate_group_by_stay_date = {base_rate.stay_date: base_rate for base_rate in
                                               target_base_rates}

        for base_rate in existing_base_rates:
            if linkage.linkage_type == LinkageType.OTHER_DATE_RANGE:
                target_date = date_linkage.get(base_rate.stay_date)
            else:
                target_date = base_rate.stay_date

            target_base_rate = target_base_rate_group_by_stay_date.get(target_date)
            base_rate.update_price_and_linkage(price=None, linkage=linkage, target_base_rate=target_base_rate,
                                               room_type_name=room_type_name, sku_name=sku_name,
                                               linked_room_type_name=linked_room_type_name,
                                               linked_sku_name=linked_sku_name, persistent_override=persistent_override)

    @staticmethod
    def _generate_date_linkage(linkage, existing_base_rates, start_date=None, end_date=None):
        if linkage.linkage_type != LinkageType.OTHER_DATE_RANGE:
            return None

        if not start_date:
            start_date = min(base_rate.stay_date for base_rate in existing_base_rates)
            end_date = max(base_rate.stay_date for base_rate in existing_base_rates)

        return map_source_range_to_target_range_with_cycle(
            source_range=(start_date, end_date),
            target_range=linkage.linkage_detail.date_range.as_tuple())
