class RestrictionsVO:
    def __init__(self, minimum_abw: int = None, maximum_abw: int = None, minimum_los: int = None, maximum_los: int = None):
        self.minimum_abw = minimum_abw
        self.maximum_abw = maximum_abw
        self.minimum_los = minimum_los
        self.maximum_los = maximum_los

    def to_json(self):
        return {
            'minimum_abw': self.minimum_abw,
            'maximum_abw': self.maximum_abw,
            'minimum_los': self.minimum_los,
            'maximum_los': self.maximum_los
        }

    @classmethod
    def from_json(cls, restrictions_json):
        return cls(
            minimum_abw=restrictions_json.get('minimum_abw'),
            maximum_abw=restrictions_json.get('maximum_abw'),
            minimum_los=restrictions_json.get('minimum_los'),
            maximum_los=restrictions_json.get('maximum_los')
        )
