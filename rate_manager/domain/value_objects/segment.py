class SegmentValue:
    def __init__(self, name=None, code=None):
        self.name = name
        self.code = code

    def to_json(self):
        return dict(
            name=self.name,
            code=self.code,
        )

    @staticmethod
    def from_json(json):
        return SegmentValue(name=json['name'], code=json['code'])


class Segment:
    def __init__(self, name=None, value=None, group_name=None):
        self.name = name
        self.value = value
        self.group_name = group_name

    def to_json(self):
        return dict(
            name=self.name,
            value=self.value.to_json() if self.value else None,
            group_name=self.group_name,
        )

    @staticmethod
    def from_json(json):
        return Segment(
            name=json['name'],
            value=SegmentValue.from_json(json['value']) if json.get('value') else None,
            group_name=json['group_name'],
        )
