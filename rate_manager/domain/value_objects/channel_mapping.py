class ChannelMappingVO(object):
    def __init__(self, channel: str, sub_channel: str, active: bool, channel_rate_plan_code: str):
        self.sub_channel = sub_channel
        self.channel = channel
        self.active = active
        self.channel_rate_plan_code = channel_rate_plan_code

    def to_json(self):
        return {
            "sub_channel": self.sub_channel,
            "channel": self.channel,
            "active": self.active,
            "channel_rate_plan_code": self.channel_rate_plan_code
        }

    @classmethod
    def from_json(cls, channel_mapping_json):
        return cls(sub_channel=channel_mapping_json.get('sub_channel'),
                   channel=channel_mapping_json.get('channel'),
                   active=channel_mapping_json.get('active'),
                   channel_rate_plan_code=channel_mapping_json.get('channel_rate_plan_code'))
