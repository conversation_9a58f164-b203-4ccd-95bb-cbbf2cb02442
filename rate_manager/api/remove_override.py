from flask import Blueprint

from api_docs import swag_route
from object_registry import inject
from rackrate_service.api.api_response import ApiResponse
from rackrate_service.api.request_parsers import schema_wrapper_parser
from rate_manager.api.request_parsers import read_user_data_from_request_header
from rate_manager.api.schemas.request.remove_override_schemas import RemoveOverride
from rate_manager.api.schemas.response.inclusion_rate_schema import InclusionBaseRateResponse
from rate_manager.api.schemas.response.rate_schemas import RatePlanRateResponseSchema
from rate_manager.api.schemas.response.room_base_rate_schema import RoomBaseRateResponse
from rate_manager.application.services.inclusion_rate_service import InclusionRateService
from rate_manager.application.services.rate_plan_rate_service import RateService
from rate_manager.application.services.room_base_rate_service import RoomBaseRateService

bp = Blueprint("Remove Override", __name__, url_prefix="/v1")


@swag_route
@bp.route("/remove-override", methods=["POST"])
@schema_wrapper_parser(RemoveOverride)
@inject(rate_plan_rate_service=RateService, room_base_rate_service=RoomBaseRateService,
        inclusion_base_rate_service=InclusionRateService)
def remove_override(rate_plan_rate_service: RateService, room_base_rate_service: RoomBaseRateService,
                    inclusion_base_rate_service: InclusionRateService,
                    parsed_request):
    """Remove Override from Rates
    ---
    operationId: remove_override
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Remove override for a rate plan rate or base rates
        tags:
            - Rate Manager / Remove Override
        parameters:
            - in: body
              name: body
              description: Data to remove override
              required: True
              schema:
                $ref: "#/definitions/RemoveOverride"
        responses:
            200:
                description: Rate for which override has been removed
                schema:
                    type: object
                    properties:
                        data_when_called_for_inclusion_base_rate:
                            $ref: "#/definitions/InclusionBaseRateResponse"
                        data_when_called_for_room_base_rate:
                            $ref: "#/definitions/RoomBaseRateResponse"
                        data_when_called_for_rate_plan_rate:
                            $ref: "#/definitions/RatePlanRateResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    _ = read_user_data_from_request_header()
    if parsed_request.get("rate_plan_id"):
        rate = rate_plan_rate_service.remove_override(parsed_request["property_id"], parsed_request["rate_plan_id"],
                                                      parsed_request["room_type_with_occupancy"],
                                                      parsed_request["stay_date"])
        response = RatePlanRateResponseSchema().dump(rate)
    else:
        if parsed_request.get("room_type_with_occupancy"):
            rate = room_base_rate_service.remove_override(parsed_request["property_id"],
                                                          parsed_request["room_type_with_occupancy"],
                                                          parsed_request["stay_date"])
            response = RoomBaseRateResponse().dump(rate)
        else:
            rate = inclusion_base_rate_service.remove_override(parsed_request["property_id"],
                                                               parsed_request["sku_id"],
                                                               parsed_request["stay_date"])
            response = InclusionBaseRateResponse().dump(rate)
    return ApiResponse.build(status_code=200, data=response.data)
