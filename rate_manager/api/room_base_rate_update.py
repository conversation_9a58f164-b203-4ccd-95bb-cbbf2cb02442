from flask import Blueprint

from api_docs import swag_route
from object_registry import inject
from rackrate_service.api.api_response import ApiResponse
from rackrate_service.api.request_parsers import schema_wrapper_parser, RequestTypes
from rate_manager.api.schemas.request.room_base_rate_schemas import UpdateRoomBaseRateSchema, GetRoomBaseRateSchema
from rate_manager.api.schemas.response.room_base_rate_schema import GetRoomBaseRateResponse, \
    GetRoomBaseRateCompressedResponse
from rate_manager.application.commands.room_base_rate_update_command import RoomBaseRateUpdateCommand
from rate_manager.application.services.room_base_rate_service import RoomBaseRateService
from rate_manager.api.request_parsers import read_user_data_from_request_header

bp = Blueprint("RoomBaseRate", __name__, url_prefix="/v1")


@swag_route
@bp.route("/room-base-rates", methods=["POST"])
@schema_wrapper_parser(UpdateRoomBaseRateSchema)
@inject(room_base_rate_service=RoomBaseRateService)
def update_room_base_rate(room_base_rate_service, parsed_request):
    """Update Room Base Rate
    ---
    operationId: update_room_base_rate
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Updates base rates for rooms on one or more dates
        tags:
            - Rate Manager / Room
        parameters:
            - in: body
              name: body
              description: Data to update room base rates
              required: True
              schema:
                $ref: "#/definitions/UpdateRoomBaseRateSchema"
        responses:
            200:
                description: Success response message
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                message:
                                    type: string
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    update_command = RoomBaseRateUpdateCommand(parsed_request.get('property_id'),
                                               parsed_request.get('room_types_with_occupancy'),
                                               parsed_request.get('start_date'),
                                               end_date=parsed_request.get('end_date'),
                                               date_range_for_price_copy=parsed_request.get(
                                                   'date_range_for_price_copy'),
                                               price=parsed_request.get('price'),
                                               dow_prices=parsed_request.get('dow_prices'),
                                               linkage=parsed_request.get('linkage'),
                                               persistent_override=parsed_request.get('persistent_override'),
                                               action_type=parsed_request.get('action_type'),
                                               comments=parsed_request.get('comments'))
    room_base_rate_service.update_room_base_rate(update_commands=[update_command], user_data=user_data)
    return ApiResponse.build(status_code=200, data=dict(message="Rate Updated"))


@swag_route
@bp.route("/room-base-rates", methods=["GET"])
@schema_wrapper_parser(GetRoomBaseRateSchema, param_type=RequestTypes.ARGS)
@inject(room_base_rate_service=RoomBaseRateService)
def get_room_base_rates(room_base_rate_service, parsed_request):
    """Fetch Room Base Rate for given property and sku, between given date range
    ---
    operationId: get_room_base_rates
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Gets base rates for rooms for given input parameters
        tags:
            - Rate Manager / Room
        parameters:
            - in: query
              name: base_rate_search_param
              required: True
              schema: GetRoomBaseRateSchema
        responses:
            200:
                description: List of room base rates. The rates response will come inside `data` key only.
                    The schema will be different based on whether compressed response is requested or not.
                    `data_when_requested_compressed` -> is just a work-around to show alternate response schema in
                    api docs. But the actual key will be `data` only
                schema:
                    type: object
                    properties:
                        data_when_requested_compressed:
                            $ref: "#/definitions/GetRoomBaseRateCompressedResponse"
                        data:
                            $ref: "#/definitions/GetRoomBaseRateResponse"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    room_base_rates = room_base_rate_service.get_room_base_rates(
        parsed_request.get('property_id'), parsed_request.get('start_date'),
        parsed_request.get('end_date'), parsed_request.get('item_ids'),
        compress=parsed_request.get('compressed', True))

    if parsed_request.get('compressed', True):
        response = GetRoomBaseRateCompressedResponse().dump(dict(rates=room_base_rates))
    else:
        response = GetRoomBaseRateResponse().dump(dict(rates=room_base_rates))
    return ApiResponse.build(status_code=200, data=response.data)
