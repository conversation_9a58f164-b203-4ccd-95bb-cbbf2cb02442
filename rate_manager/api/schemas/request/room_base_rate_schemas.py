from decimal import Decimal

from marshmallow import Schema, fields, validates_schema, ValidationError
from marshmallow.validate import Range, OneOf

from api_docs import swag_schema
from rate_manager.api.schemas.request.child_schemas import DayOfWeekPricesSchema, DateRangeSchema, \
    RoomTypesWithOccupancy
from rate_manager.api.schemas.request.linkage_schemas import RateLinkageSchema
from rate_manager.constants.rate_manager_constants import LinkageType
from rate_manager.domain.value_objects.item_ids.room_base_rate_item_id import RoomItemId
from rate_manager.domain.value_objects.occupancy import OccupancyVO


@swag_schema
class RoomBaseRateLinkageSchema(RateLinkageSchema):
    linkage_type = fields.String(
        validate=OneOf([LinkageType.OTHER_ROOM_CATEGORY.value, LinkageType.OTHER_DATE_RANGE.value]))


@swag_schema
class UpdateRoomBaseRateSchema(Schema):
    property_id = fields.String(required=True)
    room_types_with_occupancy = fields.Nested(RoomTypesWithOccupancy, many=True, required=True)
    start_date = fields.Date(required=True)
    end_date = fields.Date(allow_none=True)
    date_range_for_price_copy = fields.Nested(DateRangeSchema)
    price = fields.Decimal(validate=[Range(min=Decimal('0'), error="Price cannot be negative")])
    dow_prices = fields.Nested(DayOfWeekPricesSchema)
    linkage = fields.Nested(RoomBaseRateLinkageSchema, allow_none=True)
    persistent_override = fields.Boolean()
    action_type = fields.String()
    comments = fields.String(allow_none=True)

    @validates_schema
    def validate_data(self, data):
        if data.get('price') and data.get('dow_prices'):
            raise ValidationError("Please provide only one out of 'price' or 'dow_prices'")

        if len(data.get('room_types_with_occupancy')) > 1 and (data.get('dow_prices') or data.get('price')):
            raise ValidationError("Cannot provide any of 'price' or 'dow_prices' with multiple "
                                  "room_types_with_occupancy")
        if data.get('linkage') and (data.get('linkage').linkage_type != LinkageType.OTHER_DATE_RANGE and
                                    len(data.get('room_types_with_occupancy')) > 1):
            raise ValidationError("Cannot provide {linkage_name} linkage with multiple "
                                  "room_types_with_occupancy".format(linkage_name=data['linkage'].linkage_type.value))


@swag_schema
class GetRoomBaseRateSchema(Schema):
    property_id = fields.String(required=True)
    start_date = fields.Date(required=True)
    end_date = fields.Date(required=True)
    room_category_max_occupancies = fields.String(
        required=False,
        description="A comma-separated list of 'room_category-max_occupancy' combination. Max Occupancy is the "
                    "occupancy till which you'll get prices for all occupancies, for that room category. For e.g., "
                    "if you need price for 'RT01', for single and double occupancy, and for 'RT02' for single, "
                    "double and triple occupancy, then value for this field would be: 'RT01-2,RT02-3'")
    compressed = fields.Boolean(required=False, missing=True,
                                description="If true, the response will club multiple rates in sequential dates with "
                                            "same rates into one object with date range. With this the response will "
                                            "not have rate_id, or parent_rate_id")

    @validates_schema
    def validate_data(self, data):
        data['item_ids'] = set()
        if data.get('room_category_max_occupancies'):
            for room_category_max_occupancy in data['room_category_max_occupancies'].split(","):
                room_category, max_occupancy = room_category_max_occupancy.split("-")
                for occupancy in range(int(max_occupancy)):
                    data['item_ids'].add(RoomItemId(room_category, OccupancyVO(occupancy + 1)))


@swag_schema
class UpdateRoomRackRateSchema(Schema):
    rack_rate = fields.Decimal(required=True, validate=[Range(min=Decimal('0'), error="Rack Rate cannot be negative")])