from flask import Blueprint

from api_docs import swag_route
from object_registry import inject
from rackrate_service.api.api_response import ApiResponse
from rackrate_service.api.request_parsers import schema_wrapper_parser, RequestTypes
from rate_manager.api.schemas.request.rate_schemas import UpdateRatePlanRateSchema, GetRatePlanRatesSchema, \
    GetPricingSchema, UpdateRatePlanRateMultipleOccupancy
from rate_manager.api.schemas.response.rate_schemas import (GetRatePlanRatesResponseSchema,
                                                            GetRatePlanRatesCompressedResponseSchema,
                                                            GetPricingResponseSchema)
from rate_manager.application.commands.rate_plan_rate_update_command import RatePlanRateUpdateCommand
from rate_manager.application.dtos.pricing.pricing import PriceRequestDTO
from rate_manager.application.services.rate_plan_rate_service import RateService
from rate_manager.api.request_parsers import read_user_data_from_request_header

bp = Blueprint("Rates", __name__, url_prefix="/v1")


@swag_route
@bp.route("rate-plans/<rate_plan_id>/rates", methods=["POST"])
@schema_wrapper_parser(UpdateRatePlanRateSchema)
@inject(rate_service=RateService)
def update_rate_plan_rate(rate_service, rate_plan_id, parsed_request):
    """Update Rate Plan Rate
    ---
    operationId: base_rate
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Updates rates for rate plans on one or more dates
        tags:
            - Rate Manager / Rate Plans
        parameters:
            - in: body
              name: body
              description: Data to update rate plan rates
              required: True
              schema:
                $ref: "#/definitions/UpdateRatePlanRateSchema"
        responses:
            200:
                description: Success response message
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                message:
                                    type: string
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    update_command = RatePlanRateUpdateCommand(parsed_request.get('property_id'),
                                               rate_plan_id,
                                               parsed_request.get('room_types_with_occupancy'),
                                               parsed_request.get('start_date'),
                                               end_date=parsed_request.get('end_date'),
                                               date_range_for_price_copy=parsed_request.get(
                                                   'date_range_for_price_copy'),
                                               price=parsed_request.get('price'),
                                               dow_prices=parsed_request.get('dow_prices'),
                                               linkage=parsed_request.get('linkage'),
                                               persistent_override=parsed_request.get('persistent_override'),
                                               action_type=parsed_request.get('action_type'),
                                               comments=parsed_request.get('comments'),
                                               company_profile_id=parsed_request.get('company_profile_id'))
    rate_service.update_rate_plan_rate(update_command=update_command, user_data=user_data)

    return ApiResponse.build(status_code=200, data=dict(message="Rate Updated"))


@swag_route
@bp.route("rate-plans/<rate_plan_id>/rates", methods=["GET"])
@schema_wrapper_parser(GetRatePlanRatesSchema, param_type=RequestTypes.ARGS)
@inject(rate_service=RateService)
def get_rate_plan_prices(rate_service, rate_plan_id, parsed_request):
    """Get Rate Plan Prices
    ---
    operation_id: get_rate_plan_prices
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get rate plan prices for given date range for supported room type and occupancy
        tags:
            - Rate
        parameters:
            - in: path
              name: rate_plan_id
              description: Rate Plan Id for which prices need to be fetched
              required: True
              type: String
            - in: query
              name: get_rate_plan_price_params
              description: Date Range for which rates need to be fetched
              required: True
              schema: GetRatePlanRatesSchema
        responses:
            200:
                description: Rates of the rate plan for given DATE RANGE and available room type, occupancy
                schema:
                    type: object
                    properties:
                        data_when_requested_compressed:
                            $ref: "#/definitions/GetRatePlanRatesCompressedResponseSchema"
                        data:
                            $ref: "#/definitions/GetRatePlanRatesResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    rate_plan_prices = rate_service.get_rate_plan_prices(rate_plan_id=rate_plan_id,
                                                         property_id=parsed_request.get('property_id'),
                                                         room_types_with_occupancies=parsed_request.get(
                                                             "room_types_with_occupancy"),
                                                         start_date=parsed_request.get('start_date'),
                                                         end_date=parsed_request.get('end_date'),
                                                         compress=parsed_request.get('compressed'),
                                                         user_data=user_data)

    if parsed_request.get('compressed', True):
        response = GetRatePlanRatesCompressedResponseSchema().dump(dict(rates=rate_plan_prices))
    else:
        response = GetRatePlanRatesResponseSchema().dump(dict(rates=rate_plan_prices))
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route("/get-prices-for-rooms", methods=["POST"])
@schema_wrapper_parser(GetPricingSchema)
@inject(rate_service=RateService)
def get_pricing_for_property(rate_service, parsed_request):
    """Get Pricing for a property for valid rate plans
    ---
    operation_id: get_pricing_for_property
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Get prices for a property for given date range, room type and occupancy for rate plans which satisfy
            length of stay restrictions, booking window restrictions and policies.
        tags:
            - Pricing
        parameters:
            - in: body
              name: body
              description: Params like booking date, channel, sub channel, company profile room_type, occupancy,
                stay_dates, etc. for which rate plan wise pricing is required.
              required: True
              schema: GetPricingSchema
        responses:
            200:
                description: Room type, occupancy wise pricing for different rate plans for given stay dates.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/GetPricingResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    _ = read_user_data_from_request_header()
    price_request = PriceRequestDTO(property_id=parsed_request.get('property_id'),
                                    booking_date=parsed_request.get('booking_date'),
                                    room_types_occupancy_stay=parsed_request.get("rooms"),
                                    channel_code=parsed_request.get('channel_code'),
                                    sub_channel_code=parsed_request.get('sub_channel_code'),
                                    company_profile_id=parsed_request.get('company_profile_id'),
                                    rate_plan_id=parsed_request.get('rate_plan_id'),
                                    ignore_stop_sell_dates=parsed_request.get('ignore_stop_sell_dates'),
                                    superhero_company_code=parsed_request.get('superhero_company_code'),
                                    )
    prices = rate_service.get_pricing_for_rate_plans(price_request=price_request)

    response = GetPricingResponseSchema().dump(prices).data
    return ApiResponse.build(status_code=200, data=dict(response))


@swag_route
@bp.route("rate-plans/<rate_plan_id>/multiple-occupancy-rates", methods=["POST"])
@schema_wrapper_parser(UpdateRatePlanRateMultipleOccupancy)
@inject(rate_service=RateService)
def update_rate_plan_rate_for_multiple_occupancy(rate_service, rate_plan_id, parsed_request):
    """Update Rate Plan Rate
    ---
    operationId: base_rate
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Updates rates for rate plans on one or more dates
        tags:
            - Rate Manager / Rate Plans
        parameters:
            - in: body
              name: body
              description: Data to update rate plan rates
              required: True
              schema:
                $ref: "#/definitions/UpdateRatePlanRateMultipleOccupancy"
        responses:
            200:
                description: Success response message
                schema:
                    type: object"""
    user_data = read_user_data_from_request_header()
    rate_service.update_rate_plan_rate_for_multiple_occupancy(user_data, parsed_request.get('property_id'),
                                                              rate_plan_id,
                                                              parsed_request.get('room_types_with_occupancy_and_price'))
    return ApiResponse.build(status_code=200, data=dict(message="Rate Updated"))
