from rate_manager.domain.value_objects.item_ids.rate_plan_rate_item_id import RatePlanItemId
from rate_manager.domain.value_objects.occupancy import OccupancyVO


class RatePlanRateUpdateCommand:
    def __init__(self, property_id, rate_plan_id,room_types_with_occupancy, start_date, end_date=None,
                 date_range_for_price_copy=None, price=None, dow_prices=None, linkage=None, persistent_override=None,
                 action_type=None, comments=None, company_profile_id=None):
        self.property_id = property_id
        self.room_types_with_occupancy = room_types_with_occupancy
        self.start_date = start_date
        self.end_date = end_date
        self.date_range_for_price_copy = date_range_for_price_copy
        self.price = price
        self.dow_prices = dow_prices
        self.linkage = linkage
        self.persistent_override = persistent_override
        self.action_type = action_type
        self.item_ids = [RatePlanItemId(rate_plan_id, room_type_with_occupancy.get('room_type_id'),
                                        OccupancyVO(room_type_with_occupancy.get('adult_count')))
                         for room_type_with_occupancy in room_types_with_occupancy]
        self.comments = comments,
        self.company_profile_id = company_profile_id
