import datetime
import logging
import typing
from collections import defaultdict
from decimal import Decimal

from rackrate_service.dateutils import weekday_name, map_source_range_to_target_range_with_cycle
from rackrate_service.decorators import session_manager
from rate_manager.application.dtos.target_base_rates_with_rack_rate import BaseRatesAndRackRate
from rate_manager.constants.rate_manager_constants import LinkageType
from rate_manager.domain.entities.aggregate_roots.inclusion_base_rate import InclusionBaseRate
from rate_manager.domain.entities.aggregate_roots.room_base_rate import RoomBaseRate
from rate_manager.domain.services.base_rate_service import BaseRateDomainService
from rate_manager.domain.value_objects.date_range import DateRange
from rate_manager.domain.value_objects.day_of_week_prices import DayOfWeekPricesVO
from rate_manager.domain.value_objects.item_ids.base_item_id import ItemId
from rate_manager.domain.value_objects.item_ids.inclusion_item_id import InclusionItemId
from rate_manager.domain.value_objects.item_ids.room_base_rate_item_id import RoomItemId
from rate_manager.domain.value_objects.linkage_equation import LinkageEquationVO
from rate_manager.domain.value_objects.rate_linkage import RateLinkageVO
from rate_manager.utils.collectionutils import flatten_list, group_list

logger = logging.getLogger(__name__)


class AbstractBaseRateService:
    def _create_base_rates_with_linkage(
            self, property_id, item_ids, start_date, end_date, linkage,
            item_id_wise_target_base_rates: typing.Dict[ItemId, typing.Union[typing.List[RoomBaseRate],
                                                                             typing.List[InclusionBaseRate]]],
            item_id_wise_existing_base_rates: typing.Dict[ItemId, typing.Union[typing.List[RoomBaseRate],
                                                                               typing.List[InclusionBaseRate]]]):
        base_rates = []
        for item_id in item_ids:
            base_rates.extend(BaseRateDomainService.create_base_rate_with_linkage(
                item_id_wise_existing_base_rates.get(item_id), property_id,
                item_id, start_date, linkage, item_id_wise_target_base_rates.get(item_id), end_date,
                self.base_rate_factory))

        if base_rates:
            self.base_rate_repo.save_all(base_rates)

        return base_rates

    def _update_base_rates_with_linkage(
            self, property_id,
            item_id_wise_existing_base_rates: typing.Dict[ItemId, typing.Union[typing.List[RoomBaseRate],
                                                                               typing.List[InclusionBaseRate]]],
            linkage,
            item_id_wise_target_base_rates: typing.Dict[ItemId, typing.Union[typing.List[RoomBaseRate],
                                                                             typing.List[InclusionBaseRate]]],
            room_type_detail=None, sku_details=None, linked_room_type_name=None, linked_sku_name=None,
            persistent_override=None
    ):
        all_item_existing_base_rates = []
        for item_id, existing_base_rates in item_id_wise_existing_base_rates.items():
            room_type_name = room_type_detail.get(item_id.room_type_id) if room_type_detail else None
            sku_name = sku_details.get(item_id.sku_id) if sku_details else None
            BaseRateDomainService.update_base_rate_with_linkage(existing_base_rates, linkage,
                                                                item_id_wise_target_base_rates.get(item_id),
                                                                room_type_name=room_type_name, sku_name=sku_name,
                                                                linked_room_type_name=linked_room_type_name,
                                                                linked_sku_name=linked_sku_name,
                                                                persistent_override=persistent_override)
            all_item_existing_base_rates.extend(existing_base_rates)
        self.base_rate_repo.update_all(all_item_existing_base_rates)
        self.base_rate_event_handler.on_update(property_id, all_item_existing_base_rates)

    def _create_base_rate_without_linkage_with_copy(self, property_id, item_id_wise_source_base_rates,
                                                    existing_base_rates, date_range_for_price_copy, start_date,
                                                    end_date, persistent_override=None):
        item_id_wise_new_base_rates = defaultdict(list)
        mapped_dates = map_source_range_to_target_range_with_cycle(
            target_range=(date_range_for_price_copy['start_date'], date_range_for_price_copy['end_date']),
            source_range=(start_date, end_date))
        for item_id, source_base_rates in item_id_wise_source_base_rates.items():
            base_rates = self.base_rate_factory.create_base_rate_without_linkage(
                property_id, item_id,
                start_date=start_date, end_date=end_date, price=None,
                source_base_rates=source_base_rates,
                target_source_date_mapping=mapped_dates,
                exclude_dates={base_rate.stay_date for base_rate in existing_base_rates},
                persistent_override=persistent_override)
            if base_rates:
                item_id_wise_new_base_rates[item_id].extend(base_rates)

        for item_id, base_rates in item_id_wise_new_base_rates.items():
            self._apply_linkage_from_pre_existing_running_base_rate_to_newly_created_rates_without_linkage(
                property_id, item_id, base_rates)

        new_base_rates = flatten_list(item_id_wise_new_base_rates.values())
        if new_base_rates:
            self.base_rate_repo.save_all(new_base_rates)

        return new_base_rates

    def _update_base_rates_without_linkage_with_copy(self, property_id, item_id_wise_existing_base_rates,
                                                     item_id_wise_source_base_rates, date_range_for_price_copy,
                                                     start_date, end_date, persistent_override=None,
                                                     room_type_detail=None, sku_details=None):
        mapped_dates = map_source_range_to_target_range_with_cycle(
            target_range=(date_range_for_price_copy['start_date'], date_range_for_price_copy['end_date']),
            source_range=(start_date, end_date))
        updated_exiting_base_rates = []
        for item_id, existing_base_rates in item_id_wise_existing_base_rates.items():
            source_room_group_by_stay_date = {base_rate.stay_date: base_rate for base_rate in
                                              item_id_wise_source_base_rates.get(item_id)}
            room_type_name = room_type_detail.get(item_id.room_type_id) if room_type_detail else None
            sku_name = sku_details.get(item_id.sku_id) if sku_details else None
            existing_base_rates = sorted(existing_base_rates, key=lambda d: d.stay_date)
            for existing_base_rate in existing_base_rates:
                source_base_rate = source_room_group_by_stay_date.get(mapped_dates.get(existing_base_rate.stay_date))
                existing_base_rate.update_price_and_linkage(price=source_base_rate.price,
                                                            date_range_for_price_copy=date_range_for_price_copy,
                                                            mapped_dates=mapped_dates, end_date=end_date,
                                                            room_type_name=room_type_name,
                                                            sku_name=sku_name, persistent_override=persistent_override)
                if persistent_override:
                    existing_base_rate.mark_override_persistent()
                updated_exiting_base_rates.append(existing_base_rate)
        self.base_rate_repo.update_all(updated_exiting_base_rates)
        self.base_rate_event_handler.on_update(property_id, updated_exiting_base_rates)

    def _create_base_rates_without_linkage(self, property_id, item_id, start_date, end_date, price, dow_prices,
                                           existing_base_rates, persistent_override=None):
        base_rates = self.base_rate_factory.create_base_rate_without_linkage(
            property_id, item_id, start_date, end_date, price=price, dow_prices=dow_prices,
            exclude_dates={base_rate.stay_date for base_rate in existing_base_rates},
            persistent_override=persistent_override)

        if base_rates:
            self._apply_linkage_from_pre_existing_running_base_rate_to_newly_created_rates_without_linkage(
                property_id, item_id, base_rates)
            self.base_rate_repo.save_all(base_rates)
        return base_rates

    def _update_base_rates_without_linkage(self, property_id, existing_base_rates, price,
                                           dow_prices: DayOfWeekPricesVO, persistent_override=None, end_date=None,
                                           sku_name=None, room_type_name=None):
        for base_rate in existing_base_rates:
            if price is None:
                base_rate.update_price_and_linkage(
                    price=getattr(dow_prices, weekday_name(base_rate.stay_date.weekday())), end_date=end_date,
                    dow_prices=dow_prices, sku_name=sku_name, room_type_name=room_type_name,
                    persistent_override=persistent_override)
            else:
                base_rate.update_price_and_linkage(price=price, end_date=end_date, dow_prices=dow_prices,
                                                   sku_name=sku_name, room_type_name=room_type_name,
                                                   persistent_override=persistent_override)

            if persistent_override:
                base_rate.mark_override_persistent()
            elif persistent_override is False:
                base_rate.remove_persistent_override()

        self.base_rate_repo.update_all(existing_base_rates)
        self.base_rate_event_handler.on_update(property_id, existing_base_rates)

    @session_manager
    def _fill_missing_dates_with_running_rate_or_rack_rate(self, property_id, item_ids, base_rates_found_between_dates,
                                                           start_date, end_date, create_missing=False,
                                                           dates_for_existing_rates=None, inclusions=None):
        if not (end_date or dates_for_existing_rates):
            return
        room_item_wise_rates = group_list(base_rates_found_between_dates, 'item_id')
        rack_rates = self.rack_rate_repo.load_all(property_id, item_ids)
        item_wise_rack_rate = {rack_rate.item_id: rack_rate for rack_rate in rack_rates}

        for item_id in item_ids:
            room_base_rates = room_item_wise_rates.get(item_id, set())
            rates_found_for_dates = set([base_rate.stay_date for base_rate in room_base_rates])
            dates_with_missing_rates = set()
            if end_date:
                if not inclusions:
                    dates_with_missing_rates.update(
                        DateRange(start_date, end_date).subtract(set(rates_found_for_dates))
                    )
                else:
                    inclusion = inclusions[item_id.sku_id]
                    inclusion_start_date, inclusion_end_date = (
                        inclusion.get_applicable_start_and_end_dates(
                            stay_start=start_date, stay_end=end_date)
                    )
                    if inclusion_start_date:
                        dates_with_missing_rates.update(
                            DateRange(inclusion_start_date, inclusion_end_date).subtract(set(rates_found_for_dates))
                        )
                    else:
                        applicable_dates = inclusion.get_applicable_dates_in_range(start_date, end_date)
                        dates_with_missing_rates.update(set(applicable_dates) - set(rates_found_for_dates))
            if dates_for_existing_rates:
                dates_with_missing_rates.update(dates_for_existing_rates - rates_found_for_dates)

            if dates_with_missing_rates:
                base_rate_from_running_rates = self._create_base_rates_from_running_base_rates_for_given_dates(
                    dates_with_missing_rates, property_id, item_id, create_missing=create_missing)

                base_rates_found_between_dates.extend(base_rate_from_running_rates)
                dates_with_missing_rates = dates_with_missing_rates - {base_rate.stay_date for base_rate in
                                                                       base_rate_from_running_rates}
            if dates_with_missing_rates:
                if create_missing:
                    new_base_rates = self._create_base_rates_from_rack_rate(
                        property_id, item_id, dates_with_missing_rates, item_wise_rack_rate.get(item_id))
                    base_rates_found_between_dates.extend(new_base_rates)
                else:
                    if item_wise_rack_rate.get(item_id):
                        base_rates_found_between_dates.extend(
                            self.base_rate_factory.create_virtual_base_rates_from_rack_rate(
                                property_id, item_id, dates_with_missing_rates, item_wise_rack_rate.get(item_id)))
                    else:
                        logger.info(f"Rack Rate not configured for room_type - occupancy : {item_id}")

    def _create_base_rates_from_rack_rate(self, property_id, item_id, dates_with_missing_rates, rack_rate):
        linkage = RateLinkageVO(linkage_type=LinkageType.RACK_RATE, linkage_detail=None,
                                link_equation=LinkageEquationVO(percentage_change=Decimal(100),
                                                                fixed_change=Decimal(0)))
        new_base_rates = self.base_rate_factory.create_base_rates_from_rack_rate(
            property_id, item_id, dates_with_missing_rates, rack_rate, linkage)
        self.base_rate_repo.save_all(new_base_rates)
        return new_base_rates

    def _create_base_rates_from_running_base_rates_for_given_dates(self, dates, property_id, item_id,
                                                                   create_missing=False):
        min_date, max_date = min(dates), max(dates)

        running_base_rates = self.running_base_rate_repo.load_all(
            property_id, item_id, start_date=min_date, end_date=max_date)

        new_base_rates = []
        for running_base_rate in running_base_rates:
            if running_base_rate.linkage:
                linkage = running_base_rate.linkage

                target_base_rates = self._load_target_base_rates_for_multiple_items_for_linkage(
                    property_id, linkage, running_base_rate.start_date, max_date, [item_id],
                    create_missing=create_missing)

                base_rates = BaseRateDomainService.create_base_rate_with_linkage(
                    None, property_id, item_id, running_base_rate.start_date, running_base_rate.linkage,
                    target_base_rates, max_date, self.base_rate_factory, only_dates=dates)
            else:
                base_rates = self.base_rate_factory.create_base_rate_without_linkage(
                    property_id, item_id, running_base_rate.start_date, max_date, price=running_base_rate.price,
                    dow_prices=running_base_rate.dow_prices,
                    exclude_dates=None, only_dates=dates, persistent_override=running_base_rate.persistent_override)

            new_base_rates.extend(base_rates)
            max_date = running_base_rate.start_date - datetime.timedelta(days=1)

            if max_date < min_date:
                break

        if not new_base_rates:
            return new_base_rates

        if create_missing:
            self.base_rate_repo.save_all(new_base_rates)
        return new_base_rates

    def _load_target_base_rates_for_linkage(self, property_id, linkage, start_date, end_date,
                                            item_id) -> BaseRatesAndRackRate:
        rack_rate = None
        if linkage.linkage_type == LinkageType.OTHER_DATE_RANGE:
            rack_rates = self.rack_rate_repo.load_all(property_id, [item_id])
            if rack_rates:
                rack_rate = rack_rates[0].price
            return BaseRatesAndRackRate(self.base_rate_repo.load_base_rates(
                property_id, item_id, linkage.linkage_detail.date_range.start,
                linkage.linkage_detail.date_range.end), rack_rate)

        elif linkage.linkage_type == LinkageType.OTHER_ROOM_CATEGORY:
            rack_rates = self.rack_rate_repo.load_all(property_id, [
                RoomItemId(linkage.linkage_detail.room_type_id, linkage.linkage_detail.occupancy)])
            if rack_rates:
                rack_rate = rack_rates[0].price
            return BaseRatesAndRackRate(self.base_rate_repo.load_base_rates(
                property_id, RoomItemId(linkage.linkage_detail.room_type_id, linkage.linkage_detail.occupancy),
                start_date, end_date=end_date), rack_rate)

        elif linkage.linkage_type == LinkageType.OTHER_INCLUSION:
            rack_rates = self.rack_rate_repo.load_all(property_id, [InclusionItemId(linkage.linkage_detail.sku_id)])
            if rack_rates:
                rack_rate = rack_rates[0].price
            return BaseRatesAndRackRate(self.base_rate_repo.load_base_rates(
                property_id, InclusionItemId(linkage.linkage_detail.sku_id), start_date, end_date=end_date),
                rack_rate)

    def _load_target_base_rates_for_multiple_items_for_linkage(self, property_id, linkage, start_date, end_date,
                                                               item_ids, dates_for_existing_rates=None,
                                                               create_missing=False):
        if linkage.linkage_type == LinkageType.OTHER_DATE_RANGE:
            base_rates = self.base_rate_repo.load_base_rates_for_multiple_items(
                property_id, item_ids=item_ids, start_date=linkage.linkage_detail.date_range.start,
                end_date=linkage.linkage_detail.date_range.end)
            self._fill_missing_dates_with_running_rate_or_rack_rate(property_id, item_ids, base_rates,
                                                                    linkage.linkage_detail.date_range.start,
                                                                    linkage.linkage_detail.date_range.end,
                                                                    create_missing=create_missing,
                                                                    )
            return base_rates

        elif linkage.linkage_type == LinkageType.OTHER_ROOM_CATEGORY:
            base_rates = self.base_rate_repo.load_base_rates_for_multiple_items(
                property_id, item_ids=[RoomItemId(linkage.linkage_detail.room_type_id,
                                                  linkage.linkage_detail.occupancy)],
                start_date=start_date, end_date=end_date)
            self._fill_missing_dates_with_running_rate_or_rack_rate(property_id,
                                                                    [RoomItemId(linkage.linkage_detail.room_type_id,
                                                                                linkage.linkage_detail.occupancy)],
                                                                    base_rates, start_date, end_date,
                                                                    create_missing=create_missing,
                                                                    dates_for_existing_rates=dates_for_existing_rates
                                                                    )
            return base_rates

        elif linkage.linkage_type == LinkageType.OTHER_INCLUSION:
            base_rates = self.base_rate_repo.load_base_rates_for_multiple_items(
                property_id, item_ids=[InclusionItemId(linkage.linkage_detail.sku_id)],
                start_date=start_date, end_date=end_date)
            self._fill_missing_dates_with_running_rate_or_rack_rate(property_id, [InclusionItemId(
                linkage.linkage_detail.sku_id)], base_rates, start_date, end_date,
                                                                    create_missing=create_missing,
                                                                    dates_for_existing_rates=dates_for_existing_rates)
            return base_rates

    def _apply_linkage_from_pre_existing_running_base_rate_to_newly_created_rates_without_linkage(
            self, property_id, item_id, created_base_rates):
        if not created_base_rates:
            return

        start_date = min(base_rate.stay_date for base_rate in created_base_rates)
        end_date = max(base_rate.stay_date for base_rate in created_base_rates)

        existing_running_base_rates = self.running_base_rate_repo.load_all(property_id, item_id, start_date, end_date)
        date_range_wise_running_base_rate_with_linkage = BaseRateDomainService.find_active_running_rates_with_linkages(
            existing_running_base_rates, start_date, end_date)

        for date_range, running_base_rate_with_linkage in date_range_wise_running_base_rate_with_linkage.items():
            base_rates = [base_rate for base_rate in created_base_rates if
                          date_range[0] <= base_rate.stay_date <= date_range[1]]

            # Attach linkage from pre-existing running base rate in overridden form
            linkage: RateLinkageVO = running_base_rate_with_linkage.linkage.overridden()

            '''
            It's possible that target base rate could be a running base rate and won't be fetched.
            That is okay because the linkage is overridden and target would only be used to set parent base rate id 
            '''
            target_base_rates = self._load_target_base_rates_for_linkage(
                property_id, linkage, start_date, end_date, item_id)

            BaseRateDomainService.update_base_rate_with_linkage(base_rates, linkage, target_base_rates.base_rates)

    def _remove_override(self, item_id, property_id, stay_date):
        base_rate = self.base_rate_repo.load_base_rates(property_id, item_id, start_date=stay_date, end_date=stay_date)
        base_rate = base_rate[0]  # Because start date and end date are same, there would be only one rate
        if not base_rate.is_linkage_overridden():
            base_rate.remove_persistent_override()
        else:
            if base_rate.linkage.linkage_type == LinkageType.RACK_RATE:
                rack_rates = self.rack_rate_repo.load_all(property_id, [item_id])
                if rack_rates:
                    rack_rate = rack_rates[0].price
                target_rates = BaseRatesAndRackRate(base_rate, rack_rate)
                target_base_rate = None
            else:
                target_rates = self._load_target_base_rates_for_linkage(property_id=property_id,
                                                                        linkage=base_rate.linkage,
                                                                        start_date=base_rate.stay_date,
                                                                        end_date=base_rate.stay_date,
                                                                        item_id=item_id)
                target_base_rate = target_rates.base_rates[0]  # Because start date and end date are same
            base_rate.remove_override(target_base_rate=target_base_rate, target_rack_rate=target_rates.rack_rate)

        self.base_rate_repo.update_all([base_rate])
        return base_rate

    def _create_base_rates_without_linkage_via_rate_sync_consumer(self, property_id, item_id, start_date, end_date, price, dow_prices,
                                                                  existing_base_rate, persistent_override=None):
        return self.base_rate_factory.create_base_rate_without_linkage(
            property_id, item_id, start_date, end_date, price=price, dow_prices=dow_prices,
            exclude_dates=[existing_base_rate.stay_date if existing_base_rate else None],
            persistent_override=persistent_override)
