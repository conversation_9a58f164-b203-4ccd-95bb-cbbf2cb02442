import copy
import logging
from collections import defaultdict
from typing import List

import sentry_sdk

from object_registry import register_instance
from core.common.globals import global_context
from rackrate_service.decorators import session_manager
from rate_manager.application.audit_trail.audit_type import AuditType
from rate_manager.application.commands.rate_plan_replication_price_copy import \
    RatePlanReplicationPriceCopyCommand
from rate_manager.application.commands.rate_plan_status_update_command import RatePlanStatusUpdateCommand
from rate_manager.application.audit_trail.audit_decorator import audit
from rate_manager.application.dtos.pricing.pricing import PriceRequestDTO
from rate_manager.domain.domain_events.domain_event_registry import register_event
from rate_manager.domain.domain_events.rate_plan.rate_plan_status_updated import RatePlanStatusUpdated
from rate_manager.domain.entities.aggregate_roots.rate_plan import RatePlan
from rate_manager.application.dtos.user_data import UserData
from rate_manager.application.services.integration_event_service import \
    IntegrationEventService
from rate_manager.domain.dtos.rate_plan_search_query import RatePlanSearchQuery
from rate_manager.domain.dtos.rate_plan_search_view_dto import \
    RatePlanSearchViewDto
from rate_manager.domain.dtos.room_config_dto import RoomConfigDTO
from rate_manager.domain.entities.non_room_night_inclusion_rate import \
    NonRoomNightInclusionRate
from rate_manager.domain.factories.rate_plan_company_profile_factory import \
    RatePlanCompanyProfileFactory
from rate_manager.domain.factories.rate_plan_factory import RatePlanFactory
from rate_manager.domain.policy.facts.add_rate_plan_facts import AddRatePlanFacts
from rate_manager.domain.value_objects.channel_mapping import ChannelMappingVO
from rate_manager.domain.value_objects.date_range import DateRange
from rate_manager.domain.value_objects.item_ids.inclusion_item_id import \
    InclusionItemId
from rate_manager.domain.value_objects.occupancy import OccupancyVO
from rate_manager.domain.value_objects.payment_policy import PaymentPolicyVO
from rate_manager.domain.value_objects.cancellation_policy import CancellationPolicyVO
from rate_manager.domain.policy.engine import RuleEngine
from rate_manager.domain.policy.facts import RatePlanFacts, RatePlanDetailsFacts, UpdateRatePlanFacts, \
    ViewRoomRatePricingDetails
from rate_manager.domain.value_objects.policies import PoliciesVO
from rate_manager.infrastructure.repositories.inclusion_rack_rate_repo import \
    InclusionRackRateRepo
from rate_manager.infrastructure.repositories.package_repo import PackageRepo
from rate_manager.infrastructure.repositories.rate_plan_company_profile_repo import \
    RatePlanCompanyProfileRepository
from rate_manager.infrastructure.repositories.rate_plan_rate_linkage_repo import \
    RatePlanRateLinkageRepository
from rate_manager.infrastructure.repositories.rate_plan_rate_repo import \
    RatePlanRateRepository
from rate_manager.infrastructure.repositories.rate_plan_repo import \
    RatePlanRepo
from rate_manager.infrastructure.repositories.rate_plan_stop_sell_repo import \
    RatePlanStopSellRepository
from rate_manager.infrastructure.repositories.running_rate_plan_rate_repo import \
    RunningRatePlanRateRepository
from rate_manager.constants.rate_manager_constants import IntegrationEventType

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[RatePlanRepo, PackageRepo, RatePlanStopSellRepository, InclusionRackRateRepo, RatePlanRateRepository,
                  RunningRatePlanRateRepository, RatePlanRateLinkageRepository, RatePlanCompanyProfileRepository,
                  IntegrationEventService])
class RatePlanService:
    def __init__(self, rate_plan_repo: RatePlanRepo, package_repo: PackageRepo,
                 rate_plan_stop_sell_repo: RatePlanStopSellRepository, inclusion_rack_rate_repo: InclusionRackRateRepo,
                 rate_plan_rate_repo: RatePlanRateRepository,
                 rate_plan_running_rate_repo: RunningRatePlanRateRepository,
                 rate_plan_rate_linkage_repo: RatePlanRateLinkageRepository,
                 rate_plan_company_profile_repo: RatePlanCompanyProfileRepository,
                 integration_event_service: IntegrationEventService):
        self.rate_plan_repo = rate_plan_repo
        self.package_repo = package_repo
        self.rate_plan_stop_sell_repo = rate_plan_stop_sell_repo
        self.inclusion_rack_rate_repo = inclusion_rack_rate_repo
        self.rate_plan_rate_repo = rate_plan_rate_repo
        self.rate_plan_running_rate_repo = rate_plan_running_rate_repo
        self.linkage_repo = rate_plan_rate_linkage_repo
        self.rate_plan_company_profile_repo = rate_plan_company_profile_repo
        self.integration_event_service = integration_event_service

    @session_manager
    def create_new_rate_plan(self, rate_plan_data: dict, user_data):
        RuleEngine.action_allowed(
            action="add_rate_plan",
            facts=AddRatePlanFacts(
                user_type=user_data.user_type if user_data else None,
            ),
            fail_on_error=True,
        )
        packages = self.package_repo.load_packages_by_ids([rate_plan_data.get('package_id')])
        if not packages:
            raise ValueError("Cannot create Rate Plan - Package Id invalid")
        non_room_night_inclusions = packages[0].get_non_room_night_inclusions()
        property_id = rate_plan_data.get('property_id')
        non_room_night_inclusion_rack_rates = self.inclusion_rack_rate_repo.load_all(
            property_id, [InclusionItemId(
                sku_id=inclusion.sku_id) for inclusion in non_room_night_inclusions])
        assert len(non_room_night_inclusion_rack_rates) == len(non_room_night_inclusions), \
            "Rack rate is not configured for all the inclusions associated with the package"
        non_room_night_inclusion_rates = [NonRoomNightInclusionRate(
            inclusion_rack_rate.sku_id,
            price=inclusion_rack_rate.price) for inclusion_rack_rate in
            non_room_night_inclusion_rack_rates]
        if rate_plan_data.get('channel_mappings'):
            channel_mappings = []
            for channel_mapping in rate_plan_data['channel_mappings']:
                channel_mappings.append(
                    ChannelMappingVO(
                        channel=channel_mapping.channel,
                        sub_channel=channel_mapping.sub_channel,
                        active=channel_mapping.active,
                        channel_rate_plan_code=channel_mapping.channel_rate_plan_code,
                    )
                )
        else:
            channel_mappings = [
                ChannelMappingVO(channel='*', sub_channel='*', active=True, channel_rate_plan_code='ALL1')
            ]

        rate_plan = RatePlanFactory.create_new(name=rate_plan_data.get('name'),
                                               short_code=rate_plan_data.get('short_code'),
                                               property_id=property_id,
                                               package_id=rate_plan_data.get('package_id'),
                                               policies=rate_plan_data.get('policies'),
                                               room_type_occupancy_mappings=rate_plan_data.get(
                                                   'room_type_occupancy_mappings'),
                                               description=rate_plan_data.get('description'),
                                               restrictions=rate_plan_data.get('restrictions'),
                                               non_room_night_inclusion_rates=non_room_night_inclusion_rates,
                                               channel_mappings=channel_mappings,
                                               segments=rate_plan_data.get('segments'),
                                               extra_information=rate_plan_data.get('extra_information'),
                                               sell_start_date=rate_plan_data.get('sell_start_date'),
                                               sell_end_date=rate_plan_data.get('sell_end_date'),
                                               commission_type=rate_plan_data.get('commission_type'),
                                               commission_percent=rate_plan_data.get('commission_percent'),
                                               print_rate=rate_plan_data.get('print_rate'),
                                               suppress_rate=rate_plan_data.get('suppress_rate'),
                                               )
        self.rate_plan_repo.save(rate_plan)

        self.integration_event_service.create_rate_plan_created_event(property_id=rate_plan.property_id,
                                                                      rate_plan=rate_plan)
        return rate_plan

    @session_manager
    def update_rate_plan(self, rate_plan_id: str, rate_plan_data: dict, user_data=None):
        RuleEngine.action_allowed(
            action="update_rate_plan",
            facts=UpdateRatePlanFacts(
                user_type=user_data.user_type if user_data else global_context.get_user_type(),
            ),
            fail_on_error=True,
        )

        rate_plan = self.rate_plan_repo.load_for_update(rate_plan_id)
        old_rate_plan = copy.deepcopy(rate_plan)
        if not rate_plan:
            raise ValueError(f"Rate Plan does not exist - Invalid rate plan id({rate_plan_id})")
        if rate_plan.property_id != rate_plan_data['property_id']:
            raise ValueError(f"Rate Plan does not exist for property({rate_plan_data['property_id']})")

        for key, value in rate_plan_data.items():
            if key == 'non_room_night_inclusion_rates':
                packages = self.package_repo.load_packages_by_ids([rate_plan.package_id])
                sku_ids = [non_room_night_inclusion.sku_id for non_room_night_inclusion in value]
                packages[0].validate_if_sku_ids_in_package_exists(sku_ids)
            if key == 'extra_information':
                self._update_extra_information_for_a_rate_plan(rate_plan, rate_plan_data)
            if key == "policies" and value:
                self.update_extra_bed_child_price_custom_policy(rate_plan, value)

            setattr(rate_plan, key, value)

        self.rate_plan_repo.update_rate_plan(rate_plan)
        # Adding to Log Rate Plan Changes
        self._log_rate_plan_changes_to_rate_audit_trail(rate_plan_id=rate_plan_id,
                                                        old_rate_plan=old_rate_plan,
                                                        rate_plan_data=rate_plan_data,
                                                        user_data=user_data)

        self.integration_event_service.create_rate_plan_updated_event(property_id=rate_plan.property_id,
                                                                      rate_plan=rate_plan)
        return rate_plan

    def _log_rate_plan_changes_to_rate_audit_trail(self, rate_plan_id: str,
                                                   old_rate_plan: RatePlan,
                                                   rate_plan_data: dict, user_data=None):
        if rate_plan_data.get('is_active') is not None and \
                old_rate_plan.is_active != rate_plan_data.get('is_active'):
            update_command = RatePlanStatusUpdateCommand(property_id=old_rate_plan.property_id,
                                                         is_active=rate_plan_data.get('is_active'))
            self._log_rate_plan_state_change_to_audit_trail(rate_plan_id=rate_plan_id,
                                                            rate_plan_name=old_rate_plan.name,
                                                            rate_plan_data=rate_plan_data,
                                                            update_command=update_command,
                                                            user_data=user_data)

    @audit(audit_type=AuditType.RATE_PLAN_STATUS_UPDATED)
    def _log_rate_plan_state_change_to_audit_trail(self, rate_plan_id: str,
                                                   rate_plan_name: str,
                                                   rate_plan_data: dict,
                                                   update_command, **kwargs):
        rate_plan_activation_event = {
            "is_rate_plan_active": rate_plan_data.get('is_active'),
            "rate_plan": rate_plan_name,
            "update_command": update_command,
            "rate_change_method": IntegrationEventType.RATE_PLAN_STATUS_UPDATED.value
        }
        register_event(RatePlanStatusUpdated(rate_plan_id, **rate_plan_activation_event))

    def get_shallow_rate_plan_details_from_rate_plans(self, rate_plans=None):
        if not rate_plans:
            return []
        packages = self.package_repo.load_packages_by_ids([rate_plan.package_id for rate_plan in rate_plans])
        package_id_name_map = {package.package_id: package.package_name for package in packages}

        return [RatePlanSearchViewDto.create(rate_plan, package_name=package_id_name_map.get(rate_plan.package_id))
                for rate_plan in rate_plans]

    def search_rate_plans(self, search_query: RatePlanSearchQuery, user_data,
                          room_configurations: List[RoomConfigDTO] = None):
        RuleEngine.action_allowed(
            action="view_rate_plans",
            facts=RatePlanFacts(
                user_type=user_data.user_type,
            ),
            fail_on_error=True,
        )

        rate_plans = self.rate_plan_repo.load_rate_plans(property_id=search_query.property_id,
                                                         room_item_ids=search_query.room_item_ids,
                                                         inclusion_item_id=search_query.inclusion_item_id,
                                                         channel_code=search_query.channel_code,
                                                         sub_channel_code=search_query.sub_channel_code,
                                                         short_codes=search_query.short_codes,
                                                         is_active=True,
                                                         rate_plan_ids=search_query.rate_plan_ids)

        if search_query.booking_date:
            if room_configurations:
                valid_rate_plans = self._get_valid_room_config_rate_plans(rate_plans, search_query, room_configurations)
            else:
                valid_rate_plans = self._get_rate_plans_supporting_price_request(rate_plans, search_query)

            rate_plan_ids_with_no_stop_sell = self._get_rate_plans_not_having_stop_sell(
                rate_plan_ids=valid_rate_plans.keys(),
                start_date=search_query.stay_start,
                end_date=search_query.stay_end,
                ignore_stop_sell_dates=search_query.ignore_stop_sell_dates
            )
            rate_plans = [rp for rp in valid_rate_plans.values() if rp.rate_plan_id in rate_plan_ids_with_no_stop_sell]

        if search_query.company_profile_id or search_query.superhero_company_code:
            rate_plan_id_to_rate_plan = {
                rate_plan.rate_plan_id: rate_plan
                for rate_plan in rate_plans
            }
            if search_query.superhero_company_code:
                company_profile_rate_plans = self.rate_plan_company_profile_repo.load_rate_plan_company_profiles(
                    superhero_company_code=search_query.superhero_company_code
                )
            else:
                company_profile_rate_plans = self.rate_plan_company_profile_repo.load_rate_plan_company_profiles(
                    company_profile_id=search_query.company_profile_id
                )
            if company_profile_rate_plans:
                rate_plans = [
                    rate_plan_id_to_rate_plan[company_profile_rate_plan.rate_plan_id]
                    for company_profile_rate_plan in company_profile_rate_plans
                    if company_profile_rate_plan.rate_plan_id in rate_plan_id_to_rate_plan
                ]

        if not rate_plans:
            return []

        return self.get_shallow_rate_plan_details_from_rate_plans(rate_plans)

    def _get_valid_room_config_rate_plans(self, rate_plans, search_query, room_configurations):
        valid_rate_plans = dict()
        rate_plan_map = {rate_plan.rate_plan_id: rate_plan for rate_plan in rate_plans}
        for room_config in room_configurations:
            if room_config.rate_plan_id not in rate_plan_map:
                continue

            rate_plan = rate_plan_map[room_config.rate_plan_id]
            room_type_occupancy_stay = {
                "room_type_id": room_config.room_type_id,
                "occupancy": OccupancyVO(
                    adult_count=room_config.adult_count,
                    child_count=room_config.child_count,
                ),
                "stay_start": search_query.stay_start,
                "stay_end": search_query.stay_end,
            }
            price_request = PriceRequestDTO(property_id=search_query.property_id,
                                            booking_date=search_query.booking_date,
                                            room_types_occupancy_stay=[room_type_occupancy_stay],
                                            channel_code=search_query.channel_code,
                                            sub_channel_code=search_query.sub_channel_code,
                                            company_profile_id=search_query.company_profile_id,
                                            ignore_stop_sell_dates=search_query.ignore_stop_sell_dates)
            if rate_plan.supports_price_request(price_request, room_type_occupancy_stay):
                valid_rate_plans[rate_plan.rate_plan_id] = rate_plan
        return valid_rate_plans

    def _get_rate_plans_supporting_price_request(self, rate_plans, search_query):
        price_request = PriceRequestDTO(property_id=search_query.property_id,
                                        booking_date=search_query.booking_date,
                                        room_types_occupancy_stay=[search_query.room_type_occupancy_stay],
                                        channel_code=search_query.channel_code,
                                        sub_channel_code=search_query.sub_channel_code,
                                        company_profile_id=search_query.company_profile_id,
                                        ignore_stop_sell_dates=search_query.ignore_stop_sell_dates)
        return {
            rate_plan.rate_plan_id: rate_plan
            for rate_plan in rate_plans
            if rate_plan.supports_price_request(price_request, search_query.room_type_occupancy_stay)
        }

    def _get_rate_plans_not_having_stop_sell(self, rate_plan_ids, start_date, end_date,
                                             ignore_stop_sell_dates=None):
        stop_sell_ranges = self.rate_plan_stop_sell_repo.get_stop_sell_dates_between_dates(
            rate_plan_ids, start_date, end_date)

        rate_plan_ids_with_no_stop_sell = set(rate_plan_ids) - stop_sell_ranges.keys()
        for rate_plan_id, stop_sell_range in stop_sell_ranges.items():
            if not stop_sell_range:
                rate_plan_ids_with_no_stop_sell.add(rate_plan_id)

            if not ignore_stop_sell_dates:
                # Since none of the stop sell ranges can be ignored. Continue, as this rate plan has stop sell
                continue

            for date_range in stop_sell_range:
                if date_range.subtract(ignore_stop_sell_dates):
                    # If any of the date range has some dates outside of ignore range, break, and continue the loop
                    break
            else:
                # break didn't happen. That means, all the date_ranges in stop_sell_range are included in
                # `ignore_stop_sell_dates`
                # that means, for this request, we can sell this rate plan
                rate_plan_ids_with_no_stop_sell.add(rate_plan_id)

        return rate_plan_ids_with_no_stop_sell

    def get_rate_plan_details(self, rate_plan_id, user_data=None):
        RuleEngine.action_allowed(
            action="view_rate_plan_details",
            facts=RatePlanDetailsFacts(
                user_type=user_data.user_type if user_data else global_context.get_user_type(),
            ),
            fail_on_error=True,
        )

        return self.rate_plan_repo.load_rate_plan(rate_plan_id)

    @session_manager
    def add_stop_sell_for_rate_plan(self, rate_plan_id, user_data: UserData, start_date, end_date=None):
        _ = self.rate_plan_repo.load_rate_plan(rate_plan_id=rate_plan_id, hotel_id=user_data.hotel_id)
        self.rate_plan_stop_sell_repo.add_stop_sell(rate_plan_id, start_date, end_date)

    @session_manager
    def remove_stop_sell_for_rate_plan(self, rate_plan_id, user_data: UserData, start_date, end_date=None):
        _ = self.rate_plan_repo.load_rate_plan(rate_plan_id=rate_plan_id, hotel_id=user_data.hotel_id)
        self.rate_plan_stop_sell_repo.remove_stop_sell(rate_plan_id, start_date, end_date)

    def get_stop_sell_dates_between_dates(self, rate_plan_id, start_date, end_date, user_data=None) -> List[DateRange]:
        RuleEngine.action_allowed(
            action="view_rate_plan_pricing_details",
            facts=ViewRoomRatePricingDetails(
                user_type=user_data.user_type if user_data else global_context.get_user_type(),
            ),
            fail_on_error=True,
        )
        return self.rate_plan_stop_sell_repo.get_stop_sell_dates_between_dates(
            [rate_plan_id], start_date, end_date)[rate_plan_id]

    def get_room_night_inclusions(self, rate_plan_id):
        rate_plan = self.rate_plan_repo.load_rate_plan(rate_plan_id)
        package = self.package_repo.load(rate_plan.package_id)
        non_room_night_inclusion_ids = {inclusion.sku_id for inclusion in package.get_non_room_night_inclusions()}
        return [inclusion for inclusion in package.inclusions if inclusion.sku_id not in non_room_night_inclusion_ids]

    @session_manager
    def replicate_existing_rate_plan(self, rate_plan_id, rate_plan_data: dict):
        rate_plan = self.rate_plan_repo.load_rate_plan(rate_plan_id)

        copied_rate_plan = RatePlanFactory.create_copy_with_updated_data(
            rate_plan=rate_plan, name=rate_plan_data.get('name'), short_code=rate_plan_data.get('short_code'),
            description=rate_plan_data.get('description'),
            policies=rate_plan_data.get('policies') if 'policies' in rate_plan_data else rate_plan.policies,
            room_type_occupancy_mappings=rate_plan_data.get(
                'room_type_occupancy_mappings') if 'room_type_occupancy_mappings' in rate_plan_data else
            rate_plan.room_type_occupancy_mappings,
            restrictions=rate_plan_data.get(
                'restrictions') if 'restrictions' in rate_plan_data else rate_plan.restrictions,
            segments=rate_plan_data.get(
                'segments') if 'segments' in rate_plan_data else rate_plan.segments,
            channel_mappings=rate_plan.channel_mappings,
            sell_start_date=rate_plan.sell_start_date,
            sell_end_date=rate_plan.sell_end_date,
        )

        self.rate_plan_repo.save(copied_rate_plan)

        RatePlanReplicationPriceCopyCommand(self.rate_plan_rate_repo, self.rate_plan_running_rate_repo,
                                            self.linkage_repo).execute(rate_plan, copied_rate_plan)
        return copied_rate_plan

    @session_manager
    def add_rate_plan_company_profile(
        self, rate_plan_id: str, company_profile_id: str = None, superhero_company_code: str = None
    ):
        rate_plan = self.rate_plan_repo.load_rate_plan(rate_plan_id)
        if rate_plan is None:
            raise ValueError(f"Rate Plan does not exist - Invalid rate plan id: {rate_plan_id}")

        rate_plan_company_profile = RatePlanCompanyProfileFactory.create_new(
            rate_plan_id=rate_plan_id, company_profile_id=company_profile_id,
            superhero_company_code=superhero_company_code
        )
        self.rate_plan_company_profile_repo.save(rate_plan_company_profile)
        return rate_plan_company_profile

    @session_manager
    def add_multiple_rate_plan_company_profile_mapping(self, superhero_company_code, rate_plan_codes, hotel_id):
        rate_plans = self.rate_plan_repo.load_rate_plans(short_codes=rate_plan_codes, property_id=hotel_id)
        if not rate_plans:
            logger.info(f"Rate Plan does not exist - Invalid rate plan codes: {rate_plan_codes}")
            raise ValueError(f"Rate Plan does not exist - Invalid rate plan codes: {rate_plan_codes}")
        company_profile_rate_plans = []
        for rate_plan in rate_plans:
            company_profile_rate_plans.append(
                RatePlanCompanyProfileFactory.create_new(
                    rate_plan_id=rate_plan.rate_plan_id,
                    superhero_company_code=superhero_company_code
                )
            )
        self.rate_plan_company_profile_repo.save_all(company_profile_rate_plans)

    @session_manager
    def remove_rate_plan_company_profile(
        self, rate_plan_id: str, company_profile_id: str = None, superhero_company_code: str = None):
        self.rate_plan_company_profile_repo.remove_rate_plan_company_profile(
            rate_plan_id=rate_plan_id, company_profile_id=company_profile_id,
            superhero_company_code=superhero_company_code
        )

    def get_all_rate_plan_for_given_ids(self, rate_plan_ids: list):
        rate_plans = self.rate_plan_repo.load_rate_plans_with_ids(rate_plan_ids=rate_plan_ids)
        return rate_plans

    def create_flexi_rate_plan(self, property_id, name, short_code, room_type_occupancy_mappings, description=None):
        payment_policies = PaymentPolicyVO(advance_payment_percentage=0, unit_of_payment_percentage="",
                                           days_before_checkin_to_make_payment=0,  occupancy_percentage=0)
        rate_plan = RatePlanFactory.create_new(name=name,
                                               short_code=short_code,
                                               property_id=property_id,
                                               room_type_occupancy_mappings=room_type_occupancy_mappings,
                                               policies=PoliciesVO(payment_policies=payment_policies,
                                                                   cancellation_policies=[CancellationPolicyVO()]),
                                               description=description,
                                               is_flexi=True)
        self.rate_plan_repo.save(rate_plan)

        return rate_plan

    def _update_extra_information_for_a_rate_plan(self, rate_plan, rate_plan_data):
        extra_information = rate_plan_data.get('extra_information', {})
        if 'rate_plan_rate_details' not in extra_information:
            rate_plan.extra_information.update(extra_information)
            return
        rate_plan_rate_details = extra_information['rate_plan_rate_details']
        existing_rate_plan_rate_details = rate_plan.extra_information.get('rate_plan_rate_details', [])
        existing_rate_plan_rate_details_map = {
            f"{detail['start_date']}_{detail.get('end_date')}_{detail['room_type_list']}": detail
            for detail in existing_rate_plan_rate_details}
        updated_rate_plan_details = []
        for detail in rate_plan_rate_details:
            existing_detail = existing_rate_plan_rate_details_map.get(
                f"{detail['start_date']}_{detail.get('end_date')}_{detail['room_type_list']}", {})
            existing_detail.update(detail)
            updated_rate_plan_details.append(existing_detail)
        extra_information['rate_plan_rate_details'] = updated_rate_plan_details
        rate_plan.extra_information.update(extra_information)

    # TODO Needs to be fixed only added for emma
    @staticmethod
    def update_extra_bed_child_price_custom_policy(rate_plan, new_policies):
        """
        Note:
        As we are not sending whole policy payload from tenant-gateway like we do from pms,
        therefore we have created this function to update custom policy with name "Child price" and "Extra Bed price".
        keeping old policy intact.
        These keys will only be sent by emma.
        """

        def parse_policy_description(description):
            """
            Parse policy description into a structured format.
                input:
                Room type A
                01 Dec 2024 - 10 Dec 2024: Rs. 50.0
                11 Dec 2024 - 20 Dec 2024: Rs. 60.0

                Room type B
                01 Dec 2024 - 10 Dec 2024: Rs 50.0
                Returns:
                    {
                        "Room type A":{
                            "01 Dec 2024 - 10 Dec 2024": "Rs. 50.00",
                            "11 Dec 2024 - 20 Dec 2024": "Rs. 60.00",
                        },
                        "Room type B":{
                            "01 Dec 2024 - 10 Dec 2024": "Rs. 50.00",
                        }
                    }
            """

            room_policies = defaultdict(dict)
            if not description:
                return room_policies

            current_room_type = None
            for line in description.split('\n'):
                line = line.strip()
                if not line:
                    continue
                if "Room type" in line:
                    current_room_type = line
                    room_policies[current_room_type] = dict()
                elif current_room_type:
                    date_range, price = line.split(":")
                    room_policies[current_room_type].update(
                        {date_range: price.strip()}
                    )
            return room_policies

        def merge_policies(old_policy, new_policy):
            if old_policy and new_policy:
                old_policy.description = parse_policy_description(old_policy.description)
                new_policy.description = parse_policy_description(new_policy.description)
                for room_type, policy_details in new_policy.description.items():
                    if room_type not in old_policy.description:
                        old_policy.description[room_type] = policy_details
                        continue
                    old_policy.description[room_type].update(policy_details)
                old_policy.description = build_policy_description(old_policy.description)
                return old_policy

            return new_policy or old_policy

        def build_policy_description(policy):
            """
            Parse structured policy description into string in specific format.
            Format:
            Room type A
                01 Dec 2024 - 10 Dec 2024: Rs. 50.0
                11 Dec 2024 - 20 Dec 2024: Rs. 60.0

                Room type B
                01 Dec 2024 - 10 Dec 2024: Rs. 50.0
            """
            description_lines = []
            for room_type, policy_details in sorted(policy.items()):
                description_lines.append(room_type)
                for date_range, price in policy_details.items():
                    description_lines.append(f"{date_range}: {price}")
                description_lines.append("")
            return "\n".join(description_lines).strip()

        new_custom_policies = {
            policy.name: policy for policy in getattr(new_policies, "custom_policies", {})
        }
        old_custom_policies = (
            {policy.name: policy for policy in rate_plan.policies.custom_policies}
            if rate_plan.policies.custom_policies
            else {}
        )
        old_extra_bed_price_policy = old_custom_policies.pop("Extra Bed price", None)
        old_child_price_policy = old_custom_policies.pop("Child price", None)
        new_extra_bed_price_policy = new_custom_policies.pop("Extra Bed price", None)
        new_child_price_policy = new_custom_policies.pop("Child price", None)

        if not (old_extra_bed_price_policy or old_child_price_policy or new_extra_bed_price_policy or new_child_price_policy):
            return

        old_custom_policies.update(new_custom_policies)
        updated_extra_bed_price_policy = old_extra_bed_price_policy
        updated_child_price_policy = old_child_price_policy
        try:
            updated_extra_bed_price_policy = merge_policies(
                old_extra_bed_price_policy, new_extra_bed_price_policy,
            )
            updated_child_price_policy = merge_policies(
                old_child_price_policy, new_child_price_policy
            )
        except Exception as e:
            sentry_sdk.capture_exception(e)

        new_policies.custom_policies = list(old_custom_policies.values())

        if updated_child_price_policy:
            new_policies.custom_policies.append(updated_child_price_policy)
        if updated_extra_bed_price_policy:
            new_policies.custom_policies.append(updated_extra_bed_price_policy)
