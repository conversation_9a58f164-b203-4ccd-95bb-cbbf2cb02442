from sqlalchemy import Column, String, JSON, DATE, <PERSON><PERSON>ey, Integer, Boolean
from sqlalchemy import event, DDL
from sqlalchemy.dialects.postgresql import NUMERIC, JSONB
from sqlalchemy.sql.schema import UniqueConstraint
from treebo_commons.multitenancy.sqlalchemy import db_engine

from rate_manager.infrastructure.common_models import TimeStampedMixin, DeleteMixin


class RoomRunningBaseRateModel(db_engine.Base, TimeStampedMixin, DeleteMixin):
    __tablename__ = "room_running_base_rate"

    room_running_base_rate_id = Column(String, primary_key=True)
    property_id = Column(String, nullable=False)
    room_type_id = Column(String, nullable=False)
    adult_count = Column(Integer, nullable=False)
    start_date = Column(DATE, nullable=False)
    price = Column(NUMERIC(precision=15, scale=4))
    dow_prices = Column(JSON)
    linkage = Column(JSONB)
    persistent_override = Column(Boolean)


class RoomBaseRateModel(db_engine.Base, TimeStampedMixin):
    __tablename__ = "room_base_rate"

    room_base_rate_id = Column(String, primary_key=True)
    property_id = Column(String, nullable=False)
    room_type_id = Column(String, nullable=False)
    adult_count = Column(Integer, nullable=False)
    stay_date = Column(DATE, nullable=False)
    price = Column(NUMERIC(precision=15, scale=4), nullable=False)
    linkage = Column(JSONB)
    parent_room_base_rate_id = Column(String, ForeignKey("room_base_rate.room_base_rate_id"))
    persistent_override = Column(Boolean)
    parent_rack_rate_id = Column(Integer, ForeignKey("room_rack_rate.id"))

    __table_args__ = (
        UniqueConstraint("property_id", "room_type_id", "adult_count", "stay_date"),
    )


class RoomBaseRateLinkageModel(db_engine.Base, TimeStampedMixin):
    __tablename__ = "room_base_rate_linkage"

    id = Column(Integer, primary_key=True, autoincrement=True)
    property_id = Column(String, nullable=False)
    room_type_id = Column(String, nullable=False)
    adult_count = Column(Integer, nullable=False)
    linked_room_type_id = Column(String, nullable=False)
    linked_adult_count = Column(Integer, nullable=False)

    __table_args__ = (
        UniqueConstraint("property_id", "room_type_id", "adult_count", "linked_room_type_id",
                         "linked_adult_count"),
    )


# Trigger creation done like this is needed for integration tests to work. Since Tests will drop and re-create table,
# that also drops the trigger is created manually
# And via sqlalchemy, this is how trigger will be created
# https://docs.sqlalchemy.org/en/13/core/ddl.html#controlling-ddl-sequences

# https://www.cybertec-postgresql.com/en/recursive-queries-postgresql/
# https://www.postgresqltutorial.com/postgresql-recursive-query/
# https://stackoverflow.com/questions/26671612/prevent-and-or-detect-cycles-in-postgres
trigger_function = DDL("""
    CREATE OR REPLACE FUNCTION detect_room_base_rate_linkage_cycle()
      RETURNS TRIGGER
      LANGUAGE plpgsql AS
    $func$
    BEGIN
       IF EXISTS (
          -- search_graph represents current working table, which is updated recursively
          WITH RECURSIVE search_graph(base_rate_id, path, cycle) AS ( -- relevant columns
             SELECT g.room_base_rate_id, 
                 ARRAY[g.room_base_rate_id], 
                 false
             FROM   room_base_rate g
             WHERE  g.room_base_rate_id = NEW.room_base_rate_id  -- only test starting from new row

             UNION ALL
             
             -- Recursive branch will select all child rates for all rates in working table (from previous 
             -- non-recursive branch)
             -- The linkage chain is assumed to have cycle, when next room rate `(g.room_base_rate_id)` is 
             -- already present in current working table's path `(sg.path)`
             
             SELECT g.room_base_rate_id, 
                 sg.path || g.room_base_rate_id, 
                 g.room_base_rate_id = ANY(sg.path)
             FROM   search_graph sg
             JOIN   room_base_rate g 
             ON g.parent_room_base_rate_id = sg.base_rate_id and 
                (g.linkage is null or (g.linkage->>'linkage_overridden')::boolean is false)
             WHERE  NOT sg.cycle
         )
          SELECT FROM search_graph
          WHERE  cycle
          LIMIT  1  -- stop evaluation at first find
          )
       THEN
          RAISE EXCEPTION 'Room Base Rate Linkage Loop Detected';
       ELSE
         RETURN NEW;
       END IF;
    END
    $func$;
""")

trigger = DDL("""
    CREATE TRIGGER detect_room_base_rate_linkage_cycle_after_update
    AFTER INSERT OR UPDATE ON room_base_rate
    FOR EACH ROW EXECUTE PROCEDURE detect_room_base_rate_linkage_cycle();
""")

event.listen(RoomBaseRateModel.__table__, 'after_create', trigger_function.execute_if(dialect='postgresql'))
event.listen(RoomBaseRateModel.__table__, 'after_create', trigger.execute_if(dialect='postgresql'))
