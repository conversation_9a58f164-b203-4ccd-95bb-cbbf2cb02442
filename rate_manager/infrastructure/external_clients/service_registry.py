import enum
import logging
import os

from treebo_commons.service_discovery.service_registry import ServiceRegistry

logger = logging.getLogger(__name__)


class ServiceEndPointNames(enum.Enum):
    ROLE_MANAGER_SERVICE_URL = "role_manager_service_url"
    TENANT_SERVICE_URL = "tenant_service_url"


class ServiceRegistryClient:

    ALL_API_ENDPOINTS = ServiceRegistry.get_all_service_endpoints()

    @classmethod
    def get_role_manager_service_url(cls):
        service_name = ServiceEndPointNames.ROLE_MANAGER_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_tenant_service_url(cls):
        service_name = ServiceEndPointNames.TENANT_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))
