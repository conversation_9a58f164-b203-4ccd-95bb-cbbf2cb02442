from typing import List

from rate_manager.domain.entities.aggregate_roots.inclusion_base_rate import InclusionBaseRate
from rate_manager.infrastructure.models.inclusion_rates import InclusionBaseRateModel
from rate_manager.domain.value_objects.rate_linkage import RateLinkageVO


class InclusionBaseRateAdaptor:
    @staticmethod
    def to_domain_entity(db_models: List[InclusionBaseRateModel]) -> [InclusionBaseRate]:
        return [InclusionBaseRate(
            inclusion_base_rate_id=db_model.inclusion_base_rate_id,
            property_id=db_model.property_id,
            sku_id=db_model.sku_id,
            price=db_model.price,
            stay_date=db_model.stay_date,
            linkage=RateLinkageVO.from_json(db_model.linkage) if db_model.linkage else None,
            parent_inclusion_base_rate_id=db_model.parent_inclusion_base_rate_id,
            persistent_override=db_model.persistent_override,
            parent_rack_rate_id=str(db_model.parent_rack_rate_id) if db_model.parent_rack_rate_id else None
        ) for db_model in db_models]

    @staticmethod
    def to_db_entity(domain_entities: List[InclusionBaseRate]):
        return [InclusionBaseRateModel(
            inclusion_base_rate_id=domain_entity.inclusion_base_rate_id,
            property_id=domain_entity.property_id,
            sku_id=domain_entity.sku_id,
            price=domain_entity.price,
            stay_date=domain_entity.stay_date,
            linkage=domain_entity.linkage.to_json() if domain_entity.linkage else None,
            parent_inclusion_base_rate_id=domain_entity.parent_inclusion_base_rate_id,
            persistent_override=domain_entity.persistent_override,
            parent_rack_rate_id=int(domain_entity.parent_rack_rate_id) if domain_entity.parent_rack_rate_id else None
        ) for domain_entity in domain_entities]
