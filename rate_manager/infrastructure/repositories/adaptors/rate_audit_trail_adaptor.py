from typing import List

from rate_manager.application.audit_trail.audit_type import AuditType
from rate_manager.domain.entities.aggregate_roots.rate_audit_trail import RateAuditTrail
from rate_manager.domain.value_objects.item_ids.inclusion_item_id import InclusionItemId
from rate_manager.domain.value_objects.item_ids.rate_plan_rate_item_id import RatePlanItemId
from rate_manager.domain.value_objects.item_ids.room_base_rate_item_id import RoomItemId
from rate_manager.domain.value_objects.occupancy import OccupancyVO
from rate_manager.infrastructure.models.rate_audit_trail import RateAuditTrailModel


class RateAuditTrailAdaptor:
    @staticmethod
    def to_domain_entities(rate_audit_trail_models: List[RateAuditTrailModel]):
        audit_trails = []
        for dbm in rate_audit_trail_models:
            if dbm.sku_id:
                item_id = InclusionItemId(dbm.sku_id)
            elif dbm.rate_plan_id:
                item_id = None
            else:
                item_id = RoomItemId(dbm.room_type_id, OccupancyVO(dbm.adult_count))

            audit_trails.append(
                RateAuditTrail(dbm.audit_id, dbm.property_id, item_id, dbm.action_type, dbm.stay_start, dbm.stay_end,
                               dbm.user, dbm.user_type, dbm.created_at, AuditType(dbm.audit_type), dbm.audit_payload,
                               request_id=dbm.request_id, comments=dbm.comments))
        return audit_trails

    @staticmethod
    def to_db_model(rate_audit_trail: RateAuditTrail):
        if isinstance(rate_audit_trail.item_id, InclusionItemId):
            return RateAuditTrailModel(audit_id=rate_audit_trail.audit_id, property_id=rate_audit_trail.property_id,
                                       rate_type=rate_audit_trail.rate_type, stay_start=rate_audit_trail.stay_start,
                                       stay_end=rate_audit_trail.stay_end,
                                       sku_id=rate_audit_trail.item_id.sku_id, action_type=rate_audit_trail.action_type,
                                       user=rate_audit_trail.user, user_type=rate_audit_trail.user_type,
                                       created_at=rate_audit_trail.timestamp,
                                       audit_type=rate_audit_trail.audit_type.value,
                                       audit_payload=rate_audit_trail.audit_payload,
                                       request_id=rate_audit_trail.request_id, comments=rate_audit_trail.comments)
        elif isinstance(rate_audit_trail.item_id, RoomItemId):
            return RateAuditTrailModel(audit_id=rate_audit_trail.audit_id, property_id=rate_audit_trail.property_id,
                                       rate_type=rate_audit_trail.rate_type, stay_start=rate_audit_trail.stay_start,
                                       stay_end=rate_audit_trail.stay_end,
                                       room_type_id=rate_audit_trail.item_id.room_type_id,
                                       adult_count=rate_audit_trail.item_id.occupancy.adult_count,
                                       action_type=rate_audit_trail.action_type,
                                       user=rate_audit_trail.user, user_type=rate_audit_trail.user_type,
                                       created_at=rate_audit_trail.timestamp,
                                       audit_type=rate_audit_trail.audit_type.value,
                                       audit_payload=rate_audit_trail.audit_payload,
                                       request_id=rate_audit_trail.request_id, comments=rate_audit_trail.comments)
        elif isinstance(rate_audit_trail.item_id, RatePlanItemId):
            return RateAuditTrailModel(audit_id=rate_audit_trail.audit_id, property_id=rate_audit_trail.property_id,
                                       rate_type=rate_audit_trail.rate_type, stay_start=rate_audit_trail.stay_start,
                                       stay_end=rate_audit_trail.stay_end,
                                       rate_plan_id=rate_audit_trail.item_id.rate_plan_id,
                                       adult_count=rate_audit_trail.item_id.occupancy.adult_count,
                                       action_type=rate_audit_trail.action_type,
                                       user=rate_audit_trail.user, user_type=rate_audit_trail.user_type,
                                       created_at=rate_audit_trail.timestamp,
                                       audit_type=rate_audit_trail.audit_type.value,
                                       audit_payload=rate_audit_trail.audit_payload,
                                       request_id=rate_audit_trail.request_id, comments=rate_audit_trail.comments)

        elif rate_audit_trail.audit_type.name == AuditType.RATE_PLAN_STATUS_UPDATED.name:
            return RateAuditTrailModel(audit_id=rate_audit_trail.audit_id, property_id=rate_audit_trail.property_id,
                                       user=rate_audit_trail.user, user_type=rate_audit_trail.user_type,
                                       created_at=rate_audit_trail.timestamp,
                                       audit_type=rate_audit_trail.audit_type.value,
                                       audit_payload=rate_audit_trail.audit_payload,
                                       request_id=rate_audit_trail.request_id, comments=rate_audit_trail.comments)
        else:
            raise NotImplementedError("Rate Audit Trail Model creation is not implemented to item_id type: {0}".format(
                type(rate_audit_trail.item_id)))
