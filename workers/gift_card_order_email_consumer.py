# pylint: disable=relative-import,ungrouped-imports
import json
import logging
import sys
from argparse import ArgumentParser

import requests
from kombu import Connection
from requests import HTTPError, Timeout

from constants import B2B_APP_ALERTS
from base_rmq_consumer_v1 import BaseRMQConsumerV1
from configuration import Configuration
from utils import send_msg_to_slack
from worker_log_conf import setup_basic_config

logger = logging.getLogger(__name__)

LOYALTY_RELATIVE_SEND_GIFT_CARD_URL = "loyalty/send-gift-card-email/"


class GiftCardOrderEmailConsumer(BaseRMQConsumerV1):
    routing_key = 'gift-card-email'
    queue_name = 'gift-card-email-queue'

    def process_message(self, body, message):
        logger.info("Message received : {body}".format(body=body))
        url = '{0}{1}'.format(self.config.b2b_url_base, LOYALTY_RELATIVE_SEND_GIFT_CARD_URL)
        body = json.JSONDecoder().decode(body)
        try:
            request_headers = {'Content-type': 'application/json'}
            logger.info('Url: {u} with data: {d}, headers: {h}'.format(u=url, d=body, h=request_headers))
            response = requests.post(url=url, data=json.dumps(body), headers=request_headers,
                                     timeout=self.config.async_timeout)
            logger.info('Url: {u} with data: {d}, with response: {r}'.format(u=url, d=body,
                                                                             r=response.__dict__))
            response.raise_for_status()
        except HTTPError as e:
            try:
                msg = 'HTTPError for {u} with response({s}) {r}'.format(u=url, r=e.response.text,
                                                                        s=e.response.status_code)
                logger.error(msg)
                slack_data = "Hazraat!! Hazraat!! Hazraat!! \n Loyalty email sending failed for " \
                             "data {data}. Error {err}".format(data=body, err=str(e))
                send_msg_to_slack(msg=slack_data, url=B2B_APP_ALERTS)
            except Exception as e:
                pass

        except Timeout as e:
            msg = "{u} timed out after {t}s. Exc: {e}".format(u=url, t=self.config.async_timeout, e=str(e))
            logger.error(msg)


def main():
    parser = ArgumentParser()
    parser.add_argument("-u", "--url",
                        help="rabbitmq connection url, e.g.: amqp://guest:guest@localhost/",
                        default='amqp://guest:guest@localhost/b2b')

    parser.add_argument("-b", "--b2b-base-url",
                        help="b2b server url, e.g.: http://localhost:8001/",
                        default='http://localhost:8001/')

    parser.add_argument("-lf", "--log-file-pathname",
                        help="log file pathname, e.g.: /var/log/b2b/gift_card_order_email_consumer.log",
                        default='/var/log/b2b/gift_card_order_email_consumer.log')
    args = parser.parse_args()

    setup_basic_config(args.log_file_pathname)

    config = Configuration(rabbitmq_url=args.url,
                           b2b_url_base=args.b2b_base_url,
                           async_timeout=600
                           )

    with Connection(hostname=config.rabbitmq_url) as conn:
        worker = GiftCardOrderEmailConsumer(queue_name=GiftCardOrderEmailConsumer.queue_name,
                                            routing_key=GiftCardOrderEmailConsumer.routing_key, connection=conn,
                                            config=config)
        worker.run()


if __name__ == '__main__':
    sys.exit(main() or 0)
