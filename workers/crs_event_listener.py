#!/usr/bin/env python
# -*- coding: utf-8 -*-
# pylint: disable=relative-import
import json
import logging
import sys
from argparse import ArgumentParser

import requests
from kombu import Connection

from constants import B2B_APP_ALERTS
from base_rmq_consumer_v1 import BaseRMQConsumerV1
from configuration import Configuration
from utils import send_msg_to_slack
from worker_log_conf import setup_basic_config

logger = logging.getLogger(__name__)

B2B_CRS_HANDLER_ENDPOINT = 'b2b/crs_event_handler/'


class CRSEventListener(BaseRMQConsumerV1):
    routing_key = '#'
    queue_name = 'crs-event-listener'
    exchange_name = 'crs-events'
    declare_exchange_if_not_exists = True
    enable_delayed_retries = True
    override_existing_queue = True
    delay_in_seconds = 300
    prefetch_count = 10

    def process_message(self, body, message):
        logger.debug("Message Body: {body}".format(body=body))
        try:
            url = '{0}{1}'.format(self.config.b2b_url_base, B2B_CRS_HANDLER_ENDPOINT)
            request_headers = {'Content-type': 'application/json'}
            response = requests.post(url=url, data=json.dumps(body), headers=request_headers,
                                     timeout=self.config.async_timeout)
            logger.debug(response.text)
            response.raise_for_status()
        except Exception as e:
            msg = "Error in CRS Event Listener, body: {body}, err: {err}".format(body=body, err=str(e))
            logger.exception(msg)
            send_msg_to_slack(msg=msg, url=B2B_APP_ALERTS)


def main():
    parser = ArgumentParser()

    parser.add_argument("-u", "--url",
                        help="rabbitmq connection url, e.g.: amqp://b2badmin:b2badmin@localhost/b2b",
                        default='amqp://b2badmin:b2badmin@localhost/b2b')

    parser.add_argument("-b", "--b2b-base-url",
                        help="b2b server url, e.g.: http://localhost:8000/",
                        default='http://localhost:8000/')

    parser.add_argument("-lf", "--log-file-pathname",
                        help="log file pathname, e.g.: /var/log/b2b/work_queue.log",
                        default='/var/log/b2b/crs_event_listener.log')

    args = parser.parse_args()

    setup_basic_config(log_file_name=args.log_file_pathname, log_level=logging.CRITICAL)

    config = Configuration(rabbitmq_url=args.url,
                           b2b_url_base=args.b2b_base_url,
                           async_timeout=600)
    with Connection(hostname=config.rabbitmq_url) as conn:
        worker = CRSEventListener(queue_name=CRSEventListener.queue_name, routing_key=CRSEventListener.routing_key,
                                  connection=conn,
                                  config=config)
        worker.run()


if __name__ == '__main__':
    sys.exit(main() or 0)
