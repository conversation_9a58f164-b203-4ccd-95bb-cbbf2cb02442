#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
work-queue consumer for b2b booking-exchange
"""
import json
import logging
import sys
from argparse import ArgumentParser

import requests
from kombu import Connection

from constants import SLACK_SRE_HANDLE, B2B_APP_ALERTS
from utils import send_msg_to_slack
from base_rmq_consumer_v1 import BaseRMQConsumerV1
from configuration import Configuration
from worker_log_conf import setup_basic_config

logger = logging.getLogger(__name__)


class Worker(BaseRMQConsumerV1):
    declare_exchange_if_not_exists = True
    enable_delayed_retries = True
    override_existing_queue = True
    delay_in_seconds = 300

    def process_message(self, body, message):
        failure_url = ''
        data = ''
        try:
            data = json.loads(body)
            url = '/'.join([x.strip('/') for x in [self.config.b2b_url_base, message.headers['api'], '']])
            failure_url = '/'.join(
                [x.strip('/') for x in [self.config.b2b_url_base, message.headers['failure_api'], '']])
            request_headers = message.headers.get('api_request_headers', {})
            request_headers['Content-type'] = 'application/json'
            logger.info('Url: {u} with data: {d}, headers: {h}'.format(u=url, d=data, h=request_headers))
            response = requests.post(url=url, data=json.dumps(data), headers=request_headers,
                                     timeout=self.config.async_timeout)
            logger.debug('Url: {u} with data: {d}, headers: {h} with response: {r}'.format(u=url, d=data,
                                                                                           h=request_headers,
                                                                                           r=response.__dict__))
            response.raise_for_status()
        except Exception:
            try:
                response = requests.post(url=failure_url, data=data, timeout=self.config.async_timeout)
                logger.debug(
                    'Url: {u} with data: {d} with response: {r}'.format(u=failure_url, d=data, r=response.__dict__))
                response.raise_for_status()
            except Exception as e:
                message = "Payment Update Failure Notification Failed for {data}, error: {err}".format(data=data,
                                                                                                       err=e)
                logger.exception(message)
                message = " ".join((SLACK_SRE_HANDLE, message))
                send_msg_to_slack(msg=message, url=B2B_APP_ALERTS)


def main():
    parser = ArgumentParser()

    parser.add_argument("-u", "--url",
                        help="rabbitmq connection url, e.g.: amqp://b2badmin:b2badmin@localhost/b2b",
                        default='amqp://b2badmin:b2badmin@localhost/b2b')

    parser.add_argument("-b", "--b2b-base-url",
                        help="b2b server url, e.g.: http://localhost:8000/",
                        default='http://localhost:8000/')

    parser.add_argument("-lf", "--log-file-pathname",
                        help="log file pathname, e.g.: /var/log/b2b/b2b_payment_consumer.log",
                        default='/var/log/b2b/b2b_payment_consumer.log')

    args = parser.parse_args()

    setup_basic_config(args.log_file_pathname)

    config = Configuration(rabbitmq_url=args.url,
                           b2b_url_base=args.b2b_base_url,
                           async_timeout=600)

    with Connection(hostname=config.rabbitmq_url) as conn:
        worker = Worker(queue_name='payment-queue', routing_key='payment.#', connection=conn, config=config)
        worker.run()


if __name__ == '__main__':
    sys.exit(main() or 0)
