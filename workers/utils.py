import json
import os

import requests

from constants import SLACK_ENVIRONMENT, CONNECTION_TIMEOUT, RESPONSE_TIMEOUT


def send_msg_to_slack(msg, url):
    if os.environ.get('DJANGO_CONFIGURATION') == SLACK_ENVIRONMENT:
        headers = {'content-type': 'application/json'}
        payload = {'text': msg}
        response = requests.post(url, data=json.dumps(payload), headers=headers,
                                 timeout=(CONNECTION_TIMEOUT, RESPONSE_TIMEOUT))
