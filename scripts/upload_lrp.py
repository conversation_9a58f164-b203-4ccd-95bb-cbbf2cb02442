# import os
# import sys
#
# sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
# import csv
# import logging
# import uuid
# from argparse import ArgumentParser
# from typing import List
#
# from sqlalchemy.orm.exc import NoResultFound
#
# from autoapp import app
# from core.c_sc_p.repository.channel_repo import ChannelRepository
# from core.c_sc_p.repository.policy_repo import PolicyRepository
# from core.c_sc_p.repository.sub_channel_repo import SubChannelRepository
# from core.linked_rate_plans.data_classes.linked_rate_plan import SalePriceLinkedRatePlan, LinkedRatePlan
# from core.linked_rate_plans.respository.linked_rate_plan import SalePriceLinkedRatePlanRepository
# from rackrate_service.constants.rate_constants import AnyChannel, AnySubChannel, AnyPolicy
# from rackrate_service.domains.catalog.repositories.sku_repository import PropertySKURepository
#
# logger = logging.getLogger(__name__)
#
#
# def get_all_valid_bundle_sku_codes(property_id):
#     all_property_skus = PropertySKURepository.get_all_stay_property_sku_for_property_code(property_id)
#     all_sku_codes_for_property = [property_sku.sku_code for property_sku in all_property_skus
#                                   if not property_sku.sku.modular]
#     return all_sku_codes_for_property
#
#
# all_channels = [c.uid for c in ChannelRepository.all()] + [AnyChannel]
# all_sub_channels = [sc.uid for sc in SubChannelRepository.all()] + [AnySubChannel]
# all_policies = [p.uid for p in PolicyRepository.all()] + [AnyPolicy]
#
#
# def process_row(row) -> List[LinkedRatePlan]:
#     _sku_code = row["sku_code"]
#     if _sku_code.lower() == "all":
#         sku_codes = get_all_valid_bundle_sku_codes(row["property_id"])
#     else:
#         sku_codes = [_sku_code]
#
#     lrps = []
#     for sku_code in sku_codes:
#         channel_code = row["channel"]
#         sub_channel_code = row["sub_channel"]
#         policy_code = row['policy']
#
#         if channel_code not in all_channels:
#             raise RuntimeError(f'Invalid channel: {channel_code}')
#
#         if sub_channel_code not in all_sub_channels:
#             raise RuntimeError(f'Invalid sub_channel: {sub_channel_code}')
#
#         if policy_code not in all_policies:
#             raise RuntimeError(f'Invalid policy: {policy_code}')
#
#         lrp = SalePriceLinkedRatePlan(
#             uid=uuid.uuid4(),
#             property_code=row["property_id"],
#             sku_code=sku_code,
#             channel=channel_code,
#             sub_channel=sub_channel_code,
#             policy=policy_code,
#             multiplier=row["multiplier"],
#             constant=row["constant"],
#         )
#         lrps.append(lrp)
#     return lrps
#
#
# def main(csv_file_name, csv_file_headers):
#     with open(csv_file_name, "r") as csv_file:
#         reader = csv.DictReader(csv_file)
#         assert set(reader.fieldnames) == set(csv_file_headers)
#         lrps = []
#         for row in reader:
#             lrps += process_row(row)
#
#         for lrp in lrps:
#             try:
#                 query = {
#                     'property_code': lrp.property_code,
#                     'sku_code': lrp.sku_code,
#                     'channel_code': lrp.channel,
#                     'sub_channel_code': lrp.sub_channel,
#                     'policy_code': lrp.policy
#                 }
#                 SalePriceLinkedRatePlanRepository.update(query,
#                                                          new_dataclass=lrp,
#                                                          fields_to_update=['sale_price_multiplier',
#                                                                            'sale_price_constant'])
#             except NoResultFound:
#                 logger.info(f'Creating {lrp}')
#                 SalePriceLinkedRatePlanRepository.create(lrp)
#
#
# if __name__ == "__main__":
#     headers = [
#         "property_id",
#         "sku_code",
#         "channel",
#         "sub_channel",
#         "policy",
#         "multiplier",
#         "constant",
#     ]
#
#     parser = ArgumentParser(
#         description="Update lrp for a csv structure {headers}".format(headers=headers)
#     )
#
#     parser.add_argument("csv_file_name", type=str)
#
#     args = parser.parse_args()
#
#     with app.app_context():
#         main(csv_file_name=args.csv_file_name, csv_file_headers=headers)
