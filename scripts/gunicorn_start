#!/bin/bash

APP_DIR=/usr/src/app
SOCKFILE=/var/run/gunicorn.sock

RUNDIR=$(dirname $SOCKFILE)
echo $RUNDIR
test -d $RUNDIR || mkdir -p $RUNDIR

export PYTHON_PATH=$APP_DIR:$PYTHONPATH

echo "Starting Gunicorn"
GUNI_CONFIG_PATH=/usr/src/app/rackrate_service/config
export NEW_RELIC_APP_NAME=$CLUSTER_IDENTIFIER-$APP_NAME
export NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED=700
exec newrelic-admin run-program gunicorn wsgi:application -c $GUNI_CONFIG_PATH/guniconfig.py
