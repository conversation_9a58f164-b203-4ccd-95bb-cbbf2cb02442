from object_registry import inject
from tenant_gateway.api import ApiResponse
from tenant_gateway.api.api_helpers import RequestTypes
from tenant_gateway.api.blueprints import tenant_gateway_bp
from tenant_gateway.application.services.billing_service import BillingService
from tenant_gateway.common.utils import fetch_status_code_from_exception
from tenant_gateway.middlewares.token_verification_middleware import whatsapp_token_verification_middleware
from tenant_gateway.middlewares.whatsapp_call_log_middleware import log_whatsapp_api_request_response


@tenant_gateway_bp.route('/bills/<string:bill_id>/', methods=['GET'])
@whatsapp_token_verification_middleware
@log_whatsapp_api_request_response(log_data=RequestTypes.ARGS)
@inject(billing_service=BillingService)
def get_bill_details(billing_service, bill_id):
    """Get bill details
    ---
    operationId: get_bill_details
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post :
        description:  Accept Get bill details payload
        tags:
            - Bookings
        parameters:
            - in: path
              name: bill_id
              description: bill_id of the bill for which details are to be fetched
              required: True
              type: string
        responses:
            200:
                description: Bill details are returned
    """
    try:
        response = billing_service.get_bill_details(bill_id)
    except Exception as e:
        if hasattr(e, 'http_status_code'):
            status_code = e.http_status_code
        else:
            status_code = fetch_status_code_from_exception(str(e))
        return ApiResponse.build(data={"message": str(e)}, status_code=status_code)
    return ApiResponse.build(data=response, status_code=200)
