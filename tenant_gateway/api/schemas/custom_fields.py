from marshmallow_enum import LoadDumpOptions, EnumField


class ApispecEnumField(EnumField):
    def __init__(self, enum_value, by_value=False, load_by=None, dump_by=None, error='', *args, **kwargs):
        super().__init__(enum_value, by_value, load_by, dump_by, error, *args, **kwargs)
        if self.load_by == LoadDumpOptions.name:
            enum_meta = [e.name for e in enum_value]
        elif self.load_by == LoadDumpOptions.value:
            enum_meta = [e.value for e in enum_value]
        else:
            raise NotImplementedError

        self.metadata['enum'] = enum_meta