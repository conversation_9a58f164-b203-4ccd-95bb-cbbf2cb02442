from marshmallow import Schema, fields, validates_schema, ValidationError


class EmailSchema(Schema):
    context_data = fields.Dict(required=True)
    to_emails = fields.List(fields.Email, required=True)
    subject = fields.String(required=True)
    from_email = fields.Email(required=True)
    identifier = fields.String(required=True)


class WhatsappAndSmsSchema(Schema):
    receivers = fields.List(fields.String, required=True)
    context_data = fields.Dict(required=True)
    identifier = fields.String(required=True)


class SendCommunicationSchema(Schema):
    email = fields.Nested(EmailSchema, required=False)
    whatsapp = fields.Nested(WhatsappAndSmsSchema, required=False)
    sms = fields.Nested(WhatsappAndSmsSchema, required=False)

    @validates_schema()
    def validate_data(self, data, **kwargs):
        if data.get("whatsapp") and data.get("sms"):
            raise ValidationError('Only one of whatsapp or sms communication can be sent')
