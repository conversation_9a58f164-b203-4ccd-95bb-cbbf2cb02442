from marshmallow import Schema, fields


class PropertyDetailsSchema(Schema):
    channel_property_id = fields.String()
    channel_api_key = fields.String()
    channel_username = fields.String()
    channel_password = fields.String()


class ChannelRoomTypeMappingResponseSchema(Schema):
    channel_room_id = fields.String(description='Room id as per channel')
    channel_room_name = fields.String(description='Room name as per channel')
    superhero_room_type = fields.String(description='Room Type as per superhero')


class ChannelRatePlanMappingResponseSchema(Schema):
    superhero_rate_plan_id = fields.String(description='RatePlan id as per superhero')
    channel_rate_plan_id = fields.String(description='RatePlan id as per channel')
    channel_rate_plan_name = fields.String(description='RatePlan name as per channel')
    active = fields.Boolean(description='Status of a rate plan')


class ChannelMappingResponseSchema(Schema):
    id = fields.Integer()
    channel_name = fields.String()
    channel_code = fields.String()
    active = fields.Boolean()
    internal_hotel_id = fields.String()
    property_details = fields.Nested(PropertyDetailsSchema)
    room_type_mapping = fields.Nested(ChannelRoomTypeMappingResponseSchema, many=True)
    rate_plan_mapping = fields.Nested(ChannelRatePlanMappingResponseSchema, many=True)
