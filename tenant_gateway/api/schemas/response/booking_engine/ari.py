from marshmallow import Schema, fields


class RateSchema(Schema):
    date = fields.Date()
    total_rooms = fields.Integer()
    available_rooms = fields.Integer()
    room_type = fields.String()
    price = fields.List(fields.Dict())


class PolicySchema(Schema):
    cancellation_policies = fields.List(fields.Dict())
    child_policy = fields.Dict()
    payment_policies = fields.List(fields.Dict())


class ARIResponseSchema(Schema):
    data = fields.Dict(keys=fields.String(), values=fields.Nested(RateSchema, many=True))
    errors = fields.List(fields.String())


class PriceSchema(Schema):
    date = fields.Date()
    tax = fields.String()
    pricebeforetax = fields.String()
    priceaftertax = fields.String()
