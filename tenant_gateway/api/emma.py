from tenant_gateway.common.authentication import basic_auth
from flask import request, make_response

from object_registry import inject
from tenant_gateway.api.blueprints import emma_integration_bp
from tenant_gateway.domain.external_services.emma.decorators import log_emma_api_request_response
from tenant_gateway.domain.external_services.emma.emma_service import EmmaService


@emma_integration_bp.route('/webhooks', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_webhook_events(emma_service):
    """Accept Booking Event
    ---
    operationId: emma_accept_webhook_booking_events_xml
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Booking Events.
        tags:
            - Bookings
        parameters:
            - in: body
              name: body
              description: Request payload for booking event
              required: True
              schema:
                type: object
        responses:
            202:
                description: Webhook event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response(response.to_xml(), response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response


@emma_integration_bp.route('/reservations', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_booking_events(emma_service):
    """Accept Emma Booking Event
    ---
    operationId: emma_booking_events
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Emma Booking Events.
        tags:
            - Emma
        parameters:
            - in: body
              name: body
              description: Request payload for Emma booking event
              required: True
              schema:
                type: object
        responses:
            202:
                description: Webhook event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response(response.to_xml(), response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response


@emma_integration_bp.route('/rates', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_rate_events(emma_service):
    """Accept Emma Rate Event
    ---
    operationId: emma_rate_events
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Emma Rate Events.
        tags:
            - Emma
        parameters:
            - in: body
              name: body
              description: Request payload for Emma Rate event
              required: True
              schema:
                type: object
        responses:
            202:
                description: Webhook event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response(response.to_xml(), response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response


@emma_integration_bp.route('/profiles', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_profile_events(emma_service):
    """Accept Emma Profile Event
    ---
    operationId: emma_profile_events
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Emma Profile Events.
        tags:
            - Emma
        parameters:
            - in: body
              name: body
              description: Request payload for Emma Profile event
              required: True
              schema:
                type: object
        responses:
            202:
                description: Webhook event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response(response.to_xml(), response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response


@emma_integration_bp.route('/packages', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_package_events(emma_service):
    """Accept Emma Package Event
    ---
    operationId: emma_package_events
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Emma Package Events.
        tags:
            - Emma
        parameters:
            - in: body
              name: body
              description: Request payload for Emma Package event
              required: True
              schema:
                type: object
        responses:
            202:
                description: Webhook event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response(response.to_xml(), response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response


@emma_integration_bp.route('/sync-requests', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_sync_requests(emma_service):
    """Accept Emma Transaction Event
    ---
    operationId: emma_transaction_events
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Emma Transaction Events.
        tags:
            - Emma
        parameters:
            - in: body
              name: body
              description: Request payload for Emma Transaction event
              required: True
              schema:
                type: object
        responses:
            202:
                description: Webhook event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response(response.to_xml(), response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response


@emma_integration_bp.route('/configs', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_config_events(emma_service):
    """Accept Emma Config Event
    ---
    operationId: emma_config_events
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Emma Config Events.
        tags:
            - Emma
        parameters:
            - in: body
              name: body
              description: Request payload for Emma Config event
              required: True
              schema:
                type: object
        responses:
            202:
                description: Webhook event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response(response.to_xml(), response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response


@emma_integration_bp.route('/restrictions', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_restriction_events(emma_service):
    """Accept Emma Restrictions Event
    ---
    operationId: emma_restriction_events
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Emma Restriction Events.
        tags:
            - Emma
        parameters:
            - in: body
              name: body
              description: Request payload for Emma Restriction Events
              required: True
              schema:
                type: object
        responses:
            202:
                description: Webhook event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response(response.to_xml(), response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response


@emma_integration_bp.route('/results', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_api_results(emma_service):
    """Accept Emma API Result Event
    ---
    operationId: emma_api_results
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Result Events from Emma.
        tags:
            - Emma
        parameters:
            - in: body
              name: body
              description: Request payload for Emma Result Event
              required: True
              schema:
                type: object
        responses:
            202:
                description: Webhook event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response("", response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response


@emma_integration_bp.route('/financial-transactions', methods=['POST'])
@inject(emma_service=EmmaService)
@basic_auth.login_required
@log_emma_api_request_response()
def emma_financial_transaction_events(emma_service):
    """Accept Emma Financial Transaction Event
    ---
    operationId: emma_financial_transaction_events
    consumes:
        - text/xml
    produces:
        - text/xml
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Accept Emma Financial Transaction Events.
        tags:
            - Emma
        parameters:
            - in: body
              name: body
              description: Request payload for Emma Financial Transaction event
              required: True
              schema:
                type: object
        responses:
            202:
                description: Financial Transaction event accepted.
    """
    response = emma_service.process_emma_request(request.data)
    xml_response = make_response(response.to_xml(), response.status_code)
    xml_response.mimetype = response.mime_type
    return xml_response
