from flask import request

from object_registry import inject
from tenant_gateway.api import ApiResponse
from tenant_gateway.api.blueprints import tenant_gateway_bp
from tenant_gateway.application.services.temp_cm_booking_data_creation_service import TempBookingMigrationService


@tenant_gateway_bp.route('/migrate-cm-bookings', methods=['POST'])
@inject(temp_cm_booking_data_creation_service=TempBookingMigrationService)
def migrate_cm_bookings_in_tg(temp_cm_booking_data_creation_service):
    hotel_ids = request.args.get('hotel_ids', None)
    ota_codes = request.args.get('ota_codes', None)
    channel_booking_ids = request.args.get('booking_ids', None)
    from_date = request.args.get('from_date', None)

    if not hotel_ids and not channel_booking_ids:
        raise Exception(
            "Hotel Ids not passed in params, Please pass hotel_ids if you don't want to migrate specific bookings")

    if ota_codes and channel_booking_ids:
        raise Exception("Please pass either ota_codes or channel_booking_ids")

    hotel_ids = hotel_ids.split(',') if hotel_ids else None
    ota_codes = ota_codes.split(',') if ota_codes else None
    channel_booking_ids = channel_booking_ids.split(',') if channel_booking_ids else None

    temp_cm_booking_data_creation_service.migrate_bookings(hotel_ids, ota_codes, from_date, channel_booking_ids)

    return ApiResponse.build(data={'message': 'Bookings successfully migrated'}, status_code=200)