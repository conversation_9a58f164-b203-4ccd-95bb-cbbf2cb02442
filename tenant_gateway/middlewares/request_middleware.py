# coding=utf-8
"""
Middlewares
"""
import logging
import re

import flask
import sentry_sdk
from marshmallow import ValidationError
from ths_common.exceptions import AggregateNotFound, ResourceNotFound, ValidationException, ApiValidationException,\
    AuthorizationError, UserTypeHeaderInformationMissing, UserTypeHeaderInformationIncorrect, CRSException
from tenant_gateway.common.exceptions import (
    ValidationException, UpdateNotAllowedException, AccessDeniedException,
    AuthorizationException, BookingNotExistException, HotelNotFoundException,
    GatewayServiceException, BookingEngineCRSAPIException, ChannelManagerNotEnabledException
)
from treebo_commons.request_tracing.context import get_current_request_id
from werkzeug.exceptions import NotFound

from tenant_gateway.api import ApiResponse
from tenant_gateway.common.exceptions import AccessDeniedException, AuthorizationException, \
    RatePlanNotConfiguredException, BookingEngineException, ValidationException as BookingEngineValidationException

logger = logging.getLogger(__name__)


def before_request(request_headers):
    sentry_sdk.set_tag("tenant_id", request_headers.get("X-Tenant-Id"))
    sentry_sdk.set_tag("hotel_id", request_headers.get("X-Hotel-Id"))


def after_request(response):
    clear_thread_local()
    return response


def clear_thread_local():
    from tenant_gateway.globals import worker_context

    worker_context.clear()


def get_http_status_code_from_exception(exception) -> int:
    resource_missing_exceptions = [AggregateNotFound, ResourceNotFound, HotelNotFoundException]
    for error_cls in resource_missing_exceptions:
        if isinstance(exception, error_cls):
            return 404
    authorization_exceptions = [AuthorizationError, UserTypeHeaderInformationMissing, UserTypeHeaderInformationIncorrect]
    for error_cls in authorization_exceptions:
        if isinstance(exception, error_cls):
            return 403
    bad_request_exceptions = [ApiValidationException, ValidationException, RatePlanNotConfiguredException,
                              BookingEngineValidationException, BookingNotExistException]
    for error_cls in bad_request_exceptions:
        if isinstance(exception, error_cls):
            return 400

    resource_conflict_exceptions = [
        UpdateNotAllowedException,
        ChannelManagerNotEnabledException,
    ]
    for error_cls in resource_conflict_exceptions:
        if isinstance(exception, error_cls):
            return 409

    if isinstance(exception, BookingEngineCRSAPIException):
        return exception.http_status_code
    invalid_access_exceptions = [AccessDeniedException, AuthorizationException]
    for error_cls in invalid_access_exceptions:
        if isinstance(exception, error_cls):
            return 401
    return 500


def need_error_logging(error):
    if isinstance(error, NotFound):
        return False
    return True


def exception_handler(error, from_consumer=False):
    """
    Exception handler
    :param error:
    :param from_consumer:
    :return:
    """
    # populate status code
    if isinstance(error, ApiValidationException):
        status_code = 400
        error_messages = error.error_messages
        error_code = error.code(app_name="tenant_gateway")
        errors = []
        for error_message in error_messages:
            error = dict(code=error_code, message=error_message["error"],
                         extra_payload=dict(field=error_message["field"]), developer_message=None,
                         request_id=get_current_request_id() if not from_consumer else None)
            errors.append(error)
        response = ApiResponse.build(errors=errors, status_code=status_code)
    elif isinstance(error, CRSException):
        status_code = get_http_status_code_from_exception(error)
        error_code = error.code(app_name="tenant_gateway")
        error = dict(code=error_code, message=error.message,
                     developer_message=error.description,
                     extra_payload=error.extra_payload,
                     request_id=get_current_request_id() if not from_consumer else None)
        response = ApiResponse.build(errors=[error], status_code=status_code)
    elif isinstance(error, BookingEngineException):
        status_code = get_http_status_code_from_exception(error)
        error_code = None
        response = ApiResponse.build(errors=[error.message], status_code=status_code)
    else:
        status_code = get_http_status_code_from_exception(error)
        if getattr(error, "status_code", None):
            status_code = error.status_code
        if getattr(error, "code", None):
            status_code = error.code

        if isinstance(error, ValidationError):
            status_code = 400

        if not re.search(r'^[1-5]\d{2}$', str(status_code)):
            status_code = 500

        error_code = None
        # populate error dict
        error_dict = dict(code=status_code)
        # TODO: causing JSON serializer error for unknown types. Need to find a cleaner solution for this.
        # error_dict['extra_payload'] = error.args if hasattr(error, 'args') else None
        error_dict['extra_payload'] = dict()
        error_dict['message'] = error.message if hasattr(error, 'message') else 'Exception occurred.'
        error_dict['error_codes'] = error.error_codes if hasattr(error, 'error_codes') else None
        error_dict['developer_message'] = error.description if hasattr(error, 'description') else str(error)
        error_dict['request_id'] = get_current_request_id() if not from_consumer else None

        response = ApiResponse.build(errors=[error_dict], status_code=status_code)

    if need_error_logging(error):
        if isinstance(error, Exception) and status_code >= 500:
            sentry_sdk.capture_exception(error)
        if not from_consumer:
            request = flask.request
            request_url = request.url
            request_headers = dict(request.headers)

            if request.is_json:
                request_data = request.json if request.get_json(silent=True) else request.get_data(as_text=True)
            else:
                request_data = request.get_data(as_text=True)

            logger.exception("Exception in api: %s. Request Payload: %s", error, request_data,
                             extra=dict(error_code=error_code, status_code=status_code, request_url=request_url,
                                        request_headers=request_headers, request_data=request_data,
                                        request_method=request.method))
        else:
            logger.exception("Exception in consumer: %s", error, extra=dict(error_code=error_code))

    return response


def filter_sentry_events(event, hint):
    if 'exc_info' in hint:
        exc_type, error, tb = hint['exc_info']

        status_code = 500
        if isinstance(error, ApiValidationException):
            status_code = 400

        elif isinstance(error, GatewayServiceException):
            status_code = get_http_status_code_from_exception(error)
        else:
            # populate status code
            if getattr(error, "status_code", None):
                status_code = error.status_code
            if getattr(error, "code", None):
                status_code = error.code
            if isinstance(error, ValidationError):
                status_code = 400
            if not re.search(r'^[1-5]\d{2}$', str(status_code)):
                status_code = 500

        if isinstance(error, Exception) and status_code >= 500:
            return event

    # Discard sentry event
    return None
