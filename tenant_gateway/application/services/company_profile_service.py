import logging

from object_registry import register_instance
from flask import current_app

from treebo_commons.utils import dateutils

from tenant_gateway.application.services.tenant_settings import TenantSettings
from tenant_gateway.common.exceptions import CompanyProfileException
from tenant_gateway.common.slack_alert_helper import SlackAlert
from tenant_gateway.common.utils import get_pan_from_gstin
from tenant_gateway.domain.external_services.emma.dtos.address_dto import AddressDTO as EmmaAddressDTO
from tenant_gateway.infrastructure.external_clients.company_profile_service.company_profile_constants import (
    CompanyProfileStatus
)
from tenant_gateway.infrastructure.external_clients.company_profile_service.company_profile_client import (
    CompanyProfileServiceClient
)
from tenant_gateway.infrastructure.external_clients.su_client.dtos.booking_dto import AddressDTO as SuAddressDTO

logger = logging.getLogger(__name__)


@register_instance(dependencies=[CompanyProfileServiceClient, TenantSettings])
class CompanyProfileService:

    def __init__(self, company_profiles_client, tenant_settings):
        self.company_profiles_client = company_profiles_client
        self.tenant_settings = tenant_settings

    def fetch_ta_company_profile(self, channel, sub_channel, hotel_id, commission_applicable_date):
        superhero_company_code = self.tenant_settings.get_channel_subchannel_superhero_company_code(
            channel=channel,
            sub_channel=sub_channel,
        )
        if superhero_company_code is None:
            SlackAlert.send_alert(
                slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
                text=f"Company profile id for channel: {channel} & subchannel: {sub_channel} not configured in catalog."
            )
            return None

        company_profiles = self.company_profiles_client.search_entities(
            superhero_company_code=superhero_company_code,
            commission_hotel_id=hotel_id,
            commission_applicable_date=commission_applicable_date,
        )
        if company_profiles.sub_entities:
            return company_profiles.sub_entities[0]

        SlackAlert.send_alert(
            slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
            text=f"Company profile for superhero company code {superhero_company_code} (ota {sub_channel}) is not found.",
        )
        return None

    def fetch_or_create_company_profile(
            self,
            company_name,
            company_gstin,
            company_address,
            client_internal_code=None,
            source=None,
            hotel_id=None,
            email_id=None,
            phone_number=None,
            gstin_search=True,
            name_search=True,
            application=None,
    ):

        def fetch_created_at(sub_entity):
            return dateutils.isoformat_str_to_datetime(sub_entity.get('created_at'))

        def get_company_profile_from_name(sub_entities, trade_name):
            if not sub_entities:
                return None
            sub_entities.sort(key=fetch_created_at, reverse=True)
            sub_entities = [
                sub_entity for sub_entity in sub_entities
                if sub_entity.get('trade_name').strip().lower() == trade_name.strip().lower()
            ]
            for sub_entity in sub_entities:
                if sub_entity['status'] == CompanyProfileStatus.COMPLETE.value:
                    return sub_entity
            return sub_entities[0] if sub_entities else None

        def get_latest_company_profile(sub_entities):
            sub_entities.sort(key=fetch_created_at, reverse=True)
            for sub_entity in company_profiles.sub_entities:
                if sub_entity['status'] == CompanyProfileStatus.COMPLETE.value:
                    return sub_entity
            return company_profiles.sub_entities[0]

        def create_company_profile(company_profile, source=None):
            if company_profile:
                if not company_gstin or not isinstance(company_address, (SuAddressDTO, EmmaAddressDTO)):
                    return company_profile if company_profile.get("sub_entity_id") else None
                logger.info("GSTIN matched with no company profile, But name matched so creating a sub-entity "
                            "with same name but with GSTIN received from Su ")
                return self._create_sub_entity(
                    company_gstin,
                    company_profile.get('parent_entity_id'),
                    company_name,
                    company_address,
                    client_internal_code,
                    source,
                    email_id=email_id,
                    phone_number=phone_number,
                    application=application,
                )
            elif isinstance(company_address, (SuAddressDTO, EmmaAddressDTO)):
                logger.info("Neither GSTIN nor name matched with any company profile, so creating "
                            "parent-entity & sub-entity with same name but with GSTIN received from Su ")
                parent_entity_id = self._create_parent_entity(
                    company_address, company_name, company_gstin, hotel_id, application
                )
                return self._create_sub_entity(
                    company_gstin,
                    parent_entity_id,
                    company_name,
                    company_address,
                    client_internal_code,
                    source,
                    email_id=email_id,
                    phone_number=phone_number,
                    application=application,
                )
            else:
                raise CompanyProfileException("Can't create company profile as Address is not valid")

        # Priority Order: client_internal_code-> gstin -> trade_name
        try:
            if client_internal_code is not None:
                company_profiles = self.company_profiles_client.search_entities(
                    client_internal_code=client_internal_code,
                    source=source,
                    hotel_id=hotel_id,
                    commission_hotel_id=hotel_id,
                )
                if company_profiles:
                    return get_latest_company_profile(company_profiles.sub_entities)
                # Since client_internal_code is not present in company profile,
                # Creating a new profile even if same gstin and name exists.
                # Just Checking for parent-entity having same PAN/name now.
                parent_entity = None
                if company_gstin:
                    parent_entities = self.company_profiles_client.get_parent_company_profiles(
                        pan_number=get_pan_from_gstin(company_gstin), hotel_id=hotel_id).parent_entities
                    parent_entity = parent_entities and parent_entities[0]
                    if parent_entity:
                        return self._create_sub_entity(
                            company_gstin,
                            parent_entity['parent_entity_id'],
                            company_name,
                            company_address,
                            client_internal_code,
                            source,
                            email_id=email_id,
                            phone_number=phone_number,
                            application=application,
                        )
                    parent_entities = self.company_profiles_client.get_parent_company_profiles(
                        trade_name=company_name, hotel_id=hotel_id).parent_entities
                    parent_entity = parent_entities and parent_entities[0]
                return create_company_profile(parent_entity, source)

            elif company_gstin and gstin_search:
                company_profiles = self.company_profiles_client.search_entities(
                    gstins=company_gstin,
                    source=source,
                    hotel_id=hotel_id,
                    commission_hotel_id=hotel_id,
                )
                if company_profiles:
                    return get_latest_company_profile(company_profiles.sub_entities)
                else:
                    parent_entities = self.company_profiles_client.get_parent_company_profiles(
                        pan_number=get_pan_from_gstin(company_gstin), hotel_id=hotel_id).parent_entities
                    parent_entity = parent_entities and parent_entities[0]
                    if parent_entity:
                        return self._create_sub_entity(
                            company_gstin,
                            parent_entity['parent_entity_id'],
                            company_name,
                            company_address,
                            client_internal_code,
                            source,
                            email_id=email_id,
                            phone_number=phone_number,
                            application=application,
                        )
                    parent_entities = self.company_profiles_client.get_parent_company_profiles(
                        trade_name=company_name, hotel_id=hotel_id).parent_entities
                    parent_entity = parent_entities and parent_entities[0]
                return create_company_profile(parent_entity, source)

            elif company_name and name_search:
                logger.info("Checking if company name matches any profile")
                # Creating profile even if gstin is not present
                company_profiles = self.company_profiles_client.search_entities(
                    trade_name=company_name,
                    source=source,
                    hotel_id=hotel_id,
                    commission_hotel_id=hotel_id,
                )
                company_profile = get_company_profile_from_name(company_profiles.sub_entities, company_name)
                if company_profile is None:
                    # Search in parent entities if matching sub-entity is not found
                    parent_entities = self.company_profiles_client.get_parent_company_profiles(
                        trade_name=company_name, hotel_id=hotel_id).parent_entities
                    parent_entity = parent_entities and parent_entities[0]
                    return create_company_profile(parent_entity, source)
                return create_company_profile(company_profile, source)

            if company_name and not name_search:
                return create_company_profile(company_profile=None, source=source)

            else:
                # Case when no such profile exists and Since company_name is not present, we can't create new one
                raise CompanyProfileException(
                    "Can't fetch or create company profile as Company name is not present")
        except Exception as e:
            logger.info(str(e))
            return None

    def _create_sub_entity(
            self,
            gstin_value,
            parent_entity_code,
            company_name,
            address,
            client_internal_code,
            source,
            email_id=None,
            phone_number=None,
            application=None,
    ):
        from tenant_gateway.infrastructure.external_clients.su_client.dtos.booking_dto import AddressDTO as SuAddressDTO
        if isinstance(address, SuAddressDTO):
            # IN case we get address from clear-tax
            address = dict(address_line_1=address.address_line1, address_line_2=address.address_line2,
                           city=address.city, state=address.state, pincode=address.pin_code,
                           country=address.country_code)
        elif isinstance(address, EmmaAddressDTO):
            address_line1, address_line2 = None, None
            if address.address_line:
                lines = address.address_line.split('\n')
                address_line1 = lines[0] if len(lines) > 0 else None
                address_line2 = lines[1] if len(lines) > 1 else None
            address = dict(address_line_1=address_line1, address_line_2=address_line2,
                           city=address.city, state=address.state, pincode=address.postal_code,
                           country=address.country)
        else:
            address = dict(address_line_1=address, city=None, state=None, pincode=None, country=None)

        payload = dict(
            legal_entity_name=company_name, trade_name=company_name, registered_address=address,
            phone_numer=phone_number, status=CompanyProfileStatus.COMPLETE.value, communication_address=address,
            source=source, email_id=email_id, business_code=None, point_of_contacts=None, remarks=None,
            statutory_details=[{'field_name': 'gst', 'value': gstin_value}] if gstin_value else [],
            has_lut=None, is_sez_applicable=None, booker_modal_request=True, client_internal_code=client_internal_code,
        )

        sub_entities = self.company_profiles_client.create_company_profile_sub_entity(
            parent_entity_code,
            dict(data=[payload]),
            custom_headers={'X-Application': application} if application else None,
        )
        return sub_entities[0] if sub_entities else None

    def _create_parent_entity(self, address, company_name, gstin_value, hotel_id, application=None):
        if isinstance(address, SuAddressDTO):
            # IN case we get address from cleartax
            address = dict(address_line_1=address.address_line1, address_line_2=address.address_line2,
                           city=address.city, state=address.state, pincode=address.pin_code,
                           country=address.country_code)
        elif isinstance(address, EmmaAddressDTO):
            address = dict(address_line_1=address.address_line, address_line_2=None,
                           city=address.city, state=address.state, pincode=address.postal_code,
                           country=address.country)
        else:
            address = dict(address_line_1=address, city=None, state=None, pincode=None, country=None)

        payload = dict(
            legal_entity_name=company_name, trade_name=company_name, category=None, sub_category=None,
            registered_address=address, communication_address=address, agency_type=None, email_id=None,
            phone_numer=None, business_code=None, usage_insights=None, point_of_contacts=None,
            status=CompanyProfileStatus.COMPLETE.value, hotel_id=hotel_id, external_profile_id=None,
            is_sez_applicable=None, client_internal_code=None, remarks=None, booker_modal_request=True,
            statutory_details=[
                {'field_name': 'pan_number', 'value': get_pan_from_gstin(gstin_value)}
            ] if gstin_value else []
        )

        response = self.company_profiles_client.create_company_profile_parent_entity(
            profile_data=dict(data=payload),
            custom_headers={'X-Application': application} if application else None,
        )
        return response.company_profile_id
