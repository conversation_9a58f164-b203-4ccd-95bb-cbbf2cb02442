import logging

from object_registry import register_instance
from tenant_gateway.infrastructure.external_clients.catalog_service.catalog_client import CatalogServiceClient
from tenant_gateway.infrastructure.external_clients.crs_service_client import CrsClient

logger = logging.getLogger(__name__)


@register_instance(dependencies=[CrsClient, CatalogServiceClient])
class HotelService(object):
    def __init__(self, crs_client, catalog_service_client):
        self.crs_client = crs_client
        self.catalog_service_client = catalog_service_client

    def get_hotel_details(self, hotel_id):
        try:
            catalog_hotels_dto = self.catalog_service_client.get_hotels([hotel_id])
            if not catalog_hotels_dto:
                return None
            return catalog_hotels_dto[0].to_json()
        except Exception as e:
            logger.exception(f"Exception while fetching hotel details from catalog for hotel_id: {hotel_id}, error: {str(e)}.")
            raise e

    def get_room_type_inventory(self, hotel_id, from_date, to_date):
        try:
            room_type_inventories = self.crs_client.get_room_type_inventories(hotel_id, from_date, to_date)
            return room_type_inventories
        except Exception as e:
            logger.exception(f"Exception while fetching room type inventory from CRS for hotel_id: {hotel_id}, error: {str(e)}")
            raise e

    def release_inventory_blocks(self, hotel_id, inventory_block_ids):
        try:
            return self.crs_client.release_inventory_blocks(hotel_id, inventory_block_ids)
        except Exception as e:
            logger.exception(f"Exception while fetching room type inventory from CRS for hotel_id: {hotel_id}, error: {str(e)}")
            raise e

    def get_room_type_configs(self, hotel_id):
        try:
            hotel_room_type_dtos = self.catalog_service_client.get_room_type_configurations(hotel_id)
            return hotel_room_type_dtos.to_json()
        except Exception as e:
            logger.exception(f"Exception while fetching room type configs from catalog for hotel_id: {hotel_id}, error: {str(e)}")
            raise e
