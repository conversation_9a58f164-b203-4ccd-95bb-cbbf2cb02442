import logging
from collections import defaultdict, Counter
from datetime import datetime, timedelta, time
from decimal import Decimal
from typing import List

from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes, ChargeStatus
from ths_common.constants.booking_constants import AgeGroup, BookingStatus
from thsc.crs.entities.billing import Bill as THS<PERSON><PERSON>ill
from thsc.crs.entities.booking import Booking as THSCBooking, RatePlanInclusion as THSCInclusion
from thsc.crs.entities.booking import GuestStay
from thsc.crs.entities.booking import Price as THSCPrice
from treebo_commons.money import Money
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import (
    current_date, date_range, ymd_str_to_date, datetime_at_max_time_of_day
)

from object_registry import register_instance, locate_instance
from tenant_gateway.application.services.hotel_service import HotelService
from tenant_gateway.application.services.tenant_settings import TenantSettings
from tenant_gateway.common.constants import (
    ROOM_TYPE_PRIORITY, ModificationActionTypes, LATE_CHECKOUT, EARLY_CHECKIN,
    EciLcoConstants,
)
from tenant_gateway.common.exceptions import (
    AddExpenseException,
    BookingModificationNotAllowedException,
    RoomTypeInventoryUnavailable,
    BookingModificationException,
    RemoveRoomsException, AddGuestException,
)
from tenant_gateway.common.utils import get_iso_date, consolidate_dates
from tenant_gateway.infrastructure.external_clients.catalog_service.catalog_client import (
    CatalogServiceClient,
)
from tenant_gateway.infrastructure.external_clients.crs_service_client import CrsClient
from tenant_gateway.infrastructure.external_clients.growth_service.growth_client import (
    GrowthClient,
)
from tenant_gateway.infrastructure.external_clients.rate_manager_service.rate_manager_client import (
    RateManagerClient,
)
from tenant_gateway.infrastructure.external_clients.rate_manager_service.rate_manager_dtos import (
    RoomStayPriceDTO,
)
from tenant_gateway.infrastructure.external_clients.tax_client import TaxClient

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CrsClient,
        GrowthClient,
        CatalogServiceClient,
        RateManagerClient,
        TaxClient,
        TenantSettings,
    ]
)
class BookingModificationService(object):
    def __init__(
        self, crs_client, growth_client, catalog_client, rate_manager_client, tax_client, tenant_settings,
    ):
        self.crs_client = crs_client
        self.growth_client = growth_client
        self.catalog_client = catalog_client
        self.rate_manager_client = rate_manager_client
        self.tax_client = tax_client
        self.tenant_settings = tenant_settings

    @staticmethod
    def _get_updated_dates(thsc_booking, stay_dates):
        checkin_date = datetime.combine(ymd_str_to_date(stay_dates[0]), thsc_booking.checkin_date.time(),
                                        tzinfo=thsc_booking.checkin_date.tzinfo)
        checkout_date = datetime.combine(ymd_str_to_date(stay_dates[1]), thsc_booking.checkout_date.time(),
                                         tzinfo=thsc_booking.checkout_date.tzinfo)
        return checkin_date, checkout_date

    @staticmethod
    def _get_guest_count(room):
        adult_count = 0
        child_count = 0
        for guest in room.guests:
            if guest.status == BookingStatus.CANCELLED:
                continue
            if guest.age_group == AgeGroup.ADULT:
                adult_count += 1
            elif guest.age_group == AgeGroup.CHILD:
                child_count += 1
        return adult_count, child_count

    @staticmethod
    def _get_total_charge_for_applicable_room_stays(booking, thsc_bill, updated_crs_room_stay_ids):
        room_stay_charge_ids = []
        total_charge = Decimal('0')

        for room in booking.rooms:
            if room.status == BookingStatus.CANCELLED:
                continue
            if not updated_crs_room_stay_ids or (room.room_stay_id in updated_crs_room_stay_ids):
                room_stay_charge_ids.extend(room.charge_ids)
        charge_id_to_charges = {charge.charge_id: charge for charge in thsc_bill.charges}
        for charge_id in room_stay_charge_ids:
            charge = charge_id_to_charges[charge_id]
            if charge.status == ChargeStatus.CANCELLED:
                continue
            total_charge += charge.posttax_amount.amount
            total_charge += sum(
                charge_id_to_charges[inclusion_charge_id].posttax_amount.amount
                for inclusion_charge_id in charge.inclusion_charge_ids
                if charge_id_to_charges[inclusion_charge_id].status != ChargeStatus.CANCELLED
            )
        return total_charge

    @staticmethod
    def _build_room_stay_inclusion_price_per_date(booking: THSCBooking, bill, updated_crs_room_stay_ids):
        room_stay_inclusion_charges_per_date = defaultdict(lambda: defaultdict(list))
        charge_id_to_charge = {charge.charge_id: charge for charge in bill.charges}
        for room in booking.rooms:
            room_stay_id = room.room_stay_id
            if room.status == BookingStatus.CANCELLED or room_stay_id not in updated_crs_room_stay_ids:
                continue
            room_charges = [charge_id_to_charge[charge_id] for charge_id in room.charge_ids]
            inclusion_charge_ids = []
            for charge in room_charges:
                if charge.status == BookingStatus.CANCELLED or not charge.inclusion_charge_ids:
                    continue
                inclusion_charge_ids.extend(charge.inclusion_charge_ids)
            for charge_id in inclusion_charge_ids:
                charge = charge_id_to_charge[charge_id]
                room_stay_inclusion_charges_per_date[room_stay_id][charge.applicable_date].append({
                    "sku_id": charge.item.item_id, "price": charge.posttax_amount})
        return room_stay_inclusion_charges_per_date

    def _get_room_type_configs(self, rooms):
        room_type_configs = defaultdict(list)
        for room in rooms:
            if room.status == BookingStatus.CANCELLED:
                continue
            adult_count, child_count = self._get_guest_count(room)
            room_type_configs[room.room_type_id].append(f'{adult_count}-{child_count}')
        return dict(room_type_configs)

    @staticmethod
    def _get_booking_owner(booking):
        return next(
            (
                customer
                for customer in booking.customers
                if customer.customer_id == booking.booking_owner_id
            ),
            None,
        )

    @staticmethod
    def _build_guest_stays(checkin_date, checkout_date, room_config):
        adults, children = map(int, room_config.split('-'))
        guest_stays = [
            GuestStay(
                age_group=age_group,
                guest=None,
                checkin_date=checkin_date,
                checkout_date=checkout_date,
                status=BookingStatus.RESERVED,
            )
            for age_group, count in zip([AgeGroup.ADULT, AgeGroup.CHILD], [adults, children])
            for _ in range(count)
        ]
        return guest_stays

    def _build_payment_link_request_data(self, booking, amount, post_payment_context):
        booking_owner = self._get_booking_owner(booking)
        payment_link_expiry_in_seconds = self.tenant_settings.get_payment_link_expiry_time_for_booking_modification()
        request_data = {
            "name": f"{booking_owner.first_name} {booking_owner.last_name or ''}",
            "amount": str(amount),
            "phone": f"{booking_owner.country_code} {booking_owner.phone_number}",
            "email": booking_owner.email or "<EMAIL>",
            "hotel_code": booking.hotel_id,
            "post_payment_context": post_payment_context,
        }
        if payment_link_expiry_in_seconds:
            request_data["expire_by"] = int(
                datetime.timestamp(dateutils.current_datetime() +
                                   timedelta(seconds=(payment_link_expiry_in_seconds + 20)))
            )
        return request_data

    def _validate_booking_for_modification(self, booking, bill=None, modification_request=None):
        if not modification_request:
            modification_request = {}
        eci_lco_request = modification_request.get("checkin_checkout_time", {})
        if eci_lco_request and bill:
            self._validate_for_duplicate_eci_lco_request(
                booking, bill, eci_lco_request.get("type"), eci_lco_request.get("room_stay_ids")
            )
        if booking.source.channel_code.lower() not in ['direct', 'ota']:
            raise BookingModificationNotAllowedException(
                message="Modification is only allowed for Direct or OTA channel bookings.",
                description=f"current booking channel {booking.source.channel_code}",
            )
        if eci_lco_request.get("type") == LATE_CHECKOUT:
            invalid_booking_status = {BookingStatus.CHECKED_OUT, BookingStatus.CANCELLED, BookingStatus.NOSHOW}
            if booking.status in invalid_booking_status or booking.checkout_date.date() < current_date():
                raise BookingModificationNotAllowedException(
                    message="Late-checkout not allowed after booking checkout-date",
                    description=f"Current_date {str(current_date())}"
                )
            return
        if booking.status not in [BookingStatus.RESERVED, BookingStatus.CONFIRMED]:
            raise BookingModificationNotAllowedException(
                message="Booking status should be confirmed or reserved",
                description=f'Current Booking Status = {booking.status}',
            )
        if booking.checkin_date.date() < current_date():
            raise BookingModificationNotAllowedException(
                message="Booking Modification not allowed on or after checkin-date",
                description=f"Current_date {str(current_date())}"
            )
        if eci_lco_request and eci_lco_request.get("type") == EARLY_CHECKIN:
            hotel_service = locate_instance(HotelService)
            current_business_date = hotel_service.get_current_business_date(booking.hotel_id)
            if not current_business_date:
                return
            current_business_date = ymd_str_to_date(current_business_date)
            if booking.checkin_date.date() == current_business_date:
                raise BookingModificationNotAllowedException(
                    message="Early Checkin Not Allowed since Night Audit has already ran",
                    description=f"Current Business Date={current_business_date}"
                )

    @staticmethod
    def _validate_for_duplicate_eci_lco_request(booking, bill, service_type, room_stay_ids):
        sku_prefix = 'ECI' if service_type == EARLY_CHECKIN else 'LCO'
        requested_room_stays = room_stay_ids or [r.room_stay_id for r in booking.rooms]
        for charge in bill.charges:
            if charge.status == ChargeStatus.CANCELLED:
                continue
            sku_name = charge.item.name.split(' ')[0]
            if sku_name.split('-')[0] == sku_prefix and charge.item.details['room_stay_id'] in requested_room_stays:
                raise BookingModificationNotAllowedException(
                    message=f"Booking Modification not allowed since {service_type} is already applied",
                    description=f"Room stay {charge.item.details['room_stay_id']} already has {service_type} applied",
                )

    def _validate_inventory(
        self, booking, stay_dates, requested_room_type_configs, dates_with_missing_rate=None
    ):
        hotel_room_inventory = self.crs_client.get_room_type_inventories(
            booking.hotel_id,
            stay_dates[0],
            ymd_str_to_date(stay_dates[1]) - timedelta(days=1),
        )
        for inventory in hotel_room_inventory:
            if dates_with_missing_rate and inventory["date"] not in dates_with_missing_rate:
                continue
            room_type_id = inventory['room_type_id']
            if (
                room_type_id in requested_room_type_configs.keys() and
                inventory['actual_count'] < len(requested_room_type_configs[room_type_id])
            ):
                raise RoomTypeInventoryUnavailable(
                    message='Inventory not available for the given dates',
                    description=str(requested_room_type_configs),
                )

    def _handle_room_upgrade_request(self, booking, modification_data, request):
        modification_data["updated_crs_room_stay_ids"] = list(map(int, request["upgrade_room_stays"].split(',')))
        room_type_to_rooms = defaultdict(list)
        for room in booking.rooms:
            if room.room_stay_id in modification_data["updated_crs_room_stay_ids"]:
                room_type_to_rooms[room.room_type_id].append(room)
        if not room_type_to_rooms:
            raise BookingModificationException(message="Room Stay not found for the given room_stay_ids",
                                               description=f"room_stay_ids = {request['upgrade_room_stays']}")
        hotel_room_types = self.available_hotel_room_types(booking)
        room_type_upgrade_mapping = dict()
        for room_type, rooms in room_type_to_rooms.items():
            upgraded_room_type = next(
                (
                    rt
                    for rt in ROOM_TYPE_PRIORITY
                    if rt > room_type and hotel_room_types[rt] >= len(rooms)
                ),
                None,
            )
            if not upgraded_room_type:
                raise RoomTypeInventoryUnavailable(
                    'Room Upgrade is Not Possible',
                    description=f'current_room_type= {room_type}',
                )
            room_type_upgrade_mapping[room_type] = upgraded_room_type
            hotel_room_types[upgraded_room_type] -= len(rooms)
        requested_room_type_configs = defaultdict(list)
        for room_type, rooms in room_type_to_rooms.items():
            for room in rooms:
                upgraded_room_type = room_type_upgrade_mapping[room.room_type_id]
                room_guests = self._get_guest_count(room)
                requested_room_type_configs[upgraded_room_type].append(
                    f'{room_guests[0]}-{room_guests[1]}'
                )
        modification_data["requested_room_type_configs"] = dict(requested_room_type_configs)
        modification_data["upgraded_room_type_mapping"] = room_type_upgrade_mapping
        modification_data["soft_block_required"] = True

    def _get_cancellation_charge_if_any(self, booking, modification_data, request_type):
        cancel_room_stays = []
        if request_type == "update_stay_dates":
            for room in booking.rooms:
                if room.status == BookingStatus.CANCELLED:
                    continue
                cancel_date_ranges = []
                current_checkin = room.checkin_date.date()
                current_checkout = room.checkout_date.date()
                new_checkin = modification_data["checkin_date"]
                new_checkout = modification_data["checkout_date"]
                if current_checkin < new_checkin:
                    cancel_end = min(new_checkin, current_checkout)
                    if current_checkin < cancel_end:
                        cancel_date_ranges.append((current_checkin, cancel_end))

                if new_checkout < current_checkout:
                    cancel_start = max(new_checkout, current_checkin)
                    if cancel_start < current_checkout:
                        cancel_date_ranges.append((cancel_start, current_checkout))
                for cancel_stay_dates in cancel_date_ranges:
                    cancel_room_stays.append(
                        {
                            'room_stay_id': room.room_stay_id,
                            'cancellation_start_date': get_iso_date(cancel_stay_dates[0]),
                            'cancellation_end_date': get_iso_date(cancel_stay_dates[1]),
                        }
                    )
        elif request_type == "cancel_room_stays":
            for room in booking.rooms:
                if room.status == BookingStatus.CANCELLED or room.room_stay_id not in modification_data:
                    continue
                cancel_room_stays.append(
                    {
                        'room_stay_id': room.room_stay_id,
                        'cancellation_start_date': get_iso_date(room.checkin_date.date()),
                        'cancellation_end_date': get_iso_date(room.checkout_date.date()),
                    }
                )

        if not cancel_room_stays:
            modification_data["cancellation_charge"] = 0
            return
        cancellation_payload = dict(
            cancellation_datetime=str(dateutils.current_datetime()),
            room_stays=cancel_room_stays,
        )
        cancellation_charge = self.crs_client.calculate_cancellation_charges(
            hotel_id=booking.hotel_id,
            booking_id=booking.booking_id,
            cancellation_payload=cancellation_payload,
            resource_version=booking.version,
        )
        return cancellation_charge

    def get_hotel_room_inventory(self, hotel_id, checkin_date, checkout_date):
        return self.crs_client.get_room_type_inventories(
            hotel_id, checkin_date, checkout_date
        )

    def available_hotel_room_types(self, booking):
        hotel_room_inventory = self.get_hotel_room_inventory(
            booking.hotel_id,
            str(booking.checkin_date.date()),
            str((booking.checkout_date - timedelta(days=1)).date()),
        )
        available_room_types = {}
        for inventory in hotel_room_inventory:
            room_type_id = inventory['room_type_id']
            if room_type_id not in available_room_types:
                available_room_types[room_type_id] = inventory['actual_count']
            else:
                available_room_types[room_type_id] = min(
                    available_room_types[room_type_id], inventory['actual_count']
                )
        return available_room_types

    def _build_modification_data(self, booking, modification_request, cancellation_charge_applicable):
        modification_data = {}
        action = modification_request.get('action')
        if modification_request.get("stay_dates"):
            self._handle_update_stay_dates_request(booking, modification_data, modification_request,
                                                   cancellation_charge_applicable)
        elif modification_request.get("room_stays"):
            self._handle_update_room_stays_request(booking, modification_data, action, modification_request,
                                                   cancellation_charge_applicable)
        elif modification_request.get("guest_stays"):
            self._handle_guest_modification_request(booking, modification_data, action, modification_request)
        elif modification_request.get("upgrade_room_stays"):
            self._handle_room_upgrade_request(booking, modification_data, modification_request)
        elif modification_request.get("checkin_checkout_time"):
            self._handle_update_stay_time_request(booking, modification_request, modification_data)
        return modification_data

    def _handle_update_stay_time_request(self, booking, modification_request, modification_data):
        update_stay_time = modification_request["checkin_checkout_time"]
        is_early_checkin = update_stay_time.get("type") == EARLY_CHECKIN
        requested_time_slot = update_stay_time["time_slot"]

        rooms = self._get_valid_rooms(booking, update_stay_time.get("room_stay_ids"))
        modification_data['requested_room_type_configs'] = self._get_room_type_configs(rooms)

        modification_data["soft_block_required"] = True

        sku_prefix = self._get_sku_prefix(requested_time_slot, is_early_checkin)
        if not sku_prefix:
            raise BookingModificationException(f"Time-slot not found {requested_time_slot} data {modification_request}")
        modification_data["sku_prefix"] = sku_prefix

        room_code_to_room_type = self.catalog_client.get_room_code_to_room_type_mapping()
        sku_freq, sku_names = self._generate_sku_frequency(rooms, sku_prefix, room_code_to_room_type)

        sku_code_to_name = self._map_sku_codes_to_names(booking.hotel_id, sku_freq)
        logger.info(f"Requested sku_code_to_name {sku_code_to_name}")
        self._validate_skus(sku_freq, sku_code_to_name)

        target_date = (
            (booking.checkin_date.date() - timedelta(days=1)) if is_early_checkin
            else (booking.checkout_date.date())
        )
        modification_data["target_dates"] = str(target_date), str(target_date + timedelta(days=1))

        sku_code_to_price = self._get_sku_prices(booking.hotel_id, target_date, sku_code_to_name)
        logger.info(f"Fetched prices for requested skus {sku_code_to_price}")
        self._validate_prices(sku_code_to_name, sku_code_to_price)
        modification_data["sku_code_to_price"] = sku_code_to_price
        modification_data["sku_name_to_code"] = {sku_name: sku_code for sku_code, sku_name in sku_code_to_name.items()}
        total_price = self._calculate_total_price_with_tax(
            booking, target_date, sku_code_to_price, sku_code_to_name, sku_freq
        )
        modification_data["total_price"] = round(total_price, 2)

    @staticmethod
    def _get_valid_rooms(booking, room_stay_ids):
        if not room_stay_ids:
            return booking.rooms
        filtered_rooms = [
            room
            for room in booking.rooms
            if room.room_stay_id in room_stay_ids and room.status not in {BookingStatus.CANCELLED, BookingStatus.CHECKED_OUT}
        ]
        if not filtered_rooms:
            raise BookingModificationException("Invalid Rooms stay ids provided not room-stays found")
        return filtered_rooms

    def _generate_sku_frequency(self, rooms, sku_prefix, room_code_to_room_type):
        sku_names = []
        for room in rooms:
            occupancy = self._get_guest_count(room)
            room_type = room_code_to_room_type[room.room_type_id]
            # Keeping child count as 0 in sku-name
            sku_names.append(f"{sku_prefix} {room_type}-{occupancy[0]}-0")
        logger.info(f"Requested skus are {sku_names}")
        return Counter(sku_names), sku_names

    def _map_sku_codes_to_names(self, hotel_id, sku_freq):
        all_skus = self.rate_manager_client.get_all_property_skus(hotel_id)
        return {sku['code']: sku['name'] for sku in all_skus if sku['name'] in sku_freq}

    def _validate_skus(self, sku_freq, sku_code_to_name):
        missing_skus = set(sku_freq.keys()) - set(sku_code_to_name.values())
        if missing_skus:
            raise BookingModificationException(f"Unable to find sku_codes for {missing_skus}")

    def _get_sku_prices(self, hotel_id, target_date, sku_code_to_name):
        sku_prices = self.rate_manager_client.get_inclusion_base_rates(
            hotel_id=hotel_id,
            start_date=target_date,
            end_date=target_date,
            sku_ids=list(sku_code_to_name.keys())
        )
        return {sku_price.sku_id: sku_price.price for sku_price in sku_prices}

    def _validate_prices(self, sku_code_to_name, sku_code_to_price):
        missing_sku_price = set(sku_code_to_name.keys()) - set(sku_code_to_price.keys())
        if missing_sku_price:
            raise BookingModificationException(f"Sku Price not found for sku with code {missing_sku_price}")

    def _calculate_total_price_with_tax(self, booking, target_date, sku_code_to_price, sku_code_to_name, sku_freq):
        tax_request_prices = [
            dict(date=str(target_date), pretax_price=price, index=f"{booking.hotel_id}-{sku_code}")
            for sku_code, price in sku_code_to_price.items()
        ]
        tax_request_payload = dict(
            skus=[
                dict(
                    attributes=[dict(key="hotel_id", value=booking.hotel_id)],
                    category_id="stay",
                    prices=tax_request_prices,
                    index=f'{booking.hotel_id}-{booking.booking_id}'
                )
            ]
        )
        tax_response = self.tax_client.calculate_tax(tax_request_payload)
        prices = tax_response[0]["prices"]

        total_price = Decimal('0.00')
        for price in prices:
            sku_code = price['index'].split('-')[1]
            quantity = sku_freq[sku_code_to_name[sku_code]]
            total_price += (Decimal(price["posttax_price"]) * quantity)
        return total_price

    @staticmethod
    def _get_sku_prefix(time_slot, is_early_checkin):
        if time_slot == EciLcoConstants.BEFORE_6AM:
            time_str = "5 AM"
        elif time_slot == EciLcoConstants.AFTER_6PM:
            time_str = "6 PM"
        else:
            time_str = time_slot.split(' - ')[0]

        input_time = datetime.strptime(time_str, "%I %p").time()

        if is_early_checkin:
            if input_time < time(6, 0):
                return EciLcoConstants.ECI_B6
            elif time(6, 0) <= input_time < time(9, 0):
                return EciLcoConstants.ECI_BT69
            elif time(9, 0) <= input_time <= time(12, 0):
                return EciLcoConstants.ECI_A9
        else:
            if time(12, 0) <= input_time < time(14, 0):
                return EciLcoConstants.LCO_BT122
            elif time(14, 0) <= input_time < time(16, 0):
                return EciLcoConstants.LCO_BT24
            elif input_time >= time(16, 0):
                return EciLcoConstants.LCO_A4

    def _handle_update_stay_dates_request(
            self, booking, modification_data, modification_request, cancellation_charge_applicable=None):
        new_checkin = modification_request["stay_dates"]["checkin_date"]
        new_checkout = modification_request["stay_dates"]["checkout_date"]
        dates_with_missing_rate = []
        charge_additional = False
        if new_checkin == booking.checkin_date.date() and new_checkout == booking.checkout_date.date():
            raise BookingModificationException(
                message="New stay_dates should be different from current stay_dates"
            )
        for date in date_range(new_checkin, new_checkout, end_inclusive=False):
            if booking.checkin_date.date() <= date < booking.checkout_date.date():
                charge_additional = True
                continue
            dates_with_missing_rate.append(str(date))
        if dates_with_missing_rate:
            modification_data["dates_with_missing_rate"] = dates_with_missing_rate
            modification_data["soft_block_required"] = True
            modification_data['requested_room_type_configs'] = self._get_room_type_configs(
                booking.rooms
            )
            modification_data["charge_additional"] = charge_additional
        else:
            modification_data["stay_reduced"] = True
        modification_data["updated_stay_dates"] = (
            str(modification_request["stay_dates"]["checkin_date"]),
            str(modification_request["stay_dates"]["checkout_date"]),
        )
        if not cancellation_charge_applicable:
            return
        cancellation_charge = self._get_cancellation_charge_if_any(
            booking, modification_request["stay_dates"], "update_stay_dates"
        )
        if cancellation_charge:
            modification_data["cancellation_charge"] = cancellation_charge["cancellation_charge"]

    def _handle_update_room_stays_request(
            self, booking, modification_data, action, modification_request, cancellation_charge_applicable):
        if action == ModificationActionTypes.ADD.value:
            requested_room_type_configs = defaultdict(list)
            for room_config in modification_request["room_stays"]["room_type_configs"]:
                requested_room_type_configs[room_config["room_type_id"]].append(room_config['room_config'])
            modification_data["requested_room_type_configs"] = dict(requested_room_type_configs)
            modification_data["soft_block_required"] = True
            modification_data["charge_additional"] = True
        else:
            modification_data["cancel_room_stay_ids"] = modification_request["room_stays"]["room_stay_ids"]
            if not cancellation_charge_applicable:
                return
            cancellation_charge = self._get_cancellation_charge_if_any(
                booking, modification_data["cancel_room_stay_ids"], "cancel_room_stays"
            )
            if cancellation_charge:
                modification_data["cancellation_charge"] = cancellation_charge["cancellation_charge"]

    def _handle_guest_modification_request(self, booking, modification_data, action, modification_request):
        if action == ModificationActionTypes.ADD.value:
            requested_room_type_configs = defaultdict(list)
            updated_crs_room_stay_ids = []
            for guest_stay in modification_request["guest_stays"]:
                updated_crs_room_stay_ids.append(guest_stay["room_stay_id"])
                room_stay = next(
                    (
                        room
                        for room in booking.rooms
                        if room.room_stay_id == guest_stay["room_stay_id"]
                    ),
                    None,
                )
                if not room_stay:
                    raise BookingModificationException(message="Room Stay not found for the given room_stay_id",
                                                       description=f"guest_stay = {guest_stay}")
                adults, children = self._get_guest_count(room_stay)
                requested_adults, requested_children = map(
                    int, guest_stay["guest_config"].split("-")
                )
                requested_room_type_configs[room_stay.room_type_id].append(
                    f'{adults + requested_adults}-{children + requested_children}'
                )
            modification_data["requested_room_type_configs"] = dict(requested_room_type_configs)
            modification_data["updated_crs_room_stay_ids"] = updated_crs_room_stay_ids
        else:
            modification_data['remove_guest_rooms'] = modification_request["guest_stays"]

    def handle_free_stay_dates_update(self, booking_id, updated_stay_duration):
        modification_data = dict()
        booking: THSCBooking = THSCBooking.get(booking_id)
        self._validate_booking_for_modification(booking)
        rate_plan_details = self._fetch_rate_plan_details(booking)
        rate_plan_id = rate_plan_details['rate_plan_id']
        rate_plan_reference_id = rate_plan_details['rate_plan_reference_id']
        self._handle_update_stay_dates_request(booking, modification_data,
                                               modification_request=dict(stay_dates=updated_stay_duration))
        stay_dates = modification_data.get(
            "updated_stay_dates",
            (str(booking.checkin_date.date()), str(booking.checkout_date.date())),
        )
        if modification_data.get("stay_reduced"):
            self.update_stay_dates(booking, stay_dates, room_type_prices={}, rate_plan_id=rate_plan_id)
        else:
            thsc_bill: THSCBill = THSCBill.get(booking.bill_id)
            fetched_prices, modification_price = self._calculate_modification_charge(
                booking, thsc_bill, modification_data, rate_plan_reference_id, stay_dates
            )
            if modification_price:
               raise BookingModificationException(
                   message="Payment is required to modify booking",
                   description=f"Modification Price = {modification_price}"
               )
            self.update_stay_dates(booking, stay_dates, fetched_prices, rate_plan_id)

    def handle_modification_request(self, booking_id, modification_request):
        booking: THSCBooking = THSCBooking.get(booking_id)
        thsc_bill: THSCBill = THSCBill.get(booking.bill_id)
        self._validate_booking_for_modification(booking, thsc_bill, modification_request)
        cancellation_charge_applicable = True if thsc_bill.net_paid_amount else False
        modification_data = self._build_modification_data(booking, modification_request, cancellation_charge_applicable)
        cancellation_charge = modification_data.get("cancellation_charge", 0)
        modification_response_data = dict()
        rate_plan_details = self._fetch_rate_plan_details(booking)
        rate_plan_reference_id = rate_plan_details['rate_plan_reference_id']
        if modification_data.get('remove_guest_rooms'):
            self.remove_guest(booking, modification_data, rate_plan_reference_id)
            modification_response_data["cancellation_charge"] = cancellation_charge
        elif modification_data.get("cancel_room_stay_ids"):
            modification_response_data["cancellation_charge"] = cancellation_charge
            modification_response_data["upfront_payment_required"] = False
        elif modification_data.get("stay_reduced"):
            modification_response_data["cancellation_charge"] = cancellation_charge
            modification_response_data["upfront_payment_required"] = False
            modification_response_data[
                "message"] = f'Stay can be reduced with a cancellation charge of {cancellation_charge}'
        elif modification_request.get("checkin_checkout_time"):
            inventory_blocks = []
            if modification_data["soft_block_required"]:
                inventory_blocks = self._create_inventory_blocks(
                    booking=booking, modification_data=modification_data,
                    stay_dates=modification_data["target_dates"]
                )
            post_payment_context = self._build_post_payment_context(
                booking,
                modification_data,
                modification_request,
                modification_data["target_dates"],
                dict(total_price=modification_data["total_price"]),
                inventory_blocks,
            )
            modification_response_data["payment_link"] = self._generate_payment_link(
                booking,
                post_payment_context,
                modification_data["total_price"]
            )
            modification_response_data["modification_charge"] = modification_data["total_price"]
            modification_response_data['upfront_payment_required'] = True
        elif modification_data.get("requested_room_type_configs"):
            stay_dates = modification_data.get(
                "updated_stay_dates",
                (str(booking.checkin_date.date()), str(booking.checkout_date.date())),
            )
            fetched_prices, modification_price = self._calculate_modification_charge(
                booking, thsc_bill, modification_data, rate_plan_reference_id, stay_dates
            )
            modification_response_data["modification_charge"] = round(
                modification_price, 2
            )
            if not (modification_price or modification_data.get("cancellation_charge")):
                modification_response_data['upfront_payment_required'] = False
                modification_response_data['message'] = "Booking Can be Modified Free of cost kindly confirm"
            elif modification_price > 0:
                inventory_blocks = []
                if modification_data.get("soft_block_required"):
                    inventory_blocks = self._create_inventory_blocks(booking, modification_data, stay_dates)
                post_payment_context = self._build_post_payment_context(
                    booking,
                    modification_data,
                    modification_request,
                    stay_dates,
                    fetched_prices,
                    inventory_blocks,
                )
                modification_response_data["payment_link"] = self._generate_payment_link(
                    booking,
                    post_payment_context,
                    round(modification_price, 2),
                )
                modification_response_data['upfront_payment_required'] = True
                modification_response_data["cancellation_charge"] = cancellation_charge

        return modification_response_data

    def _calculate_modification_charge(self, booking, thsc_bill, modification_data, rate_plan_id, stay_dates):
        total_room_rent = self._get_total_charge_for_applicable_room_stays(
            booking,
            thsc_bill,
            modification_data.get("updated_crs_room_stay_ids"),
        )
        room_stay_pretax_prices = self.fetch_prices(
            booking,
            modification_data,
            stay_dates,
            rate_plan_id=rate_plan_id,
        )
        fetched_prices = self._build_fetched_prices(room_stay_pretax_prices)
        self._validate_required_room_rates(fetched_prices, modification_data)
        total_room_stay_posttax_price = self._fetch_total_room_stay_posttax_price(booking, room_stay_pretax_prices)
        modification_price = (
            total_room_stay_posttax_price
            if modification_data.get("charge_additional")
            else max(0, total_room_stay_posttax_price - total_room_rent)
        )
        return fetched_prices, modification_price

    @staticmethod
    def _validate_required_room_rates(fetched_prices, modification_data):
        for room_type in modification_data["requested_room_type_configs"].keys():
            if room_type not in fetched_prices:
                raise BookingModificationException(
                    message=f"Room Type {room_type} is not mapped with the used rate_plan",
                )

    def _fetch_rate_plan_details(self, booking):
        rate_plan_id = None
        for room in booking.rooms:
            for rate_plan in room.room_rate_plans:
                if not rate_plan_id:
                    rate_plan_id = rate_plan.rate_plan_id
                if rate_plan_id != rate_plan.rate_plan_id:
                    raise BookingModificationException(
                        message="Booking Modification for bookings with multiple rate-plan is not supported in this version",
                    )
        rate_plan_details = self.crs_client.get_booking_rate_plans(booking.booking_id)
        for rate_plan_detail in rate_plan_details:
            if rate_plan_detail['rate_plan_id'] == rate_plan_id:
                return rate_plan_detail
        raise BookingModificationException(
            message="Not Able to fetch rate_plan_id for the given booking"
        )

    def remove_guest(self, booking, modification_data, rate_plan_id):
        for remove_guest_room in modification_data['remove_guest_rooms']:
            room_stay_id = int(remove_guest_room['room_stay_id'])
            room = next((room for room in booking.rooms if room.room_stay_id == room_stay_id), None)
            if not room:
                raise BookingModificationException(
                    message="RoomStay not found with the given room_stay_id",
                )
            guest_stay_ids = remove_guest_room['guest_ids']
            room_prices, rate_plan_inclusions = self._get_remove_guest_prices(booking, room, rate_plan_id)
            booking.remove_guests(room_stay_id, guest_stay_ids, room_prices, rate_plan_inclusions)

    def _get_remove_guest_prices(self, booking, room, rate_plan_id):
        bill = THSCBill.get(booking.bill_id)
        room_prices = self._build_thsc_prices_from_room_rents(rate_plan_id, room.room_rents)
        datewise_inclusion_price = self._build_room_stay_inclusion_price_per_date(booking, bill, [room.room_stay_id])
        rate_plan_inclusions = self._build_thsc_rate_plan_inclusions(datewise_inclusion_price[room.room_stay_id])
        return room_prices, rate_plan_inclusions

    def _fetch_total_room_stay_posttax_price(self, booking, room_stay_pretax_prices):
        unique_pretax_prices = {
            rate_plan_price.pre_tax_price
            for room_stay_pretax_price in room_stay_pretax_prices
            for rate_plan_price in room_stay_pretax_price.rate_plan_prices
        }
        tax_request_prices = []
        for price in unique_pretax_prices:
            tax_request_prices.append(
                dict(date=str(current_date()), pretax_price=price, index=booking.booking_id)
            )
        tax_request_payload = dict(
            skus=[
                dict(
                    attributes=[dict(key="hotel_id", value=booking.hotel_id)],
                    category_id="stay",
                    prices=tax_request_prices,
                    index=f'{booking.hotel_id}-{booking.booking_id}'
                )
            ]
        )
        tax_response = self.tax_client.calculate_tax(tax_request_payload)
        prices = tax_response[0]["prices"]
        pretax_to_posttax_prices = {
            Decimal(price["pretax_price"]): Decimal(price["posttax_price"])
            for price in prices
        }
        return sum(
            room_stay_price.total_posttax_price(pretax_to_posttax_prices)
            for room_stay_price in room_stay_pretax_prices
        )

    def fetch_prices(
        self,
        booking,
        modification_data,
        stay_dates,
        rate_plan_id,
    ):
        if modification_data.get("soft_block_required"):
            self._validate_inventory(
                booking, stay_dates, modification_data["requested_room_type_configs"],
                modification_data.get("dates_with_missing_rate", 0)
            )
        room_type_config_map = self.get_hotel_room_type_config(booking.hotel_id)
        room_stays = []
        for room_type, room_configs in modification_data["requested_room_type_configs"].items():
            if room_type not in room_type_config_map:
                raise BookingModificationException(
                    message="Invalid Room Type",
                    description=f"Allowed Room Types :{room_type_config_map.keys()}"
                )
            for room_config in room_configs:
                adults, children = room_config.split('-')
                max_occupancy = room_type_config_map[room_type].occupancy
                if int(adults) > max_occupancy[0] or int(children) > max_occupancy[1]:
                    raise BookingModificationException(
                        message="Invalid Requested room type configs please re-check",
                        description=f"Requested room_config: {room_config}, max_occupancy: {max_occupancy}"
                    )
                if modification_data.get("dates_with_missing_rate"):
                    for date in modification_data["dates_with_missing_rate"]:
                        room_stays.append(
                            {
                                "occupancy": dict(adults=int(adults),
                                                  children=int(children)
                                                  ),
                                "room_type_id": room_type,
                                "stay_start": date,
                                "stay_end": date,
                            }
                        )
                else:
                    room_stays.append(
                        {
                            "occupancy": dict(adults=int(adults),
                                              children=int(children)
                                              ),
                            "room_type_id": room_type,
                            "stay_start": stay_dates[0],
                            "stay_end": str(ymd_str_to_date(stay_dates[1]) - timedelta(days=1)),
                        }
                    )
        pricing_request_data = dict(
            booking_date=str(current_date()),
            channel_code=booking.source.channel_code,
            property_id=booking.hotel_id,
            rate_plan_id=rate_plan_id,
            rooms=room_stays,
            sub_channel_code=booking.source.subchannel_code,
        )
        room_stay_prices = self.rate_manager_client.fetch_room_stay_prices(pricing_request_data)
        if not room_stay_prices:
            raise RoomTypeInventoryUnavailable(
                message="Unable to fetch prices for the given room_config",
                description=f"RackRate does not exist for {modification_data['requested_room_type_configs']}",
            )
        return room_stay_prices

    def update_guest_stay(self, booking: THSCBooking, bill, modification_request, rate_plan_id, modification_data):
        room_stay_inclusion_price_per_date = self._build_room_stay_inclusion_price_per_date(
            booking,
            bill,
            modification_data['updated_crs_room_stay_ids']
        )
        room_stay_id_to_room_stay = {room.room_stay_id: room for room in booking.rooms}
        for guest_stay in modification_request['guest_stays']:
            room_stay = room_stay_id_to_room_stay[guest_stay['room_stay_id']]
            new_thsc_price = self._build_thsc_prices_from_room_rents(rate_plan_id, room_stay.room_rents)
            rate_plan_inclusions = self._build_thsc_rate_plan_inclusions(
                room_stay_inclusion_price_per_date[room_stay.room_stay_id]
            )
            new_guest_stays = self._build_guest_stays(
                booking.checkin_date,
                booking.checkout_date,
                guest_stay['guest_config']
            )
            booking.add_guests(room_stay.room_stay_id, new_guest_stays, new_thsc_price, rate_plan_inclusions)

    @staticmethod
    def _build_thsc_prices_from_room_rents(rate_plan_id, room_rents):
        return [
            THSCPrice(
                applicable_date=datetime_at_max_time_of_day(room_rent.applicable_date),
                posttax_amount=room_rent.posttax_amount,
                bill_to_type=ChargeBillToTypes.GUEST,
                type=ChargeTypes.NON_CREDIT,
                rate_plan_reference_id=rate_plan_id,
            )
            for room_rent in room_rents
        ]

    @staticmethod
    def _build_thsc_rate_plan_inclusions(room_stay_inclusion_price_per_date):
        rate_plan_inclusions = []
        for applicable_date, inclusions in room_stay_inclusion_price_per_date.items():
            for inclusion in inclusions:
                rate_plan_inclusions.append(
                    THSCInclusion(
                        start_date=applicable_date.date(),
                        end_date=applicable_date.date(),
                        sku_id=inclusion['sku_id'],
                        quantity=1,
                        posttax_amount=inclusion['price'],
                    )
                )
        return rate_plan_inclusions

    def update_stay_dates(self, booking, new_stay_dates, room_type_prices, rate_plan_id):
        checkin_date, checkout_date = self._get_updated_dates(booking, new_stay_dates)
        active_rooms = [room for room in booking.rooms if room.status != BookingStatus.CANCELLED]
        for index, room in enumerate(active_rooms):
            adults, children = self._get_guest_count(room)
            room_config = f'{adults}-{children}'
            room_config_price = room_type_prices.get(room.room_type_id, {}).get(room_config)
            prices = self._build_prices(booking, room_config_price) if room_config_price else []
            rate_plan_inclusions = self._build_rate_plan_inclusions(room_config_price) if room_config_price else []
            update_stay_duration_payload = {
                "checkin_date": str(checkin_date),
                "checkout_date": str(checkout_date),
                "rate_plan": {
                    "rate_plan_id": rate_plan_id,
                },
                "prices": prices,
                "rate_plan_inclusions": rate_plan_inclusions
            }
            data = dict(data=update_stay_duration_payload, resource_version=int(booking.version) + index)
            self.crs_client.update_room_stay_duration(booking.booking_id, room.room_stay_id, data)

    @staticmethod
    def _build_prices(thsc_booking, room_config_prices):
        prices = []
        for date, room_price in room_config_prices.items():
            applicable_date = datetime.combine(ymd_str_to_date(date), thsc_booking.checkin_date.time(),
                                               tzinfo=thsc_booking.checkin_date.tzinfo)
            prices.append(
                {
                    "applicable_date": str(applicable_date),
                    "bill_to_type": "guest",
                    "pretax_amount": f'{room_price["room_rent"]} INR',
                    "type": "non-credit"
                }
            )
        return prices

    @staticmethod
    def _build_rate_plan_inclusions(room_price_per_date):
        inclusions = []
        for date, room_price in room_price_per_date.items():
            for inclusion in room_price.get("inclusions", []):
                for _ in range(inclusion['quantity']):
                    inclusions.append(
                        {
                            "start_date": date,
                            "end_date": date,
                            "pretax_amount": f'{inclusion["price"]} INR',
                            "sku_id": inclusion["sku_id"],
                            "quantity": 1
                        }
                    )
        return inclusions

    def _create_inventory_blocks(self, booking, modification_data, stay_dates):
        requested_room_type_configs = modification_data['requested_room_type_configs']
        dates_with_missing_rates = modification_data.get("dates_with_missing_rate")
        inventory_blocks = []
        for room_type_id, room_configs in requested_room_type_configs.items():
            for _ in range(len(room_configs)):
                if dates_with_missing_rates:
                    for dates in consolidate_dates(dates_with_missing_rates):
                        inventory_blocks.append(
                            {
                                'room_type_id': room_type_id,
                                'start_date': dates['start_date'],
                                'end_date': dates['end_date'],
                            }
                        )
                else:
                    inventory_blocks.append(
                        {
                            'room_type_id': room_type_id,
                            'start_date': stay_dates[0],
                            'end_date': stay_dates[1],
                        }
                    )

        soft_block_request_data = dict(
            booking_id=booking.booking_id,
            inventory_blocks=inventory_blocks,
            action="block",
        )
        return self.crs_client.create_inventory_block(
            booking.hotel_id, soft_block_request_data
        )

    @staticmethod
    def _build_post_payment_context(
        booking,
        modification_data,
        modification_request,
        stay_dates,
        room_type_prices,
        inv_blocks,
    ):
        if modification_request.get("stay_dates"):
            modification_request["stay_dates"] = (
                str(modification_request["stay_dates"]["checkin_date"]),
                str(modification_request["stay_dates"]["checkout_date"])
            )
        return dict(
            booking_id=booking.booking_id,
            modification_request=modification_request,
            fetched_prices=dict(room_type_prices),
            inventory_blocks=inv_blocks,
            stay_dates=stay_dates,
            modification_info=modification_data,
        )

    @staticmethod
    def _build_fetched_prices(room_stay_pretax_prices: List[RoomStayPriceDTO]):
        fetched_prices = defaultdict(dict)
        for room_stay_price in room_stay_pretax_prices:
            room_type = room_stay_price.room_type_id
            room_config = room_stay_price.guest_config
            fetched_prices[room_type].setdefault(
                room_config, dict()
            )
            for rate_plan_price in room_stay_price.rate_plan_prices:
                fetched_prices[room_type][room_config].update(
                    {
                        str(rate_plan_price.applicable_date): dict(
                            room_rent=rate_plan_price.pre_tax_price - rate_plan_price.total_inclusion_amount,
                            inclusions=[inclusion.to_dict() for inclusion in rate_plan_price.inclusions],
                        )
                    }
                )
        return dict(fetched_prices)

    def _generate_payment_link(self, booking, post_payment_context, amount_to_be_paid):
        payment_link_request_data = self._build_payment_link_request_data(
            booking, amount_to_be_paid, post_payment_context
        )
        payment_link_info = self.growth_client.generate_payment_link(
            booking.reference_number, payment_link_request_data
        )
        return payment_link_info['data']['payment_link']

    @staticmethod
    def _sanitize_skus(skus):
        for sku in skus:
            date_wise_prices = [dict(
                unit_posttax_price=str(Money(price['unit_posttax_price'].amount, price['unit_posttax_price'].currency))
                if price.get('unit_posttax_price') else None,
                unit_pretax_price=str(Money(price['unit_pretax_price'].amount, price['unit_pretax_price'].currency))
                if price.get('unit_pretax_price') else None,
                quantity=price['quantity'],
                status=price['status'],
                applicable_date=str(price['applicable_date'])
            ) for price in sku['date_wise_prices']]
            sku['date_wise_prices'] = date_wise_prices

    def add_expenses(self, booking_id, expenses):
        try:
            booking: THSCBooking = THSCBooking.get(booking_id)
            for expense in expenses:
                self._sanitize_skus(expense['skus'])
            self.crs_client.add_booking_expenses(booking.hotel_id, booking.booking_id, expenses, 'backend-system')
        except Exception as e:
            raise AddExpenseException(description=str(e))

    def remove_rooms(self, booking_id, room_stay_ids):
        try:
            booking: THSCBooking = THSCBooking.get(booking_id)
            remove_rooms_payload = [dict(room_stay_id=room_stay_id) for room_stay_id in room_stay_ids]
            self.crs_client.remove_rooms(
                hotel_id=booking.hotel_id,
                booking_id=booking.booking_id,
                remove_rooms_payload=dict(room_stays=remove_rooms_payload),
                resource_version=booking.version,
            )
        except Exception as e:
            raise RemoveRoomsException(description=str(e))

    def get_hotel_room_type_config(self, hotel_id):
        room_type_configs = self.catalog_client.get_room_type_configurations(
            hotel_id=hotel_id
        )
        return room_type_configs.room_type_config_map

    def add_guests(self, booking_id, add_guests_request):
        try:
            booking: THSCBooking = THSCBooking.get(booking_id)
            modification_data = {}
            self._handle_guest_modification_request(booking, modification_data, ModificationActionTypes.ADD.value,
                                                    add_guests_request)
            thsc_bill: THSCBill = THSCBill.get(booking.bill_id)
            rate_plan_details = self._fetch_rate_plan_details(booking)
            rate_plan_reference_id = rate_plan_details['rate_plan_reference_id']
            self.update_guest_stay(booking, thsc_bill, add_guests_request, rate_plan_reference_id, modification_data)
        except Exception as e:
            raise AddGuestException(description=str(e))
