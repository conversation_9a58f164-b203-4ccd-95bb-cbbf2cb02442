import psycopg2
import logging

from ths_common.exceptions import ValidationException

from object_registry import register_instance
from tenant_gateway.common.decorators import session_manager
from tenant_gateway.common.exceptions import (DatabaseError)
from tenant_gateway.common.constants import ActionType, ServiceCodes
from tenant_gateway.infrastructure.database.repositories.booking_sync_repository import BookingSyncRepository
from tenant_gateway.infrastructure.database.repositories.hotel_repository import HotelRepository
from tenant_gateway.domain.entities.booking_sync_entity import BookingSyncEntity

logger = logging.getLogger(__name__)

VALID_CRS_OTA_CODES = ['irctc', 'cleartrip', 'goomo', 'hobse', 'hrs', 'happyeasygo', 'go-mmt', 'makemytrip', 'goibibo',
                       'agoda', 'paytm', 'travelguru', 'expedia', 'easemytrip', 'hyperguestota', 'HyperguestOTA',
                       'tripfactory', 'booking-com', 'Go-MMT', 'direct booking', 'mmtgcc']


@register_instance(dependencies=[BookingSyncRepository, HotelRepository])
class TempBookingMigrationService(object):

    def __init__(self, booking_sync_repository, hotel_repository):
        self.booking_sync_service = booking_sync_repository
        self.hotel_repository = hotel_repository

    @session_manager(commit=True)
    def migrate_bookings(self, hotel_ids=None, ota_codes=None, from_date=None, channel_booking_ids=None):
        if ota_codes:
            for ota in ota_codes:
                if ota not in VALID_CRS_OTA_CODES:
                    raise ValidationException(
                        description=f"OTA code {ota} is not a valid code, doesnt exist in CRS. "
                                    f"Please see the valid ota_codes list that we have in crs {VALID_CRS_OTA_CODES}")

        if hotel_ids:
            for hotel_id in hotel_ids:
                if self.hotel_repository.load(service_code=ServiceCodes.SU.value, internal_hotel_id=hotel_id):
                    continue

        from_date = '2022-06-01' if not from_date else from_date

        hotel_ids_str = "('" + "', '".join(hotel_ids) + "')" if hotel_ids else None
        ota_codes_str = "('" + "', '".join(ota_codes) + "')" if ota_codes else None
        channel_booking_ids_str = "('" + "', '".join(channel_booking_ids) + "')" if channel_booking_ids else None

        try:
            con = psycopg2.connect(database='crs', user='tenant-treebo-AMng', port='5432',
                                   host='p-2621-aps1-01-crs.cluster-cqiwfuxda15p.ap-south-1.rds.amazonaws.com',
                                   password='VekIvT3SM4lu')
            cur = con.cursor()
            if ota_codes:
                cur.execute(
                    f"SELECT hotel_id, booking_id, reference_number from treebo_schema.booking where hotel_id "
                    f"IN {hotel_ids_str} and application_code = 'unirate' and subchannel_code IN {ota_codes_str} "
                    f"and DATE(checkout_date) >" + "'" + from_date + "'")

            elif channel_booking_ids:
                cur.execute(
                    f"SELECT hotel_id, booking_id, reference_number from treebo_schema.booking where "
                    f"reference_number IN {channel_booking_ids_str}")

            else:
                cur.execute(
                    f"SELECT hotel_id, booking_id, reference_number from treebo_schema.booking where "
                    f"hotel_id IN {hotel_ids_str} and application_code = 'unirate' "
                    f"and DATE(checkout_date) >" + "'" + from_date + "'")

            bookings = cur.fetchall()

            booking_sync_entities = []
            for hotel_id, crs_booking_id, reference_number in bookings:
                if self.booking_sync_service.load(internal_hotel_id=hotel_id, external_booking_id=reference_number):
                    logger.info(f"Booking {reference_number} already exist in tenant gateway")
                    continue
                booking_sync_entity = BookingSyncEntity(service_code=ServiceCodes.SU.value,
                                                        internal_hotel_id=hotel_id,
                                                        internal_booking_id=crs_booking_id,
                                                        external_booking_id=reference_number,
                                                        success=True, acked=True,
                                                        action_type=ActionType.CREATE,
                                                        external_hotel_id='su-treebo-' + hotel_id)
                booking_sync_entities.append(booking_sync_entity)

            self.booking_sync_service.bulk_upsert(booking_sync_entities)

        except psycopg2.DatabaseError as e:
            raise DatabaseError(description=str(e))

    def _create_booking_sync_entries(self, booking_sync_entities):
        self.booking_sync_service.bulk_upsert(booking_sync_entities)
