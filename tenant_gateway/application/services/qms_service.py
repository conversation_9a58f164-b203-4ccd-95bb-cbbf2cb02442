import logging
import datetime


from object_registry import register_instance
from tenant_gateway.common.decorators import session_manager
from tenant_gateway.domain.entities.qms_guest_feedback import QMSGuestFeedbackEntity
from tenant_gateway.infrastructure.database.repositories.qms_guest_feedback_repository import QMSGuestFeedbackRepository
from tenant_gateway.infrastructure.external_clients.crs_service_client import CrsClient
from tenant_gateway.infrastructure.external_clients.catalog_service.catalog_client import CatalogServiceClient
from tenant_gateway.infrastructure.external_clients.qms_client import QMSClient

logger = logging.getLogger(__name__)


@register_instance(dependencies=[CrsClient, CatalogServiceClient, QMSClient, QMSGuestFeedbackRepository])
class QMSGuestFeedbackService(object):

    def __init__(self, crs_client: CrsClient, catalog_client: CatalogServiceClient, qms_client: QMSClient,
                 qms_guest_feedback_repository: QMSGuestFeedbackRepository):
        self.crs_client = crs_client
        self.catalog_client = catalog_client
        self.qms_client = qms_client
        self.qms_guest_feedback_repository = qms_guest_feedback_repository

    def _format_date(self, date):
        date = date[:-6]
        date = datetime.datetime.strptime(date, '%Y-%m-%dT%H:%M:%S')
        return date.strftime("%d-%b-%Y").upper()

    def _get_room_number(self, hotel_id, customer_id, guest_room_map, failure_reason):
        if customer_id in guest_room_map.keys():
            room_id = guest_room_map[customer_id]
            room_number = self.catalog_client.get_room_number(property_id=hotel_id, room_id=room_id,
                                                              failure_reason=failure_reason)
            return room_number
        else:
            return ""

    def _prepare_booking_data(self, bookings, hotel_id):
        booking_data = {}
        booking_ids = []
        all_guests_data = []
        failure_reason = []
        external_hotel_id = self.catalog_client.get_external_hotel_id(hotel_id)

        for booking in bookings:
            guest_room_map = {}
            booking_ids.append(booking['booking_id'])
            for room_stay in booking['room_stays']:
                room_id = room_stay['room_allocation']['room_id'] if room_stay['room_allocation'] else ""
                for guest_stay in room_stay['guest_stays']:
                    customer_id = guest_stay['guest_allocation']['guest_id']
                    guest_room_map[customer_id] = room_id

            for customer in booking['customers']:
                if customer['customer_id'] in guest_room_map.keys():
                    room_number = self._get_room_number(hotel_id, customer['customer_id'], guest_room_map, failure_reason)

                    mandatory_fields = {
                        'hotel_id': hotel_id,
                        'salutation': customer['salutation'],
                        'last_name': customer['last_name'],
                        'checkout_date': booking['checkout_date'],
                        'external_hotel_id': external_hotel_id,
                        'room_number': room_number
                    }

                    all_mandatory_fields_present = True
                    for field, value in mandatory_fields.items():
                        if not value:
                            all_mandatory_fields_present = False
                            failure_reason.append(
                                f"mandatory field {field} missing for booking id {booking['booking_id']} , "
                                f"customer name :{customer['first_name']}")

                    if all_mandatory_fields_present:
                        all_guests_data.append({
                            "hotel_code": hotel_id,
                            "title": customer['salutation'],
                            "fname": customer['first_name'],
                            "lname": customer['last_name'],
                            "maingst": "1" if customer['is_primary'] else "0",
                            "chkin": self._format_date(booking['checkin_date']),
                            "chkout": self._format_date(booking['checkout_date']),
                            "room": room_number,
                            "emailid": customer['email'],
                            "phone": customer['phone']['number'] if customer["phone"] else "",
                            "bookingsource": customer['address']['country'] if customer['address'] else "",
                            "country": customer['nationality'],
                            "key": external_hotel_id
                        })

        booking_data["GuestData"] = all_guests_data
        return failure_reason, booking_data, booking_ids

    @session_manager(commit=True)
    def generate_guest_feedback(self, property_id, business_date):
        night_audit_bookings = self.crs_client.get_all_night_audit_bookings(property_id, business_date)
        failure_reason, booking_data, booking_ids = self._prepare_booking_data(night_audit_bookings, property_id)
        status_code = self.qms_client.send_data_to_qms(booking_data, failure_reason)

        qms_guest_feedback_entity = QMSGuestFeedbackEntity(booking_ids=booking_ids,
                                                           hotel_id=property_id,
                                                           business_date=business_date,
                                                           request_data=booking_data,
                                                           ack_status=status_code,
                                                           failure_reason=failure_reason
                                                           )
        self.qms_guest_feedback_repository.save(qms_guest_feedback_entity)
