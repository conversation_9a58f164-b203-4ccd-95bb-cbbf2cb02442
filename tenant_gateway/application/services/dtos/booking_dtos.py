from ths_common.value_objects import BookingSource
from thsc.crs.entities.booking import Booking as THSCBooking
from treebo_commons.utils import dateutils


class BookingDTO:
    def __init__(self, thsc_booking: THSCBooking, rate_plans=None):
        self.crs_booking_id = thsc_booking.booking_id
        self.booking_owner_email = self.booking_owner_email(thsc_booking)
        self.resource_version = thsc_booking.version
        self.status = thsc_booking.status.value
        self.stay_start = dateutils.to_date(thsc_booking.checkin_date)
        self.stay_end = dateutils.to_date(thsc_booking.checkout_date)
        self.bill_id = thsc_booking.bill_id
        self.hotel_id = thsc_booking.hotel_id
        self.comments = thsc_booking.comments
        self.booking_owner_id = thsc_booking.booking_owner_id
        self.source = thsc_booking.source
        self.default_billed_entity_category = thsc_booking.default_billed_entity_category
        self.default_billed_entity_category_for_extras = thsc_booking.default_billed_entity_category_for_extras
        self.default_payment_instruction = thsc_booking.default_payment_instruction
        self.default_payment_instruction_for_extras = thsc_booking.default_payment_instruction_for_extras
        self.customers = thsc_booking.customers
        self.rooms = thsc_booking.rooms
        self.reference_number=thsc_booking.reference_number
        self.rate_plans = rate_plans

    def to_json(self):
        booking_json = {
            'crs_booking_id': self.crs_booking_id,
            'booking_owner_email': self.booking_owner_email,
            'resource_version': self.resource_version,
            'status': self.status,
            'stay_start': dateutils.date_to_ymd_str(self.stay_start),
            'stay_end': dateutils.date_to_ymd_str(self.stay_end),
            'bill_id': self.bill_id,
            'hotel_id': self.hotel_id,
            'comments': self.comments,
            "booking_owner_id": self.booking_owner_id,
            'reference_number': self.reference_number,
            'source': self.booking_source(self.source),
            'default_billed_entity_category': self.default_billed_entity_category,
            'default_billed_entity_category_for_extras': self.default_billed_entity_category_for_extras,
            'default_payment_instruction': self.default_payment_instruction,
            'default_payment_instruction_for_extras': self.default_payment_instruction_for_extras,
            'customers': [self.customer(customer) for customer in self.customers],
            'room_stays': [self.room_stay(room) for room in self.rooms]
        }
        if self.rate_plans:
            booking_json['rate_plans'] = self.rate_plans
        return booking_json

    def booking_owner_email(self, thsc_booking: THSCBooking):
        booking_owner_id = thsc_booking.booking_owner_id

        # Using a generator expression and next with a default value
        booking_owner_email = next(
            (customer.email for customer in thsc_booking.customers if customer.customer_id == booking_owner_id), None)

        return booking_owner_email

    def customer(self, customer):
        return {
            "customer_id": customer.customer_id,
            "first_name": customer.first_name,
            "last_name": customer.last_name,
            "salutation": customer.salutation,
            "email": customer.email,
            "reference_id": customer.reference_id,
            "user_profile_id": customer.user_profile_id,
            "phone": self.phone(customer.country_code, customer.phone_number)
        }

    def phone(self, country_code, phone_number):
        if not phone_number:
            return None
        return {'number': phone_number, 'country_code': country_code}

    def booking_source(self, source: BookingSource):
        return {
            "application_code": source.application_code,
            "channel_code": source.channel_code,
            "subchannel_code": source.subchannel_code
        }

    def room_stay(self, room):
        return {
            "status": room.status.value,
            "room_type_id": room.room_type_id,
            "checkin_date": dateutils.isoformat_datetime(room.checkin_date),
            "checkout_date": dateutils.isoformat_datetime(room.checkout_date),
            "room_stay_id": room.room_stay_id,
            "type": room.type.value,
            "guest_stays": [self.guest_stay(guest) for guest in room.guests],
            "room_rents": [self.room_rent(room_rent) for room_rent in room.room_rents],
            "room_rate_plans": [self.room_rate_plan(room_rate_plan) for room_rate_plan in room.room_rate_plans]
        }

    def guest_stay(self, guest):
        return {
            "checkin_date": dateutils.isoformat_datetime(guest.checkin_date),
            "checkout_date": dateutils.isoformat_datetime(guest.checkout_date),
            "guest_id": guest.guest_id,
            "age_group": guest.age_group,
            "status": guest.status.value,
            "guest_stay_id": guest.guest_stay_id,
        }

    def room_rent(self, room_rent):
        return {
            "posttax_amount": room_rent.posttax_amount,
            "applicable_date": dateutils.isoformat_datetime(room_rent.applicable_date)
        }

    def room_rate_plan(self, room_rate_plan):
        return {
            "rate_plan_id": room_rate_plan.rate_plan_id,
            "stay_date": room_rate_plan.stay_date
        }


class GrowthBookingDTO(object):
    def __init__(self, booking_data: dict):
        self.balance = booking_data['balance']
        self.booking_id = booking_data['booking_id']
        self.check_in = booking_data['check_in']
        self.check_out = booking_data['check_out']
        self.group_code = booking_data['group_code']
        self.guest_email = booking_data['guest_email']
        self.guest_name = booking_data['guest_name']
        self.guest_phone = booking_data['guest_phone']
        self.hotel_code = booking_data['hotel_code']
        self.hotel_details = booking_data['hotel_details']
        self.hotel_info = booking_data['hotel_info']
        self.paid_amount = booking_data['paid_amount']
        self.status = booking_data['status']
        self.total_booking_amount = booking_data['total_booking_amount']

    def to_json(self):
        return {
            "balance": self.balance,
            "booking_id": self.booking_id,
            "check_in": self.check_in,
            "check_out": self.check_out,
            "group_code": self.group_code,
            "guest_email": self.guest_email,
            "guest_name": self.guest_name,
            "guest_phone": self.guest_phone,
            "hotel_code": self.hotel_code,
            "hotel_details": self.hotel_details,
            "hotel_info": self.hotel_info,
            "paid_amount": self.paid_amount,
            "status": self.status,
            "total_booking_amount": self.total_booking_amount
        }
