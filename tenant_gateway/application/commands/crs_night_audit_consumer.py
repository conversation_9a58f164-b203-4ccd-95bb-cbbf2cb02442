import logging
import os

import click
from flask.cli import with_appcontext

from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.credentials.exceptions import GetSecretError
from treebo_commons.multitenancy.sqlalchemy.db_engine import setup_tenant_sessions
from treebo_commons.request_tracing.context import setup_request_context_from_request_headers
from treebo_commons.multitenancy.tenant_client import Tenant<PERSON>lient

from object_registry import inject
from tenant_gateway.application.services.guest_feedback import AccorGuestFeedbackService
from tenant_gateway.application.services.qms_service import QMSGuestFeedbackService
from tenant_gateway.application.services.tenant_settings import TenantSettings
from tenant_gateway.domain.external_services.crs.constants import GUEST_FEEDBACK_SERVICE_ENABLED_CONFIG, \
    QMS_FEEDBACK_SERVICE_ENABLED_CONFIG

from tenant_gateway.globals import worker_context
from tenant_gateway.infrastructure.consumers.crs_night_audit_consumer import CRSNightAuditConsumer

logger = logging.getLogger(__name__)


@click.command()
@click.option('--tenant_id', help="Tenant ID for which this command should be run.",
              default=TenantClient.get_default_tenant())
@click.option('--service_code', help="Service Code for which this command should be run.")
@with_appcontext
@inject(guest_feedback_service=AccorGuestFeedbackService, qms_feedback_service=QMSGuestFeedbackService,
        tenant_settings=TenantSettings)
def start_crs_night_audit_consumer_worker(guest_feedback_service, qms_feedback_service, tenant_settings, tenant_id,
                                          service_code):
    click.echo("Tenant ID: %s" % tenant_id)
    click.echo("Service Code: %s" % service_code)

    setup_tenant_sessions(tenant_id=tenant_id)
    setup_request_context_from_request_headers({'X-Tenant-Id': tenant_id})
    worker_context.set_tenant_id(tenant_id)

    consumer_config = tenant_settings.get_crs_consumer_configs()
    if not any(consumer_config.values()):
        logger.info("CRS Consumer Worker not applicable for this tenant: %s" % tenant_id)
        return

    feedback_service = None
    if consumer_config[GUEST_FEEDBACK_SERVICE_ENABLED_CONFIG]:
        try:
            secret = AwsSecretManager.get_secret(
                tenant_id,
                f"{os.environ.get('AWS_SECRET_PREFIX', 'apps/tenant_gateway_service')}/sftp"
            )
            private_key = AwsSecretManager.get_secret(
                tenant_id,
                f"{os.environ.get('AWS_SECRET_PREFIX', 'apps/tenant_gateway_service')}/ssh_pkey"
            )
            secret.update(private_key)
            worker_context.set_sftp_secret(secret)
            feedback_service = guest_feedback_service
        except GetSecretError:
            logger.error(f"Worker not applicable for tenant: {tenant_id} as SFTP credentials" \
                         "not set in AWS Secret Manager")

    elif consumer_config[QMS_FEEDBACK_SERVICE_ENABLED_CONFIG]:
        feedback_service = qms_feedback_service

    else:
        logger.info("Guest Feedback Worker not applicable for this tenant: %s" % tenant_id)
        feedback_service = None

    consumer = CRSNightAuditConsumer(feedback_service, tenant_id, service_code)
    consumer.start_consumer()
