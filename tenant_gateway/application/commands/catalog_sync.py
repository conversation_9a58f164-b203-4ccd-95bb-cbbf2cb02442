import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.sqlalchemy.db_engine import setup_tenant_sessions
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from tenant_gateway.application.services.catalog_sync_service import CatalogSyncService
from tenant_gateway.common.constants import ServiceCodes
from tenant_gateway.domain.external_services.emma.utils import set_emma_auth
from tenant_gateway.globals import worker_context
from tenant_gateway.infrastructure.consumers.catalog_consumer import CatalogServiceConsumer

logger = logging.getLogger(__name__)


@click.command()
@click.option('--tenant_id', help="Tenant ID for which this command should be run.",
              default=TenantClient.get_default_tenant())
@click.option('--service_code', help="Service code for which this command should be run.")
@with_appcontext
@inject(catalog_sync_service=CatalogSyncService)
def start_catalog_sync_worker(catalog_sync_service, tenant_id, service_code):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    setup_tenant_sessions(tenant_id=tenant_id)
    worker_context.set_tenant_id(tenant_id)
    if service_code == ServiceCodes.EMMA.value:
        set_emma_auth(tenant_id)
    consumer = CatalogServiceConsumer(catalog_sync_service, tenant_id, service_code)
    consumer.start_consumer()
