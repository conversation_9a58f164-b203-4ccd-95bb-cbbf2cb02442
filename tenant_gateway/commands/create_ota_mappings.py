import click as click
from flask.cli import with_appcontext
import csv

from tenant_gateway.common.constants import ServiceCodes
from tenant_gateway.domain.external_services.service_selector import ServiceSelector
from tenant_gateway.domain.external_services.su.ota_setup_dto_selector import OTADtoSelector


@click.command('create_ota_mappings')
@click.option('--ota_setup_csv_file_path', help="CSV file path for the ota setup")
@click.option('--ota_mapping_csv_file_path', help="CSV file path for the ota mapping")
@click.option('--service_code', help="Service code for which this command should be run.",
              default=ServiceCodes.SU.value)
@with_appcontext
def create_su_ota_mappings(ota_setup_csv_file_path, ota_mapping_csv_file_path, service_code=None):
    if not ota_setup_csv_file_path or not ota_mapping_csv_file_path:
        raise Exception('OTA csv setup file and mapping path both required')
    ota_setup_dto = None
    ota_mapping_dtos = []
    with open(ota_setup_csv_file_path) as csv_file:
        reader = csv.DictReader(csv_file)
        for row in reader:
            ota_setup_dto = OTADtoSelector().get_setup_dto_instance(row)

    with open(ota_mapping_csv_file_path) as csv_file:
        reader = csv.DictReader(csv_file)
        for row in reader:
            ota_mapping_dtos.append(OTADtoSelector().get_mapping_dto_instance(row))

    ServiceSelector().get_service_instance(service_code).create_ota_mappings(ota_setup_dto, ota_mapping_dtos)


