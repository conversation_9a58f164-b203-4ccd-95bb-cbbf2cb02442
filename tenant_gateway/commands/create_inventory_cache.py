import click as click
from dateutil.parser import isoparse
from flask.cli import with_appcontext
from treebo_commons.multitenancy.sqlalchemy.db_engine import setup_tenant_sessions
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from tenant_gateway.common.constants import ServiceCodes
from tenant_gateway.globals import worker_context
from tenant_gateway.scripts.build_inventory_cache import InventoryCacheBuilder


@click.command('create_inventory_cache')
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    '--service_code',
    help="Service code for which this command should be run.",
    default=ServiceCodes.SU.value,
)
@click.option(
    "--hotel_ids",
    help="Hotel IDs comma separated str for which this command should run.",
    default=None,
)
@click.option(
    "--from_date",
    help="Date from which inventory cache should be build.",
    required=True,
)
@click.option(
    '--to_date', help="Date till which inventory cache should be build.", required=True
)
@with_appcontext
@inject(inventory_cache_builder=InventoryCacheBuilder)
def create_inventory_cache(
    tenant_id, service_code, hotel_ids, from_date, to_date, inventory_cache_builder
):
    request_context.tenant_id = tenant_id
    setup_tenant_sessions(tenant_id=tenant_id)
    worker_context.set_tenant_id(tenant_id)
    from_date = isoparse(from_date).date()
    to_date = isoparse(to_date).date()
    if from_date > to_date:
        raise click.BadParameter("'from_date' cannot be later than 'to_date'.")

    inventory_cache_builder.build_inventory_cache(
        service_code=service_code,
        hotel_ids=hotel_ids,
        from_date=from_date,
        to_date=to_date,
    )
