from lxml import objectify, etree as et
from lxml.builder import E

from tenant_gateway.domain.external_services.google_hotels_ads.constants import (
    LanguageCodes,
    GHAMessageTypes,
)
from tenant_gateway.domain.external_services.google_hotels_ads.dtos.gha_hotel_dto import (
    GHAHotelDto,
)
from tenant_gateway.domain.external_services.google_hotels_ads.utils import XMLUtils


class GHAXmlAdapter(object):
    xml_utils = XMLUtils()

    @staticmethod
    def build_hotels_list_xml(hotels, cs_ids_to_hotel_ids):
        root = E.listings(
            E.language(LanguageCodes.EN.value),
            *[
                GHAHotelDto(hotel, cs_ids_to_hotel_ids[hotel.hotel_id]).to_xml()
                for hotel in hotels
                if hotel.hotel_id in cs_ids_to_hotel_ids
            ]
        )
        objectify.deannotate(root, xsi_nil=True, cleanup_namespaces=True)
        tree = et.ElementTree(root)
        return tree

    def build_inventory_xml(self, property_inventory, hotel_id):
        root = self.xml_utils.generate_initial_xml(GHAMessageTypes.INVENTORY.value)
        inventories = E.Inventories(
            *[inventory.to_xml() for inventory in property_inventory],
            HotelCode=str(hotel_id)
        )
        root.append(inventories)
        inventory_xml = self.xml_utils.generate_xml_text(root)
        return inventory_xml

    def build_availability_xml(self, gha_availability_dtos, hotel_id):
        root = self.xml_utils.generate_initial_xml(GHAMessageTypes.AVAILABILITY.value)
        availability_messages = E.AvailStatusMessages(HotelCode=str(hotel_id))
        for gha_availability_dto in gha_availability_dtos:
            availability_message = gha_availability_dto.to_xml()
            availability_messages.append(availability_message)
        root.append(availability_messages)
        availability_xml = self.xml_utils.generate_xml_text(root)
        return availability_xml
