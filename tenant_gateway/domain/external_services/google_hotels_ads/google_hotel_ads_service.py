import logging
from datetime import timedelta

import sentry_sdk
from collections import defaultdict, namedtuple
from lxml import etree as et
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance, locate_instance
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import current_date, add

from tenant_gateway.application.services.dtos.inventory_dtos import RoomTypeInventoryDto
from tenant_gateway.application.services.tenant_settings import TenantSettings
from tenant_gateway.common.constants import (
    HotelStatus,
    ServiceCodes,
    INVENTORY_SYNC_DAYS, TREEBO_RATE_PLAN_CODES, CacheTimeouts,
)
from tenant_gateway.common.decorators import session_manager
from tenant_gateway.common.exceptions import (
    ExternalClientException, S3ClientException,
    NonActionableDownStreamSystemError,
    RoomTypeNotFoundException,
)
from tenant_gateway.common.utils import create_room_code_to_room_type_mapping_key
from tenant_gateway.domain.entities.hotel import HotelEntity
from tenant_gateway.domain.external_services.google_hotels_ads.adapters.gha_xml_adapter import (
    GHAXmlAdapter,
)
from tenant_gateway.domain.external_services.google_hotels_ads.strategies.hotel_rates_dto_date_finder import (
    HotelRatesDtoDateFinder
)
from tenant_gateway.domain.external_services.google_hotels_ads.strategies.rate_plan_rate_dto_date_finder import (
    RatePlanRateDtoDateFinder
)
from tenant_gateway.globals import worker_context
from tenant_gateway.infrastructure.database.extensions import cache
from tenant_gateway.infrastructure.database.repositories.hotel_repository import HotelRepository
from tenant_gateway.infrastructure.database.repositories.rate_sync_repository import RateSyncRepository
from tenant_gateway.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from tenant_gateway.domain.entities.inventory_sync_entity import InventorySyncEntity
from tenant_gateway.domain.entities.rate_sync_entity import RateSyncEntity
from tenant_gateway.domain.external_services.base_external_service import (
    BaseExternalService,
)
from tenant_gateway.domain.external_services.google_hotels_ads.constants import (
    TransactionActions,
    GHA_XML_FILE_NAME,
    NO_OF_DAYS_TO_PUSH_RATES,
    RoomAvailabilityStatus,
)
from tenant_gateway.domain.external_services.google_hotels_ads.dtos.availability_dto import (
    AvailabilityDTO,
)
from tenant_gateway.domain.external_services.google_hotels_ads.dtos.inventory_dto import (
    InventoryDto,
)
from tenant_gateway.domain.external_services.google_hotels_ads.dtos.property_transaction_dto import (
    PropertyTransactionDto,
)
from tenant_gateway.domain.external_services.google_hotels_ads.exceptions import (
    PropertyXMLConvertorException,
)
from tenant_gateway.domain.external_services.google_hotels_ads.utils import (
    XMLUtils,
)
from tenant_gateway.domain.models import ClientCallLogModel
from tenant_gateway.infrastructure.database.repositories.client_call_log_repository import (
    ClientCallLogRepository,
)
from tenant_gateway.infrastructure.external_clients.catalog_service.catalog_client import (
    CatalogServiceClient,
)
from tenant_gateway.infrastructure.external_clients.catalog_service.catalog_dtos import (
    CatalogHotelDto,
    RoomInformationDto,
)
from tenant_gateway.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from tenant_gateway.infrastructure.external_clients.crs_service_client import CrsClient
from tenant_gateway.infrastructure.external_clients.google_hotel_ads_client import (
    GoogleHotelAdsClient,
)
from tenant_gateway.infrastructure.external_clients.pricing_orchestrator.pricing_orchestrator_client import (
    POClient,
)
from tenant_gateway.infrastructure.external_clients.pricing_orchestrator.dtos.rate_dto import (
    RateDTO,
)
from tenant_gateway.infrastructure.external_clients.rate_manager_service.rate_manager_client import (
    RateManagerClient,
)
from tenant_gateway.infrastructure.external_clients.treebo_service.treebo_backend_client import (
    TreeboBackendServiceClient,
)

logger = logging.getLogger(__name__)


@register_instance()
class GoogleHotelAdsService(BaseExternalService):
    def __init__(self, service_code=ServiceCodes.GOOGLE_HOTEL_ADS.value, hotel_entity=None):
        super().__init__(service_code, hotel_entity)
        self.service_code = service_code
        self.treebo_backend_service_client = locate_instance(TreeboBackendServiceClient)
        self.gha_client = locate_instance(GoogleHotelAdsClient)
        self.xml_utils = XMLUtils()
        self.tenant_settings = locate_instance(TenantSettings)
        self.gha_xml_adapter = GHAXmlAdapter()
        self.catalog_client = locate_instance(CatalogServiceClient)
        self.po_client = locate_instance(POClient)
        self.client_call_log_repository = locate_instance(ClientCallLogRepository)
        self.crs_client = locate_instance(CrsClient)
        self.rate_manager_client = locate_instance(RateManagerClient)
        self.rate_sync_repository = locate_instance(RateSyncRepository)
        self.hotel_repository = locate_instance(HotelRepository)

    def _push_rate_plan_rates(self, rate_plan_rate_dtos, for_days=None):
        hotel_id = rate_plan_rate_dtos[0].hotel_id
        stop_sell_hotel_ids = self._get_stop_sell_hotel_ids_for_gha()
        if hotel_id in stop_sell_hotel_ids:
            msg = f"Ignoring Rate Plan Rate push since {hotel_id} is stop sell for GHA"
            logger.info(msg)
            raise NonActionableDownStreamSystemError(msg)
        cs_ids_to_web_ids_map = (
            self.treebo_backend_service_client.get_web_hotel_ids_from_cs_ids(
                [hotel_id]
            ).get("live")
        )
        if not cs_ids_to_web_ids_map:
            msg = f"Hotel with cs_id {hotel_id} not found in treebo skipping rate plan push"
            logger.info(msg)
            raise NonActionableDownStreamSystemError(msg)
        po_rate_dtos = []
        room_stay_wise_rates = defaultdict(list)
        room_type_ids = set(
            [rate_plan_rate.room_type_id for rate_plan_rate in rate_plan_rate_dtos]
        )

        room_sku_dtos = self.catalog_client.get_all_property_room_skus(
            hotel_id, room_type_ids
        )
        if not room_sku_dtos:
            msg = f"Ignoring Rate Plan Rate push since for hotel: {hotel_id} no room skus are found"
            logger.info(msg)
            raise NonActionableDownStreamSystemError(msg)
        no_of_days_to_push_rates = for_days or NO_OF_DAYS_TO_PUSH_RATES
        nearest_date, farthest_date = self.handle_nearest_farthest_dtos(
            RatePlanRateDtoDateFinder(), rate_plan_rate_dtos, no_of_days_to_push_rates
        )

        sku_ids = [room_sku_dto.sku_id for room_sku_dto in room_sku_dtos]
        date_batches = self._split_date_ranges_into_batches(nearest_date, farthest_date)
        for start_date, end_date in date_batches:
            prices = self.po_client.get_price(
                self.po_client.generate_pricing_orchestrator_request_body(
                    hotel_id, start_date, end_date, sku_ids
                )
            ).data
            po_rate_dtos.extend(
                RateDTO.create_from_po_pricing_data(hotel_id, prices, room_sku_dtos)
            )
        if not po_rate_dtos:
            msg = f"Ignoring Rate Plan Rate push for hotel: {hotel_id} since no prices are found in PO"
            logger.info(msg)
            raise NonActionableDownStreamSystemError(msg)

        po_stay_details_tuple = namedtuple(
            "po_stay_details_tuple", ["start_date", "end_date", "room_id"]
        )
        for rate in po_rate_dtos:
            room_stay_wise_rates[
                po_stay_details_tuple(rate.start_date, rate.end_date, rate.room_id)
            ].append(rate)

        self.generate_and_push_rate_notification_xml(cs_ids_to_web_ids_map[hotel_id], room_stay_wise_rates)

        return RateSyncEntity(
            service_code=self.service_code,
            success=True,
            data=dict(pushed_rates=[rate.to_json() for rate in po_rate_dtos]),
            internal_hotel_id=hotel_id,
            error=None,
            external_hotel_id=str(cs_ids_to_web_ids_map[hotel_id]),
        )

    @staticmethod
    def _split_date_ranges_into_batches(nearest_date, farthest_date):
        # PO allows a maximum of 40 days in a single request
        batches = []
        date_diff = (farthest_date - nearest_date).days + 1

        if date_diff > 40:
            start_date = nearest_date
            while start_date <= farthest_date:
                end_date = min(start_date + timedelta(days=39), farthest_date)
                batches.append((start_date, end_date))
                start_date = end_date + timedelta(days=1)
        else:
            batches.append((nearest_date, farthest_date))

        return batches

    def handle_property_message(self, property_dto):
        if property_dto.status == HotelStatus.CHURNED.value:
            room_type_inventory_dtos = self._get_room_type_inventories(property_dto.hotel_id)

            cs_ids = set(room_inventory_dto.hotel_id for room_inventory_dto in room_type_inventory_dtos)
            cs_ids_to_web_ids_map = (
                self.treebo_backend_service_client.get_web_hotel_ids_from_cs_ids(
                    list(cs_ids)
                ).get("churned")
            )
            if not (cs_ids_to_web_ids_map and cs_ids_to_web_ids_map.get(property_dto.hotel_id)):
                msg = f"Hotel with cs_id {property_dto.hotel_id} not found in treebo skipping property push"
                logger.info(msg)
                return
            self.generate_and_push_room_availability_xml(
                room_type_inventory_dtos=room_type_inventory_dtos,
                cs_ids_to_web_ids_map=cs_ids_to_web_ids_map,
                availability_status=RoomAvailabilityStatus.NOT_AVAILABLE.value,
            )
        else:
            stop_sell_hotel_ids = self._get_stop_sell_hotel_ids_for_gha()
            if property_dto.hotel_id in stop_sell_hotel_ids:
                msg = f"Ignoring Push Property for hotel: {property_dto.hotel_id} since it is stop sell for GHA"
                logger.info(msg)
                return
            self.generate_and_push_transaction_xml_for_properties(
                [property_dto], TransactionActions.OVERLAY.value
            )

    def handle_dnr_update(self, room_type_inventory_dtos, event_id):
        hotel_id = room_type_inventory_dtos[0].hotel_id
        if hotel_id in self._get_stop_sell_hotel_ids_for_gha():
            msg = f"Ignoring DNR update since {hotel_id} is stop sell for GHA"
            logger.info(msg)
            return

        cs_ids = [room_inventory.hotel_id for room_inventory in room_type_inventory_dtos]
        cs_ids_to_web_ids_map = (
            self.treebo_backend_service_client.get_web_hotel_ids_from_cs_ids(
                cs_ids
            ).get("live")
        )
        if not (cs_ids_to_web_ids_map and cs_ids_to_web_ids_map.get(hotel_id)):
            msg = f"Hotel with cs_id {hotel_id} not found in treebo skipping dnr push"
            logger.info(msg)
            raise NonActionableDownStreamSystemError(msg)

        self.generate_and_push_inventory_notification_xml(room_type_inventory_dtos, cs_ids_to_web_ids_map)
        return InventorySyncEntity(
            event_id=event_id,
            service_code=self.service_code,
            success=True,
            data=dict(
                crs_inventory_dtos=[d.__dict__ for d in room_type_inventory_dtos]
            ),
            internal_hotel_id=room_type_inventory_dtos[0].hotel_id,
        )

    def handle_inventory_update(self, room_type_inventory_dtos, event_id=None):
        hotel_id = room_type_inventory_dtos[0].hotel_id
        if hotel_id in self._get_stop_sell_hotel_ids_for_gha():
            msg = f"Ignoring Inventory message since {hotel_id} is stop sell for GHA"
            logger.info(msg)
            return

        cs_ids = [room_inventory_dto.hotel_id for room_inventory_dto in room_type_inventory_dtos]
        cs_ids_to_web_ids_map = (
            self.treebo_backend_service_client.get_web_hotel_ids_from_cs_ids(
                cs_ids
            ).get("live")
        )
        if not (cs_ids_to_web_ids_map and cs_ids_to_web_ids_map.get(hotel_id)):
            msg = f"Hotel with cs_id {hotel_id} not found in treebo skipping inventory push"
            logger.info(msg)
            raise NonActionableDownStreamSystemError(msg)
        # TODO: Need to see if sending availability with each inventory message is mandatory
        self.generate_and_push_room_availability_xml(
            room_type_inventory_dtos=room_type_inventory_dtos,
            cs_ids_to_web_ids_map=cs_ids_to_web_ids_map,
            availability_status=RoomAvailabilityStatus.AVAILABLE.value,
        )
        self.generate_and_push_inventory_notification_xml(
            room_type_inventory_dtos=room_type_inventory_dtos,
            cs_ids_to_web_ids_map=cs_ids_to_web_ids_map,
        )
        return InventorySyncEntity(
            event_id=event_id,
            service_code=self.service_code,
            success=True,
            data=dict(
                crs_inventory_dtos=[d.__dict__ for d in room_type_inventory_dtos]
            ),
            internal_hotel_id=room_type_inventory_dtos[0].hotel_id,
        )

    def generate_and_push_room_availability_xml(
            self, room_type_inventory_dtos, cs_ids_to_web_ids_map, availability_status
    ):
        hotel_id = room_type_inventory_dtos[0].hotel_id
        room_type_wise_inventories = defaultdict(list)
        room_codes = set()
        for room_inventory_dto in room_type_inventory_dtos:
            room_codes.add(room_inventory_dto.room_type)
            room_type_wise_inventories[room_inventory_dto.room_type].append(room_inventory_dto)
        try:
            room_code_to_room_type_mapping = self.get_room_code_to_room_type_mapping(room_codes)
            gha_availability_dtos = AvailabilityDTO.create_from_room_inventory_data(
                room_type_wise_inventories, hotel_id, availability_status, room_code_to_room_type_mapping
            )
            availability_xml = self.gha_xml_adapter.build_availability_xml(
                gha_availability_dtos, cs_ids_to_web_ids_map[hotel_id]
            )
            self.gha_client.push_availability_xml(availability_xml)
        except (ExternalClientException, Exception) as ex:
            logger.info(
                f"Push Availability failed for {hotel_id}, errors = {str(ex)}"
            )
            raise ex

    def generate_and_push_transaction_xml_for_properties(
            self, hotels: [CatalogHotelDto], action
    ):
        cs_ids = [hotel.hotel_id for hotel in hotels]
        cs_ids_to_web_ids_map = (
            self.treebo_backend_service_client.get_web_hotel_ids_from_cs_ids(
                cs_ids
            ).get("live")
        )
        if not cs_ids_to_web_ids_map:
            msg = f"Hotel not found in treebo skipping property push"
            logger.info(msg)
            return
        gha_hotel_dtos = PropertyTransactionDto.create_from_catalog_dto(
            hotels, cs_ids_to_web_ids_map, action
        )
        for hotel in gha_hotel_dtos:
            try:
                transaction_xml = hotel.to_xml()
                self.gha_client.push_property_xml(transaction_xml)
            except PropertyXMLConvertorException as ex:
                logger.info(ex.message)
                raise ex
            except (ExternalClientException, Exception) as ex:
                logger.info(
                    f"Push property failed for {hotel.cs_hotel_id}, errors = {str(ex)}"
                )
                raise ex
        return

    def generate_and_push_inventory_notification_xml(self, room_type_inventory_dtos, cs_ids_to_web_ids_map):
        room_codes = set([room_type_inventory.room_type for room_type_inventory in room_type_inventory_dtos])
        room_codes_to_room_type_mapping = self.get_room_code_to_room_type_mapping(room_codes)
        gha_inventory_dto = InventoryDto.create_from_crs_inventory_data(
            room_type_inventory_dtos, room_codes_to_room_type_mapping
        )
        for hotel_id, hotel_inventory in gha_inventory_dto.items():
            try:
                inventory_xml = self.gha_xml_adapter.build_inventory_xml(
                    hotel_inventory, cs_ids_to_web_ids_map[hotel_id]
                )
                self.gha_client.push_inventory_xml(inventory_xml)
            except (ExternalClientException, Exception) as ex:
                logger.info(
                    f"Push inventory failed for hotel_id {hotel_id} errors = {str(ex)}"
                )
                raise ex

    def generate_and_push_rate_notification_xml(self, hotel_id, room_stay_wise_rates):
        try:
            rate_xml = RateDTO.to_xml(room_stay_wise_rates, hotel_id)
            self.gha_client.push_rate_xml(rate_xml)
        except (ExternalClientException, Exception) as ex:
            logger.info(
                f"Push rate failed for {hotel_id}, errors = {str(ex)}"
            )
            raise ex

    def generate_and_upload_hotel_list_xml_to_s3(self):
        try:
            xml_data = self.generate_hotel_list_feed()
            self.xml_utils.generate_xml_file_for_gha_hotel_list(xml_data)
            AwsServiceClient.upload_file_to_s3(
                self._get_gha_s3_directory(), f"/tmp/{GHA_XML_FILE_NAME}"
            )
        except S3ClientException as ex:
            logger.info(ex.message)
            sentry_sdk.capture_exception(ex.message)
            raise ex
        except Exception as ex:
            logger.info(str(ex))
            sentry_sdk.capture_exception(ex)
            raise ex

    def download_gha_hotel_list_xml(self):
        try:
            AwsServiceClient.download_file_from_s3(
                f"{self._get_gha_s3_directory()}{GHA_XML_FILE_NAME}",
                f"/tmp/{GHA_XML_FILE_NAME}",
            )
            tree = et.parse(f"/tmp/{GHA_XML_FILE_NAME}")
            self._save_hotel_ids_in_client_call(tree)
            root = et.tostring(tree.getroot(), encoding="utf-8", method="xml")
            return root.decode("utf-8"), GHA_XML_FILE_NAME
        except S3ClientException as ex:
            logger.info(ex.message)
            sentry_sdk.capture_exception(ex.message)
            raise ex
        except Exception as ex:
            logger.info(str(ex))
            sentry_sdk.capture_exception(ex)
            raise ex

    def _get_gha_s3_directory(self):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        return f"{tenant_id}/{self.service_code}/files/"

    def generate_hotel_list_feed(self):
        stop_sell_hotel_ids = self._get_stop_sell_hotel_ids_for_gha()
        hotels = [
            hotel for hotel in self.catalog_client.get_hotels(status='LIVE')
            if hotel.hotel_id not in stop_sell_hotel_ids
        ]
        cs_ids = [hotel.hotel_id for hotel in hotels]
        cs_id_to_hotel_ids = (
            self.treebo_backend_service_client.get_web_hotel_ids_from_cs_ids(
                cs_ids
            ).get("live")
        )
        hotel_list_xml = self.gha_xml_adapter.build_hotels_list_xml(
            hotels, cs_id_to_hotel_ids
        )
        return hotel_list_xml

    def _save_hotel_ids_in_client_call(self, tree):
        root = tree.getroot()
        hotel_ids = [listing.find("id").text for listing in root.findall("listing")]
        model = ClientCallLogModel(
            client=ServiceCodes.GOOGLE_HOTEL_ADS.value,
            url='gha-download-list',
            call_type=BaseExternalClient.CallTypes.GET,
            request_data=str(hotel_ids),
            status_code=200,
        )
        self.client_call_log_repository.save(model)

    def _get_stop_sell_hotel_ids_for_gha(self):
        return self.tenant_settings.get_stop_sell_hotel_ids()

    @session_manager(commit=True)
    def trigger_gha_sync_for_property(self, hotel_id, for_days, cs_ids_to_web_ids_map):
        catalog_hotel_dtos = self.catalog_client.get_hotels([hotel_id])
        if not catalog_hotel_dtos:
            msg = f"Skipping GHA sync for hotel {hotel_id} since hotel couldn't be found in catalog"
            logger.info(msg)
            raise NonActionableDownStreamSystemError(msg)

        catalog_hotel_dto = catalog_hotel_dtos[0]
        room_type_inventory_dtos = self._get_room_type_inventories(hotel_id, for_days)
        rate_plan_rate_dtos = self._get_rate_plan_rates(hotel_id)

        self.handle_property_message(catalog_hotel_dto)
        self.handle_inventory_update(room_type_inventory_dtos)
        rate_sync_entity = self._push_rate_plan_rates(rate_plan_rate_dtos, for_days)
        self.rate_sync_repository.save(rate_sync_entity)

        try:
            self.hotel_repository.load(
                service_code=ServiceCodes.GOOGLE_HOTEL_ADS.value,
                internal_hotel_id=hotel_id,
                tenant_id=worker_context.get_tenant_id(),
            )
        except Exception as ex:
            logger.info(f"Exception Raised {str(ex)}")
            hotel_entity = HotelEntity(
                internal_hotel_id=hotel_id,
                external_hotel_id=str(cs_ids_to_web_ids_map[hotel_id]),
                tenant_id=worker_context.get_tenant_id(),
                service_code=ServiceCodes.GOOGLE_HOTEL_ADS.value
            )
            self.hotel_repository.save(hotel_entity)

    def _get_room_type_inventories(self, hotel_id, for_days=None):
        hotel_dnr_information = self.crs_client.get_dnr_information(
            hotel_id,
            current_date(),
            add(current_date(), days=for_days or INVENTORY_SYNC_DAYS),
        )
        room_type_inventories = self.crs_client.get_room_type_inventories(
            hotel_id,
            current_date(),
            add(current_date(), days=for_days or INVENTORY_SYNC_DAYS),
        )
        hotel_room_information = RoomInformationDto.create_from_catalog_data(
            self.catalog_client.get_rooms(hotel_id)
        )
        room_type_inventory_dtos = RoomTypeInventoryDto.create_from_crs_and_dnr_data(
            hotel_id,
            room_type_inventories=room_type_inventories,
            hotel_dnr_information=hotel_dnr_information,
            room_id_mapping=hotel_room_information.room_id_mapping,
        )
        return room_type_inventory_dtos

    def _get_rate_plan_rates(self, hotel_id, from_date=None, to_date=None, rate_plan_id=None, for_days=None):
        if not rate_plan_id:
            rate_plans = self.rate_manager_client.get_rate_plans(hotel_id)
            for rate_plan in rate_plans:
                if rate_plan.short_code == TREEBO_RATE_PLAN_CODES[0]:
                    rate_plan_id = rate_plan.rate_plan_id

        if not rate_plan_id:
            msg = f"Skipping GHA sync for hotel {hotel_id} since TRB-DEFAULT couldn't be found."
            logger.info(msg)
            raise NonActionableDownStreamSystemError(msg)

        rate_plan_dto = self.rate_manager_client.get_rate_plan(
            rate_plan_id
        )
        rate_plan_rate_dtos = self.rate_manager_client.get_rate_plan_rates(
            rate_plan_id,
            hotel_id,
            from_date or dateutils.date_to_ymd_str(current_date()),
            to_date or dateutils.date_to_ymd_str(
                dateutils.add(current_date(), days=for_days or INVENTORY_SYNC_DAYS)
            ),
            rate_plan_dto.room_type_occupancy_mappings.all_occupancy_codes,
            compressed=True,
        )
        if not rate_plan_rate_dtos:
            msg = f"Skipping GHA sync for hotel {hotel_id} since rate plan rates couldn't be found."
            logger.info(msg)
            raise NonActionableDownStreamSystemError(msg)

        return rate_plan_rate_dtos

    @session_manager(commit=True)
    def push_bulk_rate_plan_rates(self, hotel_ids, from_date, to_date, rate_plan_id):
        failed_hotels = []
        rate_sync_entities = []
        for_days = (dateutils.ymd_str_to_date(to_date) - dateutils.ymd_str_to_date(from_date)).days
        for hotel_id in hotel_ids:
            try:
                rate_plan_rate_dtos = self._get_rate_plan_rates(
                    hotel_id, from_date, to_date, rate_plan_id
                )
                rate_sync_entities.append(self._push_rate_plan_rates(rate_plan_rate_dtos, for_days))
            except Exception as ex:
                logger.info(f"Failed To Push Rates for {hotel_id} to GHA error: {str(ex)}")
                failed_hotels.append(f'{hotel_id} -Error-> {str(ex)}')
        self.rate_sync_repository.save_all(rate_sync_entities)
        return failed_hotels

    def get_room_code_to_room_type_mapping(self, room_codes: set[str]):
        cache_key = create_room_code_to_room_type_mapping_key()
        room_codes_to_room_type_mapping = cache.cache.get(cache_key)
        if room_codes_to_room_type_mapping and set(room_codes).issubset(set(room_codes_to_room_type_mapping.keys())):
            return room_codes_to_room_type_mapping
        room_codes_to_room_type_mapping = self.catalog_client.get_room_code_to_room_type_mapping()
        missing_room_codes_in_catalog = set(room_codes) - set(room_codes_to_room_type_mapping.keys())
        if missing_room_codes_in_catalog:
            logger.info(f'Room Type Not found for these room codes {list(missing_room_codes_in_catalog)}')
            raise RoomTypeNotFoundException(list(missing_room_codes_in_catalog))
        cache.cache.set(cache_key, room_codes_to_room_type_mapping, timeout=CacheTimeouts.THIRTY_DAYS.value)
        return room_codes_to_room_type_mapping

    @session_manager(True)
    def push_rackrates(self, hotel_id, rates):
        from_date, to_date = self.handle_nearest_farthest_dtos(HotelRatesDtoDateFinder(), rates)
        rate_plan_rate_dtos = self._get_rate_plan_rates(
            hotel_id, from_date, to_date
        )
        rate_sync_entity = self._push_rate_plan_rates(rate_plan_rate_dtos)
        self.rate_sync_repository.save(rate_sync_entity)
        return

    @staticmethod
    def handle_nearest_farthest_dtos(strategy, dto, no_of_days_to_push_rates=None):
        return strategy.find_nearest_and_farthest_dates(dto, no_of_days_to_push_rates)
