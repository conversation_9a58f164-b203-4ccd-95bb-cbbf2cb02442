import functools
import json

from flask import request
from treebo_commons.request_tracing.context import get_current_request_id

import object_registry
from tenant_gateway.common.decorators import session_manager
from tenant_gateway.domain.models import ClientCallLogModel
from tenant_gateway.infrastructure.database.repositories.client_call_log_repository import (
    ClientCallLogRepository,
)


def log_emma_api_request_response():
    def decorator(func):
        @functools.wraps(func)
        @session_manager(commit=True)
        def wrapper(*args, **kwargs):
            response = None
            try:
                response = func(*args, **kwargs)
            finally:
                model = ClientCallLogModel(
                    client="Emma",
                    url=request.url,
                    headers=json.dumps(dict(request.headers)),
                    call_type=request.method.lower(),
                    request_data=request.data.decode(),
                    response_data=response.data.decode() if response else None,
                    status_code=response.status_code if response else 0,
                    request_id=get_current_request_id(),
                )
                object_registry.locate_instance(ClientCallLogRepository).save(model)

            return response
        return wrapper

    return decorator
