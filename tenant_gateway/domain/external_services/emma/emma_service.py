import functools
import json
import logging
import re
from collections import defaultdict
from copy import deepcopy
from datetime import date
from decimal import Decimal

import sentry_sdk
from flask import current_app as app, render_template
from lxml import etree as ET
from pydantic import ValidationError
from ths_common.constants.billing_constants import (
    PaymentTypes as THSCPaymentTypes,
    PaymentModes as THSCPaymentModes,
    PaymentStatus as THSCPaymentStatus,
    PaymentChannels as THSCPaymentChannels,
    PaymentReceiverTypes as THSCPaymentReceiverTypes,
    ChargeStatus,
)
from ths_common.constants.booking_constants import BookingActions, BookingStatus as THSCBookingStatus
from ths_common.constants.catalog_constants import SkuCategory
from ths_common.exceptions import ResourceNotFound as ThsResourceNotFound
from ths_common.utils.common_utils import gstin_regex
from thsc.crs.entities.billing import Bill, Payment
from thsc.crs.entities.booking import Booking as THSCBooking
from thsc.crs.exceptions import CRSAPIException

from tenant_gateway.domain.external_services.emma.adaptors.user_profile_service_adaptor import EmmaUserProfileServiceAdaptor
from tenant_gateway.domain.external_services.emma.dtos.financial_transaction_dto import FinancialTransactionDTO
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import register_instance, locate_instance
from tenant_gateway.application.services.dtos.rate_manager_dto import RatePlanPackageInclusionDTO
from tenant_gateway.application.services.dtos.taxable_item import TaxableItem
from tenant_gateway.application.services.tax_service import TaxService
from tenant_gateway.application.services.tenant_settings import TenantSettings
from tenant_gateway.common.constants import (ActionType as SyncActionType, ServiceCodes, TENANT_CONFIG_SEGMENTS,
                                             Gender)
from tenant_gateway.common.decorators import session_manager
from tenant_gateway.common.exceptions import (
    InvalidMappingException,
    InvalidRequestError,
    ResourceNotFound,
    TenantConfigException,
)
from tenant_gateway.common.slack_alert_helper import SlackAlert
from tenant_gateway.common.utils import (
    create_cancelled_booking_key,
    create_room_type_configs_key,
    create_emma_config_cache_key,
    get_pan_from_gstin,
    is_valid_gstin,
)
from tenant_gateway.domain.entities.booking_sync_entity import BookingSyncEntity
from tenant_gateway.domain.entities.hotel import HotelEntity, MappingType
from tenant_gateway.domain.entities.hotel_sku_mapping_entity import (
    HotelSkuMappingEntity,
)
from tenant_gateway.domain.entities.inventory_sync_entity import InventorySyncEntity
from tenant_gateway.domain.entities.rate_plan_mapping import RatePlanMappingEntity
from tenant_gateway.domain.entities.room_type_mapping import RoomTypeMappingEntity
from tenant_gateway.domain.external_services.base_external_service import BaseExternalService
from tenant_gateway.domain.external_services.emma.adaptors.expense_adaptor import EmmaExpenseAdaptor
from tenant_gateway.domain.external_services.emma.adaptors.booking_adaptor import (
    EmmaBookingAdapter as BookingAdaptor,
)
from tenant_gateway.domain.external_services.emma.adaptors.inventory_adaptor import InventoryAdaptor
from tenant_gateway.domain.external_services.emma.adaptors.property_sku_adaptor import PropertySkuAdaptor
from tenant_gateway.domain.external_services.emma.adaptors.rate_plan_adaptor import (
    RatePlanAdaptor,
)
from tenant_gateway.domain.external_services.emma.adaptors.source_code_config_adaptor import (
    SourceCodeConfigAdaptor,
)
from tenant_gateway.domain.external_services.emma.constants import (
    EventType,
    ActionType,
    BookingStatus,
    CACHE_TIMEOUT,
    EMMA_USER_TYPE,
    MessageRequestType,
    COMMISSION_CODE_CONFIG,
    MARKET_CODE_CONFIG,
    MARKET_SEGMENT,
    EMMA_PUBLISHER_ROUTING_KEY,
    EMMA_RESERVATION_ACTION_TYPE,
    EMMA_PACKAGE_CATEGORY,
    ProfileType,
    PseudoRoomType,
    EMMA_ERROR_NOTIFICATION_SUBJECT_MAPPING,
    PROFILE_ERROR_TEMPLATE,
    NotificationSender,
    PACKAGE_ERROR_TEMPLATE,
    RATE_PLAN_ERROR_TEMPLATE,
    RESTRICTION_ERROR_TEMPLATE,
    RESERVATION_ERROR_TEMPLATE,
    COMMENTS_PREFIX,
)
from tenant_gateway.domain.external_services.emma.dtos.api_response import (
    ReservationAPIResponseDTO,
    EmmaAPIResponseDTO,
    RoomTypeConfigurationResponseDTO,
    RoomClassConfigurationResponseDTO,
    EmmaAPIErrorResponseDTO,
    MarketCodeResponseDTO,
    EmmaResultDTO,
    ProfilesAPIResponseDTO,
    InventoryAPIResponseDTO, TransactionCodeResponseDTO, CommissionCodeResponseDTO,
)
from tenant_gateway.domain.external_services.emma.dtos.address_dto import AddressDTO
from tenant_gateway.domain.external_services.emma.dtos.booking_dto import BookingDTO
from tenant_gateway.domain.external_services.emma.dtos.commission_code_config_dto import CommissionCodeConfigDto
from tenant_gateway.domain.external_services.emma.dtos.market_code_config_dto import MarketCodeConfigDto
from tenant_gateway.domain.external_services.emma.dtos.message_request_dto import MessageRequestDto
from tenant_gateway.domain.external_services.emma.dtos.package_dto import PackageDTO
from tenant_gateway.domain.external_services.emma.dtos.profile_dto import ProfileDTO
from tenant_gateway.domain.external_services.emma.dtos.rate_dto import RateDTO
from tenant_gateway.domain.external_services.emma.dtos.reservation_list_response_dto import ReservationListResponseDTO
from tenant_gateway.domain.external_services.emma.dtos.restriction_dto import RestrictionDTO
from tenant_gateway.domain.external_services.emma.dtos.room_dto import RoomDTO
from tenant_gateway.domain.external_services.emma.dtos.services_dto import ServiceDTO
from tenant_gateway.domain.external_services.emma.dtos.transaction_code_config_dto import TransactionCodeConfigDto
from tenant_gateway.infrastructure.external_clients.catalog_service.catalog_dtos import (
    RoomDetailDto,
    RoomInformationDto,
)
from tenant_gateway.infrastructure.external_clients.cleartax_client import ClearTaxServiceClient
from tenant_gateway.infrastructure.external_clients.company_profile_service.response_dto import (
    SearchEntitiesResponseDTO,
)
from tenant_gateway.infrastructure.external_clients.crs_service_client import CrsClient
from tenant_gateway.domain.external_services.emma.dtos.source_code_config_dto import SourceCodeConfigDTO
from tenant_gateway.domain.external_services.emma.exceptions import (
    EmmaException,
    EmmaPackageNotMapped,
    EmmaInventoryUpdateFailedError,
    EmmaMethodNotAllowed,
    EmmaRoomTypeMappingNotFoundException,
)
from tenant_gateway.domain.external_services.emma.utils import (
    convert_validation_errors,
    generate_request_id,
    get_xml_root_tag,
)
from tenant_gateway.infrastructure.database.extensions import cache
from tenant_gateway.infrastructure.database.repositories.booking_sync_repository import (
    BookingSyncRepository,
)
from tenant_gateway.infrastructure.database.repositories.hotel_repository import (
    HotelRepository,
)
from tenant_gateway.infrastructure.database.repositories.hotel_sku_mapping_repository import (
    HotelSkuMappingRepository,
)
from tenant_gateway.infrastructure.external_clients.catalog_service.catalog_client import (
    CatalogServiceClient,
)
from tenant_gateway.infrastructure.external_clients.company_profile_service.company_profile_client import (
    CompanyProfileServiceClient,
)
from tenant_gateway.infrastructure.external_clients.company_profile_service.company_profile_constants import (
    CompanyProfileStatus
)
from tenant_gateway.infrastructure.external_clients.emma.emma_client import EmmaClient
from tenant_gateway.infrastructure.external_clients.finance_service.finance_service_client import (
    FinanceServiceClient,
)
from tenant_gateway.infrastructure.external_clients.notification_client import NotificationServiceClient
from tenant_gateway.infrastructure.external_clients.rate_manager_service.rate_manager_client import (
    RateManagerClient,
)
from tenant_gateway.infrastructure.external_clients.rate_manager_service.rate_manager_dtos import (
    RoomTypeOccupancyMapping,
    RoomTypeOccupancyMappings
)
from tenant_gateway.infrastructure.external_clients.user_profile_service.constants import UserContactType
from tenant_gateway.infrastructure.external_clients.user_profile_service.user_profile_client import UserProfileServiceClient
from tenant_gateway.infrastructure.external_clients.user_profile_service.user_profile_dtos import UserContact
from tenant_gateway.infrastructure.publishers.emma_event_publisher import EmmaEventPublisher
from tenant_gateway.infrastructure.publishers.secondary_emma_event_publisher import SecondaryEmmaEventPublisher

logger = logging.getLogger(__name__)


def audit_reservation_change(action):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, hotel_entity, booking_dto, *args, **kwargs):
            booking_sync_entity = self.booking_sync_repository.load(
                external_hotel_id=hotel_entity.external_hotel_id,
                external_room_booking_id=booking_dto.booking_id,
            )
            _booking, room_stay_id = func(
                self,
                hotel_entity,
                booking_dto,
                *args,
                **kwargs,
                booking_sync_entity=booking_sync_entity,
            )
            if booking_sync_entity:
                booking_sync_entity.success = True
                booking_sync_entity.action_type = action
                booking_sync_entity.internal_booking_id = _booking.booking_id
                booking_sync_entity.data = dict(emma_request_id=booking_dto.request_id)
                self.booking_sync_repository.update(booking_sync_entity)
            return _booking, room_stay_id
        return wrapper
    return decorator


# TODO: Find a better resolution of how to initialize external services and use of service selector.
# TODO: Then fix use of booking_adaptor below and other dependencies
@register_instance()
class EmmaService(BaseExternalService):
    def __init__(
        self,
        service_code=ServiceCodes.EMMA.value,
        hotel_entity: HotelEntity = None,
    ):
        super().__init__(service_code, hotel_entity)
        self.hotel_repository = locate_instance(HotelRepository)
        self.tenant_settings = locate_instance(TenantSettings)
        self.booking_adaptor = locate_instance(BookingAdaptor)
        self.rate_plan_adaptor = locate_instance(RatePlanAdaptor)
        self.catalog_service_client = locate_instance(CatalogServiceClient)
        self.rate_manager_client = locate_instance(RateManagerClient)
        self.hotel_sku_mapping_repository = locate_instance(HotelSkuMappingRepository)
        self.emma_client = locate_instance(EmmaClient)
        self.company_profiles_client = locate_instance(CompanyProfileServiceClient)
        self.booking_sync_repository = locate_instance(BookingSyncRepository)
        self.crs_client = locate_instance(CrsClient)
        self.emma_event_publisher = locate_instance(EmmaEventPublisher)
        self.emma_secondary_event_publisher = locate_instance(SecondaryEmmaEventPublisher)
        self.property_sku_adaptor = locate_instance(PropertySkuAdaptor)
        self.cleartax_client = locate_instance(ClearTaxServiceClient)
        self.tax_service = locate_instance(TaxService)
        self.finance_service_client = locate_instance(FinanceServiceClient)
        self.user_profile_service_client = locate_instance(UserProfileServiceClient)
        self.notification_service_client = locate_instance(NotificationServiceClient)

    def _get_room_wise_inclusion_amount(self, hotel_entity, booking_dto: BookingDTO):
        rate_plans = self.rate_manager_client.get_rate_plans(hotel_entity.internal_hotel_id)
        short_code_to_rate_plan_mapping = {prp.short_code: prp for prp in rate_plans}
        logger.info(short_code_to_rate_plan_mapping)
        room_stay_to_inclusion_map = {}
        commission_details = None
        for room_price in booking_dto.room.rate_plans:
            logger.info(room_price)
            if room_stay_to_inclusion_map.get(room_price):
                continue
            rate_plan = short_code_to_rate_plan_mapping.get(room_price.rate_plan_code)
            logger.info(room_price.rate_plan_code)
            rate_plan_id = rate_plan.rate_plan_id
            rate_plan_details = self.rate_manager_client.get_rate_plan(rate_plan_id)
            commission_details = rate_plan_details.commission_details
            package = self.rate_manager_client.get_package_by_package_id(rate_plan_details.package_id)
            sku_ids = [inclusion.sku_id for inclusion in package.inclusions]
            inclusion_base_rates = self.rate_manager_client.get_inclusion_base_rates(
                package_id=rate_plan_details.package_id, hotel_id=hotel_entity.internal_hotel_id,
                start_date=room_price.applicable_date, end_date=room_price.applicable_date, sku_ids=sku_ids
            ) if sku_ids else []
            room_stay_to_inclusion_map[room_price] = RatePlanPackageInclusionDTO(package, inclusion_base_rates,
                                                                                 rate_plan_id)

        return room_stay_to_inclusion_map, commission_details

    @staticmethod
    def _identify_and_set_tenant(service_code):
        service_code_to_tenant = app.config["SERVICE_CODE_TO_TENANT"]
        tenant_id = service_code_to_tenant[service_code]
        request_context.tenant_id = tenant_id
        return tenant_id

    def process_emma_request(self, xml_data):
        tenant_id = self._identify_and_set_tenant(self.service_code)
        request_id = self._get_xml_request_id(xml_data)
        request_context.request_id = request_id

        try:
            root = ET.fromstring(xml_data)
            event_type = EventType(get_xml_root_tag(root))
        except ET.XMLSyntaxError as e:
            return self.create_error_response(
                request_id=request_id,
                root=None,
                message=e.msg,
                status_code="400",
            )
        except ValueError:
            return self.create_error_response(
                request_id=request_id,
                root=None,
                message=f"Unsupported message {get_xml_root_tag(root)} is sent.",
                status_code="400",
            )

        if event_type in (EventType.RATE_HEADER, EventType.PACKAGE):
            self.emma_secondary_event_publisher.publish(
                tenant_id=tenant_id,
                routing_key=EMMA_PUBLISHER_ROUTING_KEY,
                event=xml_data,
            )
        else:
            self.emma_event_publisher.publish(
                tenant_id=tenant_id,
                routing_key=EMMA_PUBLISHER_ROUTING_KEY,
                event=xml_data,
            )
        return EmmaAPIResponseDTO(
            request_id=request_id,
            hotel_id=None,
            message=None,
            status_code="202"
        )

    @session_manager(commit=True)
    def process_event(self, tenant_id, xml_data):
        request_id = self._get_xml_request_id(xml_data)
        action_type = self._get_xml_action_type(xml_data)
        root = ET.fromstring(xml_data)
        event_type = get_xml_root_tag(root)

        def handle_event_and_notify_on_failure(handler_func, dto):
            try:
                return handler_func(tenant_id, request_id, dto)
            except Exception as exc:
                self._send_failure_mail(tenant_id, action_type, request_id, dto)
                raise exc

        try:
            if event_type == EventType.RATE_HEADER.value:
                rate_dto = RateDTO.from_xml(root)
                return handle_event_and_notify_on_failure(self.handle_rate_event, rate_dto)
            elif event_type == EventType.RESERVATION.value:
                booking_dto = BookingDTO.from_xml(request_id, root)
                return handle_event_and_notify_on_failure(self.handle_reservation_event, booking_dto)
            elif event_type == EventType.PROFILE.value:
                profile_dto = ProfileDTO.from_xml(root)
                return handle_event_and_notify_on_failure(self.handle_profile_event, profile_dto)
            elif event_type == EventType.PACKAGE.value:
                package_dto = PackageDTO.from_xml(root)
                return handle_event_and_notify_on_failure(self.handle_package_event, package_dto)
            elif event_type == EventType.RESTRICTION.value:
                restriction_dto = RestrictionDTO.from_xml(root)
                return handle_event_and_notify_on_failure(self.handle_restriction_event, restriction_dto)
            elif event_type == EventType.CONFIGURATION.value:
                return self.handle_config_event(request_id, root, tenant_id)
            elif event_type == EventType.MESSAGE_REQUEST.value:
                return self.handle_sync_requests(request_id, root, tenant_id)
            elif event_type == EventType.RESULT.value:
                return self.handle_result_message(request_id, root)
            elif event_type == EventType.FINANCIAL_TRANSACTION.value:
                return self.handle_financial_transaction_message(tenant_id, request_id, root)
        except (InvalidRequestError, EmmaPackageNotMapped) as e:
            return self.create_error_response(
                request_id=request_id,
                root=root,
                message=e.message,
            )
        except EmmaMethodNotAllowed as e:
            return self.create_error_response(
                request_id=request_id,
                root=root,
                message=e.message,
                status_code="405",
            )
        except ThsResourceNotFound:
            return self.create_error_response(
                request_id=request_id,
                root=root,
                message="Invalid hotelCode submitted in request to Hotel Superhero.",
                status_code="400",
            )
        except ValidationError as e:
            return self.create_error_response(
                request_id=request_id,
                root=root,
                message=convert_validation_errors(e),
                status_code="400",
            )
        except CRSAPIException as e:
            sentry_sdk.capture_exception(e)
            try:
                message = e.errors[0]['developer_message'] or e.message
            except:
                message = e.message
            return self.create_error_response(
                request_id=request_id,
                root=root,
                message=message,
                status_code=str(e.http_status_code),
            )
        except Exception as e:
            sentry_sdk.capture_exception(e)
            return self.create_error_response(
                request_id=request_id,
                root=root,
                message=f"Unable to process {get_xml_root_tag(root)} due to server error in Hotel Superhero.",
                status_code="500",
            )

        logger.error(f"Unable to handle label {get_xml_root_tag(root)}.")

    @staticmethod
    def create_error_response(request_id, root, message, status_code="400"):
        if root is None:
            hotel_id = None
        else:
            try:
                hotel_id = (
                    root.find('HotelReference', namespaces=root.nsmap)
                        .find('hotelCode', namespaces=root.nsmap)
                        .text
                ) or (root.findtext("hotelCode", namespaces=root.nsmap))
            except:
                try:
                    hotel_id = root.findtext("hotelCode", namespaces=root.nsmap)
                except:
                    hotel_id = "UNKNOWN"

        return EmmaAPIErrorResponseDTO(
            request_id,
            hotel_id,
            message=message,
            status_code=status_code,
        )

    def handle_profile_event(self, tenant_id, request_id, profile_dto):
        hotel_entity = self.hotel_repository.load(
            tenant_id=tenant_id, external_hotel_id=profile_dto.hotel_id
        )
        if profile_dto.profile_type == ProfileType.GUEST.value:
            ups_enabled = self.tenant_settings.is_ups_enabled()
            if ups_enabled:
                self._sanitize_gender(profile_dto)
                if profile_dto.privacy_options:
                    profile_dto.privacy_options.convert_to_boolean()
                self._handle_guest_profile_update_in_ups(profile_dto, hotel_entity)
            return ProfilesAPIResponseDTO(
                request_id,
                hotel_entity.external_hotel_id,
                superhero_company_code=None,
                message="Successfully processed profile in Hotel Superhero."
            )
        profile_dto.rate_plans = [
            rate_plan for rate_plan in profile_dto.rate_plans
            if self._get_rate_plan_id(hotel_entity, rate_plan.rate_plan_id)
        ] if profile_dto.rate_plans else None

        profiles_payload = profile_dto.convert_to_company_profiles_payload(hotel_id=hotel_entity.internal_hotel_id)
        profile_certification_id = profile_dto.get_certification_id()
        if profile_dto.name_code:
            preexisting_profile = self.company_profiles_client.search_entities(
                client_internal_code=profile_dto.name_code,
                hotel_id=hotel_entity.internal_hotel_id,
                source=hotel_entity.internal_hotel_id,
            )
            if profile_certification_id:
                self._override_profile_details_from_cleartax(
                    profiles_payload,
                    profile_certification_id,
                    preexisting_profile,
                )

        elif profile_certification_id:
            preexisting_profile = self.company_profiles_client.search_entities(
                gstins=profile_certification_id,
                hotel_id=hotel_entity.internal_hotel_id,
                source=hotel_entity.internal_hotel_id,
            )
            self._override_profile_details_from_cleartax(
                profiles_payload,
                profile_certification_id,
                preexisting_profile,
            )
            if preexisting_profile:
                preexisting_profile = self.get_latest_company_profile(preexisting_profile)
        else:
            company_profiles = self.company_profiles_client.search_entities(
                trade_name=profile_dto.profile_name,
                is_fuzzy_search=False,
                hotel_id=hotel_entity.internal_hotel_id,
                source=hotel_entity.internal_hotel_id,
            )
            preexisting_profile = self.get_company_profile_from_name(
                company_profiles.sub_entities,
                profile_dto.profile_name,
            )

        if preexisting_profile:
            superhero_company_code = self.handle_update_profile_event(
                profiles_payload,
                preexisting_profile,
            )
        else:
            superhero_company_code = self.handle_create_profile_event(
                profiles_payload,
                profile_dto,
            )

        return ProfilesAPIResponseDTO(
            request_id,
            hotel_entity.external_hotel_id,
            superhero_company_code,
            message="Successfully created profile in Hotel Superhero."
            if not preexisting_profile else "Successfully updated profile in Hotel Superhero."
        )

    def handle_update_profile_event(self, profiles_payload, preexisting_profile: SearchEntitiesResponseDTO):
        parent_entity_id = preexisting_profile.sub_entities[0]['parent_entity_id']
        sub_entity_id = preexisting_profile.sub_entities[0]['sub_entity_id']
        parent_profile = self.company_profiles_client.fetch_parent_company_profile(
            parent_entity_id
        )
        self.update_parent_profile(parent_profile, profiles_payload)
        response = self.company_profiles_client.edit_company_profile_sub_entity(
            parent_entity_id,
            sub_entity_id,
            profile_data=dict(data=profiles_payload),
            custom_headers={'X-Application': ServiceCodes.EMMA.value},
        )
        return response['superhero_company_code']

    def update_parent_profile(self, parent_profile, sub_entity_profile):
        sub_entity_gstin = None
        parent_entity_pan_number = None
        for statutory_detail in (parent_profile['statutory_details'] or []):
            if statutory_detail['field_name'] in ['pan_number']:
                parent_entity_pan_number = statutory_detail['value']

        for statutory_detail in (sub_entity_profile['statutory_details'] or []):
            if statutory_detail['field_name'] in ['gst']:
                sub_entity_gstin = statutory_detail['value']

        if not sub_entity_gstin or parent_entity_pan_number or not is_valid_gstin(sub_entity_gstin):
            return
        statutory_detail = parent_profile['statutory_details'] or []
        statutory_detail.append(
            {'field_name': 'pan_number', 'value': get_pan_from_gstin(sub_entity_gstin)}
        )
        parent_profile['statutory_details'] = statutory_detail
        self.company_profiles_client.edit_company_profile_parent_entity(
            parent_profile['parent_entity_id'],
            profile_data=dict(data=parent_profile),
            custom_headers={'X-Application': ServiceCodes.EMMA.value},
        )

    def handle_create_profile_event(self, profiles_payload, profile_dto: ProfileDTO):
        parent_entity_id = None
        profile_certification_id = profile_dto.get_certification_id()
        if is_valid_gstin(profile_certification_id):
            company_profiles = self.company_profiles_client.get_parent_company_profiles(
                pan_number=get_pan_from_gstin(profile_certification_id),
                hotel_id=profiles_payload.get('hotel_id'),
            )
            if company_profiles is not None and company_profiles.parent_entities:
                parent_entity_id = company_profiles.parent_entities[0].get('parent_entity_id')

        elif (
                not profile_certification_id
                or not parent_entity_id
                or is_valid_gstin(profile_certification_id)
        ):
            company_profiles = self.company_profiles_client.get_parent_company_profiles(
                trade_name=profile_dto.profile_name,
                is_fuzzy_search=False,
                hotel_id=profiles_payload.get('hotel_id'),
            )
            if company_profiles.parent_entities:
                parent_entity_id = company_profiles.parent_entities[0]['parent_entity_id']

        if not parent_entity_id:
            parent_entity_payload = self._update_parent_entity_statutory_details(profiles_payload)
            parent_entity_payload['client_internal_code'] = None
            parent_entity_payload['hotel_id'] = profiles_payload.get('hotel_id')
            response = self.company_profiles_client.create_company_profile_parent_entity(
                profile_data=dict(data=parent_entity_payload),
                custom_headers={'X-Application': ServiceCodes.EMMA.value},
            )
            parent_entity_id = response.company_profile_id
        response = self.company_profiles_client.create_company_profile_sub_entity(
            parent_entity_id,
            profile_data=dict(data=[profiles_payload]),
            custom_headers={'X-Application': ServiceCodes.EMMA.value},
        )
        return response[0]['superhero_company_code']

    def _get_hotel_room_type_occupancy_mappings(self, tenant_id, hotel_entity):
        cache_key = create_room_type_configs_key(tenant_id, hotel_entity.internal_hotel_id)
        if cache.cache.has(cache_key):
            room_type_configs = cache.cache.get(cache_key)
        else:
            room_type_configs = self.catalog_service_client.get_room_type_configurations(
                hotel_entity.internal_hotel_id
            )
            cache.cache.set(
                cache_key, room_type_configs, timeout=CACHE_TIMEOUT
            )
        room_type_config_map = room_type_configs.room_type_config_map
        room_type_occupancy_mapping = []
        for room_type in hotel_entity.room_types:
            config = room_type_config_map.get(room_type.internal_code)
            if not config:
                logger.error(
                    f"Room type {room_type.internal_code} not found in room type config map for hotel"
                    f" {hotel_entity.internal_hotel_id}"
                )
                continue
            for adult_count in range(1, config.max_adult + 1):
                room_type_occupancy_mapping.append(
                    {
                        "room_type_id": room_type.internal_code,
                        "adult_count": adult_count,
                    }
                )
        return room_type_occupancy_mapping

    def handle_reservation_event(self, tenant_id, request_id, booking_dto):
        thsc_booking, crs_room_stay_id = None, None
        guest_profiles = []
        ups_enabled = self.tenant_settings.is_ups_enabled()
        for profile_dto in booking_dto.profiles:
            if profile_dto.certification_id and bool(gstin_regex.match(profile_dto.certification_id)):
                cleartax_data = self.cleartax_client.get_corporate_detail(profile_dto.certification_id)
                cleartax_address = AddressDTO.create_from_cleartax_data(cleartax_data)
                profile_dto.update_address(cleartax_address)
            if profile_dto.profile_type == ProfileType.GUEST.value:
                if ups_enabled:
                    self._sanitize_gender(profile_dto)
                    if profile_dto.privacy_options:
                        profile_dto.privacy_options.convert_to_boolean()
                    guest_profiles.append(profile_dto)

        booking_dto.corporate = BookingDTO.get_corporate(booking_dto.profiles)
        booking_dto.travel_agent = BookingDTO.get_travel_agent(booking_dto.profiles)

        external_hotel_id = booking_dto.hotel_id
        hotel_entity = self.hotel_repository.load(
            tenant_id=tenant_id, external_hotel_id=external_hotel_id
        )
        channel_details = self.tenant_settings.get_channel_details(hotel_entity.internal_hotel_id)
        source_code_to_channel_mapping = {detail['source_code']: detail for detail in channel_details}
        if self.booking_adaptor is None:  # TODO: Fix initialization of BookingAdaptor dependency
            self.booking_adaptor = locate_instance(BookingAdaptor)
        if booking_dto.booking_action == ActionType.ADD.value:
            guest_user_profiles = self._get_user_profiles_from_ups(hotel_entity, guest_profiles)
            thsc_booking, crs_room_stay_id = self._create_booking_in_crs(
                hotel_entity, booking_dto, source_code_to_channel_mapping, guest_user_profiles
            )
        elif booking_dto.booking_action == ActionType.EDIT.value:
            guest_user_profiles = self._get_user_profiles_from_ups(hotel_entity, guest_profiles)
            thsc_booking, crs_room_stay_id = self._update_booking_in_crs(
                hotel_entity, booking_dto, source_code_to_channel_mapping, guest_user_profiles=guest_user_profiles
            )
        elif booking_dto.booking_action == ActionType.CANCEL.value:
            thsc_booking, crs_room_stay_id = self._cancel_booking_in_crs(
                hotel_entity, booking_dto, source_code_to_channel_mapping
            )
        elif booking_dto.booking_action == ActionType.REINSTATE.value:
            thsc_booking, crs_room_stay_id = self._reverse_cancel_booking_in_crs(
                hotel_entity, booking_dto, source_code_to_channel_mapping
            )
        if booking_dto.services and booking_dto.booking_action in [
            ActionType.ADD.value, ActionType.EDIT.value, ActionType.REINSTATE.value
        ]:
            self.post_expenses(hotel_entity, booking_dto, thsc_booking, tenant_id, crs_room_stay_id)
        return ReservationAPIResponseDTO(
            request_id,
            booking_dto,
        )

    def _create_booking_in_crs(self, hotel_entity, booking_dto: BookingDTO, source_code_to_channel_mapping,
                               guest_user_profiles=None):
        booking_sync_entity = self.booking_sync_repository.load(
            external_hotel_id=hotel_entity.external_hotel_id,
            external_room_booking_id=booking_dto.booking_id,
        )
        if booking_sync_entity:
            raise InvalidRequestError(
                f"Booking already exists in Hotel Superhero, Ignoring this booking creation request"
            )
        cache_key = create_cancelled_booking_key(hotel_entity.tenant_id, hotel_entity.internal_hotel_id,
                                                 booking_dto.booking_id)
        if cache.cache.has(cache_key):
            raise InvalidRequestError(
                f"Booking cancellation request already received in Hotel Superhero, "
                f"Ignoring this booking creation request"
            )

        room_stay_date_wise_inclusion, commission_details = self._get_room_wise_inclusion_amount(
            hotel_entity,
            booking_dto,
        )
        primary_group_booking = self.booking_sync_repository.load_first_group_booking(
            service_code=self.service_code,
            external_booking_id=booking_dto.booking_group_id,
            external_hotel_id=hotel_entity.external_hotel_id,
        )
        segments_config = self.tenant_settings.get_segments_config(hotel_id=hotel_entity.internal_hotel_id)
        crs_room_stay_id = 1
        market_segments = next((config['values'] for config in segments_config if config['name'] == MARKET_SEGMENT), [])

        if not primary_group_booking:
            self.send_alert_if_market_segment_not_present(market_segments, booking_dto.room.market_segment_code)
            thsc_booking: THSCBooking = self.booking_adaptor.to_thsc_entity(
                hotel_entity, booking_dto, room_stay_date_wise_inclusion=room_stay_date_wise_inclusion,
                source_code_to_channel_mapping=source_code_to_channel_mapping, commission_details=commission_details,
                guest_user_profiles=guest_user_profiles, segments_config=segments_config
            )
            thsc_booking.reference_number = booking_dto.booking_group_id
            thsc_booking = thsc_booking.create()
        else:
            thsc_booking: THSCBooking = THSCBooking.get(primary_group_booking.internal_booking_id)
            room = thsc_booking.add_room(
                self.booking_adaptor.build_thsc_room_stay(
                    hotel_entity,
                    booking_dto,
                    room_stay_date_wise_inclusion,
                    commission_details,
                    guest_user_profiles,
                )
            )
            thsc_booking.extra_information = primary_group_booking.extra_information
            old_guest_visible_remarks = thsc_booking.extra_information and thsc_booking.extra_information.get(
                "guest_visible_remarks")
            self._update_guarantee_information(booking_dto, thsc_booking)
            updated_comments = self._update_booking_comments(thsc_booking.comments, room)
            updated_guest_visible_remarks = self._update_booking_comments(
                old_guest_visible_remarks, room, is_guest_visible=True
            )
            if updated_comments:
                thsc_booking.comments = updated_comments
            if updated_guest_visible_remarks:
                thsc_booking.extra_information.update(guest_visible_remarks=updated_guest_visible_remarks)
            if updated_comments or updated_guest_visible_remarks:
                thsc_booking = thsc_booking.update()

            crs_room_stay_id = room.room_stay_id

        booking_sync_entity = BookingSyncEntity(
            service_code=self.service_code,
            internal_hotel_id=hotel_entity.internal_hotel_id,
            external_hotel_id=hotel_entity.external_hotel_id,
            internal_booking_id=thsc_booking.booking_id,
            external_booking_id=booking_dto.booking_group_id,
            external_room_booking_id=booking_dto.booking_id,
            success=True,
            action_type=SyncActionType.CREATE,
            last_booking_modification=None,
            created_at=booking_dto.original_booking_date,
            extra_information=thsc_booking.extra_information,
            data=dict(emma_request_id=booking_dto.request_id),
            acked=True,
            status=BookingStatus.RESERVED.value,
        )
        booking_sync_entity.set_crs_room_stay_id(crs_room_stay_id)
        booking_dto.superhero_booking_id = booking_sync_entity.internal_room_booking_id
        self.booking_sync_repository.save(booking_sync_entity)
        return thsc_booking, crs_room_stay_id

    @audit_reservation_change(action=SyncActionType.MODIFY)
    def _update_booking_in_crs(
        self,
        hotel_entity,
        booking_dto: BookingDTO,
        source_code_to_channel_mapping,
        booking_sync_entity=None,
        guest_user_profiles=None
    ):
        if not booking_sync_entity:
            return self._create_booking_in_crs(hotel_entity, booking_dto, source_code_to_channel_mapping,
                                               guest_user_profiles)
        cache_key = create_cancelled_booking_key(hotel_entity.tenant_id, hotel_entity.internal_hotel_id,
                                                 booking_dto.booking_id)
        if cache.cache.has(cache_key):
            raise InvalidRequestError(
                f"Booking cancellation request already received in Hotel Superhero, "
                f"Ignoring this booking updation request"
            )
        room_stay_date_wise_inclusion, commission_details = self._get_room_wise_inclusion_amount(
            hotel_entity,
            booking_dto,
        )

        thsc_booking: THSCBooking = THSCBooking.get(booking_sync_entity.internal_booking_id)
        thsc_room = self.booking_adaptor.build_thsc_room_stay(
            hotel_entity,
            booking_dto,
            room_stay_date_wise_inclusion,
            commission_details,
        )
        thsc_room.room_stay_id = booking_sync_entity.crs_room_stay_id
        thsc_booking.replace_room(thsc_room)
        self._update_guarantee_information(booking_dto, thsc_booking)
        new_comments = thsc_room.extra_information and thsc_room.extra_information.get("comments")
        old_comments = booking_sync_entity.extra_information and booking_sync_entity.extra_information.get("comments")
        if new_comments != old_comments:
            thsc_booking.comments = self._update_booking_comments(thsc_booking.comments, thsc_room)

        new_guest_visible_remarks = thsc_room.extra_information and thsc_room.extra_information.get(
            "guest_visible_remarks")
        old_guest_visible_remarks = booking_sync_entity.extra_information and booking_sync_entity.extra_information.get(
            "guest_visible_remarks")
        if new_guest_visible_remarks != old_guest_visible_remarks:
            thsc_booking.extra_information.update(
                guest_visible_remarks=self._update_booking_comments(
                    thsc_booking.comments, thsc_room, is_guest_visible=True
                )
            )
        if new_comments != old_comments or new_guest_visible_remarks != old_guest_visible_remarks:
            thsc_booking.update()

        if not booking_dto.superhero_booking_id:
            booking_dto.superhero_booking_id = booking_sync_entity.internal_room_booking_id
        return thsc_booking, booking_sync_entity.crs_room_stay_id

    @audit_reservation_change(SyncActionType.CANCEL)
    def _cancel_booking_in_crs(
        self,
        hotel_entity,
        booking_dto,
        source_code_to_channel_mapping,
        booking_sync_entity=None,
    ):
        cache_key = create_cancelled_booking_key(hotel_entity.tenant_id, hotel_entity.internal_hotel_id,
                                                 booking_dto.booking_id)
        if not booking_sync_entity:
            thsc_booking, _ = self._create_booking_in_crs(hotel_entity, booking_dto, source_code_to_channel_mapping)
        else:
            thsc_booking: THSCBooking = THSCBooking.get(booking_sync_entity.internal_booking_id)
            if thsc_booking.is_cancelled():
                logger.info('Booking is already cancelled')
                return thsc_booking, booking_sync_entity.crs_room_stay_id

        not_cancelled_rooms = list()
        current_room_stay_status = BookingStatus.RESERVED
        for room in thsc_booking.rooms:
            if booking_sync_entity and room.room_stay_id == booking_sync_entity.crs_room_stay_id:
                current_room_stay_status = room.status
            if room.status != THSCBookingStatus.CANCELLED:
                not_cancelled_rooms.append(room)

        if current_room_stay_status == THSCBookingStatus.CANCELLED:
            cache.cache.set(cache_key, str(dateutils.current_datetime(dateutils.local_timezone())), timeout=259200)
            logger.info('Booking is already cancelled')
            return thsc_booking, booking_sync_entity.crs_room_stay_id if booking_sync_entity else None
        if len(not_cancelled_rooms) == 1:
            # As room requested for cancellation is last room, booking cancel process is followed
            thsc_booking.cancel("No reason")
            bill = Bill.get(thsc_booking.bill_id)
            if not self.tenant_settings.has_refund_rule(hotel_entity.internal_hotel_id):
                self._refund_payment_for_bill(bill)
        else:
            # booking_sync_entity.crs_room_stay_id can be used directly
            # as if it is None code will go into above if condition anyway
            thsc_booking.remove_rooms([booking_sync_entity.crs_room_stay_id])
        booking_sync_entity.status = BookingStatus.CANCELED.value
        cache.cache.set(cache_key, str(dateutils.current_datetime(dateutils.local_timezone())), timeout=259200)
        return thsc_booking, booking_sync_entity.crs_room_stay_id

    @audit_reservation_change(action=SyncActionType.REVERSE_CANCEL)
    def _reverse_cancel_booking_in_crs(
        self,
        hotel_entity,
        booking_dto: BookingDTO,
        source_code_to_channel_mapping,
        booking_sync_entity=None,
    ):
        if not booking_sync_entity:
            return self._create_booking_in_crs(hotel_entity, booking_dto, source_code_to_channel_mapping)

        thsc_booking: THSCBooking = THSCBooking.get(booking_sync_entity.internal_booking_id)
        if booking_sync_entity.status != BookingStatus.CANCELED.value:
            return thsc_booking, booking_sync_entity.crs_room_stay_id

        room_stay_date_wise_inclusion, commission_details = self._get_room_wise_inclusion_amount(
            hotel_entity,
            booking_dto,
        )
        rooms_to_cancel = list()
        if thsc_booking.is_cancelled():
            booking_actions, resource_version = thsc_booking.get_booking_actions()
            cancel_action = [action for action in booking_actions if action.action_type == BookingActions.CANCEL][0]
            thsc_booking.reverse_booking_action(cancel_action.action_id)
            # Refresh Booking Version
            thsc_booking: THSCBooking = THSCBooking.get(booking_sync_entity.internal_booking_id)
            for room in thsc_booking.rooms:
                if room.status != THSCBookingStatus.CANCELLED:
                    rooms_to_cancel.append(room)

        # CRS can't handle reverse cancellation of individual room stay, so we need to create a new room stay
        new_room = thsc_booking.add_room(
            self.booking_adaptor.build_thsc_room_stay(
                hotel_entity,
                booking_dto,
                room_stay_date_wise_inclusion,
                commission_details,
            )
        )
        if rooms_to_cancel:
            # Refresh Booking Version
            thsc_booking: THSCBooking = THSCBooking.get(booking_sync_entity.internal_booking_id)
            thsc_booking.remove_rooms([room.room_stay_id for room in rooms_to_cancel])
        self._delete_cancellation_cache_key(hotel_entity, booking_dto)
        booking_sync_entity.set_crs_room_stay_id(new_room.room_stay_id)
        booking_sync_entity.status = BookingStatus.RESERVED.value
        return thsc_booking, new_room.room_stay_id

    def post_expenses(self, hotel_entity, booking: BookingDTO, thsc_booking: THSCBooking, tenant_id, room_stay_id):
        """
        Post expenses for a booking to the CRS.

        Args:
            booking (BookingDTO): The booking DTO containing expense information.
            thsc_booking (THSCBooking): The THSCBooking object to which expenses will be applied.
            hotel_entity: The hotel entity.
            tenant_id: The ID of the tenant.
            room_stay_id: The room_id for which the expenses should be added
        Returns:
            None
        """
        try:
            room_stay = next(
                (room_stay for room_stay in thsc_booking.rooms if room_stay.room_stay_id == room_stay_id),
                None
            )
            if not room_stay:
                return
            thsc_bill = Bill.get(thsc_booking.bill_id)
            billed_entities = self.crs_client.get_billed_entities(thsc_booking.bill_id, thsc_booking.hotel_id,
                                                                  EMMA_USER_TYPE)
            billing_entity = next(
                (billed_entity
                    for billed_entity in billed_entities
                    if (
                        billed_entity['category'] == thsc_booking.default_billed_entity_category and
                        billed_entity['status'] == "active"
                    )
                 ),
                None
            )
            emma_packages = [service.inventory_code for service in booking.services]
            inclusions = self._check_for_inclusions(emma_packages, hotel_entity.internal_hotel_id, tenant_id,
                                                    hotel_entity.external_hotel_id)
            sku_mappings = {inclusion.sku_name: inclusion for inclusion in inclusions}
            replace_expenses = any(service.action == ActionType.CHANGE.value for service in booking.services)
            if replace_expenses:
                self.cancel_old_expenses(thsc_bill, room_stay)
            expenses = EmmaExpenseAdaptor().to_expenses(hotel_entity, thsc_booking, booking, billing_entity, room_stay,
                                                        sku_mappings)
            if expenses:
                self.crs_client.add_booking_expenses(thsc_booking.hotel_id, thsc_booking.booking_id, expenses,
                                                     EMMA_USER_TYPE)
        except EmmaPackageNotMapped as e:
            sentry_sdk.capture_exception(e)
        except CRSAPIException as e:
            sentry_sdk.capture_exception(e)
        except Exception as e:
            sentry_sdk.capture_exception(e)
        return

    @staticmethod
    def cancel_old_expenses(bill, room_stay):
        charge_id_to_charges = {charge.charge_id: charge for charge in bill.charges}
        non_stay_charges = ServiceDTO.extract_non_stay_charges(room_stay, charge_id_to_charges)
        if not non_stay_charges:
            return
        for charge in non_stay_charges:
            charge.status = ChargeStatus.CANCELLED.value
        bill.update_charges(charges=non_stay_charges)

    @staticmethod
    def _delete_cancellation_cache_key(hotel_entity, booking_dto: BookingDTO):
        cache_key = create_cancelled_booking_key(
            hotel_entity.tenant_id,
            hotel_entity.internal_hotel_id,
            booking_dto.booking_id,
        )
        if cache.cache.has(cache_key):
            cache.cache.delete(cache_key)

    def _refund_payment_for_bill(self, bill: Bill):
        net_payable_amount = bill.net_payable * -1
        if net_payable_amount <= 0:
            return

        if self._is_payment_mode_airpay(bill):
            paid_by = THSCPaymentReceiverTypes.TREEBO
            payment_mode = THSCPaymentModes.AIR_PAY
        else:
            paid_by = THSCPaymentReceiverTypes.TREEBO
            payment_mode = THSCPaymentModes.PAID_AT_OTA

        payment = Payment.create_instance(
            amount=net_payable_amount,
            date_of_payment=dateutils.current_datetime(dateutils.local_timezone()),
            paid_to=THSCPaymentReceiverTypes.GUEST,
            paid_by=paid_by,
            payment_channel=THSCPaymentChannels.ONLINE,
            payment_type=THSCPaymentTypes.REFUND,
            payment_mode=payment_mode,
            status=THSCPaymentStatus.DONE,
            amount_in_payment_currency=net_payable_amount
        )
        bill.add_payment(payment)

    def _is_payment_mode_airpay(self, bill: Bill):
        for payment in bill.get_payments():
            if payment.payment_mode == THSCPaymentModes.AIR_PAY.value:
                return True
        return False

    def handle_financial_transaction_message(self, tenant_id, request_id, root):
        financial_transaction_dto = FinancialTransactionDTO.from_xml(root)
        # hotel_entity = self.hotel_repository.load(
        #     tenant_id=tenant_id,
        #     external_hotel_id=financial_transaction_dto.hotel_id,
        # )
        return EmmaAPIResponseDTO(
            request_id,
            financial_transaction_dto.hotel_id,
            message="Successfully updated financial transaction in Hotel Superhero."
        )

    def handle_rate_event(self, tenant_id, request_id, rate_dto):
        external_hotel_id = rate_dto.hotel_code
        hotel_entity = self.hotel_repository.load(
            service_code=self.service_code, tenant_id=tenant_id, external_hotel_id=external_hotel_id
        )
        room_type_occupancy_mapping = self._get_hotel_room_type_occupancy_mappings(
            hotel_entity.tenant_id, hotel_entity
        )
        room_type_max_occupancy_codes = self._get_room_type_max_occupancy_codes(room_type_occupancy_mapping)
        room_type_max_occupancy_mapping = self._get_room_type_max_occupancy_mapping(room_type_max_occupancy_codes)

        try:
            commission_percent = next(
                (
                    Decimal(commission.amount)
                    for commission in
                    self._get_eligible_commission_codes(hotel_id=hotel_entity.internal_hotel_id).values
                    if commission.item_code and rate_dto.commission_code
                       and commission.item_code.lower() == rate_dto.commission_code.lower()
                ),
                Decimal(0)
            )
        except Exception as ex:
            logger.info(f'Exception occurred while fetching commission_percent error={str(ex)}')
            commission_percent = Decimal(0)
        try:
            rate_plan_rates_for_multiple_occupancy = RatePlanAdaptor.to_rate_plan_rates(
                hotel_entity, rate_dto, room_type_max_occupancy_mapping)
        except EmmaRoomTypeMappingNotFoundException as e:
            raise InvalidRequestError(e.message)
        except InvalidMappingException:
            raise InvalidRequestError("Invalid room type received in the request")
        rate_plan_id = self._get_rate_plan_id(hotel_entity, rate_dto.rate_code)
        market_segment = None
        if rate_dto.market_code:
            market_segment = self._get_eligible_market_codes(
                hotel_entity=hotel_entity,
                market_code=rate_dto.market_code,
            )

        try:
            hotel_sku_mappings = self._check_for_inclusions(
                rate_dto.inclusions, hotel_entity.internal_hotel_id, tenant_id, external_hotel_id
            )
        except EmmaPackageNotMapped as e:
            raise InvalidRequestError(e.message)
        emma_controlled_channel_names = self.tenant_settings.get_emma_controlled_channels(
            hotel_entity.internal_hotel_id
        )
        emma_controlled_channel_names = [channel.lower() for channel in emma_controlled_channel_names]
        channel_details = self.tenant_settings.get_channel_details(hotel_entity.internal_hotel_id)
        emma_controlled_channels = [
            channel_detail
            for channel_detail in channel_details
            if channel_detail['channel'] and channel_detail['channel'].lower() in emma_controlled_channel_names
        ]
        if not rate_plan_id:
            mapped_sku_codes = [sku.sku_id for sku in hotel_sku_mappings]
            skus = []
            if mapped_sku_codes:
                skus = self.catalog_service_client.get_property_skus(property_id=hotel_entity.internal_hotel_id,
                                                                     sku_codes=','.join(mapped_sku_codes))
            package_id = self.create_package_from_inclusions(hotel_entity.internal_hotel_id, skus, rate_dto.rate_code)
            rate_plan = self.rate_plan_adaptor.to_rate_plan(
                rate_dto, hotel_entity.internal_hotel_id, room_type_occupancy_mapping, ActionType.ADD,
                commission_percent, emma_controlled_channels, package_id, market_segment,
            )
            # We are skipping restriction for now
            rate_plan['restrictions'] = {}
            rate_plan_id = self.rate_manager_client.create_rate_plan(rate_plan, user_type=EMMA_USER_TYPE)
            hotel_entity.add_rate_plan_mappings(
                [RatePlanMappingEntity(
                    internal_code=rate_plan_id,
                    external_code=rate_dto.rate_code,
                    service_code=self.service_code,
                    is_active=True,
                )]
            )
            self.hotel_repository.update(hotel_entity)
        else:
            rate_plan = self.rate_plan_adaptor.to_rate_plan(
                rate_dto, hotel_entity.internal_hotel_id, room_type_occupancy_mapping, ActionType.EDIT,
                commission_percent, emma_controlled_channels, market_segment=market_segment,
            )
            # We are skipping restriction for now
            rate_plan['restrictions'] = {}
            self.rate_manager_client.update_rate_plan(rate_plan_id, rate_plan, user_type=EMMA_USER_TYPE)

        if not rate_dto.base_rate_code:
            self.rate_manager_client.update_rate_plan_rates_for_multiple_occupancy(
                rate_plan_id,
                rate_plan_rates_for_multiple_occupancy,
                user_type=EMMA_USER_TYPE,
            )
        else:
            base_rate_plan_id = self._get_rate_plan_id(hotel_entity, rate_dto.base_rate_code)
            if base_rate_plan_id is None:
                return EmmaAPIErrorResponseDTO(
                    request_id,
                    external_hotel_id,
                    message=f"Parent Rate Plan {rate_dto.base_rate_code} doesn't exist.",
                    status_code="400",
                )
            linkage_rates = RatePlanAdaptor.to_linkage_rates(
                hotel_entity,
                base_rate_plan_id,
                room_type_occupancy_mapping,
                rate_dto,
            )
            base_rate_plan = self.rate_manager_client.get_rate_plan(base_rate_plan_id)
            self._normalize_linkage_dates_wrt_rate_plan_sell_dates(linkage_rates, base_rate_plan)
            for linkage_rate in linkage_rates:
                self.rate_manager_client.update_rate_plan_rates(
                    rate_plan_id,
                    linkage_rate,
                    user_type=EMMA_USER_TYPE,
                )
            if rate_dto.has_non_floating_rate_details:
                self.rate_manager_client.update_rate_plan_rates_for_multiple_occupancy(
                    rate_plan_id,
                    rate_plan_rates_for_multiple_occupancy,
                    user_type=EMMA_USER_TYPE,
                )
        return EmmaAPIResponseDTO(
            request_id,
            hotel_entity.external_hotel_id,
            message="Successfully updated rates in Hotel Superhero."
        )

    @staticmethod
    def _get_room_type_max_occupancy_mapping(room_type_max_occupancy_codes):
        room_type_max_occupancy_mapping = {}
        for room_type_max_occupancy_code in room_type_max_occupancy_codes:
            room_type, max_occupancy = room_type_max_occupancy_code.split('-')
            room_type_max_occupancy_mapping[room_type] = int(max_occupancy)
        return room_type_max_occupancy_mapping

    def _get_room_type_max_occupancy_codes(self, room_type_occupancy_mappings_data):
        room_type_occupancy_mappings = RoomTypeOccupancyMappings(
            [
                RoomTypeOccupancyMapping(
                    room_type_id=room_type_occupancy_mapping.get("room_type_id"),
                    adult_count=room_type_occupancy_mapping.get("adult_count"),
                )
                for room_type_occupancy_mapping in room_type_occupancy_mappings_data
            ]
        )
        return room_type_occupancy_mappings.all_max_occupancy_codes

    def handle_restriction_event(self, tenant_id, request_id, restriction_dto):
        external_hotel_id = restriction_dto.hotel_id
        hotel_entity = self.hotel_repository.load(
            service_code=self.service_code, tenant_id=tenant_id, external_hotel_id=external_hotel_id
        )

        rate_plan_ids = list()
        for rate_code in self.rate_plan_adaptor.to_rate_codes_from_restrictions(restriction_dto):
            rate_plan_id = self._get_rate_plan_id(hotel_entity, rate_code)
            if rate_plan_id is None:
                continue
            rate_plan_ids.append(rate_plan_id)

        rate_plan_id_to_rate_plan = dict()
        rate_code_to_rate_plan = dict()
        rate_plans = self.rate_manager_client.get_rate_plans_v2(rate_plan_ids=",".join(rate_plan_ids))
        for rate_plan in rate_plans:
            rate_code_to_rate_plan[rate_plan.short_code] = rate_plan
            rate_plan_id_to_rate_plan[rate_plan.rate_plan_id] = rate_plan

        for range_detail in restriction_dto.range_details:
            for restriction_detail in range_detail.restriction_details:
                if restriction_detail.rate_code not in rate_code_to_rate_plan:
                    continue
                rate_plan = rate_code_to_rate_plan[restriction_detail.rate_code]
                if not rate_plan:
                    continue
                if not rate_plan.extra_information:
                    rate_plan.extra_information = defaultdict(list)
                else:
                    rate_plan.extra_information = defaultdict(list, rate_plan.extra_information)
                rate_plan.extra_information["restriction_details"].append(json.dumps(restriction_detail.__dict__))

        for rate_plan_id, rate_plan in self.rate_plan_adaptor.to_rate_plans_from_restrictions(
            restriction_dto, hotel_entity.internal_hotel_id, rate_code_to_rate_plan
        ):
            # Skipping logical implementation of restrictions for now, just adding restriction details to
            # extra information of the rate plan and not making any changes to the existing restrictions
            rate_plan.pop('restrictions', None)
            rate_plan['extra_information'] = rate_code_to_rate_plan[
                rate_plan_id_to_rate_plan[rate_plan_id].short_code
            ].extra_information
            self.rate_manager_client.update_rate_plan(
                rate_plan_id,
                rate_plan,
                user_type=EMMA_USER_TYPE,
            )

        return EmmaAPIResponseDTO(
            request_id,
            hotel_entity.external_hotel_id,
            message="Successfully updated restrictions in Hotel Superhero."
        )

    def handle_package_event(self, tenant_id, request_id, package_dto):
        external_hotel_id = package_dto.hotel_code
        if package_dto.package_action == ActionType.DELETE.value:
            self._send_failure_mail(tenant_id, package_dto.package_action, request_id, package_dto)
            return EmmaAPIResponseDTO(
                request_id,
                external_hotel_id,
                message="Successfully deleted packages in Hotel Superhero."
            )
        hotel_entity = self.hotel_repository.load(
            service_code=self.service_code, tenant_id=tenant_id, external_hotel_id=external_hotel_id
        )
        sku_data = self.rate_plan_adaptor.to_inclusions(package_dto)
        try:
            hotel_sku_mapping = self.hotel_sku_mapping_repository.load(
                internal_hotel_id=hotel_entity.internal_hotel_id,
                sku_name=package_dto.package_code,
                tenant_id=tenant_id,
            )
            update_property_sku_data = self.property_sku_adaptor.to_property_sku(sku_data, package_dto.sell_separate)
            self.catalog_service_client.update_sku(
                property_id=hotel_entity.internal_hotel_id,
                sku_code=hotel_sku_mapping.sku_id,
                sku_data=update_property_sku_data
            )
        except ResourceNotFound:
            sku = self.catalog_service_client.create_sku(
                property_id=hotel_entity.internal_hotel_id,
                sku_data=sku_data,
                user_type=EMMA_USER_TYPE,
            )
            sku_id = sku['data']['code']
            hotel_sku_mapping = HotelSkuMappingEntity(
                tenant_id=tenant_id,
                internal_hotel_id=hotel_entity.internal_hotel_id,
                external_hotel_id=external_hotel_id,
                sku_id=sku_id,
                sku_name=package_dto.package_code,
                is_active=package_dto.package_action != 'DELETE',
            )
            self.hotel_sku_mapping_repository.save(hotel_sku_mapping)
        if package_dto.tax_included and package_dto.package_details:
            self._update_package_with_pretax_prices(
                hotel_entity.internal_hotel_id,
                package_dto.package_details,
                package_dto.currency_code,
            )
        self.update_inclusion_base_rate(
            hotel_entity.internal_hotel_id,
            hotel_sku_mapping.sku_id,
            package_dto.package_details,
        )
        return EmmaAPIResponseDTO(
            request_id,
            hotel_entity.external_hotel_id,
            message="Successfully updated packages in Hotel Superhero."
        )

    def _update_package_with_pretax_prices(self, hotel_id, package_details, currency=CurrencyType.INR):
        taxable_items = list()
        for package_detail in package_details:
            taxable_items.append(TaxableItem(
                sku_category_id=EMMA_PACKAGE_CATEGORY,
                applicable_date=dateutils.isoformat_str_to_datetime(package_detail.start_date).date(),
                posttax_amount=Money(package_detail.price, CurrencyType(currency)),
            ))
        taxable_items = self.tax_service.calculate_taxes(taxable_items=taxable_items, hotel_id=hotel_id)
        pretax_prices = dict()
        for taxable_item in taxable_items:
            pretax_prices[
                (taxable_item.applicable_date.isoformat(), taxable_item.posttax_amount.amount)
            ] = taxable_item.pretax_amount.amount
        for package_detail in package_details:
            package_detail.price = str(pretax_prices[(package_detail.start_date, Decimal(package_detail.price))])

    def update_inclusion_base_rate(self, hotel_id, sku_id, package_details):
        for package_detail in package_details:
            inclusion_base_rates = self.rate_plan_adaptor.to_inclusion_base_rates(
                hotel_id, [sku_id], package_detail
            )
            for inclusion_base_rate in inclusion_base_rates:
                self.rate_manager_client.update_inclusion_base_rate(
                    inclusion_base_rate, user_type=EMMA_USER_TYPE
                )

    def _check_for_inclusions(self, inclusions, hotel_id, tenant_id, external_hotel_id):
        # check for inclusion in hotel sku mapping table if not found raise exception
        try:
            hotel_sku_mappings = self.hotel_sku_mapping_repository.load_all(
                internal_hotel_id=hotel_id,
                tenant_id=tenant_id,
            )
        except ResourceNotFound:
            raise EmmaPackageNotMapped(
                f"Packages are not mapped for hotel {external_hotel_id}. Create package first for this hotel."
            )

        applicable_sku_mappings = [
            hsm for inclusion in inclusions
            if (hsm := next((h for h in hotel_sku_mappings if h.sku_name == inclusion), None))
        ]

        if len(applicable_sku_mappings) != len(inclusions):
            missing_inclusions = [inclusion for inclusion in inclusions
                                  if inclusion not in {h.sku_name for h in hotel_sku_mappings}]
            raise EmmaPackageNotMapped(
                "Packages {0} not mapped for hotel {1}".format(
                    missing_inclusions, external_hotel_id
                )
            )

        return applicable_sku_mappings

    def create_package_from_inclusions(self, hotel_id, skus, rate_code):
        package = RatePlanAdaptor.to_package(hotel_id, skus, rate_code)
        packages = self.rate_manager_client.get_all_packages(hotel_id)
        for p in packages:
            if p.package_name == package["package_name"]:
                return p.package_id
        package_id = self.rate_manager_client.create_package(package, user_type=EMMA_USER_TYPE)
        return package_id

    def handle_dnr_update(self, room_type_inventory_dtos, event_id):
        room_type_inventory_dto = room_type_inventory_dtos[0]
        external_room_type_code = None
        transaction_id = self._get_transaction_id(event_id)
        for room_type in self.hotel_entity.room_types:
            if room_type.internal_code == room_type_inventory_dto.room_type:
                external_room_type_code = room_type.external_code
                break

        if external_room_type_code in PseudoRoomType:
            return

        request_id = generate_request_id()
        from_date = min(inventory_dto.date for inventory_dto in room_type_inventory_dtos)
        to_date = max(inventory_dto.date for inventory_dto in room_type_inventory_dtos)
        dnrs = self.crs_client.get_dnr_information(
            self.hotel_entity.internal_hotel_id, from_date, to_date
        )
        rooms = self.catalog_service_client.get_rooms(self.hotel_entity.internal_hotel_id)
        room_detail_dtos = [
            RoomDetailDto.create_from_catalog_data(room) for room in rooms
        ]
        emma_inventory_payloads = InventoryAdaptor().to_emma_inventory_payloads(
            request_id,
            self.hotel_entity.external_hotel_id,
            external_room_type_code,
            transaction_id,
            room_type_inventory_dtos,
            dnrs,
            room_detail_dtos,
        )
        logger.info(
            f"Syncing inventory with Emma for hotel {self.hotel_entity.external_hotel_id} "
            f"and transaction id {transaction_id}"
        )
        inventory_sync_entity = InventorySyncEntity(
            external_hotel_id=self.hotel_entity.external_hotel_id,
            internal_hotel_id=self.hotel_entity.internal_hotel_id,
            service_code=self.service_code,
            event_id=event_id
        )
        try:
            for emma_inventory_payload in emma_inventory_payloads:
                self.emma_client.sync_inventories(request_id, emma_inventory_payload)
            inventory_sync_entity.data = {"transaction_id": transaction_id}
            inventory_sync_entity.success = True
        except (EmmaInventoryUpdateFailedError, Exception) as e:
            inventory_sync_entity.data = str(emma_inventory_payloads)
            inventory_sync_entity.success = False
            inventory_sync_entity.error = str(e)

        return inventory_sync_entity

    def handle_inventory_update(self, room_type_inventory_dtos, event_id):
        for inventory_dto in room_type_inventory_dtos:
            room_type = self.hotel_entity.get_mapping(mapping_type=MappingType.ROOM_TYPE,
                                                 internal_code=inventory_dto.room_type, _raise=False)
            if not room_type:
                continue
            if room_type.external_code not in PseudoRoomType:
                break
        else:
            return
        from_date = min(inventory_dto.date for inventory_dto in room_type_inventory_dtos)
        to_date = max(inventory_dto.date for inventory_dto in room_type_inventory_dtos)
        emma_inventory_dto = self.build_emma_inventory_dto(from_date, to_date)
        inventory_sync_entity = InventorySyncEntity(
            external_hotel_id=self.hotel_entity.external_hotel_id,
            internal_hotel_id=self.hotel_entity.internal_hotel_id,
            service_code=self.service_code,
            event_id=event_id
        )
        transaction_id = self._get_transaction_id(event_id)
        emma_inventory_snapshot_payload = emma_inventory_dto.to_xml()
        try:
            self.emma_client.sync_inventories(emma_inventory_dto.request_id, emma_inventory_snapshot_payload)
            inventory_sync_entity.data = {"transaction_id": transaction_id}
            inventory_sync_entity.success = True
        except (EmmaInventoryUpdateFailedError, Exception) as e:
            inventory_sync_entity.data = emma_inventory_snapshot_payload
            inventory_sync_entity.success = False
            inventory_sync_entity.error = str(e)

        return inventory_sync_entity

    def build_emma_inventory_dto(self, from_date, to_date, request_id=None):
        if request_id is None:
            request_id = generate_request_id()
        hotel_dnr_information = self.crs_client.get_dnr_information(
            self.hotel_entity.internal_hotel_id, from_date, to_date
        )
        room_type_inventories = self.crs_client.get_room_type_inventories(
            self.hotel_entity.internal_hotel_id,
            from_date,
            to_date)
        hotel_room_information = RoomInformationDto.create_from_catalog_data(
            self.catalog_service_client.get_rooms(self.hotel_entity.internal_hotel_id)
        )
        room_type_to_max_allowed_overbooking = self.tenant_settings.get_overbooking_count_for_emma(
            self.hotel_entity.internal_hotel_id)
        return InventoryAPIResponseDTO.from_inventory_event(
            request_id,
            self.hotel_entity,
            room_type_inventories,
            hotel_dnr_information,
            hotel_room_information,
            room_type_to_max_allowed_overbooking,
        )

    def handle_config_event(self, request_id, root, tenant_id):
        config_dto = SourceCodeConfigDTO.from_xml(root)
        external_hotel_id = config_dto.hotel_id
        hotel_entity = self.hotel_repository.load(
            tenant_id=tenant_id, external_hotel_id=external_hotel_id
        )
        hotel_configs = self.catalog_service_client.get_tenant_configs(hotel_entity.internal_hotel_id)
        hotel_config = {config.config_name: config for config in hotel_configs}
        tenant_config_dto = SourceCodeConfigAdaptor.to_tenant_config(config_dto, hotel_config)
        self.catalog_service_client.update_tenant_config(
            property_id=hotel_entity.internal_hotel_id,
            data=tenant_config_dto.to_representation(),
            user_type=EMMA_USER_TYPE,
        )
        return EmmaAPIResponseDTO(
            request_id,
            hotel_entity.external_hotel_id,
            message="Successfully updated source code configs in Hotel Superhero."
        )

    @staticmethod
    def _get_xml_request_id(xml_string):
        pattern = r'<\?Label\s(.*?)\?>'

        # Find the label using regex
        if type(xml_string) is bytes:
            xml_string = xml_string.decode()
        label_match = re.search(pattern, xml_string)

        # Extract the label if found
        if label_match:
            label = label_match.group(1)
            if len(label.split('|')) >= 2:
                return label.split('|')[-2]
        return generate_request_id()

    @staticmethod
    def _get_xml_action_type(xml_string):
        pattern = r'<\?Label\s(.*?)\?>'

        # Find the label using regex
        if type(xml_string) is bytes:
            xml_string = xml_string.decode()
        label_match = re.search(pattern, xml_string)

        # Extract the label if found
        if label_match:
            label = label_match.group(1)
            parts = label.split('|')
            if len(parts) >= 1:
                return label.split('|')[-1]

    def create_booking_from_crs_event(self, hotel_entity, booking_event, user_action):
        booking_action = ActionType.ADD.value
        self._process_crs_booking_event(hotel_entity, booking_event, booking_action, user_action)

    def update_booking_from_crs_event(self, hotel_entity, booking_event, room_update_event, user_action):
        updated_room_stay_ids = self._extract_affected_room_stay_ids(room_update_event)
        room_action_type = self._extract_room_action_type(room_update_event, user_action)
        booking_action = room_action_type or ActionType.EDIT.value
        self._process_crs_booking_event(
            hotel_entity, booking_event, booking_action, user_action, updated_room_stay_ids
        )

    def cancel_booking_from_crs_event(self, hotel_entity, booking_event, room_cancel_event, user_action):
        cancelled_room_stay_ids = self._extract_affected_room_stay_ids(room_cancel_event)
        room_action_type = self._extract_room_action_type(room_cancel_event, user_action)
        booking_action = room_action_type or ActionType.CANCEL.value
        self._process_crs_booking_event(
            hotel_entity, booking_event, booking_action, user_action, cancelled_room_stay_ids
        )

    def _extract_affected_room_stay_ids(self, room_event):
        if not room_event:
            return None
        return room_event.get('affected_resource_ids')

    def _extract_room_action_type(self, room_event, user_action):
        if room_event:
            room_action_type = room_event.get('user_action')
            if room_action_type == 'add':
                return ActionType.ADD.value
            elif room_action_type in ['update']:
                return ActionType.EDIT.value
            elif room_action_type == 'cancellation':
                return ActionType.CANCEL.value
        return {
            "create_new_booking": ActionType.ADD.value,
            "add_room_stay": ActionType.ADD.value,
            "noshow_partial_booking": ActionType.NOSHOW.value,
            "noshow": ActionType.NOSHOW.value,
            "checkin": ActionType.CHECKIN.value,
            "checkout": ActionType.CHECKOUT.value,
            "undo_checkout": ActionType.REINSTATE.value,
            "undo_noshow": ActionType.REINSTATE.value,
            "undo_cancellation": ActionType.REINSTATE.value,
            "cancel_partial_booking": ActionType.EDIT.value,
            "cancel_booking": ActionType.CANCEL.value,
            "cancel_soft_blocked_booking": ActionType.CANCEL.value,
        }.get(user_action)

    def _process_crs_booking_event(
            self, hotel_entity, booking_event, booking_action, user_action, room_stay_ids_to_process=None
    ):
        bill: Bill = Bill.get(booking_event['bill_id'])
        charge_id_to_charges = {charge.charge_id: charge for charge in bill.charges}
        room_id_to_room_number_mapping = self._fetch_room_number_mapping(
            property_id=hotel_entity.internal_hotel_id,
            booking_event=booking_event,
        )
        rate_plan_details = self.crs_client.get_booking_rate_plans(booking_event['booking_id'])
        rp_codes_to_rp_details = {
            rate_plan_detail['rate_plan_code']: rate_plan_detail for rate_plan_detail in rate_plan_details
        }
        booking_dtos_to_process = BookingDTO.generate_from_crs_event(
            hotel_entity,
            booking_event,
            booking_action,
            charge_id_to_charges,
            room_stay_ids_to_process,
            rp_codes_to_rp_details,
            bill.total_posttax_amount,
            room_id_to_room_number_mapping,
        )
        for booking_dto, room_stay in booking_dtos_to_process:
            try:
                booking_sync_entity, action_type = self._get_booking_sync_entity_for_crs_event(
                    hotel_entity,
                    booking_dto,
                    booking_event,
                    room_stay,
                    user_action,
                )
                if action_type is None:
                    continue
                booking_dto.superhero_booking_id = booking_sync_entity.internal_room_booking_id
                for guest in booking_dto.guests:
                    guest.superhero_booking_id = booking_sync_entity.internal_room_booking_id
                response = self.emma_client.send_booking_details(booking_dto.request_id, booking_dto.to_xml())
                logger.info(response)
            except Exception as e:
                sentry_sdk.capture_exception(e)
                logger.info(str(e))

    @session_manager(commit=True)
    def _get_booking_sync_entity_for_crs_event(self, hotel_entity, booking_dto, booking_event, room_stay, user_action):
        booking_sync_entity = self.booking_sync_repository.load(
            external_hotel_id=hotel_entity.external_hotel_id,
            internal_booking_id=booking_event["booking_id"],
            crs_room_stay_id=room_stay["room_stay_id"],
        )
        if booking_sync_entity is not None:
            action_type = self._get_booking_sync_action_type(user_action, booking_dto.booking_action)
            reservation_status = RoomDTO.get_status_from_thsc_room_status(room_stay["status"])
            # If EMMA decides to support partial checkin and checkout etc. we can send all event updates
            if action_type != SyncActionType.MODIFY and booking_sync_entity.status == reservation_status.value:
                return booking_sync_entity, None
            booking_sync_entity.action_type = action_type
            booking_sync_entity.status = reservation_status.value
            self.booking_sync_repository.update(booking_sync_entity)
            return booking_sync_entity, booking_sync_entity.action_type

        booking_sync_entity = BookingSyncEntity(
            service_code=self.service_code,
            internal_hotel_id=hotel_entity.internal_hotel_id,
            external_hotel_id=hotel_entity.external_hotel_id,
            internal_booking_id=booking_event["booking_id"],
            external_booking_id=booking_dto.booking_group_id,
            success=True,
            action_type=SyncActionType.CREATE,
            last_booking_modification=None,
            created_at=booking_dto.original_booking_date,
            extra_information=booking_event["extra_information"],
            data=dict(emma_request_id=booking_dto.request_id),
            acked=True,
            status=BookingStatus.RESERVED.value,
        )
        if booking_dto.booking_id:
            booking_sync_entity.external_room_booking_id = booking_dto.booking_id
        booking_sync_entity.set_crs_room_stay_id(room_stay["room_stay_id"])
        self.booking_sync_repository.save(booking_sync_entity)
        return booking_sync_entity, SyncActionType.CREATE

    @staticmethod
    def _get_booking_sync_action_type(user_action, booking_action):
        return {
            "create_new_booking": SyncActionType.CREATE,
            "add_room_stay": SyncActionType.CREATE,
            "noshow_partial_booking": SyncActionType.NO_SHOW,
            "noshow": SyncActionType.NO_SHOW,
            "checkin": SyncActionType.CHECKIN,
            "update_room_stay_room_type": SyncActionType.MODIFY,
            "undo_checkin": SyncActionType.UNDO_CHECKIN,
            "update_room_stay_dates": SyncActionType.MODIFY,
            "cancel_partial_booking": SyncActionType.MODIFY if booking_action == ActionType.EDIT.value else
            SyncActionType.CANCEL,
            "checkout": SyncActionType.CHECKOUT,
            "undo_checkout": SyncActionType.UNDO_CHECKOUT,
            "add_expenses": SyncActionType.MODIFY,
            "allocate_room": SyncActionType.MODIFY,
            "edit_booking": SyncActionType.MODIFY,
            "undo_noshow": SyncActionType.UNDO_NO_SHOW,
            "cancel_booking": SyncActionType.CANCEL,
            "undo_cancellation": SyncActionType.REVERSE_CANCEL,
            "confirm_booking": SyncActionType.MODIFY,
            "update_billing_instruction": SyncActionType.MODIFY,
            "add_guest_stay": SyncActionType.MODIFY,
            "update_room_stay_rate_plan": SyncActionType.MODIFY,
            "replace_booking": SyncActionType.MODIFY,
            "update_disallow_charge_addition": SyncActionType.MODIFY,
            "add_expense": SyncActionType.MODIFY,
            "update_customer_details": SyncActionType.MODIFY,
            "update_room_stay_prices_v2": SyncActionType.MODIFY,
            "cancel_soft_blocked_booking": SyncActionType.CANCEL,
        }.get(user_action, SyncActionType.MODIFY)

    def _get_charge_id_to_charges_dict_from_bill_id(self, bill_id):
        bill = Bill.get(bill_id)
        return {charge.charge_id: charge for charge in bill.charges}

    def _get_rate_plan_id(self, hotel_entity, external_code):
        try:
            hotel_rate_plan = hotel_entity.get_mapping(mapping_type=MappingType.RATE_PLAN, external_code=external_code)
        except InvalidMappingException:
            return None
        return hotel_rate_plan.internal_code

    def handle_result_message(self, request_id, root):
        try:
            result_dto = EmmaResultDTO.from_xml(request_id, root)
            if not result_dto.success:
                raise EmmaException(f"Received failed result message from Emma for {request_id}.")
            logger.info(result_dto.message)
            if result_dto.result_ids:
                result_id_dto = result_dto.result_ids[0]
                booking_sync_entity = self.booking_sync_repository.load(
                    external_hotel_id=result_dto.hotel_id,
                    internal_room_booking_id=result_id_dto.reservation_id,
                )
                booking_sync_entity.external_room_booking_id = result_id_dto.confirmation_id
                for reservation_reference in result_id_dto.reservation_references:
                    if reservation_reference.reference_type == "EMMAID":
                        booking_sync_entity.external_booking_id = reservation_reference.reference_number
                        break
                self.booking_sync_repository.update(booking_sync_entity)
        except EmmaException as e:
            sentry_sdk.capture_exception(e)
        except Exception as e:
            sentry_sdk.capture_exception(e)
            return self.create_error_response(
                request_id=request_id,
                root=root,
                message="Unable to handle result message due to server error in Hotel Superhero.",
                status_code="500",
            )

    @staticmethod
    def fetch_created_at(sub_entity):
        return dateutils.isoformat_str_to_datetime(sub_entity.get('created_at'))

    def get_latest_company_profile(self, company_profile):
        sub_entities = company_profile.sub_entities
        sub_entities.sort(key=self.fetch_created_at, reverse=True)
        for sub_entity in sub_entities:
            if sub_entity['status'] == CompanyProfileStatus.COMPLETE.value:
                return SearchEntitiesResponseDTO.from_json(dict(sub_entities=[sub_entity]))
        return SearchEntitiesResponseDTO.from_json(dict(sub_entities=[sub_entities[0]] if sub_entities else []))

    def handle_sync_requests(self, request_id, root, tenant_id):
        message_request_dto = MessageRequestDto.from_xml(root)
        external_hotel_id = message_request_dto.hotel_id
        hotel_entity = self.hotel_repository.load(
            tenant_id=tenant_id, external_hotel_id=external_hotel_id, only_active_mappings=False,
        )
        if message_request_dto.message_type == MessageRequestType.ROOM_TYPE.value:
            eligible_room_type_configs = self._get_eligible_room_type_configs(message_request_dto.request_items, hotel_entity)
            return RoomTypeConfigurationResponseDTO(
                request_id=request_id,
                action_type='SYNC_ROOMTYPE',
                config_type=message_request_dto.message_type,
                hotel_entity=hotel_entity,
                room_type_configs=eligible_room_type_configs
            )
        elif message_request_dto.message_type == MessageRequestType.ROOM_CLASS.value:
            eligible_room_class_configs = self._get_eligible_room_class_configs(message_request_dto.request_items, hotel_entity)
            return RoomClassConfigurationResponseDTO(
                request_id=request_id,
                action_type='SYNC_CLASS',
                config_type=message_request_dto.message_type,
                hotel_entity=hotel_entity,
                room_class_configs=eligible_room_class_configs
            )

        elif message_request_dto.message_type == MessageRequestType.RESERVATION.value:
            internal_room_booking_id = message_request_dto.booking_id
            if not internal_room_booking_id:
                return self.build_reservation_list_response_dto(
                    request_id,
                    hotel_entity,
                    message_request_dto.date_range.begin_date,
                    message_request_dto.date_range.end_date,
                )

            booking_sync_entity = self.booking_sync_repository.load(
                external_hotel_id=hotel_entity.external_hotel_id,
                internal_room_booking_id=internal_room_booking_id,
            )
            if booking_sync_entity is None:
                raise InvalidRequestError(f"No booking found for id: {internal_room_booking_id}")
            thsc_booking: THSCBooking = THSCBooking.get(booking_sync_entity.internal_booking_id)
            room_id_to_room_number_mapping = self._fetch_room_number_mapping(
                property_id=hotel_entity.internal_hotel_id,
                thsc_booking=thsc_booking,
            )
            bill: Bill = Bill.get(thsc_booking.bill_id)
            rate_plan_details = self.crs_client.get_booking_rate_plans(thsc_booking.booking_id)
            rp_codes_to_rp_details = {
                rate_plan_detail['rate_plan_code']: rate_plan_detail for rate_plan_detail in rate_plan_details
            }
            charge_id_to_charges = {charge.charge_id: charge for charge in bill.charges}
            user_profile_info = {}
            ups_enabled = self.tenant_settings.is_ups_enabled()
            if ups_enabled:
                user_profile_info = self._get_user_profile_details_for_thsc_customers(hotel_entity, thsc_booking.customers)
            return BookingDTO.create_for_thsc_room_stay(
                request_id,
                booking_sync_entity.crs_room_stay_id,
                booking_sync_entity.external_room_booking_id,
                internal_room_booking_id,
                thsc_booking,
                hotel_entity,
                EMMA_RESERVATION_ACTION_TYPE.SYNCH.value,
                charge_id_to_charges,
                rp_codes_to_rp_details,
                bill.total_posttax_amount,
                room_id_to_room_number_mapping,
                user_profile_info,
            )

        elif message_request_dto.message_type == MessageRequestType.MARKET_CODE.value:
            market_code_config_dto = self._get_eligible_market_codes(hotel_entity=hotel_entity,
                                                                     message_request_dto=message_request_dto)
            return MarketCodeResponseDTO(request_id=request_id,
                                         hotel_entity=hotel_entity,
                                         market_code_config_dto_values=market_code_config_dto.values)

        elif message_request_dto.message_type == MessageRequestType.INVENTORY.value:
            self.hotel_entity = hotel_entity
            return self.build_emma_inventory_dto(
                message_request_dto.date_range.begin_date,
                message_request_dto.date_range.end_date,
                request_id,
            )
        elif message_request_dto.message_type == MessageRequestType.TRANSACTION_CODE.value:
            transaction_code_config_dto = self._get_eligible_transaction_code(hotel_entity, message_request_dto)
            return TransactionCodeResponseDTO(request_id=request_id,
                                              hotel_entity=hotel_entity,
                                              transaction_code_config_dto=transaction_code_config_dto)
        elif message_request_dto.message_type == MessageRequestType.COMMISSION_CODE.value:
            commission_code_config_dto = self._get_eligible_commission_codes(
                hotel_id=hotel_entity.internal_hotel_id, message_request_dto=message_request_dto)
            return CommissionCodeResponseDTO(
                request_id=request_id,
                hotel_entity=hotel_entity,
                commission_code_config_dto_values=commission_code_config_dto.values)

    def get_company_profile_from_name(self, sub_entities, trade_name):
        if not sub_entities:
            return None
        sub_entities.sort(key=self.fetch_created_at, reverse=True)
        sub_entities = [sub_entity for sub_entity in sub_entities if
                        sub_entity.get('trade_name').strip().lower() == trade_name.strip().lower()]
        for sub_entity in sub_entities:
            if sub_entity['status'] == CompanyProfileStatus.COMPLETE.value:
                return SearchEntitiesResponseDTO.from_json(dict(sub_entities=[sub_entity]))
        return SearchEntitiesResponseDTO.from_json(dict(sub_entities=[sub_entities[0]])) if sub_entities else None

    def _get_transaction_id(self, transaction_str: str):
        return re.sub(r'\D', '', transaction_str)

    def _get_eligible_room_type_configs(self, request_items, hotel_entity: HotelEntity):
        room_type_configs = self.catalog_service_client.get_room_type_configurations(hotel_entity.internal_hotel_id)
        requested_room_types = []
        for request_item in request_items:
            if request_item[1].lower() != 'ALL'.lower():
                requested_room_types.append(request_item[1])

        eligible_room_type_configs = []
        if not requested_room_types:
            for room_type in hotel_entity.room_types:
                if room_type.internal_code in room_type_configs.room_type_config_map:
                    eligible_room_type_configs.append(
                        room_type_configs.room_type_config_map[room_type.internal_code])
            return eligible_room_type_configs

        for requested_room_type in requested_room_types:
            for room_type in hotel_entity.room_types:
                if (
                    requested_room_type == room_type.external_code
                    and room_type.internal_code in room_type_configs.room_type_config_map
                ):
                    eligible_room_type_configs.append(
                        room_type_configs.room_type_config_map[room_type.internal_code])
                    break
            else:
                raise InvalidRequestError(f"Room type mapping not found for itemCode: {requested_room_type}")
        return eligible_room_type_configs

    @staticmethod
    def _get_eligible_room_class_configs(request_items, hotel_entity: HotelEntity):
        requested_room_classes = []
        for request_item in request_items:
            if request_item[1].lower() != 'ALL'.lower():
                requested_room_classes.append(request_item[1])

        eligible_room_class_configs = set()
        if not requested_room_classes:
            for room_type in hotel_entity.room_types:
                if room_type.room_class_code and room_type.room_class_name:
                    eligible_room_class_configs.add((room_type.room_class_code, room_type.room_class_name))
        else:
            for requested_room_class in requested_room_classes:
                mapping_found = False
                for room_type in hotel_entity.room_types:
                    if requested_room_class == room_type.room_class_code:
                        eligible_room_class_configs.add((room_type.room_class_code, room_type.room_class_name))
                        mapping_found = True
                        break
                if not mapping_found:
                    raise InvalidRequestError(f"Room class mapping not found for itemCode: {requested_room_class}")

        return eligible_room_class_configs

    def _get_eligible_market_codes(self, hotel_entity: HotelEntity, message_request_dto: MessageRequestDto = None,
                                   market_code: str = None):
        hotel_configs = self.catalog_service_client.get_tenant_configs(hotel_entity.internal_hotel_id)
        hotel_config = {config.config_name: config for config in hotel_configs}
        current_config = hotel_config.get(TENANT_CONFIG_SEGMENTS, [])
        # TODO: move this logic to tenant settings
        current_config = current_config.get_config_value()
        market_code_config_dto = None
        for config in current_config:
            if config['name'] == 'Market Segment':
                market_code_config_dto = MarketCodeConfigDto.create_dtos_from_config_value(config)
                break
        if not market_code_config_dto:
            raise TenantConfigException("Market Segment config is not configured.")
        if market_code:
            return market_code_config_dto.get_requested_market_code(market_code)
        if message_request_dto:
            return market_code_config_dto.filter_applicable_market_code(message_request_dto)
        return market_code_config_dto

    def _get_eligible_transaction_code(self, hotel_entity: HotelEntity, message_request_dto: MessageRequestDto):
        transaction_config = self.finance_service_client.get_transaction_codes(hotel_entity.internal_hotel_id)
        transaction_code_config_dto = TransactionCodeConfigDto.create_dtos_from_config_value(transaction_config)
        if not transaction_code_config_dto.values:
            raise TenantConfigException("Transaction Code config is not configured.")
        return transaction_code_config_dto.filter_applicable_transaction_codes(message_request_dto)

    def _get_eligible_commission_codes(self, hotel_id, message_request_dto=None):
        commission_config = self.tenant_settings.get_commission_codes(hotel_id=hotel_id)
        commission_code_config_dto = CommissionCodeConfigDto.create_dtos_from_config_value(commission_config)
        if not commission_code_config_dto:
            raise TenantConfigException("Commission Code config is not configured.")
        if not message_request_dto:
            return commission_code_config_dto
        return commission_code_config_dto.filter_applicable_commission_code(message_request_dto)

    def sync_market_and_commission_codes(self, tenant_id, hotel_id):
        hotel_entity = self.hotel_repository.load(
            tenant_id=tenant_id,
            service_code=self.service_code,
            internal_hotel_id=hotel_id,
        )
        commission_code_response_dtos = self._sync_codes(tenant_id, hotel_entity, COMMISSION_CODE_CONFIG)
        market_code_response_dtos = self._sync_codes(tenant_id, hotel_entity, MARKET_CODE_CONFIG)
        for config_dto in (commission_code_response_dtos + market_code_response_dtos):
            self.emma_client.send_result(config_dto.request_id, config_dto.to_xml())

    def _sync_codes(self, tenant_id, hotel_entity, code_type):
        code_config_dto = self._get_eligible_codes(hotel_entity, code_type)
        cache_key = create_emma_config_cache_key(tenant_id, hotel_entity.internal_hotel_id, code_type)

        if not cache.cache.has(cache_key):
            cache.cache.set(cache_key, code_config_dto.values, timeout=-1)
            return [self._create_response_dto(code_type, hotel_entity, code_config_dto.values, ActionType.ADD)]

        response_dtos = []
        cached_code_values_config = cache.cache.get(cache_key)
        cached_code_values_to_config_mapping = {config.item_code: config for config in cached_code_values_config}
        add_values = [code_config_value for code_config_value in code_config_dto.values
                      if code_config_value.item_code not in cached_code_values_to_config_mapping]

        change_values = []
        for code_config_value in code_config_dto.values:
            item_code = code_config_value.item_code
            if (cached_code_values_to_config_mapping.get(item_code) and
                    code_config_value.time_stamp != cached_code_values_to_config_mapping[item_code].time_stamp):
                change_values.append(code_config_value)

        if add_values:
            response_dtos.append(
                self._create_response_dto(code_type, hotel_entity, add_values, ActionType.ADD)
            )
        if change_values:
            response_dtos.append(
                self._create_response_dto(code_type, hotel_entity, change_values, ActionType.CHANGE)
            )
        delete_values = self._get_deleted_config_values(code_type, cached_code_values_config, code_config_dto.values)
        if delete_values:
            response_dtos.append(
                self._create_response_dto(code_type, hotel_entity, delete_values, ActionType.DELETE)
            )
        cache.cache.set(cache_key, code_config_dto.values, timeout=-1)
        return response_dtos

    def _get_deleted_config_values(self, code_type, cached_code_values_config, updated_code_values):
        if code_type == COMMISSION_CODE_CONFIG:
            updated_commission_code_to_config_mapping = {config.item_code: config for config in updated_code_values}
            delete_values = [code_config_value for code_config_value in cached_code_values_config
                             if code_config_value.item_code not in updated_commission_code_to_config_mapping]
            return delete_values
        return []

    def _create_response_dto(self, code_type, hotel_entity, code_config_values, action_type):
        if code_type == COMMISSION_CODE_CONFIG:
            return CommissionCodeResponseDTO(hotel_entity=hotel_entity,
                                             commission_code_config_dto_values=code_config_values,
                                             action_type=action_type.value)
        elif code_type == MARKET_CODE_CONFIG:
            return MarketCodeResponseDTO(hotel_entity=hotel_entity,
                                         market_code_config_dto_values=code_config_values,
                                         action_type=action_type.value)

    def _get_eligible_codes(self, hotel_entity, code_type):
        if code_type == COMMISSION_CODE_CONFIG:
            return self._get_eligible_commission_codes(hotel_entity.internal_hotel_id)
        elif code_type == MARKET_CODE_CONFIG:
            return self._get_eligible_market_codes(hotel_entity)

    def add_room_type_config(self, room_type_config_dto):
        room_type_mapping_entity = RoomTypeMappingEntity(
            internal_code=room_type_config_dto.room_type_id,
            external_code=room_type_config_dto.external_room_code or room_type_config_dto.room_type_id,
            service_code=self.service_code,
            is_active=True)
        self.hotel_entity.add_room_type_mappings([room_type_mapping_entity])
        self.hotel_repository.update(self.hotel_entity)

        room_type_config = RoomTypeConfigurationResponseDTO(
            action_type=ActionType.ADD.value,
            config_type=MessageRequestType.ROOM_TYPE.value,
            hotel_entity=self.hotel_entity,
            room_type_configs=[room_type_config_dto],
        )
        self.emma_client.send_result(room_type_config.request_id, room_type_config.to_xml())

    def update_room_types(self, room_type_config_dtos):
        room_type_config = RoomTypeConfigurationResponseDTO(
            action_type=ActionType.CHANGE.value,
            config_type=MessageRequestType.ROOM_TYPE.value,
            hotel_entity=self.hotel_entity,
            room_type_configs=room_type_config_dtos,
        )
        self.emma_client.send_result(room_type_config.request_id, room_type_config.to_xml())

    def delete_room_type_config(self, room_type_config_dto):
        external_code = room_type_config_dto.external_room_code if room_type_config_dto.external_room_code else (
            room_type_config_dto.room_type_id)

        self.hotel_entity.mark_room_type_inactive(external_code=external_code)
        self.hotel_repository.update(self.hotel_entity)

        room_type_config = RoomTypeConfigurationResponseDTO(
            action_type=ActionType.DELETE.value,
            config_type=MessageRequestType.ROOM_TYPE.value,
            hotel_entity=self.hotel_entity,
            room_type_configs=[room_type_config_dto],
        )
        self.emma_client.send_result(room_type_config.request_id, room_type_config.to_xml())

    def send_room_class_room_type_config_updates(self, tenant_id, hotel_id):
        hotel_entity = self.hotel_repository.load(
            tenant_id=tenant_id,
            service_code=self.service_code,
            internal_hotel_id=hotel_id,
            only_active_mappings=False,
        )
        self.hotel_entity = hotel_entity
        config_update_dtos = []
        cache_key = create_emma_config_cache_key(tenant_id, hotel_entity.internal_hotel_id,
                                                 MessageRequestType.ROOM_CLASS.value)
        room_class_configs = {
            room_type.room_class_code: room_type.room_class_name
            for room_type in hotel_entity.room_types
        }
        # TODO: Add better alternative than cache as someone may clear cache causing unrequired config messages
        cached_room_class_configs = cache.cache.get(cache_key) or {}

        room_class_codes = list(room_class_configs.keys())
        cached_room_class_codes = list(cached_room_class_configs.keys())
        new_room_class_codes = set(room_class_codes) - set(cached_room_class_codes)
        deleted_room_class_codes = set(cached_room_class_codes) - set(room_class_codes)
        changed_room_class_codes = set(room_class_codes) & set(cached_room_class_codes)
        changed_room_class_configs = [
            (code, room_class_configs[code]) for code in changed_room_class_codes
            if room_class_configs[code] != cached_room_class_configs[code]
        ]
        changed_room_types = self._get_room_type_updates(tenant_id, hotel_entity)

        def _create_response_dto(room_class_configs, action_type):
            return RoomClassConfigurationResponseDTO(
                action_type=action_type.value,
                config_type=MessageRequestType.ROOM_CLASS.value,
                hotel_entity=hotel_entity,
                room_class_configs=room_class_configs
            )

        if new_room_class_codes:
            config_update_dtos.append(
                _create_response_dto(
                    [(code, room_class_configs[code]) for code in new_room_class_codes],
                    ActionType.ADD,
                )
            )

        if changed_room_types:
            config_update_dtos.append(changed_room_types)

        if deleted_room_class_codes:
            config_update_dtos.append(
                _create_response_dto(
                    [(code, cached_room_class_configs[code]) for code in deleted_room_class_codes],
                    ActionType.DELETE,
                )
            )

        if changed_room_class_configs:
            config_update_dtos.append(
                _create_response_dto(
                    changed_room_class_configs,
                    ActionType.CHANGE,
                )
            )

        for config_dto in config_update_dtos:
            self.emma_client.send_result(config_dto.request_id, config_dto.to_xml())
        cache.cache.set(cache_key, room_class_configs, timeout=-1)

    def _get_room_type_updates(self, tenant_id, hotel_entity):
        cache_key = create_emma_config_cache_key(tenant_id, hotel_entity.internal_hotel_id,
                                                 MessageRequestType.ROOM_TYPE.value)
        room_type_configs = {
            room_type.external_code: (room_type.room_class_code, room_type.is_active)
            for room_type in hotel_entity.room_types
        }
        cached_room_type_configs = cache.cache.get(cache_key) or {}
        catalog_room_type_configs = self.catalog_service_client.get_room_type_configurations(
            hotel_entity.internal_hotel_id).room_type_config_map
        changed_room_type_configs = []
        room_type_dto = None
        for internal_room_type in catalog_room_type_configs:
            for room_type in hotel_entity.room_types:
                if room_type.internal_code == internal_room_type:
                    external_room_type = room_type.external_code
                    if room_type_configs.get(external_room_type) != cached_room_type_configs.get(external_room_type):
                        changed_room_type_configs.append(catalog_room_type_configs[internal_room_type])
                    break

        if changed_room_type_configs:
            room_type_dto = RoomTypeConfigurationResponseDTO(
                action_type=ActionType.CHANGE.value,
                config_type=MessageRequestType.ROOM_TYPE.value,
                hotel_entity=hotel_entity,
                room_type_configs=changed_room_type_configs,
            )
        cache.cache.set(cache_key, room_type_configs, timeout=-1)
        return room_type_dto

    def _override_profile_details_from_cleartax(self, profiles_payload, gstin, prexisting_profile):
        def get_fields_to_fetch(entity):
            return ['registered_address', 'communication_address'] if entity else []

        if not is_valid_gstin(gstin):
            logger.info(f"{gstin} is not a valid Indian GST number")
            return

        # Determine if we need to fetch corporate details
        if prexisting_profile:
            fields_to_fetch = get_fields_to_fetch(prexisting_profile.sub_entities[0])
        else:
            fields_to_fetch = get_fields_to_fetch(profiles_payload)

        if not fields_to_fetch:
            return

        try:
            cleartax_data = self.cleartax_client.get_corporate_detail(gstin)
            address = AddressDTO.create_from_cleartax_data(cleartax_data)
        except Exception as e:
            logger.info(f"Error fetching details from ClearTax for GSTIN {gstin}: {str(e)}")
            sentry_sdk.capture_exception(e)
            return

        corporate_detail_mapping = {
            'registered_address': address.to_json() if address else None,
            'communication_address': address.to_json() if address else None,
        }

        # If there are required fields, update only those fields
        if fields_to_fetch:
            corporate_detail_mapping = {key: value for key, value in corporate_detail_mapping.items()
                                        if key in fields_to_fetch}
        profiles_payload.update({key: value for key, value in corporate_detail_mapping.items() if value})

    @staticmethod
    def _update_booking_comments(booking_comments, room_stay, is_guest_visible=False):
        room_stay_comments = room_stay.extra_information and room_stay.extra_information.get(
            'guest_visible_remarks' if is_guest_visible else 'comments')
        room_wise_comments = booking_comments.split('\n') if booking_comments else []
        room_prefix = COMMENTS_PREFIX.format(room_stay.room_stay_id)
        updated = False
        for index, room_stay_comment in enumerate(room_wise_comments):
            if room_stay_comment.startswith(room_prefix):
                room_wise_comments[index] = f"{room_prefix} {room_stay_comments}"
                updated = True
                break

        if room_stay_comments and not updated:
            room_wise_comments.append(f"{room_prefix} {room_stay_comments}")

        return '\n'.join(room_wise_comments)

    @staticmethod
    def _update_guarantee_information(booking_dto, thsc_booking):
        if not (booking_dto.payment_guarantee or booking_dto.payments):
            return

        credit_cards = BookingAdaptor.get_credit_cards(booking_dto)
        new_guarantee_information = BookingAdaptor.get_guarantee_information(
            booking_dto,
            credit_cards,
            thsc_booking.travel_agent_details,
            thsc_booking.company_details,
        )
        if not new_guarantee_information:
            return

        if new_guarantee_information != thsc_booking.guarantee_information and thsc_booking.guarantee_information:
            msg = (
                f"Different payment_guarantee received for booking_id: {thsc_booking.booking_id}."
                f"Old payment_guarantee_details: {thsc_booking.guarantee_information.guarantee_details}."
                f"New payment_guarantee_details: {booking_dto.room.payment_guarantee}"
            )
            SlackAlert.send_alert(text=msg, slack_webhook_url=app.config['EMMA_SLACK_WEBHOOK_URL'])
        thsc_booking.guarantee_information = new_guarantee_information

    @staticmethod
    def _update_parent_entity_statutory_details(profiles_payload):
        parent_entity_payload = deepcopy(profiles_payload)
        statutory_details = parent_entity_payload.get('statutory_details', [])
        new_statutory_details = []

        for statutory_detail in statutory_details:
            if statutory_detail['field_name'] == 'gst':
                if not is_valid_gstin(statutory_detail['value']):
                    continue
                statutory_detail['field_name'] = 'pan_number'
                statutory_detail['value'] = get_pan_from_gstin(statutory_detail['value'])
            new_statutory_details.append(statutory_detail)

        parent_entity_payload['statutory_details'] = new_statutory_details
        return parent_entity_payload

    def build_reservation_list_response_dto(self, request_id, hotel_entity, begin_date, end_date):
        crs_bookings = self.crs_client.get_all_date_range_bookings(hotel_entity.internal_hotel_id, begin_date, end_date)
        booking_ids = []
        room_to_reservation_details = dict()
        for booking in crs_bookings:
            booking_ids.append(booking["booking_id"])
            customers = {customer["customer_id"]: customer for customer in booking["customers"]}
            for room_stay in booking["room_stays"]:
                try:
                    customer = customers[room_stay["guest_stays"][0]["guest_allocation"]["guest_id"]]
                except KeyError:
                    continue
                room_to_reservation_details[(booking["booking_id"], int(room_stay["room_stay_id"]))] = {
                    "last_name": customer["last_name"] or "NA",
                    "first_name": customer["first_name"],
                }
        booking_sync_bookings = self.booking_sync_repository.load_all_by_internal_booking_ids(
            service_code=self.service_code,
            internal_hotel_id=hotel_entity.internal_hotel_id,
            internal_booking_ids=booking_ids,
        )
        synced_booking_rooms = set()
        for booking in booking_sync_bookings:
            room_booking_key = (booking.internal_booking_id, int(booking.crs_room_stay_id))
            if room_booking_key not in room_to_reservation_details:
                continue
            room_to_reservation_details[room_booking_key].update({
                "external_room_booking_id": booking.external_room_booking_id or "",
                "internal_room_booking_id": booking.internal_room_booking_id or "",
                "status": booking.status,
            })
            synced_booking_rooms.add(room_booking_key)

        missing_booking_rooms = set(room_to_reservation_details.keys()) - synced_booking_rooms
        for missing_room in missing_booking_rooms:
            del room_to_reservation_details[missing_room]

        return ReservationListResponseDTO(
            request_id=request_id,
            hotel_id=hotel_entity.external_hotel_id,
            reservations=room_to_reservation_details.values(),
        )

    def _fetch_room_number_mapping(self, property_id, booking_event=None, thsc_booking=None):
        room_id_to_room_number_mapping = defaultdict(str)
        try:
            rooms = self.catalog_service_client.get_rooms(property_id)
        except Exception as e:
            logger.info(f"Error fetching room numbers from Catalog for property {property_id}, Error: {str(e)}")
            return room_id_to_room_number_mapping

        if not rooms:
            return room_id_to_room_number_mapping

        if booking_event:
            crs_room_stays = booking_event["room_stays"]
            for room_stay in crs_room_stays:
                room_id = room_stay["room_allocation"] and room_stay["room_allocation"]["room_id"]
                if not room_id:
                    continue
                for room in rooms:
                    if int(room_id) == room.get('id'):
                        room_id_to_room_number_mapping[room_id] = room['room_number']
                        break
        else:
            crs_room_stays = thsc_booking.rooms
            for room_stay in crs_room_stays:
                if not room_stay.room_id:
                    continue
                for room in rooms:
                    if int(room_stay.room_id) == room.get('id'):
                        room_id_to_room_number_mapping[room_stay.room_id] = room['room_number']
                        break
        return room_id_to_room_number_mapping

    def _handle_guest_profile_update_in_ups(self, profile_dto: ProfileDTO, hotel_entity):
        if not (profile_dto.phone_numbers or profile_dto.email):
            return
        try:
            user = self._update_or_create_ups_user(hotel_entity, profile_dto)
            user_profile = self._update_or_create_ups_user_profile(hotel_entity, user, profile_dto)
        except Exception as e:
            logger.exception(f"Error {str(e)} occurred while updating guest profile in UPS")

    def _get_ups_user(self, hotel_entity, profile_dto):
        user = self.user_profile_service_client.search_user_details(hotel_id=hotel_entity.internal_hotel_id,
                                                                    phone=profile_dto.phone_numbers[0].phone_number if
                                                                    profile_dto.phone_numbers else None,
                                                                    email=profile_dto.email if
                                                                    profile_dto.email else None)
        return user

    def _get_or_create_ups_user(self, hotel_entity, profile_dto):
        user = self._get_ups_user(hotel_entity, profile_dto)
        if not (user and user.user_id and user.contact_ids):
            user = self.user_profile_service_client.create_new_user(
                EmmaUserProfileServiceAdaptor(hotel_entity).to_user_entity(profile_dto))
        return user

    def _update_or_create_ups_user(self, hotel_entity, profile_dto):
        user = self._get_ups_user(hotel_entity, profile_dto)
        if not (user and user.user_id and user.contact_ids):
            user = self.user_profile_service_client.create_new_user(
                EmmaUserProfileServiceAdaptor(hotel_entity).to_user_entity(profile_dto))
        else:
            user = self.user_profile_service_client.edit_user(user.user_id,
                EmmaUserProfileServiceAdaptor(hotel_entity).to_user_entity(profile_dto)
            )
            self._update_user_contact_in_ups(user, profile_dto)
        return user

    def _get_or_create_ups_user_profile(self, hotel_entity, user, profile_dto):
        user_profile = self.user_profile_service_client.search_user_profile(user_id=user.user_id,
                                                                            profile_type=ProfileType.B2C.value)
        if not user_profile:
            user_profile = self.user_profile_service_client.create_new_user_profile(user.user_id,
                                                                                    EmmaUserProfileServiceAdaptor(hotel_entity).to_user_profile_entity(
                                                                                        user, profile_dto))
        return user_profile

    def _update_or_create_ups_user_profile(self, hotel_entity, user, profile_dto):
        user_profile = self.user_profile_service_client.search_user_profile(user_id=user.user_id,
                                                                            profile_type=ProfileType.B2C.value)
        if not user_profile:
            user_profile = self.user_profile_service_client.create_new_user_profile(user.user_id,
                                                                                    EmmaUserProfileServiceAdaptor(hotel_entity).to_user_profile_entity(
                                                                                        user, profile_dto))
        else:
            user_profile = self.user_profile_service_client.edit_user_profile(user.user_id, user_profile.user_profile_id,
                                                                              EmmaUserProfileServiceAdaptor(hotel_entity).to_user_profile_entity(
                                                                                  user, profile_dto))
        return user_profile

    def _get_user_profiles_from_ups(self, hotel_entity, guest_profiles):
        user_profile_info = []
        for profile in guest_profiles:
            if not (profile.phone_numbers or profile.email):
                continue
            user = self._get_or_create_ups_user(hotel_entity, profile)
            user_profile = self._get_or_create_ups_user_profile(hotel_entity, user, profile)
            address_type = profile.addresses[0].address_type if profile.addresses else None
            user_profile_info.append(dict(user=user, user_profile=user_profile, address_type=address_type))
        return user_profile_info

    def _sanitize_gender(self, profile_dto):
        if profile_dto.gender == "UNKNOWN":
            profile_dto.gender = Gender.OTHER.value
        profile_dto.gender = profile_dto.gender.lower()

    def _update_user_contact_in_ups(self, user, profile_dto):
        if not profile_dto.email:
            return
        email = profile_dto.email
        if not user.email:
            email_contact = UserContact(contact_type=UserContactType.EMAIL, contact_value=email)
            self.user_profile_service_client.add_user_contact(user.user_id, email_contact)

    def _send_failure_mail(self, tenant_id, action_type, request_id, data):
        try:
            hotel_entity = self.hotel_repository.load(
                tenant_id=tenant_id, external_hotel_id=data.hotel_id
            )
            room_type_mapping = {
                room_type.external_code: room_type.internal_code for room_type in hotel_entity.room_types
            }
            hotel_id = hotel_entity.internal_hotel_id
            entity_id = None
            subject = None
            error_template = None
            source_code_to_channel_mapping = None

            if isinstance(data, ProfileDTO):
                subject = EMMA_ERROR_NOTIFICATION_SUBJECT_MAPPING.get(EventType.PROFILE.value)
                entity_id = data.name_code
                error_template = PROFILE_ERROR_TEMPLATE
            elif isinstance(data, PackageDTO):
                subject = EMMA_ERROR_NOTIFICATION_SUBJECT_MAPPING.get(EventType.PACKAGE.value)
                entity_id = data.package_code
                error_template = PACKAGE_ERROR_TEMPLATE
            elif isinstance(data, RateDTO):
                subject = EMMA_ERROR_NOTIFICATION_SUBJECT_MAPPING.get(EventType.RATE_HEADER.value)
                entity_id = data.rate_code
                error_template = RATE_PLAN_ERROR_TEMPLATE
            elif isinstance(data, RestrictionDTO):
                action_type = data.action_type
                subject = EMMA_ERROR_NOTIFICATION_SUBJECT_MAPPING.get(EventType.RESTRICTION.value)
                error_template = RESTRICTION_ERROR_TEMPLATE
            elif isinstance(data, BookingDTO):
                subject = EMMA_ERROR_NOTIFICATION_SUBJECT_MAPPING.get(EventType.RESERVATION.value)
                entity_id = data.booking_id
                error_template = RESERVATION_ERROR_TEMPLATE
                channel_details = self.tenant_settings.get_channel_details(hotel_id)
                source_code_to_channel_mapping = {detail['source_code']: detail for detail in channel_details}

            subject = subject.format(
                action_type=action_type,
                entity_id=entity_id,
                request_id=request_id,
                hotel_id=hotel_id,
            )
            body_html = render_template(
                error_template,
                data=data,
                action_type=action_type,
                hotel_id=hotel_id,
                room_type_mapping=room_type_mapping,
                source_code_to_channel_mapping=source_code_to_channel_mapping,
            )
            recipients, cc_recipients = self.tenant_settings.get_emma_failure_email_recipients(hotel_id)
            self.notification_service_client.send_email(
                hotel_id=hotel_id,
                body_html=body_html,
                subject=subject,
                consumer=ServiceCodes.EMMA.value,
                recipients=recipients,
                reply_mail=NotificationSender.NOREPLY.value,
                sender=NotificationSender.SENDER_EMMA.value,
                sender_name=NotificationSender.EMMA_ALERT.value,
                cc_recipients=cc_recipients,
                slack_channel_webhook_url=app.config['EMMA_SLACK_WEBHOOK_URL'],
            )
        except Exception as e:
            sentry_sdk.capture_exception(e)

    def _get_user_profile_details_for_thsc_customers(self, hotel_entity, customers):
        user_profile_id_to_profile = {}
        for customer in customers:
            user = self.user_profile_service_client.search_user_details(hotel_id=hotel_entity.internal_hotel_id,
                                                                        phone=customer.phone_number if
                                                                        customer.phone_number else None,
                                                                        email=customer.email if
                                                                        customer.email else None)
            if not user:
                continue
            user_profile = self.user_profile_service_client.search_user_profile(user_id=user.user_id,
                                                                                profile_type=ProfileType.B2C.value)
            user_profile_id_to_profile[user_profile.user_profile_id] = user_profile
        return user_profile_id_to_profile

    @staticmethod
    def _normalize_linkage_dates_wrt_rate_plan_sell_dates(linkage_rates, base_rate_plan):
        start_date, end_date = date.max, date.min
        if base_rate_plan.sell_start_date:
            start_date = min(start_date, base_rate_plan.sell_start_date)
        if base_rate_plan.sell_end_date:
            end_date = max(end_date, base_rate_plan.sell_end_date)
        for linkage_rate in linkage_rates:
            linkage_rate['start_date'] = dateutils.date_to_ymd_str(
                max(start_date, dateutils.ymd_str_to_date(linkage_rate['start_date']))
            )
            if linkage_rate['end_date'] is None:
                continue
            linkage_rate['end_date'] = dateutils.date_to_ymd_str(
                min(end_date, dateutils.ymd_str_to_date(linkage_rate['end_date']))
            )

    def process_allowance_event_from_crs(self, hotel_entity, bill_event, user_action):
        bill: Bill = Bill.get(bill_event.get("bill_id"))
        thsc_booking: THSCBooking = THSCBooking.get(bill.parent_info.get("booking_id"))
        room_id_to_room_number_mapping = self._fetch_room_number_mapping(
            property_id=hotel_entity.internal_hotel_id,
            thsc_booking=thsc_booking,
        )
        rate_plan_details = self.crs_client.get_booking_rate_plans(thsc_booking.booking_id)
        rp_codes_to_rp_details = {
            rate_plan_detail['rate_plan_code']: rate_plan_detail for rate_plan_detail in rate_plan_details
        }
        charge_id_to_charges = {charge.charge_id: charge for charge in bill.charges}
        booking_sync_entities = self.booking_sync_repository.load_all_by_internal_booking_ids(
            service_code=hotel_entity.service_code,
            internal_hotel_id=hotel_entity.internal_hotel_id,
            internal_booking_ids=[thsc_booking.booking_id],
        )
        user_profile_info = self._get_user_profile_details_for_thsc_customers(hotel_entity, thsc_booking.customers)
        for booking_sync_entity in booking_sync_entities:
            booking_dto = BookingDTO.create_for_thsc_room_stay(
                generate_request_id(),
                booking_sync_entity.crs_room_stay_id,
                booking_sync_entity.external_room_booking_id,
                booking_sync_entity.internal_room_booking_id,
                thsc_booking,
                hotel_entity,
                EMMA_RESERVATION_ACTION_TYPE.SYNCH.value,
                charge_id_to_charges,
                rp_codes_to_rp_details,
                bill.total_posttax_amount,
                room_id_to_room_number_mapping,
                user_profile_info,
            )
            response = self.emma_client.send_booking_details(booking_dto.request_id, booking_dto.to_xml())
            logger.info(response)

    @staticmethod
    def send_alert_if_market_segment_not_present(market_segments, market_segment_code):
        for market_segment in market_segments:
            if market_segment['code'] == market_segment_code and not market_segment['disabled']:
                return
        SlackAlert.send_alert(
            slack_webhook_url=app.config['EMMA_SLACK_WEBHOOK_URL'],
            text=f"Market segment '{market_segment_code}' missing from catalog. "
        )
