from tenant_gateway.domain.external_services.su.dtos.value_objects import Facilities, Images, Position, Address


class OccupancyDto(object):
    def __init__(self, max_occupancy):
        self.max_occupancy = max_occupancy

    def to_su_payload(self):
        return dict(MaxOccupancy=self.max_occupancy)


class RoomDto(object):
    def __init__(self, room_rate, quantity, room_type, size_measurement, room_id=None, size_measurement_unit="sqft"):
        self.room_id = room_id
        self.room_rate = room_rate
        self.quantity = quantity
        self.room_type = room_type
        self.size_measurement = size_measurement
        self.size_measurement_unit = size_measurement_unit

    def to_su_payload(self):
        return dict(RoomID=self.room_id, RoomRate=self.room_rate, Quantity=self.quantity, RoomType=self.room_type,
                    SizeMeasurement=self.size_measurement, SizeMeasurementUnit=self.size_measurement_unit)


class DescriptionDto(object):
    def __init__(self, text, room_description):
        self.text = text
        self.room_description = room_description

    def to_su_payload(self):
        return dict(Text=self.text, RoomDescription=self.room_description)


class GuestRoomDto(object):
    def __init__(self, occupancy: OccupancyDto, room: RoomDto, description: DescriptionDto,
                 facilities: Facilities = None, images: Images = None,
                 position: Position = None, address: Address = None):
        self.occupancy = occupancy
        self.room = room
        self.description = description
        self.facilities = facilities
        self.images = images
        self.position = position
        self.address = address

    def to_su_payload(self):
        return dict(Occupancy=self.occupancy.to_su_payload(), Room=self.room.to_su_payload(),
                    Description=self.description.to_su_payload(),
                    Facilities=self.facilities.to_su_payload() if self.facilities else None,
                    Images=self.images.to_su_payload() if self.images else None,
                    Position=self.position.to_su_payload() if self.position else None,
                    Address=self.address.to_su_payload() if self.address else None)


class SellableProductDto(object):
    def __init__(self, inv_status_type, guest_room: GuestRoomDto = None, inv_code=None, inv_notif_type=None):
        self.inv_status_type = inv_status_type
        self.guest_room = guest_room
        self.inv_code = inv_code
        self.inv_notif_type = inv_notif_type

    def to_su_payload(self):
        return dict(InvStatusType=self.inv_status_type.value,
                    GuestRoom=self.guest_room.to_su_payload() if self.guest_room else "",
                    InvCode=self.inv_code, InvNotifType=self.inv_notif_type.value if self.inv_notif_type else None)


class RoomPushDto(object):
    def __init__(self, hotel_id, sellable_product_dtos: [SellableProductDto]):
        self.hotel_id = hotel_id
        self.sellable_product_dtos = sellable_product_dtos

    def to_su_payload(self):
        return dict(SellableProducts=dict(HotelCode=self.hotel_id,
                                          SellableProduct=[sellable_product_dto.to_su_payload()
                                                           for sellable_product_dto in self.sellable_product_dtos]))
