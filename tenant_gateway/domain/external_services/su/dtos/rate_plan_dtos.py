class RatePlanDto(object):
    class Description(object):
        def __init__(self, name):
            self.name = name

        def to_su_payload(self):
            return dict(Name=self.name)

    def __init__(self, rate_plan_notif_type, rate_plan_id, meal_plan_id=None, name=None):
        self.rate_plan_notif_type = rate_plan_notif_type
        self.rate_plan_id = rate_plan_id
        self.meal_plan_id = meal_plan_id
        self.description = RatePlanDto.Description(name) if name else None

    def to_su_payload(self):
        return dict(RatePlanNotifType=self.rate_plan_notif_type.value,
                    RatePlanID=self.rate_plan_id,
                    MealPlanID=self.meal_plan_id,
                    Description=self.description.to_su_payload() if self.description else None)


class HotelRatePlanDto(object):
    def __init__(self, hotel_id, rate_plans: [RatePlanDto]):
        self.hotel_id = hotel_id
        self.rate_plans = rate_plans

    def to_su_payload(self):
        return dict(HotelCode=self.hotel_id,
                    RatePlan=[rate_plan.to_su_payload() for rate_plan in self.rate_plans])


class RatePlanPushDto(object):
    def __init__(self, hotel_rate_plan_dto: HotelRatePlanDto):
        self.hotel_rate_plan_dto = hotel_rate_plan_dto

    def to_su_payload(self):
        return dict(RatePlans=self.hotel_rate_plan_dto.to_su_payload())


class Price(object):
    def __init__(self, adult_count, price):
        self.adult_count = adult_count
        self.price = price

    def to_su_payload(self):
        return dict(NumberOfGuests=self.adult_count,
                    value=self.price)


class DateWisePrice(object):
    def __init__(self, rate_plan_code, prices: [Price], start_date=None, end_date=None, closed=None, minimum_stay=None,
                 maximum_stay=None, closed_on_arrival=None, extra_adult_rate=None, extra_child_rate=None,
                 closed_on_departure=None, stay_date=None):
        self.start_date = start_date
        self.end_date = end_date
        self.rate_plan_code = rate_plan_code
        self.prices = prices
        self.closed = closed
        self.minimum_stay = minimum_stay
        self.maximum_stay = maximum_stay
        self.closed_on_arrival = closed_on_arrival
        self.extra_adult_rate = extra_adult_rate
        self.extra_child_rate = extra_child_rate
        self.closed_on_departure = closed_on_departure
        self.stay_date = stay_date

    def to_su_payload(self):
        # NOTE: commented some fields as we are currently not supporting those fields updates
        date_wise_price = dict(value=self.stay_date, to=self.end_date,
                               price=[price.to_su_payload() for price in self.prices],
                               rate=[{"id": self.rate_plan_code}]
                               # closed=self.closed, minimumstay=self.minimum_stay,
                               # maximumstay=self.maximum_stay, closedonarrival=self.closed_on_arrival,
                               # closedondeparture=self.closed_on_departure, extraadultrate=self.extra_adult_rate,
                               # extrachildrate=self.extra_child_rate
                               )
        date_wise_price.update({"from": self.start_date})
        return date_wise_price


class RoomRateDto(object):
    def __init__(self, room_type_id, date_wise_prices: [DateWisePrice]):
        self.room_type_id = room_type_id
        self.date_wise_prices = date_wise_prices

    def to_su_payload(self):
        return dict(id=self.room_type_id,
                    date=[date_wise_price.to_su_payload() for date_wise_price in self.date_wise_prices])


class RatePushDto(object):
    def __init__(self, hotel_id, room_rates: [RoomRateDto]):
        self.hotel_id = hotel_id
        self.room_rates = room_rates

    def to_su_payload(self):
        return dict(hotelid=self.hotel_id,
                    room=[room_rate.to_su_payload() for room_rate in self.room_rates])
