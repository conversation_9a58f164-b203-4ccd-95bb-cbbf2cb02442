from tenant_gateway.domain.external_services.su.dtos.value_objects import Images, Facilities, Position, Address


class Name(object):
    def __init__(self, given_name, sur_name):
        self.given_name = given_name
        self.sur_name = sur_name

    def to_su_payload(self):
        return dict(GivenName=self.given_name, SurName=self.sur_name)


class Names(object):
    def __init__(self, name: Name):
        self.name = name

    def to_su_payload(self):
        return dict(Name=self.name.to_su_payload())


class Addresses(object):
    def __init__(self, address: Address):
        self.address = address

    def to_su_payload(self):
        return dict(Address=self.address.to_su_payload())


class Emails(object):
    def __init__(self, email):
        self.email = email

    def to_su_payload(self):
        return dict(Email=self.email)


class Phone(object):
    def __init__(self, phone_number, phone_tech_type="5"):
        self.phone_number = phone_number
        self.phone_tech_type = phone_tech_type

    def to_su_payload(self):
        return dict(PhoneNumber=self.phone_number, PhoneTechType=self.phone_tech_type)


class Phones(object):
    def __init__(self, phones: [Phone]):
        # Marking atleast one of the phone numbers with phone tech type "1" (voice) needed for Su
        phones[0].phone_tech_type = "1"
        self.phones = phones

    def to_su_payload(self):
        return dict(Phone=[phone.to_su_payload() for phone in self.phones])


class ContactInfo(object):
    def __init__(self, contact_profile_type, addresses: Addresses, notification_email=None, emails: Emails = None,
                 phones: Phones = None, names: Names = None):
        self.contact_profile_type = contact_profile_type
        self.names = names
        self.addresses = addresses
        self.notification_email = notification_email
        self.emails = emails
        self.phones = phones

    def to_su_payload(self):
        return dict(ContactProfileType=self.contact_profile_type.value,
                    Names=self.names.to_su_payload() if self.names else None,
                    Addresses=self.addresses.to_su_payload(), NotificationEmail=self.notification_email,
                    Emails=self.emails.to_su_payload() if self.emails else None,
                    Phones=self.phones.to_su_payload() if self.phones else None)


class ContactInfos(object):
    def __init__(self, contact_infos: [ContactInfo]):
        self.contact_infos = contact_infos

    def to_su_payload(self):
        return dict(ContactInfo=[contact_info.to_su_payload() for contact_info in self.contact_infos])


class HotelInfo(object):
    def __init__(self, position: Position):
        self.position = position

    def to_su_payload(self):
        return dict(Position=self.position.to_su_payload())


class HotelDto(object):
    def __init__(self, hotel_name, timezone, hotel_code, chain_id, base_currency_code,
                 hotel_descriptive_content_notif_type, contact_infos: ContactInfos, hotel_info: HotelInfo,
                 facilities: Facilities, hotel_description, images: Images = None, language_code="en", hotel_type="1",
                 property_license_number=None):
        self.hotel_name = hotel_name
        self.timezone = timezone
        self.hotel_code = hotel_code
        self.chain_id = chain_id
        self.currency_code = base_currency_code
        self.hotel_descriptive_content_notif_type = hotel_descriptive_content_notif_type
        self.contact_infos = contact_infos
        self.hotel_info = hotel_info
        self.facilities = facilities
        self.images = images
        self.hotel_description = hotel_description
        self.language_code = language_code
        self.hotel_type = hotel_type
        self.property_license_number = property_license_number

    def to_su_payload(self):
        return dict(HotelName=self.hotel_name, HotelType=self.hotel_type, TimeZone=self.timezone,
                    HotelCode=self.hotel_code, ChainID=self.chain_id,
                    CurrencyCode=self.currency_code if isinstance(self.currency_code,
                                                                  str) else self.currency_code.value,
                    PropertyLicenseNumber=self.property_license_number,
                    HotelDescriptiveContentNotifType=self.hotel_descriptive_content_notif_type.value,
                    ContactInfos=self.contact_infos.to_su_payload(), HotelInfo=self.hotel_info.to_su_payload(),
                    Facilities=self.facilities.to_su_payload(),
                    Images=self.images.to_su_payload() if self.images else None,
                    HotelDescription=self.hotel_description, LanguageCode=self.language_code)


class HotelPushDto(object):
    def __init__(self, hotel_dto: HotelDto):
        self.hotel_dto = hotel_dto

    def to_su_payload(self):
        return dict(HotelDescriptiveContents=dict(HotelDescriptiveContent=self.hotel_dto.to_su_payload()))
