from typing import List


class DateWiseAvailability:
    def __init__(self, date, room_count):
        self.date = date
        self.room_count = room_count

    def to_su_payload(self):
        return dict(value=self.date, roomstosell=str(self.room_count))


class RoomAvailabilityDTO:
    def __init__(self, room_type_external_code, date_wise_availabilities: [DateWiseAvailability]):
        self.room_type_external_code = room_type_external_code
        self.date_wise_availabilities = date_wise_availabilities

    def to_su_payload(self):
        return dict(
            date=[
                date_wise_availability.to_su_payload() for date_wise_availability in self.date_wise_availabilities
            ],
            id=self.room_type_external_code,
        )


class InventoryDTO:
    def __init__(self, hotel_id, room_availabilities: List[RoomAvailabilityDTO]):
        self.hotel_id = hotel_id
        self.room_availabilities = room_availabilities

    def to_su_payload(self):
        return dict(room=[r.to_su_payload() for r in self.room_availabilities], hotelid=self.hotel_id)
