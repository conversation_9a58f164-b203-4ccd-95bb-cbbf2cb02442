from tenant_gateway.domain.external_services.su.constants import OTAChannelIds
from tenant_gateway.domain.external_services.su.dtos.ota_mapping_dtos import AgodaMappingDto, BookingComMappingDto, ExpediaMappingDto, GoMMTMappingDto
from tenant_gateway.domain.external_services.su.dtos.ota_setup_dtos import AgodaSetupDto, BookingComSetupDto, ExpediaSetupDto, GoMMTSetupDto

class OTADtoSelector:
    dto_map = {
        OTAChannelIds.AGODA.value: dict(setup_dto=AgodaSetupDto, mapping_dto=AgodaMappingDto),
        OTAChannelIds.BOOKINGCOM.value: dict(setup_dto=BookingComSetupDto, mapping_dto=BookingComMappingDto),
        OTAChannelIds.GOMMT.value: dict(setup_dto=GoMMTSetupDto, mapping_dto=GoMMTMappingDto),
        OTAChannelIds.EXPEDIA.value: dict(setup_dto=ExpediaSetupDto, mapping_dto=ExpediaMappingDto)
    }

    def get_setup_dto_instance(self, setup_data):
        setup_dto = self.dto_map.get(setup_data['channelid'], {}).get('setup_dto')
        if not setup_dto:
            raise Exception("Invalid channelid given")
        return setup_dto(**setup_data)
    
    def get_mapping_dto_instance(self, mapping_data):
        setup_dto = self.dto_map.get(mapping_data['channelid'], {}).get('mapping_dto')
        if not setup_dto:
            raise Exception("Invalid channelid given")
        return setup_dto(**mapping_data)