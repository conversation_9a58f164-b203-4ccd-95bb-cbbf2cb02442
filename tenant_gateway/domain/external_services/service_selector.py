from object_registry import register_instance
from tenant_gateway.common.constants import ServiceCodes
from tenant_gateway.domain.booking_engine.service.booking_engine_service import BookingEngineService
from tenant_gateway.domain.entities.hotel import HotelEntity
from tenant_gateway.domain.external_services.avail_pro.avail_pro_service import AvailProService
from tenant_gateway.domain.external_services.emma.emma_service import EmmaService
from tenant_gateway.domain.external_services.google_hotels_ads.google_hotel_ads_service import GoogleHotelAdsService
from tenant_gateway.domain.external_services.su.su_service import SuService


@register_instance()
class ServiceSelector:
    service_map = {
        ServiceCodes.AVAIL_PRO.value: AvailProService,
        ServiceCodes.SU.value: SuService,
        ServiceCodes.BOOKING_ENGINE.value: BookingEngineService,
        ServiceCodes.GOOGLE_HOTEL_ADS.value: GoogleHotelAdsService,
        ServiceCodes.EMMA.value: EmmaService,
    }

    def get_service_instance(self, service_code, hotel_dto: HotelEntity = None):
        service = self.service_map.get(service_code)
        if not service:
            raise Exception("Invalid service code given")
        return service(service_code, hotel_dto)
