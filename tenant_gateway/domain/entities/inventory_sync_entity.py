from treebo_commons.utils import dateutils


class InventorySyncEntity:
    def __init__(self, service_code, internal_hotel_id, data=None, success=False, created_at=None,
                 inventory_sync_id=None, error=None, event_id=None, modified_at=None, external_hotel_id=None):
        self.inventory_sync_id = inventory_sync_id
        self.service_code = service_code
        # hotel id which is being used by internal service like RateManager/Catalog/CRS etc.
        self.internal_hotel_id = internal_hotel_id
        self._data = data
        self._success = success
        self._error = error
        self._event_id = event_id
        self.created_at = created_at if created_at else dateutils.current_datetime()
        self.modified_at = modified_at if modified_at else dateutils.current_datetime()
        # hotel id which is being used by external service like D-Edge/Su etc.
        self.external_hotel_id = external_hotel_id

    @property
    def success(self):
        return self._success

    @property
    def data(self):
        return self._data

    @property
    def error(self):
        return self._error

    @property
    def event_id(self):
        return self._event_id

    @success.setter
    def success(self, value):
        self._success = bool(value)

    @data.setter
    def data(self, value):
        self._data = value

    @error.setter
    def error(self, value):
        self._error = value

    @event_id.setter
    def event_id(self, value):
        self._event_id = value
