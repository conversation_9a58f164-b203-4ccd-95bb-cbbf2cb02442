class HotelSkuMappingEntity:

    def __init__(self, internal_hotel_id, external_hotel_id, sku_id, is_active, sku_name, tenant_id,
                 created_at=None, modified_at=None):
        self.internal_hotel_id = internal_hotel_id
        self.external_hotel_id = external_hotel_id
        self.sku_id = sku_id
        self.is_active = is_active
        self.sku_name = sku_name
        self.tenant_id = tenant_id
        self.created_at = created_at
        self.modified_at = modified_at
