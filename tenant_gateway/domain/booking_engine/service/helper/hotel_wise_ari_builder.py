from collections import defaultdict, namedtuple
from decimal import Decimal
from typing import List, Dict

from treebo_commons.money.constants import CurrencyType

from tenant_gateway.domain.booking_engine.aggregate.ari_aggregate import ARIAggregate
from tenant_gateway.domain.booking_engine.dtos.rate_dto import (
    HotelARIDTO,
    PolicyDTO,
    RateDTO,
    PriceDTO,
)
from tenant_gateway.domain.booking_engine.value_objects.rate_plan_info import (
    RatePlanInfo,
)
from tenant_gateway.domain.entities.hotel_room_inventory import HotelRoomInventory
from tenant_gateway.domain.entities.hotel_room_type import HotelRoomType
from tenant_gateway.domain.entities.rate import Rate


class HotelWiseARIBuilder:
    RoomInventoryGroupKey = namedtuple(
        "RoomInventoryGroupKey",
        ["room_type_details", "inventory"],
    )

    def __init__(
        self,
        ari_aggregates: [ARIAggregate],
        room_type_name_mappings: Dict[str, str],
    ):
        self.room_type_name_mappings: Dict[str, str] = room_type_name_mappings
        self.data = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        for ari_aggregate in ari_aggregates:
            self.add(ari_aggregate)

    def add(self, ari_aggregate: ARIAggregate):
        hotel_id = ari_aggregate.hotel_id
        rate_plan_info = ari_aggregate.rate_plan_info
        room_inventory_key = self.RoomInventoryGroupKey(
            ari_aggregate.room_type_details,
            ari_aggregate.inventory,
        )
        self.data[hotel_id][rate_plan_info][room_inventory_key].append(
            ari_aggregate.rate
        )

    def build(self):
        hotels_ari_dict = defaultdict(list)
        for hotel_id in self._get_all_hotel_ids():
            rate_plans = self._get_latest_rate_plan(self._get_rate_plans(hotel_id))
            # when we start supporting multiple rate plans replace the above line with the below line
            # rate_plans = self._get_rate_plans(hotel_id)
            for rate_plan_info in rate_plans:
                policies = self._build_policies(rate_plan_info)
                rates = self._build_rate_list(hotel_id, rate_plan_info)
                hotels_ari_dict[hotel_id].append(
                    self._build_ari_details(policies, rate_plan_info, rates)
                )
        return hotels_ari_dict

    @staticmethod
    def _get_latest_rate_plan(rate_plans: List[RatePlanInfo]):
        return [max(rate_plans, key=lambda x: x.created_at)] if rate_plans else []

    def _get_all_hotel_ids(self):
        return list(self.data.keys())

    def _get_rate_plans(self, hotel_id: str) -> List[RatePlanInfo]:
        return list(self.data[hotel_id].keys())

    def _get_room_inventory_details(
        self, hotel_id: str, rate_plan_info: RatePlanInfo
    ) -> List[RoomInventoryGroupKey]:
        return list(self.data[hotel_id][rate_plan_info].keys())

    def _get_rate_plan_rates(
        self,
        hotel_id: str,
        rate_plan_info: RatePlanInfo,
        room_inventory: RoomInventoryGroupKey,
    ) -> List[Rate]:
        return self.data[hotel_id][rate_plan_info][room_inventory]

    def _build_rate_list(self, hotel_id, rate_plan_info):
        rates = []
        for room_inventory_details in self._get_room_inventory_details(
            hotel_id, rate_plan_info
        ):
            room_type: HotelRoomType = room_inventory_details.room_type_details
            inventory: HotelRoomInventory = room_inventory_details.inventory
            rate_plan_rates = self._get_rate_plan_rates(
                hotel_id,
                rate_plan_info,
                room_inventory_details,
            )
            prices = self._build_price_items(rate_plan_rates)
            rates.append(self._build_rate_items(inventory, prices, room_type))
        return rates

    @staticmethod
    def _build_ari_details(policies, rate_plan_info, rates):
        return HotelARIDTO(
            rate_plan_name=rate_plan_info.external_rate_plan_code,
            base_currency=CurrencyType.INR.value,
            policies=policies,
            rates=rates,
        )

    def _build_price_items(self, rate_plan_rates):
        prices = []
        for rate in rate_plan_rates:
            tax = self.calculate_tax(rate.price)
            posttax = rate.price + tax
            prices.append(
                PriceDTO(
                    pretax=rate.price,
                    posttax=posttax,
                    tax=tax,
                    adult_count=rate.occupancy.adult_count,
                )
            )
        return prices

    @staticmethod
    def calculate_tax(pre_tax):
        # Accurate tax will be calculated only at the booking creation,
        # taking into account the customer's GST information.
        tax_percentage = '0.18' if pre_tax > 7500 else '0.12'
        return pre_tax * Decimal(tax_percentage)

    def _build_rate_items(self, inventory, prices, room_type):
        total_rooms = max(room_type.total_rooms - inventory.out_of_order, 0)
        rate = RateDTO(
            date=inventory.date,
            total_rooms=total_rooms,
            available_rooms=max(min(inventory.availability_count, total_rooms), 0),
            room_type=self.room_type_name_mappings.get(
                inventory.room_type, inventory.room_type
            ),
            prices=prices,
        )
        return rate

    @staticmethod
    def _build_policies(rate_plan_info):
        return PolicyDTO(
            cancellation_policies=rate_plan_info.cancellation_policies,
            child_policy=rate_plan_info.child_policy,
            payment_policies=rate_plan_info.payment_policies,
        )
