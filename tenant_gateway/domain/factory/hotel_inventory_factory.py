from tenant_gateway.domain.entities.hotel_room_inventory import HotelRoomInventory


class HotelInventoryFactory:

    @staticmethod
    def create_hotel_inventory(room_type_inventory_dto):
        out_of_order = 1 if room_type_inventory_dto.should_increment_dnr else room_type_inventory_dto.out_of_order
        return HotelRoomInventory(
            hotel_id=room_type_inventory_dto.hotel_id,
            room_type=room_type_inventory_dto.room_type,
            date=room_type_inventory_dto.date,
            out_of_order=out_of_order,
            out_of_service=0,
            availability_count=room_type_inventory_dto.availability_count,
        )
