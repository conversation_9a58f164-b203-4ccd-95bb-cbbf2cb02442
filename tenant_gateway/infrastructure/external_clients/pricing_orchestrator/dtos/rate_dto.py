from lxml.builder import E
import re

from tenant_gateway.domain.external_services.google_hotels_ads.constants import GHAMessageTypes, PackageData
from tenant_gateway.domain.external_services.google_hotels_ads.utils import XMLUtils


class RateDTO(object):
    xml_utils = XMLUtils()

    def __init__(
        self,
        property_id,
        start_date,
        end_date,
        room_id,
        post_tax_price,
        currency,
        guest_count,
        coupon_code=None,
    ):
        self.property_id = property_id
        self.start_date = start_date
        self.end_date = end_date
        self.room_id = room_id
        self.post_tax_price = post_tax_price
        self.currency = currency
        self.guest_count = guest_count
        self.coupon_code = coupon_code

    def to_json(self):
        return dict(start_date=self.start_date, end_date=self.end_date, room_type=self.room_id,
                    occupancy=self.guest_count, price=self.post_tax_price, coupon_code=self.coupon_code
                    )

    @classmethod
    def create_from_po_pricing_data(cls, hotel_id, po_rate_details, room_sku_dtos):
        rate_dtos, date_wise_prices = [], []
        sku_id_wise_room_sku_dto = {
            room_sku_dto.sku_id: room_sku_dto for room_sku_dto in room_sku_dtos
        }
        hotel_wise_price = po_rate_details.get("price_per_hotel", [])
        if hotel_wise_price:
            policy_wise_price = hotel_wise_price[0].get("hotel_price_per_policy", [])
            if policy_wise_price:
                date_wise_prices = policy_wise_price[0].get("sku_wise_prices", [])

        for date_wise_price in date_wise_prices:
            skus = date_wise_price.get("skus", [])
            for sku in skus:
                room_sku_dto = sku_id_wise_room_sku_dto.get(int(sku["sku_id"]))
                rate_dtos.append(
                    RateDTO(
                        property_id=hotel_id,
                        start_date=date_wise_price["date"],
                        end_date=date_wise_price["date"],
                        room_id=str(room_sku_dto.room_type).lower(),
                        post_tax_price=str(sku["post_tax_price"]),
                        currency="INR",
                        guest_count=cls._extract_adult_count_from_occupancy(room_sku_dto.occupancy),
                        coupon_code=sku.get("coupon_code"),
                    )
                )
        return rate_dtos

    @staticmethod
    def _extract_adult_count_from_occupancy(occupancy):
        occupancy_pattern = re.compile(r"^(\d+)-\d+")
        return str(occupancy_pattern.search(occupancy).group(1))

    @classmethod
    def to_xml(cls, room_stay_wise_rates, hotel_id):
        root = cls.xml_utils.generate_initial_xml(GHAMessageTypes.RATE.value)
        rate_amount_messages = E.RateAmountMessages(HotelCode=str(hotel_id))
        for po_stay_details_tuple, stay_rates in room_stay_wise_rates.items():
            rate_amount_message = cls._build_rate_amount_message_element(po_stay_details_tuple, stay_rates)
            rate_amount_messages.append(rate_amount_message)
        root.append(rate_amount_messages)
        rate_xml = cls.xml_utils.generate_xml_text(root)
        return rate_xml

    @classmethod
    def _build_rate_amount_message_element(cls, po_stay_details_tuple, stay_rates):
        rate_amount_message = E.RateAmountMessage(
            E.StatusApplicationControl(
                Start=po_stay_details_tuple.start_date,
                End=po_stay_details_tuple.end_date,
                InvTypeCode=po_stay_details_tuple.room_id,
                RatePlanCode=PackageData.TRB_DEFAULT['package_id'],
            ),
            E.Rates(
                E.Rate(
                    E.BaseByGuestAmts(
                        *[E.BaseByGuestAmt(
                            AmountAfterTax=stay_rate.post_tax_price,
                            CurrencyCode=stay_rate.currency,
                            NumberOfGuests=stay_rate.guest_count
                        ) for stay_rate in stay_rates]

                    )
                )
            )
        )
        return rate_amount_message
