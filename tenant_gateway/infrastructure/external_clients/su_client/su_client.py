import json
import logging

from flask import current_app

from object_registry import register_instance
from tenant_gateway.common.exceptions import ExternalServiceException
from tenant_gateway.common.slack_alert_helper import Slack<PERSON><PERSON>t
from tenant_gateway.domain.external_services.su.constants import SU_API_MAX_RETRY_COUNT
from tenant_gateway.domain.external_services.su.dtos.hotel_dtos import HotelPushDto
from tenant_gateway.domain.external_services.su.dtos.ota_mapping_dtos import OTAMappingDto
from tenant_gateway.domain.external_services.su.dtos.ota_setup_dtos import OTASetupDto
from tenant_gateway.domain.external_services.su.dtos.rate_plan_dtos import RatePlanPushDto
from tenant_gateway.domain.external_services.su.dtos.room_dtos import RoomPushDto
from tenant_gateway.infrastructure.external_clients.core.base_client import BaseExternalClient
from tenant_gateway.infrastructure.external_clients.su_client.dtos.acknowledge_reservation_dto import \
    AcknowledgeReservationDTO
from tenant_gateway.infrastructure.external_clients.su_client.dtos.update_noshow_dto import NoShowDTO
import time

logger = logging.getLogger(__name__)


@register_instance()
class SuClient(BaseExternalClient):
    page_map = {
        'create_or_update_hotel': dict(type=BaseExternalClient.CallTypes.POST,
                                       url_regex="/SUAPI/jservice/OTA_HotelDescriptiveContentNotif"),
        'push_rates_or_availability': dict(type=BaseExternalClient.CallTypes.POST,
                                           url_regex="/SUAPI/jservice/availability"),
        'acknowledge_reservation': dict(type=BaseExternalClient.CallTypes.POST,
                                        url_regex="/SUAPI/jservice/Reservation_notif"),
        'create_reporting': dict(type=BaseExternalClient.CallTypes.POST,
                                 url_regex="/SUAPI/jservice/reporting"),
        'get_update_logs': dict(type=BaseExternalClient.CallTypes.POST,
                                url_regex="/SUAPI/jservice/updatelog"),
        'create_or_update_room_types': dict(type=BaseExternalClient.CallTypes.POST,
                                            url_regex="/SUAPI/jservice/OTA_HotelRoom"),
        'create_or_update_rate_plans': dict(type=BaseExternalClient.CallTypes.POST,
                                            content=BaseExternalClient.ContentTypes.JSON,
                                            url_regex="/SUAPI/jservice/OTA_HotelRatePlan"),
        'create_ota_setup': dict(type=BaseExternalClient.CallTypes.POST,
                                 content=BaseExternalClient.ContentTypes.JSON,
                                 url_regex="/SUAPI/jservice/OTA_Setup?{ota_name}"),
        'create_ota_rate_plan_mapping': dict(type=BaseExternalClient.CallTypes.POST,
                                            content=BaseExternalClient.ContentTypes.JSON,
                                            url_regex="/SUAPI/jservice/OTA_RatePlanMap?{ota_name}")
    }

    def __init__(self):
        super().__init__(timeout=120)

    @staticmethod
    def custom_headers():
        return {"Authorization": current_app.config['SU_BASIC_AUTH_HEADER']}

    def get_domain(self):
        return current_app.config['SU_INTEGRATION_ENDPOINT_URL']

    def create_or_update_hotel(self, hotel_push_dto: HotelPushDto):
        page_name = "create_or_update_hotel"
        start_time = time.time()
        response = self.make_call(page_name=page_name,
                                  data=hotel_push_dto.to_su_payload(),
                                  custom_headers=self.custom_headers())
        logger.info(f"time taken for create_or_update_hotel {time.time() - start_time}")
        self.create_log(page_name, request_data=json.dumps(hotel_push_dto.to_su_payload()),
                        response_data=json.dumps(response.data), status_code=response.status_code)
        if not response.is_success():
            SlackAlert.send_alert(slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
                                  text=f"Su API Error In Create/Update Property. Status Code: {response.status_code}, "
                                       f"Errors: {response.data}, request data : {hotel_push_dto.to_su_payload()}, "
                                       f"custom_headers :{self.custom_headers()}, requested endpoint:{self.get_domain()}")
            raise ExternalServiceException(
                "Su API Error In Create/Update Property. Status Code: {0}, Errors: {1}".format(response.response_code,
                                                                                               response.data))
        return response.json_response

    def create_or_update_room_types(self, room_push_dto: RoomPushDto):
        page_name = "create_or_update_room_types"
        start_time = time.time()
        response = self.make_call(page_name=page_name, data=room_push_dto.to_su_payload(),
                                  custom_headers=self.custom_headers())
        logger.info(f"time taken for create_or_update_room_types {time.time() - start_time}")
        self.create_log(page_name, request_data=json.dumps(room_push_dto.to_su_payload()),
                        response_data=json.dumps(response.data), status_code=response.status_code)
        if not response.is_success():
            if response.data and "Room Already Exist" in response.data:
                return response.json_response
            if response.data and self.is_property_inactive_error(response.data):
                """
                Ignoring sentry and slack alert in the above error
                as these are non-actionable errors
                """
                return response.json_response
            SlackAlert.send_alert(slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
                                  text="Su API Error In Create/Update RoomType. Status Code: {0}, Errors: {1}".format(
                                      response.response_code, response.data))
            raise ExternalServiceException(
                "Su API Error In Create/Update RoomType. Status Code: {0}, Errors: {1}".format(response.response_code,
                                                                                               response.data))
        return response.json_response

    def push_rates_or_availability(self, data, retry_count=0):
        page_name = "push_rates_or_availability"
        start_time = time.time()
        try:
            response = self.make_call(page_name=page_name, data=data.to_su_payload(),
                                      custom_headers=self.custom_headers())
            logger.info(
                f"time taken for push_rates_or_availability {time.time() - start_time}")

            self.create_log(page_name, request_data=json.dumps(data.to_su_payload()),
                            response_data=json.dumps(response.data), status_code=response.status_code)
            if not response.is_success():
                if response.status_code in (502, 503, 504) and retry_count < SU_API_MAX_RETRY_COUNT:
                    self.push_rates_or_availability(data, retry_count=retry_count + 1)
                if not response.data or 'date has passed' not in response.data:
                    SlackAlert.send_alert(slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
                                          text=f"Su API Error In Push Rates and Availability. Status Code: "
                                               f"{response.status_code}, Errors: {response.errors}, "
                                               f"external_hotel_id: {data.hotel_id}, response :{response.json_response}")
                raise ExternalServiceException(
                    f"Su API Error In Push Availability. Status Code: {response.status_code}, Errors: {response.errors}")
            return response

        except Exception as e:
            logger.exception(
                f"Failed pushing rate or availability to SU for request data: {json.dumps(data.to_su_payload())},"
                f" headers : {self.custom_headers()} and error: {str(e)}")

    def acknowledge_reservation(self, ack_data: AcknowledgeReservationDTO):
        page_name = "acknowledge_reservation"
        start_time = time.time()
        response = self.make_call(page_name=page_name, data=ack_data.to_su_payload(),
                                  custom_headers=self.custom_headers())
        logger.info(
            f"time taken for acknowledge_reservation {time.time() - start_time}")
        self.create_log(page_name, request_data=json.dumps(ack_data.to_su_payload()),
                        response_data=json.dumps(response.data),
                        status_code=response.status_code)
        if not response.is_success():
            SlackAlert.send_alert(slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
                                  text=f"Su API Error In Acknowledge Reservation. Status Code: {response.status_code}, "
                                       f"Errors: {response.data}")
            raise ExternalServiceException(
                f"Su API Error In Acknowledge Reservation. Status Code: {response.status_code}, Errors: {response.data}")
        return response

    def create_reporting(self, noshow_data: NoShowDTO):
        page_name = "create_reporting"
        start_time = time.time()
        response = self.make_call(page_name=page_name, data=noshow_data.to_su_payload(),
                                  custom_headers=self.custom_headers())
        logger.info(f"time taken for create_reporting {time.time() - start_time}")
        self.create_log(page_name, request_data=json.dumps(noshow_data.to_su_payload()), response_data=json.dumps(
            response.data),
                        status_code=response.status_code)
        if not response.is_success():
            if response.data and 'invalid reservation_notif_id' in str(response.data).lower():
                """
                Ignoring sentry and slack alert in the above error
                as these are non-actionable errors
                """
                return response
            SlackAlert.send_alert(slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
                                  text=f"Su API Error In Create Reporting. Status Code: {response.status_code}, "
                                       f"Errors: {response.data}")
            raise ExternalServiceException(
                f"Su API Error In Create Reporting. Status Code: {response.status_code}, Errors: {response.data}")
        return response

    def get_update_logs(self, update_log_request_data):
        page_name = "get_update_logs"
        response = self.make_call(page_name=page_name, data=update_log_request_data,
                                  custom_headers=self.custom_headers())
        self.create_log(page_name, request_data=json.dumps(update_log_request_data),
                        response_data=json.dumps(response.data), status_code=response.status_code)
        if not response.is_success():
            SlackAlert.send_alert(slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
                                  text=f"Su API Error In update log. Status Code: {response.status_code}, Errors: "
                                       f"{response.data}")
            raise ExternalServiceException(
                "Su API Error In update log. Status Code: {0}, Errors: {1}".format(response.response_code,
                                                                                   response.data))
        return response.json_response

    def create_or_update_rate_plans(self, rate_plan_push_dto: RatePlanPushDto):
        page_name = "create_or_update_rate_plans"
        start_time = time.time()
        response = self.make_call(page_name=page_name, data=rate_plan_push_dto.to_su_payload(),
                                  custom_headers=self.custom_headers())
        logger.info(f"time taken for create_or_update_rate_plans {time.time() - start_time}")

        self.create_log(page_name, request_data=json.dumps(rate_plan_push_dto.to_su_payload()),
                        response_data=json.dumps(response.data), status_code=response.status_code)
        if not response.is_success() and not self.is_property_inactive_error(response.data):
            SlackAlert.send_alert(slack_webhook_url=current_app.config['SU_SLACK_WEBHOOK_URL'],
                                  text=f"Su API Error In create/update rate plan Status Code: {response.status_code}, "
                                       f"Errors: {response.data}")
            raise ExternalServiceException(
                "Su API Error In Create/Update RoomType. Status Code: {0}, Errors: {1}".format(response.response_code,
                                                                                               response.data))
        return response.json_response

    def create_ota_setup(self, setup_dtos: [OTASetupDto]):
        page_name = "create_ota_setup"
        errors = []
        for setup_dto in setup_dtos:
            request_data = setup_dto.to_su_payload()
            url_parameters = dict(ota_name=setup_dto.ota_name)
            response = self.make_call(page_name=page_name, data=request_data,
                                      url_parameters=url_parameters,
                                      custom_headers=self.custom_headers())
            if not response.is_success():
                errors.append({"request_payload": request_data, "response_data": json.dumps(response.data)})
        if errors:
            logger.exception('Some errors occurred in creating setup: ', errors)
        return errors

    def create_ota_rate_plan_mapping(self, mapping_dtos: [OTAMappingDto]):
        page_name = "create_ota_rate_plan_mapping"
        errors = []
        for mapping_dto in mapping_dtos:
            request_data = mapping_dto.to_su_payload()
            url_parameters = dict(ota_name=mapping_dto.ota_name)
            response = self.make_call(page_name=page_name, data=request_data,
                                      url_parameters=url_parameters,
                                      custom_headers=self.custom_headers())
            if not response.is_success():
                errors.append({"request_payload": request_data, "response_data": json.dumps(response.data)})
        if errors:
            logger.exception('Some errors occurred in creating setup: ', errors)

        return errors

    @staticmethod
    def is_property_inactive_error(error_data):
        return bool(error_data.get('text') and "property is inactive" in error_data['text'].lower())
