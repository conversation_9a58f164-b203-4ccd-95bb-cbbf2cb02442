from tenant_gateway.infrastructure.external_clients.company_profile_service.company_profile_constants import \
    CompanyProfileConstants, CompanyProfileStatus


class CompanyProfileParentEntity:
    def __init__(self, legal_entity_name, trade_name, external_profile_id, client_internal_code, registered_address,
                 communication_address,
                 phone_number, email_id,
                 point_of_contacts, hotel_id, status=CompanyProfileStatus.COMPLETE.value,
                 category=CompanyProfileConstants.NOT_AVAILABLE, sub_category=CompanyProfileConstants.NOT_AVAILABLE
                 ):
        self.legal_entity_name = legal_entity_name
        self.trade_name = trade_name
        self.category = category
        self.sub_category = sub_category
        self.registered_address = registered_address
        self.communication_address = communication_address
        self.email_id = email_id
        self.phone_number = phone_number
        self.point_of_contacts = point_of_contacts
        self.status = status
        self.hotel_id = hotel_id
        self.external_profile_id = external_profile_id
        self.client_internal_code = client_internal_code

    def to_dict(self):
        return dict(legal_entity_name=self.legal_entity_name, trade_name=self.trade_name, category=self.category,
                    sub_category=self.sub_category, registered_address=self.registered_address,
                    communication_address=self.communication_address,
                    email_id=self.email_id, phone_numer=self.phone_number,
                    point_of_contacts=self.point_of_contacts, status=self.status, hotel_id=self.hotel_id,
                    external_profile_id=self.external_profile_id,
                    client_internal_code=self.client_internal_code)


class CompanyProfileSubEntity:
    def __init__(self, legal_entity_name, trade_name, external_profile_id, client_internal_code, registered_address,
                 communication_address,
                 phone_number, email_id,
                 point_of_contacts, hotel_id, status=CompanyProfileStatus.COMPLETE.value
                 ):
        self.legal_entity_name = legal_entity_name
        self.trade_name = trade_name
        self.registered_address = registered_address
        self.communication_address = communication_address
        self.email_id = email_id
        self.phone_number = phone_number
        self.point_of_contacts = point_of_contacts
        self.status = status
        self.hotel_id = hotel_id
        self.external_profile_id = external_profile_id
        self.client_internal_code = client_internal_code

    def to_dict(self):
        return dict(legal_entity_name=self.legal_entity_name, trade_name=self.trade_name,
                    registered_address=self.registered_address,
                    communication_address=self.communication_address,
                    email_id=self.email_id, phone_numer=self.phone_number,
                    point_of_contacts=self.point_of_contacts, status=self.status, hotel_id=self.hotel_id,
                    external_profile_id=self.external_profile_id,
                    client_internal_code=self.client_internal_code)

