from tenant_gateway.domain.entities.rate import Rate
from tenant_gateway.domain.models import RatePlanRateModel
from tenant_gateway.domain.value_objects.occupancy import OccupancyVO


class RatePlanRateAdapter(object):
    @staticmethod
    def to_domain_entity(db_models: [RatePlanRateModel]):
        return [Rate(
            rate_plan_id=d.rate_plan_id, property_id=d.property_id,
            room_type_id=d.room_type_id, occupancy=OccupancyVO(d.adult_count), price=d.price,
            stay_date=d.stay_date, ) for d in
            db_models]

    @staticmethod
    def to_db_entity(domain_entities: [Rate]):
        return [RatePlanRateModel(
            rate_plan_id=e.rate_plan_id,
            property_id=e.property_id,
            room_type_id=e.room_type_id,
            adult_count=e.occupancy.adult_count,
            price=e.price,
            stay_date=e.stay_date, )
            for e in domain_entities]

    @staticmethod
    def to_entity(db_model: RatePlanRateModel) -> Rate:
        # creating this new method because to_domain_entity supports only list of items
        # and has multiple usage
        return Rate(
            rate_plan_id=db_model.rate_plan_id,
            property_id=db_model.property_id,
            room_type_id=db_model.room_type_id,
            occupancy=OccupancyVO(db_model.adult_count),
            price=db_model.price,
            stay_date=db_model.stay_date,
        )
