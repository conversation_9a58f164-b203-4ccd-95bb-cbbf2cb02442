from tenant_gateway.domain.entities.ota_commission_entity import OtaCommissionEntity
from tenant_gateway.domain.models import OTACommissionModel


class OtaCommissionAdaptor:
    @staticmethod
    def to_db_model(domain_entity: OtaCommissionEntity):
        return OTACommissionModel(id=domain_entity.id,
                                  service_code=domain_entity.service_code,
                                  external_hotel_id=domain_entity.external_hotel_id,
                                  tenant_id=domain_entity.tenant_id,
                                  ota_code=domain_entity.ota_code,
                                  internal_hotel_id=domain_entity.internal_hotel_id,
                                  start_date=domain_entity.start_date,
                                  end_date=domain_entity.end_date,
                                  commission_type=domain_entity.commission_type,
                                  deleted=domain_entity.deleted,
                                  commission=domain_entity.commission,
                                  created_at=domain_entity.created_at)

    @staticmethod
    def to_domain_entity(db_model: OTACommissionModel):
        return OtaCommissionEntity(id=db_model.id,
                                   service_code=db_model.service_code,
                                   external_hotel_id=db_model.external_hotel_id,
                                   tenant_id=db_model.tenant_id,
                                   ota_code=db_model.ota_code,
                                   internal_hotel_id=db_model.internal_hotel_id,
                                   start_date=db_model.start_date,
                                   end_date=db_model.end_date,
                                   commission_type=db_model.commission_type,
                                   commission=db_model.commission,
                                   deleted=db_model.deleted,
                                   created_at=db_model.created_at,
                                   modified_at=db_model.modified_at)
