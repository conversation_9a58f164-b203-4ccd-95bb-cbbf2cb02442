from tenant_gateway.domain.entities.hotel_sku_mapping_entity import HotelSkuMappingEntity
from tenant_gateway.domain.models import HotelSkuMappingModel


class HotelSkuMappingAdaptor(object):

    @staticmethod
    def to_db_model(domain_entity):
        return HotelSkuMappingModel(
                                    internal_hotel_id=domain_entity.internal_hotel_id,
                                    external_hotel_id=domain_entity.external_hotel_id,
                                    sku_id=domain_entity.sku_id,
                                    is_active=domain_entity.is_active,
                                    sku_name=domain_entity.sku_name,
                                    tenant_id=domain_entity.tenant_id,
                                    created_at=domain_entity.created_at, modified_at=domain_entity.modified_at)

    @staticmethod
    def to_domain_entity(db_model):
        return HotelSkuMappingEntity(
                                     internal_hotel_id=db_model.internal_hotel_id,
                                     external_hotel_id=db_model.external_hotel_id,
                                     sku_id=db_model.sku_id,
                                     is_active=db_model.is_active,
                                     sku_name=db_model.sku_name,
                                     tenant_id=db_model.tenant_id,
                                     created_at=db_model.created_at, modified_at=db_model.modified_at)
