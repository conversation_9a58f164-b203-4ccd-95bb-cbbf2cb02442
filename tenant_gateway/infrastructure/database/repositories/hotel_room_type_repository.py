import logging
from typing import List

from object_registry import register_instance
from tenant_gateway.domain.entities.hotel_room_type import HotelRoomType
from tenant_gateway.domain.models import HotelRoomTypeModel
from tenant_gateway.infrastructure.database.adaptors.hotel_room_type_adaptor import (
    HotelRoomTypeAdaptor,
)
from tenant_gateway.infrastructure.database.repositories.base_repository import (
    BaseRepository,
)
from tenant_gateway.common.exceptions import DatabaseError

logger = logging.getLogger(__name__)


@register_instance()
class HotelRoomTypeRepository(BaseRepository):
    _model = HotelRoomTypeModel
    _adaptor = HotelRoomTypeAdaptor()

    def create(self, hotel_room_type: HotelRoomType):
        hotel_room_type_models = self._adaptor.to_db_entity(hotel_room_type)
        try:
            self._save(hotel_room_type_models)
        except Exception:
            raise DatabaseError

    def update(self, hotel_room_type: HotelRoomType):
        hotel_room_type_models = self._adaptor.to_db_entity(hotel_room_type)
        try:
            self._update(hotel_room_type_models)
        except Exception as ex:
            raise DatabaseError(message=ex.__str__())

    def create_all(self, hotel_room_type_configs: List[HotelRoomType]):
        hotel_room_type_configs_models = [
            self._adaptor.to_db_entity(domain_entity=hotel_room_type_config)
            for hotel_room_type_config in hotel_room_type_configs
        ]
        try:
            self._save_all(hotel_room_type_configs_models)
        except Exception as ex:
            raise DatabaseError(message=ex.__str__())

    def update_all(self, hotel_room_types: List[HotelRoomType]):
        hotel_room_type_configs_models = [
            self._adaptor.to_db_entity(domain_entity=hotel_room_type_config)
            for hotel_room_type_config in hotel_room_types
        ]
        self._update_all(hotel_room_type_configs_models)

    def load_for_update(self, hotel_id, room_type_code):
        return self._adaptor.to_domain_entity(
            self.get_for_update(
                HotelRoomTypeModel, hotel_id=hotel_id, room_type_code=room_type_code
            )
        )

    def load_hotel_room_types(self, hotel_id, room_type_codes=None, is_active=True):
        query = self.query(self._model).filter(
            self._model.hotel_id == hotel_id,
        )
        if is_active is not None:
            query = query.filter(self._model.is_active == is_active)
        if room_type_codes:
            query = query.filter(self._model.room_type_code.in_(room_type_codes))
        hotel_room_type_models = query.all()
        return [
            self._adaptor.to_domain_entity(model) for model in hotel_room_type_models
        ]

    def get_distinct_hotel(self):
        hotel_room_type_models = (
            self.query(self._model).distinct(self._model.hotel_id).all()
        )
        return [
            self._adaptor.to_domain_entity(model) for model in hotel_room_type_models
        ]
