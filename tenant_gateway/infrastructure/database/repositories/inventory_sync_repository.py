from typing import List

from object_registry import register_instance
from tenant_gateway.domain.entities.inventory_sync_entity import InventorySyncEntity
from tenant_gateway.domain.models import InventorySyncModel
from tenant_gateway.infrastructure.database.adaptors.inventory_sync_adaptor import InventorySyncAdaptor
from tenant_gateway.infrastructure.database.repositories.base_repository import BaseRepository


@register_instance()
class InventorySyncRepository(BaseRepository):

    def save(self, domain_entity: InventorySyncEntity):
        db_model = InventorySyncAdaptor.to_db_model(domain_entity)
        self._save(db_model)

    def save_all(self, domain_entities: List[InventorySyncEntity]):
        db_models = [InventorySyncAdaptor.to_db_model(e) for e in domain_entities]
        self._save_all(db_models)

    def exists(self, event_id):
        return bool(self.session().query(InventorySyncModel).filter(InventorySyncModel.event_id == event_id,
                                                                    InventorySyncModel.success == True).first())  # noqa

    def if_inventory_already_synced(self, event_id, service_code):
        return bool(self.session().query(InventorySyncModel).filter(InventorySyncModel.event_id == event_id,
                                                                    InventorySyncModel.success == True,
                                                                    InventorySyncModel.service_code == service_code).first())
