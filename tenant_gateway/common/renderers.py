import abc
import json

from treebo_commons.utils import dateutils


@abc.abstractmethod
class ConfigValueRenderer(object):
    @abc.abstractmethod
    def render(self, value):
        return value


class IntegerRenderer(ConfigValueRenderer):
    def render(self, value):
        return str(value)


class StringRenderer(ConfigValueRenderer):
    def render(self, value):
        return str(value)


class JsonRenderer(ConfigValueRenderer):
    def render(self, value):
        return json.dumps(value)


class BooleanRenderer(ConfigValueRenderer):
    def render(self, value):
        return "true" if value else "false"


class ListRenderer(ConfigValueRenderer):
    def render(self, value):
        return str(value)


class DateRenderer(ConfigValueRenderer):
    def render(self, value):
        return dateutils.date_to_ymd_str(value)
