aenum==3.1.11
amqp==5.2.0
asgiref==3.7.2
attrs==22.2.0
Babel==2.11.0
beautifulsoup4==4.12.2
boto3==1.34.37
botocore==1.34.37
cached-property==1.3.1
cachetools==5.3.2
cairocffi==1.6.1
CairoSVG==2.6.0
certifi==2022.12.7
cffi==1.15.1
chardet==3.0.4
charset-normalizer==3.3.2
click==8.1.3
cmake==3.27.7
coreapi==2.3.3
coreschema==0.0.4
cryptography==3.4.8
cssselect2==0.7.0
defusedxml==0.7.1
diff-match-patch==20200713
dj-database-url==0.4.0
Django==3.2
django-auditlog==3.0.0
django-autocomplete-light==3.9.3
django-bootstrap3==23.4
django-bulk-update==2.2.0
django-configurations==2.5
django-currentuser==0.6.1
django-extensions==3.1.1
django-fernet-fields==0.6
django-filter==2.4.0
django-grappelli==2.15.1
django-health-check==3.17.0
django-import-export==2.5.0
django-lrucache-backend==0.2.1
django-model-utils==4.0.0
django-request-id==1.0.0
django-rest-swagger==2.2.0
django-secure==1.0.1
# TODO: Remove below requirement after migrations cleanup
django-simple-history==3.4.0
django-storages==1.9.1
djangorestframework==3.14.0
djangorestframework-camel-case==1.3.0
djutil==0.2
docutils==0.15.2
drfdocs==0.0.11
enum34==1.1.6
et-xmlfile==1.1.0
factory-boy==2.10.0
Faker==17.3.0
flatten-json==0.1.7
geographiclib==1.52
geopy==2.2.0
gevent==23.9.1
google-auth==2.23.3
googlemaps==3.0.2
greenlet==3.0.3
gunicorn==19.9.0
html5lib==1.1
htmlmin==0.1.10
idna==2.6
IMAPClient==2.3.1
importlib-metadata==4.12.0
itypes==1.2.0
Jinja2==3.1.2
jmespath==0.10.0
jsonfield==3.1.0
kombu==5.3.5
lib==4.0.0
logstash-formatter==0.5.16
lru-dict==1.1.8
lxml==5.1.0
mail-parser==3.15.0
Markdown==3.5
MarkupPy==1.14
MarkupSafe==2.1.3
marshmallow==3.20.2
mock==2.0.0
more-itertools==9.1.0
newrelic==8.7.0
oauthlib==3.2.2
odfpy==1.4.1
openapi-codec==1.3.2
openpyxl==3.1.2
packaging==23.2
pbr==5.11.1
phonenumbers==8.10.17
pika==1.3.1
Pillow==9.4.0
pluggy==0.6.0
psycogreen==1.0
psycopg2-binary==2.9.9
py==1.11.0
pyasn1==0.5.0
pyasn1-modules==0.3.0
pycparser==2.21
pygrok==1.0.0
PyJWT==2.8.0
pymemcache==4.0.0
pymongo==4.0.2
pyparsing==3.0.9
pyphen==0.13.2
pytest==3.5.0
pytest-django==3.2.1
python-dateutil==2.8.0
pytz==2024.1
PyYAML==6.0.1
regex==2022.10.31
requests==2.31.0
requests-oauthlib==0.8.0
rsa==4.9
s3transfer==0.10.0
sentry-sdk==1.9.0
simplejson==3.17.0
six==1.16.0
slackclient==1.1.0
soupsieve==2.5
SQLAlchemy==1.4.0
sqlparse==0.4.4
tablib==3.3.0
tinycss2==1.2.1
treebo-csv-uploader==1.0.15
treebo-health-check==3.0.0
treebo-logging-config==1.0.4
treebo-rewards-p3==1.2.1
typing_extensions==4.5.0
uritemplate==4.1.1
urllib3==1.25.4
vine==5.1.0
WeasyPrint==51
web-sku==0.4.65
webencodings==0.5.1
websocket-client==0.59.0
xlrd==2.0.1
xlwt==1.3.0
zipp==3.15.0
zope.event==4.6
zope.interface==5.5.2
