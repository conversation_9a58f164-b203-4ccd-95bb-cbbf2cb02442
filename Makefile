isvirtualenv:
	@if [ -z "$(VIRTUAL_ENV)" ]; then echo "ERROR: Not in a virtualenv." 1>&2; exit 1; fi

clean-pyc:
	find . -name '*.pyc' -exec rm --force {} +
	find . -name '*.pyo' -exec rm --force {} +

utest:
	pytest --color=yes prometheus/tests

itest:
	pytest --color=yes prometheus/itests

qatest:
	pytest --color=yes prometheus/integration_tests

test: utest itest qatest

lint:
	flake8

run-crs:
	./local.sh
	FLASK_APP=prometheus/wsgi.py flask run

run-pos:
	./local.sh
	FLASK_APP=pos/wsgi.py flask run

utestcovweb:
	coverage erase
	coverage run --omit=*/repositories/* --source=prometheus/domain/ -m pytest prometheus/tests
	coverage html --fail-under=40
	open coverage_html_report/index.html

help:
	@echo "    clean-pyc"
	@echo "        Remove *.pyc files."
	@echo "    lint"
	@echo "        Check style with flake8."
	@echo "    utest"
	@echo "        Run unit tests"
	@echo "    itest"
	@echo "        Run itests"
	@echo "    test"
	@echo "        Run unit tests and itests"
	@echo "    run-crs"
	@echo "        Run CRS service on your local machine."
	@echo "    run-pos"
	@echo "        Run POS Backend service on your local machine."
