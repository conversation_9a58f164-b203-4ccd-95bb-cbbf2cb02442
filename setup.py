#!/usr/bin/env python
# encoding: utf-8
from setuptools import setup, find_packages
from setuptools.dist import Distribution


class BinaryDistribution(Distribution):
    def is_pure(self):
        return True


setup(
    name='b2b',
    version='0.1.0',
    author="<PERSON><PERSON><PERSON><PERSON>",
    author_email="<EMAIL>",
    url='api.treebo.com/b2b',
    description="B2B Corporate Bookings",
    include_package_data=True,
    distclass=BinaryDistribution,
    package_dir={'': 'b2b'},
    packages=find_packages('b2b'),
    py_modules=['wsgi', 'manage', 'urls'],
    install_requires='',
    extras_require={},
    scripts=[],
    zip_safe=False,
    license='Proprietary',
    classifiers=[
        'Programming Language :: Python :: 2',
        'Intended Audience :: Developers',
    ],
    setup_requires=['wheel']
)
