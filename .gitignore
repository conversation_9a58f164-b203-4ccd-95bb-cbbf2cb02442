*.py[cod]
*.sw*
.idea/
.pytest_cache/

# migrations/
db_creds.json

coverage.xml
htmlcov/

# C extensions
*.so

# Packages
*.egg
*.egg-info
build
eggs
parts
bin
var
sdist
develop-eggs
.installed.cfg
lib
lib64

# Installer logs
pip-log.txt

# Unit test / coverage reports
.coverage
.tox
nosetests.xml

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# Complexity
output/*.html
output/*/index.html

# Sphinx
docs/_build

.webassets-cache

# Virtualenvs
env/

# npm
/node_modules/

# webpack-built files
/rackrate_service/static/build/
/rackrate_service/webpack/manifest.json
/rackrate_service/config/local.py
