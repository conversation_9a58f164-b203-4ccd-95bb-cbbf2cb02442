package_name = 'create package'
package_uri = '/rate-manager/v1/packages'

update_room_base_rate = 'update room base rate'
update_room_base_rate_uri = '/rate-manager/v1/room-base-rates'

get_room_base_rate = 'get room base rate'
get_room_base_rate_uri = 'rate-manager/v1/room-base-rates?property_id={}&start_date={}&end_date={}&' \
                         'room_category_max_occupancies={}&compressed={}'

update_inclusion_base_rate = 'update inclusion base rate'
update_inclusion_base_rate_uri = '/rate-manager/v1/inclusion-base-rates'

get_inclusion_base_rate = 'get inclusion base rate'
get_inclusion_base_rate_uri = 'rate-manager/v1/inclusion-base-rates?property_id={}&start_date={}&end_date={}&' \
                              'sku_ids={}&compressed={}'

rate_plan_name = 'create rate plan'
rate_plan_uri = '/rate-manager/v1/rate-plans'

update_rate_plan_name = 'update rate plan'
update_rate_plan_uri = '/rate-manager/v1/rate-plans/{}'

update_rate_plan_price_name = 'Update rate plan price'
update_rate_plan_rate_uri = '/rate-manager/v1/rate-plans/{}/rates'

get_rate_plan_rate_uri = '/rate-manager/v1/rate-plans/{}/rates?property_id={}&start_date={}&end_date={' \
                         '}&room_type_max_occupancies={}&compressed={}'

stop_sell = 'add or delete or get stop sell'
stop_sell_uri = '/rate-manager/v1/rate-plans/{}/stop-sells'

remove_override = "remove override"
remove_override_uri = '/rate-manager/v1/remove-override'

replicate_rate_plan_uri = '/rate-manager/v1/rate-plans/{}/replicate'

add_comment = 'add new comment'
add_comment_uri = '/rate-manager/v1/comments'

get_comment_rate_plan = '/rate-manager/v1/comments?property_id={}&start_date={}&end_date={}' \
                        '&room_type_with_max_occupancy={}&rate_plan_id={}'

get_comment_room_type = '/rate-manager/v1/comments?property_id={}&start_date={}&end_date={}' \
                        '&room_type_with_max_occupancy={}'

get_comment_inclusion = '/rate-manager/v1/comments?property_id={}&start_date={}&end_date={}' \
                        '&sku_id={}'

company_profile_uri = '/rate-manager/v1/rate-plans/{}/company-profiles-mappings'

get_rate_plan_by_company_profile_uri = '/rate-manager/v1/rate-plans?company_profile_id={}'

get_prices_for_rooms = "/rate-manager/v1/get-prices-for-rooms"

get_rate_plan_details = "get rate plan API"
get_rate_plan_details_uri = "/rate-manager/v1/rate-plans/{}"

get_package_details = "get package to validated prices for pricing API"
get_package_details_uri = "/rate-manager/v1/packages/{}"

"Get Rate Plan for a given property across channel and sub-channel"
get_rate_plan_for_property = '/rate-manager/v1/rate-plans?property_id={property_id}&' \
                             'channel_code={channel_code}&sub_channel_code={subchannel_code}'
get_audit_rate_plan_uri = '/rate-manager/v1/rate-audit-trails?property_id={property_id}'