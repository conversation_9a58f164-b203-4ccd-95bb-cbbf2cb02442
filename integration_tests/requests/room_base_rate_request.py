import json

from integration_tests.utilities import excel_utils
from integration_tests.builders import update_room_base_builder
from integration_tests.config import sheet_names
from integration_tests.config.request_uris import update_room_base_rate_uri, get_room_base_rate_uri
from integration_tests.requests.base_requests import BaseRequest
from integration_tests.utilities.common_utils import del_none, return_date
from tests.mockers import mock_role_manager


class RoomBaseRateRequest(BaseRequest):
    def update_room_base_rate(self, client, test_case_id, status_code):
        request_json = json.dumps(del_none(update_room_base_builder.UpdateRoomBase(
            sheet_names.update_room_base_rate_sheet_name, test_case_id).__dict__))
        with mock_role_manager():
            response = self.request_processor(client, 'POST', update_room_base_rate_uri, status_code,
                                          request_json, user_type= 'super-admin')
        return response.json

    def get_room_base_rate(self, client, test_case_id, status_code, room_type_index, compressed=True):
        test_data = excel_utils.get_test_case_data(sheet_names.update_room_base_rate_sheet_name, test_case_id)[0]
        if test_data['expected_date']:
            uri = get_room_base_rate_uri.format(test_data['property_id'], return_date(int(test_data['get_start_date'])),
                                                return_date(
                                                    int(test_data['expected_date']) + int(test_data['get_start_date'])),
                                                str(test_data['room_types_with_occupancy']).split(",")[room_type_index],
                                                compressed)
            with mock_role_manager():
                response = self.request_processor(client, 'GET', uri, status_code, user_type= 'super-admin')
        else:
            uri = get_room_base_rate_uri.format(test_data['property_id'], return_date(int(test_data['get_start_date'])),
                                                return_date(int(test_data['get_end_date'])),
                                                str(test_data['room_types_with_occupancy']).split(",")[room_type_index],
                                                compressed)
            with mock_role_manager():
                response = self.request_processor(client, 'GET', uri, status_code, user_type= 'super-admin')
        return response.json

    def get_room_base_rate_linkage(self, client, property_id, start_date, end_date, room_type_id, status_code,
                                   compressed=True):
        uri = get_room_base_rate_uri.format(property_id, start_date, end_date, str(room_type_id),
                                            compressed)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, user_type='super-admin')
        return response.json
