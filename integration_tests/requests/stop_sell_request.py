import json

from integration_tests.builders import stop_sell_builder
from integration_tests.config.request_uris import stop_sell_uri
from integration_tests.config.sheet_names import stop_sell_sheet_name
from integration_tests.requests.base_requests import BaseRequest
from integration_tests.utilities.common_utils import del_none


class StopSellRequest(BaseRequest):
    def create_stop_sell(self, client, test_case_id, status_code):
        request_json = json.dumps(
            del_none(
                stop_sell_builder.StopSellBuilder(stop_sell_sheet_name, test_case_id).__dict__))
        uri = stop_sell_uri.format(self.rate_plan_id)
        response = self.request_processor(client, 'POST', uri, status_code, request_json)
        return response.json

    def delete_stop_sell(self, client, status_code):
        uri = stop_sell_uri.format(self.rate_plan_id)
        response = self.request_processor(client, 'DELETE', uri, status_code)
        return response.json
