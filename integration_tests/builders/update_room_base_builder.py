from integration_tests.builders.common_builders import DateRangeForPriceCopy, DowPrices, Linkage, RoomTypeOccupancy
from integration_tests.config import sheet_names
from integration_tests.utilities import excel_utils
from integration_tests.utilities.common_utils import sanitize_blank, return_date


class UpdateRoomBase:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        if test_data['company_profile_id']:
            self.company_profile_id = sanitize_blank(test_data['company_profile_id'])
        if test_data['persistent_override'] == 'TRUE':
            self.persistent_override = True
        else:
            self.persistent_override = False
        if test_data['price']:
            self.price = sanitize_blank(test_data['price'])
        self.property_id = sanitize_blank(test_data['property_id'])
        if test_data['date_range_for_price_copy']:
            self.date_range_for_price_copy = DateRangeForPriceCopy(test_data).__dict__
        if test_data['dow_prices']:
            self.dow_prices = DowPrices(sheet_names.dow_prices_sheet_name, test_data['dow_prices']).__dict__
        self.linkage = Linkage(sheet_names.linkage_sheet_name, test_data['linkage']).__dict__ if test_data[
            'linkage'] else None
        self.room_types_with_occupancy = []
        for room_type_data in test_data['room_types_with_occupancy_case'].split(","):
            self.room_types_with_occupancy.append(
                RoomTypeOccupancy(sheet_names.other_room_category_linkage_detail, room_type_data).__dict__)
        if test_data['start_date']:
            self.start_date = sanitize_blank(return_date(test_data['start_date']))
        if test_data['end_date']:
            self.end_date = sanitize_blank(return_date(test_data['end_date']))
