create_addon_request = [
    {
        "charge_checkin": True,
        "charge_checkout": True,
        "charge_other_days": True,
        "name": "name",
        "posttax_price": 100.0,
        "pretax_price": 90.0,
        "quantity": 1,
        "room_stay_id": 'MAPLE',
        "sku_category_id": 101,
        "tax": 10.0,
    }
]

create_addon_response = [
    {
        "charge_checkin": True,
        "charge_checkout": True,
        "charge_other_days": True,
        "id": "1",
        "name": "name",
        "posttax_price": 100.0,
        "pretax_price": 90.0,
        "quantity": 1,
        "room_stay_id": 'MAPLE',
        "sku_category_id": 101,
        "status": "CREATED",
        "tax": 10.0,
    }
]

get_room_response = {
    "actual_checkin_date": "2018-04-20T06:40:34",
    "actual_checkout_date": "2018-04-20T06:40:34",
    "booking_id": "CRS-1234",
    "charge_ids": [0],
    "checkin_date": "2018-04-20T06:40:34",
    "checkout_date": "2018-04-20T06:40:34",
    "guest_stays": [
        {
            "actual_checkin_date": "2018-04-20T06:40:34",
            "actual_checkout_date": "2018-04-20T06:40:34",
            "age_group": "ADULT",
            "booking_version_id": 0,
            "checkin_date": "2018-04-20T06:40:34",
            "checkout_date": "2018-04-20T06:40:34",
            "guest_allocation": {
                "assigned_by": "string",
                "checkin_date": "2018-04-20T06:40:34",
                "checkout_date": "2018-04-20T06:40:34",
                "guest_id": "string",
                "id": 0,
            },
            "guest_allocation_history": [
                {
                    "assigned_by": "string",
                    "checkin_date": "2018-04-20T06:40:34",
                    "checkout_date": "2018-04-20T06:40:34",
                    "guest_id": "string",
                    "id": 0,
                }
            ],
            "id": 567,
            "status": "RESERVED",
        }
    ],
    "id": 987,
    "room_allocation": {
        "assigned_by": "string",
        "checkin_date": "2018-04-20T06:40:34",
        "checkout_date": "2018-04-20T06:40:34",
        "id": 0,
        "room_id": 0,
        "room_type_id": "string",
    },
    "room_allocation_history": [
        {
            "assigned_by": "string",
            "checkin_date": "2018-04-20T06:40:34",
            "checkout_date": "2018-04-20T06:40:34",
            "id": 0,
            "room_id": 0,
            "room_type_id": "string",
        }
    ],
    "room_type_id": "MAPLE",
    "status": "RESERVED",
    "type": "NIGHT",
}

update_room_request = {
    "booking_version_id": 1,
    "checkin_date": "2018-04-20T06:40:34",
    "checkout_date": "2018-04-20T06:40:34",
    "prices": [
        {
            "applicable_date": "2018-04-20T06:40:34",
            "bill_to_type": "COMPANY",
            "posttax_amount": 1100.0,
            "pretax_amount": 100.0,
            "type": "CREDIT",
        }
    ],
    "room_type_id": "CS-room-id-12",
    "type": "NIGHT",
}

update_room_response = {
    "actual_checkin_date": "2018-04-20T06:40:34",
    "actual_checkout_date": "2018-04-20 06:40:34",
    "booking_id": "CRS-1234",
    "charge_ids": [0],
    "checkin_date": "2018-04-20T06:40:34",
    "checkout_date": "2018-04-20T06:40:34",
    "guest_stays": [
        {
            "actual_checkin_date": "2018-04-20T06:40:34",
            "actual_checkout_date": "2018-04-20T06:40:34",
            "age_group": "ADULT",
            "booking_version_id": 0,
            "checkin_date": "2018-04-20T06:40:34",
            "checkout_date": "2018-04-20T06:40:34",
            "guest_allocation": {
                "assigned_by": "string",
                "checkin_date": "2018-04-20T06:40:34",
                "checkout_date": "2018-04-20T06:40:34",
                "guest_id": "string",
                "id": 0,
            },
            "guest_allocation_history": [
                {
                    "assigned_by": "string",
                    "checkin_date": "2018-04-20T06:40:34",
                    "checkout_date": "2018-04-20T06:40:34",
                    "guest_id": "string",
                    "id": 0,
                }
            ],
            "id": 0,
            "status": "RESERVED",
        }
    ],
    "id": 0,
    "room_allocation": {
        "assigned_by": "string",
        "checkin_date": "2018-04-20T06:40:34",
        "checkout_date": "2018-04-20T06:40:34",
        "id": 0,
        "room_id": 0,
        "room_type_id": "string",
    },
    "room_allocation_history": [
        {
            "assigned_by": "string",
            "checkin_date": "2018-04-20T06:40:34",
            "checkout_date": "2018-04-20T06:40:34",
            "id": 0,
            "room_id": 0,
            "room_type_id": "string",
        }
    ],
    "room_type_id": "MAPLE",
    "status": "RESERVED",
    "type": "NIGHT",
}

delete_rooms_response = {
    "actual_checkin_date": "2018-04-20T11:48:41.544Z",
    "actual_checkout_date": "2018-04-20T11:48:41.544Z",
    "booking_id": "string",
    "charge_ids": [0],
    "checkin_date": "2018-04-20T11:48:41.544Z",
    "checkout_date": "2018-04-20T11:48:41.544Z",
    "guest_stays": [
        {
            "actual_checkin_date": "2018-04-20T11:48:41.544Z",
            "actual_checkout_date": "2018-04-20T11:48:41.544Z",
            "age_group": "INFANT",
            "booking_version_id": 0,
            "checkin_date": "2018-04-20T11:48:41.544Z",
            "checkout_date": "2018-04-20T11:48:41.544Z",
            "guest_allocation": {
                "assigned_by": "string",
                "checkin_date": "2018-04-20T11:48:41.544Z",
                "checkout_date": "2018-04-20T11:48:41.544Z",
                "guest_id": "string",
                "id": 0,
            },
            "guest_allocation_history": [
                {
                    "assigned_by": "string",
                    "checkin_date": "2018-04-20T11:48:41.544Z",
                    "checkout_date": "2018-04-20T11:48:41.544Z",
                    "guest_id": "string",
                    "id": 0,
                }
            ],
            "id": 0,
            "status": "RESERVED",
        }
    ],
    "id": 0,
    "room_allocation": {
        "assigned_by": "string",
        "checkin_date": "2018-04-20T11:48:41.544Z",
        "checkout_date": "2018-04-20T11:48:41.544Z",
        "id": 0,
        "room_id": 0,
        "room_type_id": "string",
    },
    "room_allocation_history": [
        {
            "assigned_by": "string",
            "checkin_date": "2018-04-20T11:48:41.544Z",
            "checkout_date": "2018-04-20T11:48:41.544Z",
            "id": 0,
            "room_id": 0,
            "room_type_id": "string",
        }
    ],
    "room_type_id": "string",
    "status": "RESERVED",
    "type": "NIGHT",
}

add_rooms_response = {
    "actual_checkin_date": "2018-04-20T06:40:34",
    "actual_checkout_date": "2018-04-20T06:40:34",
    "booking_id": "CRS-1234",
    "charge_ids": [0],
    "checkin_date": "2018-04-20T06:40:34",
    "checkout_date": "2018-04-20T06:40:34",
    "guest_stays": [
        {
            "actual_checkin_date": "2018-04-20T06:40:34",
            "actual_checkout_date": "2018-04-20T06:40:34",
            "age_group": "ADULT",
            "booking_version_id": 0,
            "checkin_date": "2018-04-20T06:40:34",
            "checkout_date": "2018-04-20T06:40:34",
            "guest_allocation": {
                "assigned_by": "string",
                "checkin_date": "2018-04-20T06:40:34",
                "checkout_date": "2018-04-20T06:40:34",
                "guest_id": "string",
                "id": 0,
            },
            "guest_allocation_history": [
                {
                    "assigned_by": "string",
                    "checkin_date": "2018-04-20T06:40:34",
                    "checkout_date": "2018-04-20T06:40:34",
                    "guest_id": "string",
                    "id": 0,
                }
            ],
            "id": 0,
            "status": "RESERVED",
        }
    ],
    "id": 0,
    "room_allocation": {
        "assigned_by": "string",
        "checkin_date": "2018-04-20T06:40:34",
        "checkout_date": "2018-04-20T06:40:34",
        "id": 0,
        "room_id": 0,
        "room_type_id": "string",
    },
    "room_allocation_history": [
        {
            "assigned_by": "string",
            "checkin_date": "2018-04-20T06:40:34",
            "checkout_date": "2018-04-20T06:40:34",
            "id": 0,
            "room_id": 0,
            "room_type_id": "string",
        }
    ],
    "room_type_id": "MAPLE",
    "status": "RESERVED",
    "type": "NIGHT",
}

add_rooms_request = {
    "booking_version_id": 0,
    "room_stays": {
        "checkin_date": "2018-04-20T06:40:34",
        "checkout_date": "2018-04-20T06:40:34",
        "guest_stays": [
            {
                "age_group": "ADULT",
                "checkin_date": "2018-04-20T06:40:34",
                "checkout_date": "2018-04-20T06:40:34",
                "guest": {
                    "address": {
                        "city": "Bangalore",
                        "country": "India",
                        "field1": "abc street",
                        "field2": "xyz colony",
                        "pincode": "560078",
                        "state": "Karnataka",
                    },
                    "age": 25,
                    "country_code": "IN",
                    "email": "<EMAIL>",
                    "first_name": "First Name",
                    "gender": "MALE",
                    "last_name": "Last Name",
                    "nationality": "Indian",
                    "phone": {"country_code": "+91", "number": "**********"},
                },
            }
        ],
        "prices": [
            {
                "applicable_date": "2018-04-20T06:40:34",
                "bill_to_type": "COMPANY",
                "posttax_amount": 1100,
                "pretax_amount": 100,
                "type": "CREDIT",
            }
        ],
        "room_type_id": "CS-room-id-12",
        "type": "NIGHT",
    },
}

update_booking_request = {
    "booking_owner": {
        "address": {
            "city": "c1",
            "country": "India",
            "field1": "abc street",
            "pincode": "560078",
            "state": "Karnataka",
        },
        "email": "<EMAIL>",
        "gst_details": {
            "gstin_number": "g1",
            "registered_address": "g2",
            "registered_name": "g3",
        },
        "name": "b2",
        "phone": "**********",
        "profile_type": "INDIVIDUAL",
        "reference_id": "0001",
    },
    "comments": "Comments",
    "extra_information": {"desc": "abc"},
    "hold_till": "2018-04-18T09:45:19.760000+00:00",
    "hotel_id": "CS-123",
    "payments": [
        {
            "amount": 100.0,
            "created_at": "2018-04-18T09:45:19.760000+00:00",
            "paid_by": "abc",
            "paid_to": "TREEBO",
            "payment_channel": "ONLINE",
            "payment_details": {},
            "payment_mode": "CASH",
            "payment_ref_id": "123",
            "payment_type": "ADVANCE",
            "status": "DONE",
        }
    ],
    "guests": [
        {
            "address": {
                "city": "Bangalore",
                "country": "India",
                "field1": "abc street",
                "field2": "xyz colony",
                "pincode": "560078",
                "state": "Karnataka",
            },
            "age": 25,
            "country_code": "IN",
            "email": "<EMAIL>",
            "first_name": "First Name",
            "gender": "MALE",
            "id_kyc_url": "www.com",
            "id_number": "1",
            "id_proof_type": "DRIVING_LICENSE",
            "image_url": "www.com",
            "last_name": "Last Name",
            "nationality": "Indian",
            "phone": {"country_code": "+91", "number": "**********"},
            "reference_id": "1",
            "profile_type": "INDIVIDUAL",
        }
    ],
    "reference_number": "ref-1234",
    "room_stays": [
        {
            "checkin_date": "2018-04-18T09:45:19.760000+00:00",
            "checkout_date": "2018-04-18T09:45:19.760000+00:00",
            "guest_stays": [
                {
                    "age_group": "ADULT",
                    "checkin_date": "2018-04-18T09:45:19.760000+00:00",
                    "checkout_date": "2018-04-18T09:45:19.760000+00:00",
                    "guest": {
                        "address": {
                            "city": "Bangalore",
                            "country": "India",
                            "field1": "abc street",
                            "field2": "xyz colony",
                            "pincode": "560078",
                            "state": "Karnataka",
                        },
                        "age": 25,
                        "country_code": "IN",
                        "email": "<EMAIL>",
                        "first_name": "First Name",
                        "gender": "MALE",
                        "id_kyc_url": "www.com",
                        "id_number": "1",
                        "id_proof_type": "DRIVING_LICENSE",
                        "image_url": "www.com",
                        "last_name": "Last Name",
                        "nationality": "Indian",
                        "phone": {"country_code": "+91", "number": "**********"},
                        "reference_id": "1",
                        "profile_type": "INDIVIDUAL",
                    },
                }
            ],
            "prices": [
                {
                    "applicable_date": "2018-04-18T09:45:19.760000+00:00",
                    "bill_to_type": "COMPANY",
                    "posttax_amount": 1100.0,
                    "pretax_amount": 100.0,
                    "type": "CREDIT",
                }
            ],
            "room_type_id": "CS-room-id-12",
            "type": "NIGHT",
        }
    ],
    "source": {
        "application_code": "source_name",
        "channel_code": "1",
        "subchannel_code": "3",
    },
    "status": "RESERVED",
}

create_booking_request = {
    "booking_owner": {
        "address": {
            "city": "c1",
            "country": "India",
            "field1": "abc street",
            "pincode": "560078",
            "state": "Karnataka",
        },
        "email": "<EMAIL>",
        "gst_details": {
            "gstin_number": "g1",
            "registered_address": "g2",
            "registered_name": "g3",
        },
        "name": "b2",
        "phone": "**********",
        "profile_type": "INDIVIDUAL",
        "reference_id": "0001",
    },
    "comments": "Comments",
    "extra_information": {"desc": "abc"},
    "hold_till": "2018-04-18T09:45:19.760000+00:00",
    "hotel_id": "0016932",
    "payments": [
        {
            "amount": 100.0,
            "created_at": "2018-04-18T09:45:19.760000+00:00",
            "paid_by": "abc",
            "paid_to": "TREEBO",
            "payment_channel": "ONLINE",
            "payment_details": {},
            "payment_mode": "CASH",
            "payment_ref_id": "123",
            "payment_type": "ADVANCE",
            "status": "DONE",
        }
    ],
    "reference_number": "ref-1234",
    "room_stays": [
        {
            "checkin_date": "2018-04-18T09:45:19.760000+00:00",
            "checkout_date": "2018-04-18T09:45:19.760000+00:00",
            "guest_stays": [
                {
                    "age_group": "ADULT",
                    "checkin_date": "2018-04-18T09:45:19.760000+00:00",
                    "checkout_date": "2018-04-18T09:45:19.760000+00:00",
                    "guest": {
                        "address": {
                            "city": "Bangalore",
                            "country": "India",
                            "field1": "abc street",
                            "field2": "xyz colony",
                            "pincode": "560078",
                            "state": "Karnataka",
                        },
                        "age": 25,
                        "country_code": "IN",
                        "email": "<EMAIL>",
                        "first_name": "First Name",
                        "gender": "MALE",
                        "last_name": "Last Name",
                        "phone": {"country_code": "+91", "number": "**********"},
                    },
                }
            ],
            "prices": [
                {
                    "applicable_date": "2018-04-18T09:45:19.760000+00:00",
                    "bill_to_type": "COMPANY",
                    "posttax_amount": 1100.0,
                    "pretax_amount": 100.0,
                    "type": "CREDIT",
                }
            ],
            "room_type_id": "rt01",
            "type": "NIGHT",
        },
        {
            "checkin_date": "2018-04-18T09:45:19.760000+00:00",
            "checkout_date": "2018-04-18T09:45:19.760000+00:00",
            "guest_stays": [
                {
                    "age_group": "ADULT",
                    "checkin_date": "2018-04-18T09:45:19.760000+00:00",
                    "checkout_date": "2018-04-18T09:45:19.760000+00:00",
                    "guest": {
                        "address": {
                            "city": "Bangalore",
                            "country": "India",
                            "field1": "abc street",
                            "field2": "xyz colony",
                            "pincode": "560078",
                            "state": "Karnataka",
                        },
                        "age": 25,
                        "country_code": "IN",
                        "email": "<EMAIL>",
                        "first_name": "First Name",
                        "gender": "MALE",
                        "last_name": "Last Name",
                        "phone": {"country_code": "+91", "number": "**********"},
                    },
                }
            ],
            "prices": [
                {
                    "applicable_date": "2018-04-18T09:45:19.760000+00:00",
                    "bill_to_type": "COMPANY",
                    "posttax_amount": 1100.0,
                    "pretax_amount": 100.0,
                    "type": "CREDIT",
                }
            ],
            "room_type_id": "rt01",
            "type": "NIGHT",
        },
    ],
    "source": {
        "application_code": "source_name",
        "channel_code": "1",
        "subchannel_code": "3",
    },
    "type": "ROOM",
}

cancel_booking_request = dict(
    action_type='CANCEL',
    booking_version_id=0,
    entity='BOOKING',
    entity_id='1234',
    payload={"cancel": {}},
)

create_booking_response = {
    "bill_id": "bill-1234",
    "bill_summary": {
        "amount_due": 0,
        "other_charges_pretax": 0,
        "paid_amount": 0,
        "room_charges_pretax": 0,
        "total_posttax": 0,
        "total_tax": 0,
    },
    "booking_id": "CRS-1234",
    "booking_owner": {
        "address": {
            "city": "Banglore",
            "country": "India",
            "field1": "1",
            "field2": "2",
            "pincode": "123456",
            "state": "Karnataka",
        },
        "customer_id": "cs1234",
        "email": "<EMAIL>",
        "gst_details": {
            "gstin_number": "1",
            "registered_address": "2",
            "registered_name": "3",
        },
        "name": "owner",
        "phone": "123456",
        "profile_type": "INDIVIDUAL",
        "reference_id": "refid",
    },
    "checkin_date": "2018-04-17 13:46:20.385Z",
    "checkout_date": "2018-04-17 13:46:20.385Z",
    "comments": "xyz",
    "extra_information": {'a': 'b'},
    "guests": [{"first_name": "guest", "id": 0, "last_name": "treebo"}],
    "hold_till": "2018-04-17 13:46:20.385Z",
    "hotel_id": "1234",
    "reference_number": "ref1234",
    "room_stays": [
        {
            "guest_stays": [
                {
                    "age_group": "INFANT",
                    "guest_id": "1234",
                    "id": 0,
                    "status": "RESERVED",
                }
            ],
            "id": 0,
            "room_id": 0,
            "room_type_id": "MAPLE",
            "status": "RESERVED",
        }
    ],
    "source": {
        "application_code": "ap",
        "channel_code": "ch",
        "subchannel_code": "sch",
    },
    "status": "RESERVED",
    "version_id": 0,
}

get_bill_response = {
    'bill_date': '2018-04-13T15:30:08',
    'charges': {
        'applicable_date': '2018-04-13T15:30:08',
        'bill_to_type': 'COMPANY',
        'pretax_amount': '120.475',
        'status': 'CREATED',
        'type': 'CREDIT',
        'charge_splits': [
            {'amount': 126.350, 'charge_to': 'John Doe'},
            {'amount': 126.350, 'charge_to': 'John Doe'},
        ],
        'charge_id': 3,
        'created_at': '2018-04-13T15:30:08',
        'posttax_amount': '126.390',
        'created_by': 'John Doe',
        'item': {'details': {}, 'name': 'John Doe', 'sku_category_id': 2},
        'comments': 'I want Jeep Compass!!!',
    },
    'bill_id': '1',
    'invoices': [
        {
            'invoice_date': '2018-04-13T15:30:08',
            'posttax_amount': 100.0,
            'tax_amount': 20.0,
            'invoice_id': '1',
            'url': 'www.google.com',
            'pretax_amount': 80.0,
            'status': 'PREVIEW',
            'invoice_no': 'ABC-123',
            'bill_to': 'John Doe',
            'due_date': '2018-04-13T15:30:08',
        }
    ],
    'payments': [
        {
            'amount': 100,
            'created_at': '2018-04-13T15:30:08',
            'paid_by': 'John Doe',
            'paid_to': 'TREEBO',
            'payment_channel': 'ONLINE',
            'payment_details': {},
            'payment_mode': 'CASH',
            'payment_ref_id': '123',
            'payment_type': 'ADVANCE',
            'status': 'DONE',
            'payment_id': '1',
        }
    ],
    'version_id': 1,
}

get_invoice_response = {
    'invoice_date': '2018-04-13T15:30:08',
    'posttax_amount': 100.0,
    'tax_amount': 20.0,
    'invoice_id': '1',
    'url': 'www.google.com',
    'pretax_amount': 80.0,
    'status': 'PREVIEW',
    'invoice_no': 'ABC-123',
    'bill_to': 'John Doe',
    'due_date': '2018-04-13T15:30:08',
}

get_payments_response = [
    {
        'amount': 100,
        'created_at': '2018-04-13T15:30:08',
        'paid_by': 'John Doe',
        'paid_to': 'TREEBO',
        'payment_channel': 'ONLINE',
        'payment_details': {},
        'payment_mode': 'CASH',
        'payment_ref_id': '123',
        'payment_type': 'ADVANCE',
        'status': 'DONE',
        'payment_id': '1',
    }
]

get_charges_response = [
    {
        'applicable_date': '2018-04-13T15:30:08',
        'bill_to_type': 'COMPANY',
        'pretax_amount': 120.475,
        'status': 'CREATED',
        'type': 'CREDIT',
        'charge_splits': [
            {'amount': 126.350, 'charge_to': 'John Doe'},
            {'amount': 126.350, 'charge_to': 'John Doe'},
        ],
        'charge_id': 3,
        'created_at': '2018-04-13T15:30:08',
        'posttax_amount': 126.390,
        'created_by': 'John Doe',
        'item': {'details': {}, 'name': 'John Doe', 'sku_category_id': 2},
        'comments': 'I want Jeep Compass!!!',
    }
]
add_guest_request = {
    "age_group": "INFANT",
    "checkin_date": "2018-04-20T06:40:34",
    "checkout_date": "2018-04-20T06:40:34",
    "guest": {
        "address": {
            "city": "Banglore",
            "country": "India",
            "field1": "1",
            "field2": "2",
            "pincode": "123456",
            "state": "Karnataka",
        },
        "age": 25,
        "country_code": "IN",
        "email": "<EMAIL>",
        "first_name": "First Name",
        "gender": "MALE",
        "id_kyc_url": "www.com",
        "id_number": "**********",
        "id_proof_type": "DRIVING_LICENSE",
        "image_url": "www.com",
        "last_name": "Last Name",
        "nationality": "Indian",
        "phone": {"country_code": "+91", "number": "**********"},
        "reference_id": "1",
        "profile_type": "INDIVIDUAL",
    },
    "new_room_stay_prices": [
        {
            "applicable_date": "2018-04-20T06:40:34",
            "bill_to_type": "COMPANY",
            "posttax_amount": 1100.0,
            "pretax_amount": 100.0,
            "type": "CREDIT",
        }
    ],
}

add_guest_response = {
    "actual_checkin_date": "2018-04-18T09:45:19.760000+00:00",
    "actual_checkout_date": "2018-04-18T09:45:19.760000+00:00",
    "age_group": "INFANT",
    "booking_version_id": 0,
    "checkin_date": "2018-04-18T09:45:19.760000+00:00",
    "checkout_date": "2018-04-18T09:45:19.760000+00:00",
    "guest_allocation": {
        "assigned_by": "string",
        "checkin_date": "2018-04-18T09:45:19.760000+00:00",
        "checkout_date": "2018-04-18T09:45:19.760000+00:00",
        "guest_id": "string",
        "id": 0,
    },
    "guest_allocation_history": [
        {
            "assigned_by": "string",
            "checkin_date": "2018-04-18T09:45:19.760000+00:00",
            "checkout_date": "2018-04-18T09:45:19.760000+00:00",
            "guest_id": "string",
            "id": 0,
        }
    ],
    "id": 0,
    "status": "RESERVED",
}

update_guest_response = {
    "address": {
        "city": "Banglore",
        "country": "India",
        "field1": "1",
        "field2": "2",
        "pincode": "123456",
        "state": "Karnataka",
    },
    "age": 25,
    "country_code": "IN",
    "email": "<EMAIL>",
    "first_name": "First Name",
    "gender": "MALE",
    "id_kyc_url": "www.com",
    "id_number": "**********",
    "id_proof_type": "DRIVING_LICENSE",
    "image_url": "www.com",
    "last_name": "Last Name",
    "nationality": "Indian",
    "phone": {"country_code": "+91", "number": "**********"},
    "reference_id": "1",
    "profile_type": "INDIVIDUAL",
    "id": 0,
    "status": "ACTIVE",
}

update_guest_request = {
    "guest_details": [
        {
            "address": {
                "city": "Banglore",
                "country": "India",
                "field1": "1",
                "field2": "2",
                "pincode": "123456",
                "state": "Karnataka",
            },
            "age": 25,
            "country_code": "IN",
            "email": "<EMAIL>",
            "first_name": "First Name",
            "gender": "MALE",
            "id_kyc_url": "www.com",
            "id_number": "**********",
            "id_proof_type": "DRIVING_LICENSE",
            "image_url": "www.com",
            "last_name": "Last Name",
            "nationality": "Indian",
            "phone": {"country_code": "+91", "number": "**********"},
            "reference_id": "1",
            "profile_type": "INDIVIDUAL",
        }
    ],
    "guest_stay_id": "1",
    "version_id": 0,
}

update_guest_error_response = [
    {
        "code": "04010206",
        "developer_message": "string",
        "extra_payload": {},
        "message": "Room Not Found",
    }
]

update_guest_error_response_with_unknown_error_code = [
    {
        "code": "05010206",
        "developer_message": "string",
        "extra_payload": {},
        "message": "Something went wrong",
    }
]
