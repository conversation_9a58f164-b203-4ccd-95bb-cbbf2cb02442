from thsc.crs.convertors.booking_convertors import AddonConvertor, AddonGetConvertor
from thsc.crs.request_templates.request import Request


class Addon(Request):
    _convertor = AddonConvertor

    def __init__(self, url, request_method, data=None):
        super(Addon, self).__init__(url, request_method, data)

    def get_uri(self):
        return super(Addon, self).get_uri()


class AddAddon(Addon):
    _many = False
    _api_version = 'v2'

    def __init__(self, booking_id, addon):
        super(AddAddon, self).__init__(
            url='/bookings/{booking_id}/addons', request_method='POST', data=addon
        )
        self.booking_id = booking_id

    def get_uri(self):
        return super(AddAddon, self).get_uri().format(booking_id=self.booking_id)

    def from_dict(self, response):
        if type(response) == list:
            resp = self._convertor().from_dict(response, many=True)
        else:
            resp = self._convertor().from_dict(response)
        return resp


class AddAddons(Addon):
    _many = True
    _api_version = 'v2'

    def __init__(self, booking_id, addons, resource_version):
        super(AddAddons, self).__init__(
            url='/bookings/{booking_id}/addons-list', request_method='POST', data=addons
        )
        self.booking_id = booking_id
        self.resource_version = resource_version

    def get_uri(self):
        return super(AddAddons, self).get_uri().format(booking_id=self.booking_id)

    def to_dict(self):
        response = super(AddAddons, self).to_dict()
        response["data"] = dict(addons=response["data"])
        return response

    def from_dict(self, response):
        if type(response) == list:
            resp = self._convertor().from_dict(response, many=True)
        else:
            resp = self._convertor().from_dict(response)
        return resp


class DeleteAddon(Addon):
    _payload = False
    _parse_response = False

    def __init__(self, booking_id, add_on_id):
        super(DeleteAddon, self).__init__(
            url='/bookings/{booking_id}/addons/{add_on_id}', request_method='DELETE'
        )
        self.add_on_id = add_on_id
        self.booking_id = booking_id

    def get_uri(self):
        return (
            super(DeleteAddon, self)
            .get_uri()
            .format(booking_id=self.booking_id, add_on_id=self.add_on_id)
        )


class GetAddons(Addon):
    _payload = True
    _api_version = 'v2'
    _convertor = AddonGetConvertor
    _response_convertor = AddonConvertor
    _many = False

    def __init__(self, booking_id, query_params):
        self._payload = True if query_params else False
        super(GetAddons, self).__init__(
            url='/bookings/{booking_id}/addons', request_method='GET', data=query_params
        )
        self.booking_id = booking_id

    def get_uri(self):
        return super(GetAddons, self).get_uri().format(booking_id=self.booking_id)

    def to_dict(self):
        request = self._convertor().to_dict(self.data, many=self._many)
        return request

    def from_dict(self, response):
        if type(response) == list:
            resp = self._response_convertor().from_dict(response, many=True)
        else:
            resp = self._response_convertor().from_dict(response)
        return resp
