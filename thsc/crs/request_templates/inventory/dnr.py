from thsc.crs.convertors.inventory_convertors import (
    DNRConvertor,
    DNRSearchQueryConvertor,
    UpdateDNRConvertor,
)
from thsc.crs.request_templates.request import Request


class DNR(Request):
    _convertor = DNRConvertor

    def __init__(self, url, request_method, data=None):
        super(DNR, self).__init__(url, request_method, data)

    def get_uri(self):
        return super(DNR, self).get_uri()


class MarkDNR(DNR):
    _convertor = DNRConvertor

    def __init__(self, hotel_id, dnr):
        self.hotel_id = hotel_id
        super(MarkDNR, self).__init__(
            url='/hotels/{hotel_id}/dnrs', request_method='POST', data=dnr
        )

    def get_uri(self):
        return super(MarkDNR, self).get_uri().format(hotel_id=self.hotel_id)


class GetDNR(DNR):
    _payload = False

    def __init__(self, hotel_id, dnr_id):
        super(GetDNR, self).__init__('/hotels/{hotel_id}/dnrs/{dnr_id}', 'GET')
        self.hotel_id = hotel_id
        self.dnr_id = dnr_id

    def get_uri(self):
        return (
            super(GetDNR, self)
            .get_uri()
            .format(hotel_id=self.hotel_id, dnr_id=self.dnr_id)
        )


class UpdateDNR(DNR):
    _convertor = UpdateDNRConvertor

    def __init__(self, hotel_id, dnr_id, resource_version, dnr):
        super(UpdateDNR, self).__init__(
            '/hotels/{hotel_id}/dnrs/{dnr_id}', 'PATCH', data=dnr
        )
        self.hotel_id = hotel_id
        self.dnr_id = dnr_id
        self.resource_version = resource_version

    def get_uri(self):
        return (
            super(UpdateDNR, self)
            .get_uri()
            .format(hotel_id=self.hotel_id, dnr_id=self.dnr_id)
        )

    def to_dict(self):
        response = super(UpdateDNR, self).to_dict()
        response['resource_version'] = self.resource_version
        return response


class ResolveDNR(DNR):
    _convertor = DNRConvertor
    _payload = False

    def __init__(self, hotel_id, dnr_id, resource_version):
        super(ResolveDNR, self).__init__(
            '/hotels/{hotel_id}/dnrs/{dnr_id}/resolve-dnr', 'POST'
        )
        self.hotel_id = hotel_id
        self.dnr_id = dnr_id
        self.resource_version = resource_version

    def get_uri(self):
        return (
            super(ResolveDNR, self)
            .get_uri()
            .format(hotel_id=self.hotel_id, dnr_id=self.dnr_id)
        )


class SearchDNR(DNR):
    _payload = True
    _convertor = DNRSearchQueryConvertor
    _response_convertor = DNRConvertor

    def __init__(self, hotel_id, dnr_search_query):
        super(SearchDNR, self).__init__(
            url='/hotels/{hotel_id}/dnrs', request_method='GET', data=dnr_search_query
        )
        self.hotel_id = hotel_id

    def get_uri(self):
        return super(SearchDNR, self).get_uri().format(hotel_id=self.hotel_id)

    def to_dict(self):
        request = self._convertor().to_dict(self.data, many=self._many)
        return request

    def from_dict(self, response):
        resp = self._response_convertor().from_dict(response, many=True)
        return resp
