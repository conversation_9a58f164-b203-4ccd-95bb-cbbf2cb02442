import os

import sentry_sdk
from treebo_commons.utils import dateutils

from hawkeye.application.decorators import session_manager
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.application.services.price_calculation_service import (
    PriceCalculationService,
)
from hawkeye.constants.hawkeye_constant import (
    PRICE_APPROVAL_ALERT_SUBJECT,
    CommunicationTypeIdentifier,
    PriceTriggerSource,
)
from hawkeye.domain.factory.price_calculation_trigger_factory import (
    PriceCalculationTriggerFactory,
)
from hawkeye.infrastructure.exception import PriceAlertExpiredError, ValidationException
from hawkeye.infrastructure.external_clients.communication_client import (
    CommunicationServiceClient,
)
from hawkeye.infrastructure.repositories.compset_pricing_threshold_repository import (
    CompsetPricingThresholdRepository,
)
from hawkeye.infrastructure.repositories.hotel_config_repository import (
    HotelConfigRepository,
)
from hawkeye.infrastructure.repositories.price_alert_repository import (
    PriceAlertRepository,
)
from object_registry import register_instance
import logging

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelConfigRepository,
        CommunicationServiceClient,
        CompsetPricingThresholdRepository,
        PriceAlertRepository,
        PriceCalculationService,
        CatalogService,
    ]
)
class PriceAlertService:
    def __init__(
        self,
        hotel_config_repository: HotelConfigRepository,
        communication_service_client: CommunicationServiceClient,
        compset_pricing_threshold_repository: CompsetPricingThresholdRepository,
        price_alert_repository: PriceAlertRepository,
        price_calculation_service: PriceCalculationService,
        catalog_service: CatalogService,
    ):
        self.hotel_config_repository = hotel_config_repository
        self.communication_service_client = communication_service_client
        self.compset_pricing_threshold_repository = compset_pricing_threshold_repository
        self.price_alert_repository = price_alert_repository
        self.price_calculation_service = price_calculation_service
        self.catalog_service = catalog_service

    def send_price_alert(self, price_alert):
        try:
            hotel_config = self.hotel_config_repository.load(price_alert.hotel_id)
            hotel_details = self.catalog_service.get_catalog_hotel(price_alert.hotel_id)
            revenue_manager_emails = hotel_config.revenue_poc_emails.split(",")
            email_context = dict(
                hotel_id=price_alert.hotel_id,
                stay_date=str(price_alert.stay_date),
                hawkeye_predicted_price=round(price_alert.hawkeye_price, 2),
                alert_threshold_percentage=price_alert.alert_threshold_percentage,
                competition_price=round(price_alert.competitive_price, 2),
                suggested_change_percentage=price_alert.suggested_change_percentage,
                approval_url=os.environ.get("ADMIN_BASE_URL") + "price-alert/list",
                increase_or_decrease="decrease"
                if price_alert.competitive_price < price_alert.hawkeye_price
                else "increase",
                hotel_name=hotel_details.hotel_name,
                hotel_city=hotel_details.hotel_city,
            )
            logger.info(f"Price approval alert sent to communication with context_data:{email_context}, to_emails:{revenue_manager_emails}")
            response = self.communication_service_client.send_email(
                CommunicationTypeIdentifier.PRICE_APPROVAL_ALERT.value,
                context_data=email_context,
                to_emails=revenue_manager_emails,
                subject=PRICE_APPROVAL_ALERT_SUBJECT.format(
                    hotel_details.hotel_name, hotel_details.hotel_city, str(price_alert.stay_date)
                ),
            )
            logger.info(f"Communication service response: {response}")
        except Exception as e:
            sentry_sdk.capture_exception(e)

    def approve_price_alert(self, price_alert_id, approved_by):
        price_calculation_trigger = self._validate_price_alert(price_alert_id, approved_by)
        if price_calculation_trigger:
            self.price_calculation_service.trigger_price_calculation(
                config_date_time=None, price_calculation_trigger=price_calculation_trigger
            )

    @session_manager(commit=True)
    def _validate_price_alert(self, price_alert_id, approved_by):
        price_alert = self.price_alert_repository.load_for_update(price_alert_id)
        if price_alert is None:
            raise ValidationException("Price alert not found or expired")
        if price_alert.is_expired:
            raise PriceAlertExpiredError(price_alert_id)

        price_alert.is_expired = True
        new_price_alert = (
            self.price_calculation_service.calculate_rule_based_price_for_price_alert(price_alert)
        )
        if not new_price_alert or price_alert != new_price_alert:
            self.price_alert_repository.update(price_alert)
            return

        abw = (price_alert.stay_date - dateutils.current_date()).days
        price_calculation_trigger = (
            PriceCalculationTriggerFactory.create_price_calculation_trigger(
                PriceTriggerSource.PRICE_ALERT_APPROVAL,
                abw,
                abw,
                price_alert.hotel_id,
                approved_by,
            )
        )
        price_alert.price_trigger_id = price_calculation_trigger.id
        price_alert.approver_email = approved_by
        compset_pricing_threshold = self.compset_pricing_threshold_repository.load_for_update(
            price_alert.hotel_id, price_alert.stay_date
        )

        if price_alert.competitive_price < price_alert.hawkeye_price:
            compset_pricing_threshold.decreased_percentage_threshold = (
                price_alert.suggested_change_percentage
            )
        else:
            compset_pricing_threshold.increased_percentage_threshold = (
                price_alert.suggested_change_percentage
            )

        self.compset_pricing_threshold_repository.update(compset_pricing_threshold)
        self.price_alert_repository.update(price_alert)
        return price_calculation_trigger
