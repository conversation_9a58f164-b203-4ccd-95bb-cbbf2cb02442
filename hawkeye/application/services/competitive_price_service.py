from collections import defaultdict

import sentry_sdk

from hawkeye.application.decorators import session_manager
from hawkeye.constants.hawkeye_constant import GU<PERSON>DR<PERSON>IL_MAX, GUARDRAIL_MIN
from hawkeye.infrastructure.exception import PriceOutOfBoundsError
from hawkeye.infrastructure.repositories.competitor_hotel_pricing_repository import CompetitorHotelPricingRepository
from hawkeye.infrastructure.repositories.competitve_hotel_mapping import CompetitiveHotelMappingRepository
from hawkeye.infrastructure.repositories.compset_pricing_threshold_repository import CompsetPricingThresholdRepository
from hawkeye.infrastructure.repositories.hotel_config_repository import HotelConfigRepository
from hawkeye.utils.collectionutils import chunks
from object_registry import register_instance


@register_instance(dependencies=[
    CompetitiveHotelMappingRepository,
    CompetitorHotelPricingRepository,
    CompsetPricingThresholdRepository,
    HotelConfigRepository,
])
class CompetitivePriceService:
    def __init__(
        self,
        competitive_hotel_mapping_repository: CompetitiveHotelMappingRepository,
        competitor_hotel_pricing_repository: CompetitorHotelPricingRepository,
        compset_pricing_threshold_repository: CompsetPricingThresholdRepository,
        hotel_config_repository: HotelConfigRepository,
    ):
        self.competitive_hotel_mapping_repository = competitive_hotel_mapping_repository
        self.competitor_hotel_pricing_repository = competitor_hotel_pricing_repository
        self.compset_pricing_threshold_repository = compset_pricing_threshold_repository
        self.hotel_config_repository = hotel_config_repository

    def _calculate_normalized_competitive_price_avg(
        self,
        datewise_compset_pricing_thresholds,
        competitive_hotel_wise_normalization_factor,
        competitor_hotel_pricings,
    ):
        competitor_hotel_pricings = self._exclude_out_of_range_prices(competitor_hotel_pricings)
        datewise_competitor_hotel_pricings = defaultdict(list)
        for competitor_hotel_pricing in competitor_hotel_pricings:
            datewise_competitor_hotel_pricings[competitor_hotel_pricing.stay_date].append(competitor_hotel_pricing)

        for stay_date, competitor_hotel_pricings in datewise_competitor_hotel_pricings.items():
            compset_pricing_threshold = datewise_compset_pricing_thresholds.get(stay_date)
            if compset_pricing_threshold is None:
                continue
            normalized_competitive_price_avg = 0
            for competitor_hotel_pricing in competitor_hotel_pricings:
                normalized_competitive_price_avg += (
                    competitor_hotel_pricing.price
                    * competitive_hotel_wise_normalization_factor[competitor_hotel_pricing.competitor_hotel_id]
                )
            normalized_competitive_price_avg /= len(competitor_hotel_pricings)
            compset_pricing_threshold.normalized_competitive_price_avg = round(normalized_competitive_price_avg, 4)

    @session_manager(commit=True)
    def calculate_normalized_price(self, hotel_config):
        compset_pricing_thresholds = self.compset_pricing_threshold_repository.load_all_for_hotel(hotel_config.hotel_id)
        if not compset_pricing_thresholds:
            return None

        competitive_hotel_mappings = self.competitive_hotel_mapping_repository.load_active_mappings(hotel_config.hotel_id)
        if not competitive_hotel_mappings:
            return None

        competitive_hotel_wise_normalization_factor = {
            competitive_hotel_mapping.competitive_hotel_id: competitive_hotel_mapping.normalization_factor
            for competitive_hotel_mapping in competitive_hotel_mappings
        }
        datewise_compset_pricing_thresholds = {}
        for compset_pricing_threshold in compset_pricing_thresholds:
            compset_pricing_threshold.normalized_competitive_price_avg = 0
            datewise_compset_pricing_thresholds[compset_pricing_threshold.stay_date] = compset_pricing_threshold

        for stay_dates in chunks(list(datewise_compset_pricing_thresholds.keys()), 50):
            competitor_hotel_pricings = list()
            competitor_hotel_ids = competitive_hotel_wise_normalization_factor.keys()
            for competitor_hotel_id in competitor_hotel_ids:
                competitor_hotel_pricings.extend(
                    self.competitor_hotel_pricing_repository.load_competitor_prices_of_hotel_competitor(
                        hotel_config.hotel_id,
                        competitor_hotel_id,
                        stay_dates,
                    )
                )
            if not competitor_hotel_pricings:
                continue
            self._calculate_normalized_competitive_price_avg(
                datewise_compset_pricing_thresholds, competitive_hotel_wise_normalization_factor, competitor_hotel_pricings
            )
            updated_compset_pricing_thresholds = [datewise_compset_pricing_thresholds[stay_date] for stay_date in stay_dates]
            self.compset_pricing_threshold_repository.update_all(updated_compset_pricing_thresholds)
        return

    def calculate_normalized_prices(self):
        hotel_configs = self.hotel_config_repository.load_all_enabled()
        for hotel_config in hotel_configs:
            self.calculate_normalized_price(hotel_config)
        return

    @staticmethod
    def _exclude_out_of_range_prices(competitor_hotel_pricings):
        filtered_prices = []

        for competitor_hotel_pricing in competitor_hotel_pricings:
            try:
                if GUARDRAIL_MIN <= competitor_hotel_pricing.price <= GUARDRAIL_MAX:
                    filtered_prices.append(competitor_hotel_pricing)
                else:
                    raise PriceOutOfBoundsError(
                        hotel_id=competitor_hotel_pricing.competitor_hotel_id,
                        price=competitor_hotel_pricing.price,
                        stay_date=competitor_hotel_pricing.stay_date
                    )
            except PriceOutOfBoundsError as e:
                pass
                # sentry_sdk.capture_exception(e)

        return filtered_prices
