from hawkeye.domain.entities.rate_info import RateInfoEntity
from hawkeye.infrastructure.database.models import RateInfo as RateInfoModel
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.rate_info_adaptor import (
    RateInfoAdaptor,
)
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class RateInfoRepository(BaseRepository):
    _model = RateInfoModel
    _adaptor = RateInfoAdaptor()

    def create_all(self, rates_info: [RateInfoEntity]):
        rate_info_models = [self._adaptor.to_db_entity(rate_info) for rate_info in rates_info]
        try:
            super().create_all(rate_info_models)
        except Exception as e:
            raise DatabaseError(description=e.__str__())

    def update_all(self, rates_info: RateInfoEntity):
        rate_info_models = [self._adaptor.to_db_entity(rate_info) for rate_info in rates_info]
        try:
            self._update_all(rate_info_models)
        except Exception as e:
            raise DatabaseError(description=e.__str__())

    def mark_all_rates_inactive(self):
        queryset = self.query(self._model).filter(self._model.is_active == True)
        queryset.update({"is_active": False}, synchronize_session=False)

    def load_unique_hotel_ids(self):
        return [hotel_id for hotel_id, in self.query(self._model.hotel_id).filter(self._model.is_active == True).distinct().all()]

    def load_all_active_rates_of_hotel(self, hotel_id):
        hotel_active_rates = self.query(self._model).filter(self._model.is_active == True, self._model.hotel_id == hotel_id).all()
        return [self._adaptor.to_domain_entity(rate) for rate in hotel_active_rates]
