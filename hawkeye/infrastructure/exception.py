# coding=utf-8
"""
Exceptions
"""


class HawkeyeException(Exception):
    error_code = "0001"
    message = "Something went wrong"

    def __init__(self, description=None, extra_payload=None):
        self.description = description
        self.extra_payload = extra_payload

    def __str__(self):
        return "exception: error_code=%s message=%s description=%s" % (
            self.error_code,
            self.message,
            self.description,
        )

    def with_description(self, description):
        self.description = description
        return self

    @property
    def code(self):
        return "0401" + self.error_code


class ValidationException(HawkeyeException):
    error_code = "0002"
    message = "Validation Exception"

    def __init__(
        self,
        error=None,
        description=None,
        message=None,
        extra_payload=None,
        format_dict=None,
    ):
        if error:
            self.error_code = error.error_code
            self.message = (
                error.message.format(**format_dict) if format_dict else error.message
            )
        else:
            self.error_code = self.error_code
            self.message = message if message else self.message
        super(ValidationException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class DatabaseError(HawkeyeException):
    error_code = "0003"
    message = "Something went wrong with the database."


class ResourceNotFoundException(HawkeyeException):
    error_code = "0004"
    message = "Requested resource not found."


class HotelRoomTypeExistException(HawkeyeException):
    message = "Hotel Room type is already present."


class CrsClientException(HawkeyeException):
    message = "Unable to fetch data from Crs"


class CatalogClientException(HawkeyeException):
    message = "Unable to fetch data from catalog"


class CsvRuleFileValidationException(HawkeyeException):
    error_code = "0008"
    message = "Unable to upload Csv file"


class SKUCodeNotFoundException(HawkeyeException):
    message = "sku code not found"


class PriceAggregateInitializationError(HawkeyeException):
    error_code = "0009"
    message = "Base Room Type is required to initialize Price PriceAggregate."


class InvalidFileTypeError(HawkeyeException):
    error_code = "0010"
    message = "File type is invalid"


class InvalidHotelIdError(HawkeyeException):
    error_code = "0011"
    message = "Hotel id is invalid"


class MissingOccupancyRuleError(HawkeyeException):
    error_code = "0012"
    message = "Occupancy Rule missing"


class PercentageOccupancyRangeError(HawkeyeException):
    error_code = "0013"
    message = "Start and end occupancy range should not be configured same for Percentage Occupancy Rule"


class RackRateMissingError(HawkeyeException):
    error_code = "0014"
    message = "Rack rate is not properly configured"


class MissingPriceBoundaryRuleError(HawkeyeException):
    error_code = "0015"
    message = "Price Boundary Rule Missing for Extra-Adult Sku Types"


class NegativeFinalPriceError(HawkeyeException):
    error_code = "0016"
    def __init__(self, key):
        self.message = f"Final Price should not be negative for {key}"


class PriceAlertExpiredError(HawkeyeException):
    error_code = "0017"

    def __init__(self, key):
        self.message = f"Price Alert {key} has been expired and is no longer valid for approval"
        super().__init__()


class InvalidRoomTypeError(HawkeyeException):
    error_code = "0018"
    message = "Room type is Invalid"


class PriceOutOfBoundsError(HawkeyeException):
    error_code = "0019"
    message = "Competitive Price is outside the set guardrail boundaries"

    def __init__(self, hotel_id, price, stay_date):
        self.description = f"Price from Competitive Hotel: {hotel_id} is {price} for date {stay_date}"


class InvalidHotelMappingError(HawkeyeException):
    error_code = "0019"
    message = "Invalid Competitive Hotel Mapping"
