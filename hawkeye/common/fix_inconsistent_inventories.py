import logging
from datetime import datetime
from object_registry import inject

from hawkeye.application.decorators import session_manager
from hawkeye.application.services.inventory_service import InventoryService
from hawkeye.domain.price_calculation.factories.price_factory import PriceFactory
from hawkeye.infrastructure.exception import CrsClientException
from hawkeye.infrastructure.repositories.hotel_inventory_repository import HotelRoomTypeInventoryRepository
from treebo_commons.utils import dateutils


logger = logging.getLogger(__name__)


@inject(inventory_service=InventoryService, hotel_inventory_repository=HotelRoomTypeInventoryRepository)
@session_manager(commit=True)
def fix_inconsistent_inventories(inventory_service, hotel_inventory_repository, price_aggregate):
    logger.info(f"Fixing inconsistent room inventory for hotel id:{price_aggregate.hotel_id}, date: {str(price_aggregate.target_date)}")
    crs_hotel_room_inventories = []
    try:
        crs_hotel_room_inventories = inventory_service.load_hotel_inventory_from_crs(
            hotel_id=price_aggregate.hotel_id, from_date=price_aggregate.target_date, to_date=price_aggregate.target_date)
    except CrsClientException as ex:
        logger.error(f"Skipped inventory sync for hotel id:{price_aggregate.hotel_id}, Exception: {ex}")

    if not crs_hotel_room_inventories:
        return price_aggregate

    hotel_inventory_repository.update_all(crs_hotel_room_inventories)

    price_aggregates = PriceFactory.create_base_room_type_prices(
        hotel_id=price_aggregate.hotel_id,
        start_date=datetime.combine(price_aggregate.target_date, datetime.min.time(), tzinfo=dateutils.get_timezone()),
        end_date=datetime.combine(price_aggregate.target_date, datetime.min.time(), tzinfo=dateutils.get_timezone()),
        price_trigger_id=price_aggregate.price_trigger_id,
        base_room_type_price_rules=price_aggregate.base_room_type_price_rules,
        room_type_entities=price_aggregate.room_type_entities,
        hotel_room_inventories=crs_hotel_room_inventories,
        incremental_price_rules=price_aggregate.incremental_price_rules,
        competitive_price_weightages=price_aggregate.competitive_price_weightages,
        compset_pricing_thresholds=price_aggregate.compset_pricing_thresholds,
    )

    return price_aggregates[0]

