import logging

from treebo_commons.utils import dateutils

from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.constants.hawkeye_constant import (
    IGNORE_PRICE_DIFFERENCE,
    BASE_ROOM_PRICE_CACHE_EXPIRY
)
from hawkeye.domain.value_objects.rule_definition import RuleDefinitions
from hawkeye.infrastructure.base_entity import EntityChangeTracker
from hawkeye.infrastructure.cache import cache
from hawkeye.infrastructure.cache.cache_key import KeyConvertor
from object_registry import locate_instance

logger = logging.getLogger(__name__)


class RoomTypePrice(EntityChangeTracker):
    """
    Price Update config
    """

    def __init__(
        self,
        hotel_id,
        target_date,
        input_price,
        final_price,
        rules: RuleDefinitions,
        is_published,
        price_trigger_id,
        room_type=None,
        is_skipped=False,
        occupancy_percentage=None,
        sku_code=None,
        sku_name=None,
        id=None,
        new=True,
        dirty=True,
    ):
        super().__init__(new, dirty)
        self.id = id
        self.hotel_id = hotel_id
        self.room_type = room_type
        self.target_date = target_date
        self.input_price = input_price
        self.final_price = final_price
        self.rules = rules
        self.is_published = is_published
        self.price_trigger_id = price_trigger_id
        self.sku_name = sku_name
        self.is_skipped = is_skipped
        self.sku_code = sku_code
        self.occupancy_percentage = occupancy_percentage

    def apply_price_rule(self, price_rule):
        self.final_price = self.final_price * float(price_rule.factor) + float(price_rule.sum_factor)
        self.rules.append(price_rule)

    def apply_price_boundaries_rule(self, price_rule):
        if self.final_price > price_rule.end_price:
            self.final_price = float(price_rule.end_price)
        elif self.final_price < price_rule.start_price:
            self.final_price = float(price_rule.start_price)

        self.rules.append(price_rule)

    def mark_published(self):
        self.is_published = True
        self.mark_dirty()

    def mark_unpublished(self):
        self.is_skipped = False
        self.is_published = False
        self.mark_dirty()

    def populate_is_skipped(self):
        final_price_key = KeyConvertor.make_final_price_key(self.hotel_id, self.sku_code, self.target_date)
        current_price = cache.get_from_cache(final_price_key)
        if current_price and (float(current_price) - IGNORE_PRICE_DIFFERENCE <= self.final_price <=
                              float(current_price) + IGNORE_PRICE_DIFFERENCE):
            self.is_skipped = True
            self.mark_published()
            return

        cache.set_in_cache(final_price_key, self.final_price, expiry=self._get_final_price_expiry())
        self.is_skipped = False

    def populate_sku_code(self, occupancy=None):
        # TODO: Populate this using HotelRoomTypeModel when creating this Entity
        cache_key = KeyConvertor.make_sku_code_key(self.room_type, occupancy)
        sku_code = cache.get_from_cache(cache_key)
        if sku_code:
            self.sku_code = sku_code
            return
        catalog_service = locate_instance(CatalogService)
        sku_name_to_sku_code = catalog_service.fetch_and_cache_skus()
        self.sku_code = sku_name_to_sku_code[cache_key]

    def _get_final_price_expiry(self):
        return int((
                dateutils.add(self.target_date, days=BASE_ROOM_PRICE_CACHE_EXPIRY) - dateutils.current_date()
        ).total_seconds())
