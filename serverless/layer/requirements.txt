--trusted-host pypi.tree.bo
--extra-index-url https://pypi.tree.bo/simple/

Werkzeug==2.0.3
Flask==2.0.2
SQLAlchemy==1.4.27
flasgger==0.9.5
flask-swagger-ui==3.6.0
git-pylint-commit-hook
requests==2.23.0
#easyjoblite==0.7.11
#easyjoblite-p3==1.4.1
transitions==0.6.8
aenum==2.1.2
python-dateutil==2.8.0
aiohttp==3.7.4.post0
object-mapper==1.1.0
psycogreen==1.0
marshmallow>=2,<3
pycountry==22.1.10
logstash-formatter==0.5.16
newrelic==**********

#Utils
apispec==3.3.2,<4
apispec-webframeworks==0.5.2
jsonpickle==1.4.1
simplejson==3.17.0

# Devops
flaskhealthcheck==1.4.3

# Google's phonenumber library
phonenumbers==8.10.17

# Segment analytics
analytics-python==1.2.3

# Sentry sdk
sentry-sdk[flask]==1.4.3
boto3==1.20.11
PyYAML==5.4.1
kombu>=5.2.0
treebo-commons==3.2.7

# Keep this at the end, since this library uses a modified version of marshmallow library.
# If there is some library after this, which installs marshmallow, then importing this will fail with:
# ImportError: cannot import name 'SchemaJit'
toastedmarshmallow==2.15.2.post1
pydantic==2.10.6
