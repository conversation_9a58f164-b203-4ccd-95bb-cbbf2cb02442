from ths_common.exceptions import CRSError


class PolicyError(CRSError):
    # Use Error code sequence starting "09" and "10" and "11"
    NOT_AUTHORIZED_TO_CREATE_ORDER = (
        "1081",
        "You are not authorized to create order",
    )
    NOT_AUTHORIZED_TO_EDIT_ORDER = (
        "1082",
        "You are not authorized to edit order",
    )
    NOT_AUTHORIZED_TO_CANCEL_ORDER = (
        "1083",
        "You are not authorized to cancel order",
    )
    NOT_AUTHORIZED_TO_SETTLE_BILL_FOR_ORDER = (
        "1084",
        "You are not authorized to settle bill for an order",
    )
    NOT_AUTHORIZED_TO_PRINT_BILL_FOR_ORDER = (
        "1085",
        "You are not authorized to print bill for an order",
    )
    NOT_AUTHORIZED_TO_SPLIT_BILL_FOR_ORDER = (
        "1086",
        "You are not authorized to split bill for an order",
    )
    NOT_AUTHORIZED_TO_SPLIT_ORDER_ITEMS_TO_MULTIPLE_BILLS_FOR_ORDER = (
        "1087",
        "You are not authorized to split order items to multiple bills for an order",
    )
