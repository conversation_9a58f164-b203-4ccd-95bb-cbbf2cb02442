import logging
import os

from kombu import Exchange, Producer
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from pos.application.errors import PosConfigurationError
from pos.application.exceptions import PosException
from pos.infrastructure.messaging.queue_service import BaseQueueService

logger = logging.getLogger(__name__)


@register_instance()
class IntegrationEventPublisher(BaseQueueService):
    def _setup_entities(self):
        self._integration_event_exchange = Exchange(
            'pos-events', type='topic', durable=True
        )
        self._tenant_wise_producers = dict()
        for tenant_id, conn in self.tenant_wise_connection.items():
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=conn.channel(), exchange=self._integration_event_exchange
            )

    def publish(self, event):
        current_tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        self._initialize()
        payload = IntegrationEventPublisher.get_event_payload(event)
        logger.debug('Current tenant %s', get_current_tenant_id())
        logger.debug('Publishing event %s', payload)

        if not self._tenant_wise_producers[current_tenant_id]:
            raise PosException(
                error=PosConfigurationError.NO_RMQ_CONFIG_FOR_TENANT,
                description="RMQ Producer not configured for tenant_id: {0}".format(
                    current_tenant_id
                ),
            )

        self._publish(
            self._tenant_wise_producers[current_tenant_id],
            payload,
            event.event_type.routing_key,
        )

    @staticmethod
    def get_event_payload(event):
        return event.body
