from marshmallow import Schema, fields
from marshmallow.decorators import post_load
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import MoneyField

from pos.core.api_docs import swag_schema
from pos.domain.order.value_objects.discount import Discount
from pos.domain.order.value_objects.room_booking_detail import (
    RoomBookingCustomerChargeToDetail,
    RoomBookingDetail,
)
from shared_kernel.serializers.validators import validate_positive_integer
from shared_kernel.serializers.value_objects import TaxDetailSchema


@swag_schema
class PriceSchema(Schema):
    pretax_amount = MoneyField(
        allow_none=True, required=False, description='Amount exclusive of taxes'
    )
    posttax_amount = MoneyField(
        allow_none=True, required=False, description='Amount inclusive of taxes'
    )
    tax_amount = MoneyField(
        allow_none=True, required=False, description='Amount inclusive of taxes'
    )
    tax_details = fields.Nested(TaxDetailSchema, many=True)


@swag_schema
class RoomBookingCustomerChargeToSchema(Schema):
    guest_id = fields.String()

    @post_load
    def create_object(self, data):
        return RoomBookingCustomerChargeToDetail(guest_id=data.get("guest_id"))


@swag_schema
class RoomBookingDetailSchema(Schema):
    crs_booking_id = fields.String()
    room_number = fields.String(required=False, allow_none=True)
    charge_to = fields.Nested(
        RoomBookingCustomerChargeToSchema,
        many=True,
        description="Should only be sent if settlement_method is transfer_to_room",
        required=False,
        allow_none=True,
    )

    @post_load
    def create_object(self, data):
        return RoomBookingDetail(
            crs_booking_id=data.get('crs_booking_id'),
            room_number=data.get('room_number'),
            charge_to=data.get('charge_to'),
        )


@swag_schema
class GuestDetailsSchema(Schema):
    actual_checkin_date = fields.DateTime()
    actual_checkout_date = fields.DateTime()
    guest_id = fields.String()


@swag_schema
class SettleOrderRoomBookingDetailSchema(Schema):
    crs_booking_id = fields.String()
    assigned_to = fields.Nested(GuestDetailsSchema, many=True)
    room_stay_id = fields.Integer()


@swag_schema
class DiscountSchema(Schema):
    discount_id = fields.String()
    name = fields.String(required=True)
    value_type = fields.String(required=True)
    value = fields.Integer(required=True)
    amount = MoneyField(description='Discount Amount', required=True)

    @post_load
    def create_object(self, data):
        return Discount(
            discount_id=data.get('discount_id'),
            name=data.get('name'),
            value_type=data.get("value_type"),
            amount=data.get("amount"),
            value=data.get("value"),
        )
