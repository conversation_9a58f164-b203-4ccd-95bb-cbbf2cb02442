from flask import request

from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.request.order import EditOrderSchema
from pos.api.serializers.response.order import OrderWithKOTResponseSchema
from pos.application.services.order_application_service import OrderApplicationService
from pos.common.request_parsers import (
    read_user_data_from_request_header_and_set_context,
)
from pos.core.api_docs import swag_route
from pos.core.globals import pos_context
from shared_kernel.api_helpers.request_parsers import schema_wrapper_and_version_parser
from shared_kernel.api_response import ApiResponse


@swag_route
@pos_bp.route('/orders/<string:order_id>', methods=['PATCH'])
@schema_wrapper_and_version_parser(EditOrderSchema)
@inject(order_app_service=OrderApplicationService)
def edit_order(order_app_service, order_id, resource_version, parsed_request):
    """Edit the given POS Order
    ---
    operationId: edit_order
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: Edit the POS Order
        tags:
            - Order
        parameters:
            - in: path
              name: order_id
              description: The order_id of the pos order that needs to be edited
              required: True
              type: string
            - in: body
              name: edit_order_details
              description: The order details which needs to be updated
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/definitions/EditOrderSchema"
        responses:
            200:
                description: Updated POS Order object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/OrderWithKOTResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header_and_set_context(
        default="backend-system"
    )
    order_with_kot_dto = order_app_service.edit_order(
        parsed_request, user_data, order_id, resource_version
    )
    order_response_schema = OrderWithKOTResponseSchema()
    response = order_response_schema.dump(
        {
            'order': order_with_kot_dto.order_aggregate,
            'kots': order_with_kot_dto.kots,
            'removed_items': order_with_kot_dto.removed_items,
        }
    )
    return ApiResponse.build(data=response.data, status_code=201)
