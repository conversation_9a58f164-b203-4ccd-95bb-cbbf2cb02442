class EditOrderDto:
    def __init__(
        self,
        extra_information,
        order_type,
        order_items,
        seller_type,
        remarks,
        room_booking_details,
        table_id,
        customers,
        bill_to,
        source_of_customer,
        scheduled_datetime,
        status,
        guest_count,
        order_remarks,
    ):
        self.extra_information = extra_information
        self.order_type = order_type
        self.seller_type = seller_type
        self.remarks = remarks
        self.order_items = order_items
        self.room_booking_detail = room_booking_details
        self.table_id = table_id
        self.customers = customers
        self.guest_count = guest_count
        self.bill_to = bill_to
        self.source_of_customer = source_of_customer
        self.scheduled_datetime = scheduled_datetime
        self.status = status
        self.order_remarks = order_remarks

    def update_added_by_in_order_item_discounts(self, added_by):
        for order_item in self.order_items:
            order_item.update_added_by_in_discounts(added_by)


class EditOrderItemDto:
    def __init__(
        self,
        order_item_id,
        sku_id,
        sku_category_code,
        item_name,
        quantity,
        unit_price_pretax,
        unit_price_posttax,
        item_detail,
        discounts,
        charge_type,
        bill_to_type,
        remarks,
        kitchen_ids,
        is_complimentary,
        complimentary_details,
        status,
        order_remarks,
    ):
        self.order_item_id = order_item_id
        self.sku_id = sku_id
        self.sku_category_code = sku_category_code
        self.item_name = item_name
        self.quantity = quantity
        self.unit_price_pretax = unit_price_pretax
        self.unit_price_posttax = unit_price_posttax
        self.item_detail = item_detail
        self.charge_type = charge_type
        self.bill_to_type = bill_to_type
        self.remarks = remarks
        self.is_complimentary = is_complimentary
        self.complimentary_details = complimentary_details
        self.status = status
        self.charge_to = None
        self.discounts = discounts
        self.kitchen_ids = kitchen_ids
        self.order_remarks = order_remarks

    @property
    def total_price_pretax(self):
        return (
            self.unit_price_pretax * self.quantity if self.unit_price_pretax else None
        )

    @property
    def total_price_posttax(self):
        return (
            self.unit_price_posttax * self.quantity if self.unit_price_posttax else None
        )

    def update_added_by_in_discounts(self, added_by):
        if not self.discounts:
            return
        for discount in self.discounts:
            discount.update_added_by(added_by)


class EditPosCustomerDto(object):
    def __init__(
        self,
        customer_id,
        company_profile_id,
        first_name,
        last_name,
        phone,
        email,
        gstin_num,
        room_booking_details=None,
    ):
        self.customer_id = customer_id
        self.first_name = first_name
        self.last_name = last_name
        self.phone = phone
        self.email = email
        self.gstin_num = gstin_num
        self.company_profile_id = company_profile_id
        self.room_booking_details = room_booking_details


class OrderRemarkDto(object):
    def __init__(self, order_item_id, action, remark):
        self.order_item_id = order_item_id
        self.action = action
        self.remark = remark


class EditOrderRemarkDto(object):
    def __init__(self, remark):
        self.remark = remark
