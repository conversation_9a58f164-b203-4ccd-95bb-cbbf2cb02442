import logging
import os
from urllib.parse import urlparse

import boto3 as boto3
from botocore.client import Config
from botocore.exceptions import ClientError

from object_registry import register_instance
from shared_kernel.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from ths_common.exceptions import DownstreamSystemFailure

EXTERNAL_CLIENTS_LOGGER_PREFIX = "external_clients_"

logger = logging.getLogger(EXTERNAL_CLIENTS_LOGGER_PREFIX + __name__)


@register_instance()
class AwsServiceClient(BaseExternalClient):
    """
    Amazon Web Services Client
    """

    def get_domain(self):
        # Stub to confirm to abstract implementation BaseExternalClient
        raise NotImplementedError("get_domain for AWS client is invalid")

    @staticmethod
    def get_region():
        return os.environ.get('AWS_REGION')

    @staticmethod
    def get_s3_bucket_name():
        return os.environ.get('AWS_S3_BUCKET_NAME')

    @classmethod
    def get_client(cls, service_name):
        session = boto3.session.Session()
        return session.client(
            service_name,
            region_name=cls.get_region(),
            config=Config(signature_version='s3v4'),
        )

    @classmethod
    def get_s3_client(cls):
        return cls.get_client('s3')

    @classmethod
    def upload_file_to_s3_and_get_presigned_url(
        cls, s3_directory, file_path, expires_in
    ):
        key_name = cls.upload_file_to_s3(file_path, s3_directory)
        return cls.get_presigned_url(key_name, expires_in)

    @classmethod
    def upload_file_to_s3(cls, file_path: str, s3_directory):
        s3_client = cls.get_s3_client()
        try:
            object_name = file_path.split('/')[-1]
            key_name = s3_directory + object_name
            s3_client.upload_file(file_path, cls.get_s3_bucket_name(), key_name)
        except FileNotFoundError:
            logger.exception("File not found at: {0}".format(file_path))
            raise
        except ClientError as e:
            logging.error(e)
            raise DownstreamSystemFailure(message="Failed to upload file to S3")
        except Exception:
            logger.exception("Unknown error occurred while upload file to S3")
            raise

        return key_name

    @classmethod
    def get_presigned_url(cls, key_name: str, link_expires_in=None):
        s3_client = cls.get_s3_client()
        try:
            return s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': cls.get_s3_bucket_name(), 'Key': key_name},
                ExpiresIn=link_expires_in,
            )
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to get signed url for file.",
                extra_payload={"exception": e},
            )

    @classmethod
    def get_presigned_url_from_s3_url(cls, s3_url: str, link_expires_in=None):
        try:
            url = urlparse(s3_url)
            url = url.path.lstrip("/")
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to parse url for s3 link: {}".format(s3_url),
                extra_payload={"exception": e},
            )
        return cls.get_presigned_url(url, link_expires_in)
