from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from pos.domain.order.entities.order_item import OrderItem
from pos.domain.order.value_objects.discount import Discount
from pos.domain.order.value_objects.sku import SkuVO
from pos.domain.order.value_objects.split_bill import OrderItemBill
from pos.infrastructure.database.base_db_to_domain_entity_adaptor import BaseAdaptor
from pos.models import OrderItemModel
from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes
from ths_common.pos.constants.order_constants import PosOrderItemStatus


class OrderItemAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: OrderItem, **kwargs):
        # noinspection PyArgumentList
        bills = (
            [order_item_bill.to_json() for order_item_bill in domain_entity.bills]
            if domain_entity.bills
            else None
        )
        discounts = []
        if domain_entity.discounts:
            discounts = [discount.to_json() for discount in domain_entity.discounts]
        return OrderItemModel(
            order_id=kwargs.get('order_id'),
            order_item_id=domain_entity.order_item_id,
            sku_id=domain_entity.sku_vo.sku_id,
            sku_category_code=domain_entity.sku_vo.sku_category_code,
            item_name=domain_entity.sku_vo.item_name,
            quantity=domain_entity.quantity,
            unit_price_pretax=domain_entity.unit_price_pretax.amount
            if domain_entity.unit_price_pretax
            else None,
            unit_price_posttax=domain_entity.unit_price_posttax.amount
            if domain_entity.unit_price_posttax
            else None,
            status_updated_at=domain_entity.status_updated_at,
            charge_id=domain_entity.charge_id,
            item_detail=domain_entity.sku_vo.item_detail,
            status=domain_entity.status.value,
            deleted=domain_entity.deleted,
            bills=bills,
            charge_to=domain_entity.charge_to,
            kitchen_ids=domain_entity.kitchen_ids,
            bill_to_type=domain_entity.bill_to_type.value
            if domain_entity.bill_to_type
            else None,
            charge_type=domain_entity.charge_type.value,
            remarks=domain_entity.remarks,
            added_by=domain_entity.added_by,
            complimentary_details=domain_entity.complimentary_details,
            is_complimentary=domain_entity.is_complimentary,
            discounts=discounts,
        )

    def to_domain_entity(self, db_entity: OrderItemModel, **kwargs):
        sku_vo = SkuVO(
            db_entity.sku_category_code,
            db_entity.item_name,
            sku_id=db_entity.sku_id,
            item_detail=db_entity.item_detail,
        )
        currency = kwargs.get('base_currency')
        bills = (
            [OrderItemBill.from_json(bill) for bill in db_entity.bills]
            if db_entity.bills
            else None
        )
        discounts = []
        if db_entity.discounts:
            discounts = [
                Discount.from_json(discount, currency)
                for discount in db_entity.discounts
            ]
        return OrderItem(
            order_item_id=db_entity.order_item_id,
            sku_vo=sku_vo,
            quantity=db_entity.quantity,
            unit_price_posttax=Money(db_entity.unit_price_posttax, currency)
            if db_entity.unit_price_posttax
            else None,
            unit_price_pretax=Money(db_entity.unit_price_pretax, currency)
            if db_entity.unit_price_pretax
            else None,
            status=PosOrderItemStatus(db_entity.status) if db_entity.status else None,
            deleted=db_entity.deleted,
            charge_id=db_entity.charge_id,
            kitchen_ids=db_entity.kitchen_ids,
            charge_type=ChargeTypes(db_entity.charge_type)
            if db_entity.charge_type
            else None,
            bill_to_type=ChargeBillToTypes(db_entity.bill_to_type)
            if db_entity.bill_to_type
            else None,
            status_updated_at=db_entity.status_updated_at,
            bills=bills,
            charge_to=db_entity.charge_to,
            remarks=db_entity.remarks,
            complimentary_details=db_entity.complimentary_details,
            added_by=db_entity.added_by,
            is_complimentary=db_entity.is_complimentary,
            discounts=discounts,
        )
