from pos.infrastructure.database.base_db_to_domain_entity_adaptor import BaseA<PERSON>ptor
from pos.pos_restaurant.entities.seller_table import SellerTable
from shared_kernel.infrastructure.database.common_models import SellerTableModel
from ths_common.pos.constants.order_constants import SellerTableStatus


class SellerTableAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: SellerTable, **kwargs):
        # noinspection PyArgumentList
        return SellerTableModel(
            table_id=domain_entity.table_id,
            seller_id=domain_entity.seller_id,
            name=domain_entity.name,
            table_number=domain_entity.table_number,
            current_status=domain_entity.current_status.value,
            status_updated_at=domain_entity.status_updated_at,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            deleted=domain_entity.deleted,
        )

    def to_domain_entity(self, db_entity: SellerTableModel, **kwargs):
        # noinspection PyArgumentList
        return SellerTable(
            table_id=db_entity.table_id,
            seller_id=db_entity.seller_id,
            name=db_entity.name,
            table_number=db_entity.table_number,
            current_status=SellerTableStatus(db_entity.current_status),
            status_updated_at=db_entity.status_updated_at,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            deleted=db_entity.deleted,
        )
