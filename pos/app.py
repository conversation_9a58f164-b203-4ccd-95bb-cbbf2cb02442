# coding=utf-8
"""
App Initiate file
"""
import logging
import os

import click
from flask import Flask
from healthcheck import HealthCheck
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.request_tracing.flask.after_request import clear_request_context

from object_registry import locate_instance
from pos.api import pos_bp
from pos.commands.erp_worker import start_erp_worker
from pos.commands.integration_event_worker import start_integration_event_worker
from pos.config.default_config import DefaultConfig
from pos.core.api_docs import init_docs
from pos.core.logging.logging_conf import configure_logging
from pos.middlewares.common_middlewares import exception_handler

logger = logging.getLogger(__name__)


def create_app():
    """
    Create App
    :return:
    """
    app = Flask(
        __name__,
        instance_relative_config=True,
        instance_path=os.environ.get('FLASK_APP_INSTANCE_PATH'),
    )
    setup_config(app)
    register_extensions(app)
    register_blueprints(app)
    register_commands(app)
    app.before_request_funcs = {None: app.config['BEFORE_REQUEST_MIDDLEWARES']}
    app.after_request_funcs = {None: app.config['AFTER_REQUEST_MIDDLEWARES']}
    register_error_handlers(app)
    configure_swagger(app)
    init_docs(app)
    setup_health_check(app)

    @app.shell_context_processor
    def make_shell_context():
        return {'locate_instance': locate_instance, 'ctx': app.test_request_context()}

    @app.teardown_request
    def shutdown_session(exception=None):
        db_engine.remove_session()

    @app.teardown_appcontext
    def clear_thread_local(exception=None):
        clear_request_context()

    return app


def register_error_handlers(app):
    """

    :param app:
    :return:
    """
    app.register_error_handler(Exception, exception_handler)


def setup_config(app):
    """ " """
    environment = os.environ.get('APP_ENV', 'local')
    # load the default config
    app.config.from_object(DefaultConfig)
    # load from config set by the app
    try:
        app.config.from_envvar('POS_CONFIG_FILE', silent=False)
    except RuntimeError:
        if not os.environ.get('POS_CONFIG_FILE'):
            click.echo(
                "POS_CONFIG_FILE environment variable is not set. Default Config will be used"
            )
        else:
            click.echo(
                "Couldn't load config file from: %s" % os.environ.get('POS_CONFIG_FILE')
            )

    click.echo(
        "Setting up Flask App: '%s', using environment: '%s', and config file: %s"
        % (__name__, environment, os.environ.get('POS_CONFIG_FILE', 'DefaultConfig'))
    )
    configure_logging(app)


def setup_health_check(app):
    """

    :param app:
    :return:
    """
    health = HealthCheck(app, '/api/health', ['rds'])

    def rds_available():
        for tenant_id, scoped_session in db_engine.tenant_wise_sessions.items():
            Session = scoped_session()
            try:
                logger.info(
                    "Making connection with RDS for tenant_id {0}".format(tenant_id)
                )
                Session.execute('SELECT 1')
                logger.info("Connection successful with RDS")
            except Exception as e:
                logger.error('Exception occured while connection with RDS %s' % e)
                raise e
            finally:
                scoped_session.remove()
        return True, "connection successful"

    health.add_check(rds_available)


def register_extensions(app):
    """
    Registering extensions
    :param app:
    :return:
    """
    pass


def register_blueprints(app):
    """
    Registering BluePrints
    :param app:
    :return:
    """
    app.register_blueprint(pos_bp, url_prefix=pos_bp.url_prefix)


def register_commands(app):
    """Register Click commands."""
    app.cli.add_command(start_integration_event_worker)
    app.cli.add_command(start_erp_worker)


def configure_swagger(app):
    app.config['SWAGGER'] = {
        'title': 'POS',
        'uiversion': 3,
    }
