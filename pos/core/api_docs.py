import apispec.exceptions
from apispec import APISpec
from apispec.ext.marshmallow import MarshmallowPlugin
from apispec_webframeworks.flask import FlaskPlugin
from flasgger import Swagger

schema_classes = []
swagger_views = []


def swag_schema(cls):
    schema_classes.append(cls)
    return cls


def swag_route(func):
    swagger_views.append(func)
    return func


def setup_schema_definition(spec):
    for cls in schema_classes:
        try:
            spec.components.schema(cls.__name__, schema=cls)
        except apispec.exceptions.DuplicateComponentNameError:
            continue


def setup_path(spec: APISpec):
    for view in swagger_views:
        spec.path(view=view)


def init_docs(app):
    ctx = app.test_request_context()
    ctx.push()

    # Create an APISpec
    spec = APISpec(
        title='Swagger POS',
        version='1.0.0',
        openapi_version='3.0.2',
        plugins=[FlaskPlugin(), MarshmallowPlugin()],
    )

    setup_schema_definition(spec)
    setup_path(spec)
    sw = Swagger(template=spec.to_dict())
    sw.init_app(app)
