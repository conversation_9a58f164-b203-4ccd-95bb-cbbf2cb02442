class Reservation(object):
    def __init__(
        self,
        reservation_id=None,
        table_id=None,
        seller_id=None,
        start_datetime=None,
        duration=None,
        guests=None,
        guest_count=None,
        occasion=None,
        allergens=None,
        special_requests=None,
        status=None,
        created_at=None,
        modified_at=None,
        deleted=False,
    ):
        self.reservation_id = reservation_id
        self.table_id = table_id
        self.seller_id = seller_id
        self.start_datetime = start_datetime
        self.duration = duration
        self.guests = guests
        self.guest_count = guest_count
        self.occasion = occasion
        self.allergens = allergens
        self.special_requests = special_requests
        self.status = status
        self.created_at = created_at
        self.modified_at = modified_at
        self.deleted = deleted

    def delete(self):
        self.deleted = True

    def update_table_id(self, table_id):
        self.table_id = table_id

    def update_order_id(self, order_id):
        self.order_id = order_id

    def update_start_datetime(self, start_datetime):
        self.start_datetime = start_datetime

    def update_duration(self, duration):
        self.duration = duration

    def update_guests(self, guests):
        self.guests = guests

    def update_guest_count(self, guest_count):
        self.guest_count = guest_count

    def update_occasion(self, occasion):
        self.occasion = occasion

    def update_allergens(self, allergens):
        self.allergens = allergens

    def update_status(self, status):
        self.status = status

    def update_special_requests(self, special_requests):
        self.special_requests = special_requests
