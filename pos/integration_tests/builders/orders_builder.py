import json

from pos.integration_tests.builders.common_request_builder import Customer, OrderItems
from pos.integration_tests.config.sheet_names import *
from pos.integration_tests.utilities import excel_utils
from pos.integration_tests.utilities.common_utils import sanitize_test_data


class CreateOrderRequest(object):
    def __init__(self, sheet_name, test_case_id, seller_id='1'):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = OrderData(test_data, seller_id).__dict__


class OrderData(object):
    def __init__(self, test_data, seller_id):
        if sanitize_test_data(test_data['Customer_details']) is not None:
            self.bill_to = sanitize_test_data(
                Customer(json.loads(test_data['Customer_details'])).__dict__
            )
        self.extra_information = sanitize_test_data(test_data['extra_information'])
        if sanitize_test_data(test_data['order_items']) is not None:
            self.order_items = []
            order_items_type = test_data['order_items'].split(",")
            for order_type in order_items_type:
                order_data = excel_utils.get_test_case_data(
                    ORDER_ITEMS_SHEET_NAME, order_type
                )[0]
                self.order_items.append(
                    sanitize_test_data(OrderItems(order_data).__dict__)
                )
        self.order_type = sanitize_test_data(test_data['order_type'])
        self.remark = sanitize_test_data(test_data['remarks'])
        self.room_booking_details = sanitize_test_data(
            test_data['room_booking_details']
        )
        self.seller_id = sanitize_test_data(seller_id)
        self.table_number = sanitize_test_data(str(test_data['table_number']))
