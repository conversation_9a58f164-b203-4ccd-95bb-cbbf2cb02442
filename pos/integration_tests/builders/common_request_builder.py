import json

from pos.integration_tests.utilities.common_utils import sanitize_test_data


class Customer(object):
    def __init__(self, customer_data):
        self.email = sanitize_test_data(customer_data['email'])
        self.first_name = sanitize_test_data(customer_data['first_name'])
        self.gstin_num = sanitize_test_data(customer_data['gstin_num'])
        self.last_name = sanitize_test_data(customer_data['last_name'])
        self.phone = {
            'country_code': sanitize_test_data(customer_data['country_code']),
            'number': sanitize_test_data(customer_data['number']),
        }


class CustomerSettleOrder(object):
    def __init__(self, customer_data):
        self.company_profile_id = sanitize_test_data(
            customer_data['company_profile_id']
        )
        self.customer_id = sanitize_test_data(customer_data['customer_id'])
        self.email = sanitize_test_data(customer_data['email'])
        self.first_name = sanitize_test_data(customer_data['first_name'])
        self.gstin_num = sanitize_test_data(customer_data['gstin_num'])
        self.last_name = sanitize_test_data(customer_data['last_name'])
        self.phone = {
            'country_code': sanitize_test_data(customer_data['country_code']),
            'number': sanitize_test_data(customer_data['number']),
        }
        self.room_booking_details = RoomBookingDetails(
            customer_data, booking_id=None
        ).__dict__


class RoomBookingDetails(object):
    def __init__(self, room_booking_details, booking_id):
        self.guest_id = sanitize_test_data(room_booking_details['guest_id'])
        self.crs_booking_id = booking_id
        self.room_number = sanitize_test_data(room_booking_details['room_number'])
        self.room_stay_id = sanitize_test_data(room_booking_details['room_stay_id'])
        self.charge_to = sanitize_test_data(room_booking_details['charge_to'])


class OrderItems(object):
    def __init__(self, order_items_data, order_id=None):
        self.order_item_id = order_id
        self.bill_to_type = sanitize_test_data(order_items_data['bill_to_type'])
        self.charge_type = sanitize_test_data(order_items_data['charge_type'])
        self.item_name = sanitize_test_data(order_items_data['item_name'])
        self.quantity = sanitize_test_data(order_items_data['quantity'])
        self.item_detail = sanitize_test_data(order_items_data['item_detail'])

        if sanitize_test_data(order_items_data['discounts']):
            self.discounts = []
            discounts_types = order_items_data['discounts'].split('#')
            for dt in discounts_types:
                self.discounts.append(
                    sanitize_test_data(Discounts(json.loads(dt)).__dict__)
                )

        if sanitize_test_data(order_items_data['kitchen_ids']):
            self.kitchen_ids = []
            kitchen_id_types = order_items_data['kitchen_ids'].split(',')
            for dt in kitchen_id_types:
                self.kitchen_ids.append(sanitize_test_data(dt))

        self.remarks = sanitize_test_data(order_items_data['remarks'])
        self.sku_category_code = sanitize_test_data(
            order_items_data['sku_category_code']
        )
        self.sku_id = sanitize_test_data(order_items_data['sku_id'])
        self.unit_price_posttax = sanitize_test_data(
            order_items_data['unit_price_posttax']
        )
        self.unit_price_pretax = sanitize_test_data(
            order_items_data['unit_price_pretax']
        )


class Discounts(object):
    def __init__(self, discounts_data, discount_id=None):
        self.amount = sanitize_test_data(discounts_data['amount'])
        self.discount_id = discount_id
        self.value = sanitize_test_data(discounts_data['value'])
        self.value_type = sanitize_test_data(discounts_data['value_type'])
        self.name = sanitize_test_data(discounts_data['name'])
