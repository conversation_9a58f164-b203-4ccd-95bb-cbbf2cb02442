import json

from pos.integration_tests.builders import booking_action_builder, booking_builder
from pos.integration_tests.config.sheet_names import *
from pos.integration_tests.external_clients.crs_clients import CrsClient
from pos.integration_tests.requests.base_request import BaseRequest
from pos.integration_tests.utilities.common_utils import del_none


class BookingRequests(BaseRequest):
    def create_booking_request(self, test_case_id):
        request_json = json.dumps(
            del_none(
                booking_builder.CreateBookingRequest(
                    CREATE_BOOKING_SHEET_NAME, test_case_id
                ).__dict__
            )
        )
        response = CrsClient().create_booking(request_json).json()
        self.booking_id = response['data']['booking_id']
        self.booking_bill_id = response['data']['bill_id']
        return response

    def checkin_booking_request(self, test_case_id, booking_id):
        request_json = json.dumps(
            del_none(
                booking_action_builder.BookingActionBuilder(
                    CHECKIN_BOOKING_SHEET_NAME, test_case_id
                ).__dict__
            )
        )
        response = CrsClient().booking_action(request_json, booking_id).json()
        self.action_id = response['data']['action_id']
        return response

    @staticmethod
    def reverse_action_request(booking_id, action_id):
        response = CrsClient().reverse_booking_action(booking_id, action_id)
        return response
