import pytest

from pos.integration_tests.config.common_config import *
from pos.integration_tests.tests.base_test import BaseTest
from pos.integration_tests.tests.order.validations.validation_settle_order import (
    ValidationSettleOrder,
)


class TestSettleOrder(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id,previous_actions,tc_description, multi_currency,status_code, user_type,skip_message",
        [
            (
                "SettleOrder_01",
                [CREATE_ORDER_ONE_ORDERITEM],
                "Settle the Order with one order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_02",
                [CREATE_ORDER_TWO_ORDERITEM],
                "Settle the Order with two order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_03",
                [{'id': "CreateOrder_07", 'type': 'create_order'}],
                "Settle the Order with three order item",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_04",
                [
                    CREATE_ORDER_TWO_ORDERITEM,
                    {'id': "EditOrder_26", 'type': 'edit_order'},
                    {'id': "EditOrder_27", 'type': 'edit_order'},
                ],
                "Settle the Order after editing and changing quantity",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_05",
                [CREATE_ORDER_ONE_ORDERITEM],
                "Settle the order with charges as credit",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_06",
                [{'id': "CreateOrder_08", 'type': 'create_order'}],
                "Settle the Order with charges as non-credit and payments as null",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrder_07",
                [CREATE_ORDER_ONE_ORDERITEM],
                "Settle payment without status",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrder_08",
                [{'id': "CreateOrder_08", 'type': 'create_order'}],
                "settle payment without comment",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_09",
                [CREATE_ORDER_ONE_ORDERITEM],
                "settle payment without amount",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrder_10",
                [CREATE_ORDER_ONE_ORDERITEM],
                "settle payment without payment_mode",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrder_11",
                [CREATE_ORDER_ONE_ORDERITEM],
                "Settle Order without bill_to",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_12",
                [
                    CREATE_ORDER_ONE_ORDERITEM,
                    {'id': "SettleOrder_05", 'type': 'settle_order'},
                ],
                "Settle the already settled order",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrder_13",
                [{'id': "CreateOrder_21", 'type': 'create_order'}],
                "Settle a order with 100 items",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_14",
                [
                    CREATE_ORDER_ONE_ORDERITEM,
                    {'id': "CancelOrder_01", 'type': 'cancel_order'},
                ],
                "Settle a cancelled order",
                False,
                400,
                None,
                "",
            ),
            (
                "SettleOrder_16",
                CREATE_ORDER_FOR_BOOKING_WITH_TWO_ITEM,
                "Settle the Order with two order item into room",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_17",
                CREATE_ORDER_FOR_BOOKING_WITH_THREE_ITEM,
                "Settle the Order with three order item into room",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_18",
                CREATE_ORDER_FOR_BOOKING_WITH_TWO_ITEM,
                "Settle the Order without actual_checkout date",
                False,
                200,
                None,
                "",
            ),
            (
                "SettleOrder_19",
                CREATE_ORDER_FOR_BOOKING_WITH_THREE_ITEM
                + [{'id': 'EditOrder_26', 'type': 'edit_order_for_booking'}],
                "Settle the Order which is modified",
                False,
                200,
                None,
                "",
            ),
        ],
    )
    @pytest.mark.regression
    def test_settle_order(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        multi_currency,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)
        if multi_currency:
            seller_id = SELLER_ID[1]
        else:
            seller_id = SELLER_ID[0]
        if previous_actions:
            self.common_request_caller(client_, previous_actions, seller_id)

        if self.booking_request.booking_id:
            response = self.order_request.settle_order_for_booking_request(
                test_case_id, self.booking_request.booking_id
            )
        else:
            response = self.order_request.settle_order_request(
                client_, test_case_id, status_code, user_type
            )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(
                response,
                test_case_id,
                self.booking_request.booking_bill_id,
                self.order_request.bill_id,
                self.order_request.invoice_id,
                seller_id,
            )
        else:
            assert False, "Response status code is not matching"

        if self.booking_request.booking_id:
            cancel_booking = self.booking_request.reverse_action_request(
                self.booking_request.booking_id, self.booking_request.action_id
            )

    @staticmethod
    def validation(
        response,
        test_case_id,
        booking_bill_id=None,
        bill_id=None,
        invoice_id=None,
        seller_id='1',
    ):
        validation = ValidationSettleOrder(
            test_case_id, response, booking_bill_id, bill_id, invoice_id, seller_id
        )
        if booking_bill_id:
            validation.validate_booking_response()
        else:
            validation.validate_response()
