import json

from pos.integration_tests.config import sheet_names
from pos.integration_tests.requests.reservation_request import ReservationsRequest
from pos.integration_tests.utilities.common_utils import assert_, sanitize_test_data
from pos.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationCreateReservation:
    def __init__(self, client_, response, test_case_id, seller_id, order_id, user_type):
        self.test_data = get_test_case_data(
            sheet_names.NEW_RESERVATION_SHEET_NAME, test_case_id
        )[0]
        self.client_ = client_
        self.order_id = order_id
        self.user_type = user_type
        self.response = response
        self.seller_id = seller_id
        self.test_case_id = test_case_id
        self.ReservationsRequest = ReservationsRequest()

    def validate_response(self):
        expected_data = get_test_case_data(
            sheet_names.NEW_RESERVATION_SHEET_NAME, self.test_case_id
        )[0]
        self.validate_reservation(self.response, expected_data)

    def validate_reservation(self, response_data, expected_data):
        if expected_data['occasion'] and expected_data['occasion'] != 'NULL':
            assert_(
                sanitize_test_data(expected_data['occasion']),
                (response_data['data']['occasion']),
            )

        if expected_data['guest_count'] and expected_data['guest_count'] != 'NULL':
            assert_(
                sanitize_test_data(expected_data['guest_count']),
                (response_data['data']['guest_count']),
            )

        if expected_data['allergens'] and expected_data['allergens'] != 'NULL':
            assert_(
                sanitize_test_data(expected_data['allergens']),
                (response_data['data']['allergens']),
            )

        if expected_data['duration'] and expected_data['duration'] != 'NULL':
            assert_(
                sanitize_test_data(expected_data['duration']),
                (response_data['data']['duration']),
            )

        if (
            expected_data['special_requests']
            and expected_data['special_requests'] != 'NULL'
        ):
            assert_(
                sanitize_test_data(expected_data['special_requests']),
                (response_data['data']['special_requests']),
            )

        if (
            expected_data['start_datetime']
            and expected_data['start_datetime'] != 'NULL'
        ):
            assert_(
                sanitize_test_data(expected_data['start_datetime']),
                (response_data['data']['start_datetime']),
            )

        if expected_data['table_id'] and expected_data['table_id'] != 'NULL':
            assert_(
                str(sanitize_test_data(expected_data['table_id'])),
                str((response_data['data']['table_id'])),
            )

        assert response_data['data']['reservation_id'] is not None

    def validate_reservation_exist_in_get_reservation_response(
        self, client_, expected_reservation_id, status_code, seller_id
    ):
        get_reservations_response = self.ReservationsRequest.get_reservations_request(
            client_, status_code, seller_id
        )
        assert any(
            reservation['reservation_id'] == expected_reservation_id
            for reservation in get_reservations_response['data']
        )
