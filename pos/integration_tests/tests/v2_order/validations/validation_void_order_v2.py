from pos.integration_tests.config import sheet_names
from pos.integration_tests.utilities.common_utils import assert_
from pos.integration_tests.utilities.excel_utils import get_test_case_data
from thsc.crs.entities.billing import Bill


class ValidationVoidOrderV2:
    def __init__(self, test_case_id, response, bill_id):
        self.test_data = get_test_case_data(
            sheet_names.VOID_ORDER_SHEET_NAME, test_case_id
        )[0]
        self.response = response
        self.bill_id = bill_id

    def validate_response(self):
        assert_(self.test_data['pos_status'], self.response['data'][0]['message'])
        assert self.response['data'][0]['order_id'] is not None
        assert_(self.test_data['kitchen'], self.response['data'][0]['kitchen_id'])
        assert_(str(self.test_data['kot']), str(self.response['data'][0]['kot_id']))
