import json

from prometheus.domain.booking.services.booking_id_generator import BookingIdGenerator
from prometheus.integration_tests.builders import common_request_builder
from prometheus.integration_tests.builders.common_request_builder import booking_repo
from prometheus.integration_tests.config import common_config, sheet_names
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import (
    increment_date,
    sanitize_blank,
    sanitize_test_data,
)


class CreateBookingRequest(object):
    def __init__(self, sheet_name, test_case_id, hotel_id=None, enable_rate_plan=False, is_inclusion_added=False):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = _BookingData(test_data, hotel_id, enable_rate_plan, is_inclusion_added).__dict__


class EditBookingRequest(object):
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = _EditBookingData(test_data).__dict__
        if "EditBooking_07" in test_case_id:
            self.resource_version = 1000
        elif "EditBooking_08" in test_case_id:
            self.resource_version = None
        elif "EditBooking_09" in test_case_id:
            self.resource_version = 1
        else:
            self.resource_version = booking_repo().load(booking_id).booking.version


class EditCustomerRequest(object):
    def __init__(self, sheet_name, test_case_id, booking_id, is_booker=None):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        if "EditCustomer_09" in test_case_id:
            test_data['customer_id'] = 1000
        self.data = common_request_builder.Customer(test_data, is_booker).__dict__
        if "EditCustomer_16" in test_case_id:
            self.resource_version = 1000
        elif "EditCustomer_17" in test_case_id:
            self.resource_version = None
        elif "EditCustomer_18" in test_case_id:
            self.resource_version = 1
        else:
            self.resource_version = booking_repo().load(booking_id).booking.version


class EditBulkCustomerRequest(object):
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = []
        for customer_data in test_data:
            customer_payload = common_request_builder.Customer(customer_data).__dict__
            customer_payload["customer_id"] = customer_data['customer_id']
            self.data.append(customer_payload)
        self.resource_version = booking_repo().load(booking_id).booking.version


class _BookingData(object):
    def __init__(self, test_data, hotel_id, enable_rate_plan=False, is_inclusion_added=False):
        if sanitize_test_data(test_data[0]['booking_owner']):
            owner_data = excel_utils.get_test_case_data(sheet_names.customer_data_sheet_name,
                                                        test_data[0]['booking_owner'])
            self.booking_owner = common_request_builder.Customer(owner_data[0]).__dict__
        if test_data[0]['TC_ID'] == 'Booking_55':
            self.reference_number = sanitize_test_data(test_data[0]['reference_number'])
        else:
            self.reference_number = str(BookingIdGenerator.rand_x_digit_num())
        if test_data[0]['TC_ID'] == 'Booking_16':
            self.hotel_id = sanitize_test_data(test_data[0]['hotel_id'])
        elif test_data[0]['TC_ID'] == 'Booking_17':
            self.hotel_id = None
        else:
            self.hotel_id = hotel_id if hotel_id else common_config.HOTEL_ID[0]
        if sanitize_test_data(test_data[0]['room_stays']):
            room_test_data = excel_utils.get_test_case_data(sheet_names.add_room_stay_sheet_name,
                                                            test_data[0]['room_stays'])
            self.room_stays = []
            for room_data in room_test_data:
                room_stay = common_request_builder.RoomStay(room_data, enable_rate_plan, is_inclusion_added).__dict__
                self.room_stays.append(room_stay)
        self.source = common_request_builder.Source(test_data[0]).__dict__
        if sanitize_blank(test_data[0]['payments']) is not None:
            self.payments = []
            payment_data = excel_utils.get_test_case_data(sheet_names.add_payment_sheet_name,
                                                          test_data[0]['payments'])
            self.payments.append(common_request_builder.Payment(payment_data[0]).__dict__)
        if test_data[0]['TC_ID'] == 'Booking_31':
            self.hold_till = increment_date(int(test_data[0]['hold_till'][0]), '%Y-%m-%dT%H:%M:%S', 1)
        elif sanitize_test_data(test_data[0]['hold_till']):
            self.hold_till = increment_date(int(test_data[0]['hold_till'].split(":")[0]), common_config.CURRENT,
                                            int(test_data[0]['hold_till'].split(":")[1]))
        self.status = sanitize_test_data(test_data[0]['status'])
        self.comments = sanitize_test_data(test_data[0]['comments'])
        if sanitize_test_data(test_data[0]['travel_agent_details']):
            travel_agent_data = json.loads(test_data[0]['travel_agent_details'])
            self.travel_agent_details = common_request_builder.TravelAgent(travel_agent_data).__dict__
        if sanitize_test_data(test_data[0]['default_billed_entity_category']):
            self.default_billed_entity_category = test_data[0]['default_billed_entity_category']


class _EditBookingData(object):
    def __init__(self, test_data):
        self.reference_number = sanitize_blank(test_data[0]['reference_number'])
        self.source = common_request_builder.Source(test_data[0]).__dict__
        self.comments = sanitize_test_data(test_data[0]['comments'])
