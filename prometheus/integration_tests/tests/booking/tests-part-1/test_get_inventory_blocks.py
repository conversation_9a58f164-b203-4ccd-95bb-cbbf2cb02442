import pytest
from datetime import date, timedelta
from object_registry import locate_instance
from prometheus.application.inventory import InventoryBlockAsyncJobHandler
from prometheus.integration_tests.tests.booking.validations.validation_get_inventory_blocks import \
    ValidationGetInventoryBlockDetails
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestGetInventoryBlocks(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, skip_message, actions, perform_night_audit, block_type",
        [
            ("GetInventoryBlockDetails_01", 'Verify Inventory Block Details for an Early Check-in (ECI) Block '
                                            'on a Single-Day, Single-Room Booking',
             CREATE_ECI_BLOCK_FOR_BOOKING_V1, 200, SUPER_ADMIN, "", "", "", False, [], False, "early_checkin_block"),

            ("GetInventoryBlockDetails_02", 'Verify Inventory Block Details for a Late Check-out (LCO) Block '
                                            'on a Single-Day, Single-Room Booking',
             CREATE_LCO_BLOCK_FOR_BOOKING, 200, SUPER_ADMIN, "", "", "", False, [], False, "late_checkout_block"),

            ("CreateInventoryBlock_01", 'Verify Inventory Block Details for a Temporary (temp) Block on a '
                                        'Single-Day, Single-Room Booking',
             CREATE_TEMP_BLOCK_FOR_BOOKING_V1, 200, SUPER_ADMIN, "", "", "", False, [], False, "temp_block"),

            ("GetInventoryBlockDetails_03", 'Verify Inventory Block Details for a Late Checkout (LCO) Block on a '
                                            'Single-Day, Single-Room Booking with Same-Day Check-In and Check-Out, '
                                            'and Block is Released',
             CHECKIN_CHECKOUT_BOOKING_WITH_LCO_V1, 200, SUPER_ADMIN, "", "", "", False, [], False,
             "late_checkout_block"),

            ("GetInventoryBlockDetails_04", 'Verify Inventory Block Details for a Late Checkout (LCO) Block on a '
                                            'Single-Day, Single-Room Booking with Check-In, Night Audit, '
                                            'Normal Check-Out, and Block is Fulfilled',
             CHECKIN_BOOKING_WITH_LCO_BLOCK, 200, SUPER_ADMIN, "", "", "", False,
             [{'id': "AddPaymentCheckoutV2_22", 'type': 'add_payment_v2'},
              {'id': "invoicePreviewCheckoutV2_17", 'type': 'preview_invoice'},
              {'id': "CheckoutV2_159", 'type': 'checkout_v2'}], True, "late_checkout_block"),

            ("GetInventoryBlockDetails_05", 'Verify Inventory Block Details for an Early Check-In (ECI) Block on a '
                                            'Single-Day, Single-Room Booking with Block Fulfillment Post Night Audit',
             CREATE_ECI_BLOCK_FOR_BOOKING_V1, 200, SUPER_ADMIN, "", "", "", False, [], True, "early_checkin_block"),

            ("GetInventoryBlockDetails_06", 'Verify Inventory Block Details for an Early Check-In (ECI) Block on a '
                                            'Single-Day, Single-Room Booking Fulfilled Post Night Audit, '
                                            'Followed by Check-In and Check-Out',
             CREATE_ECI_BLOCK_FOR_BOOKING_V2, 200, SUPER_ADMIN, "", "", "", False,
             [{'id': "checkin_80", 'type': 'checkin_v2'},
              {'id': "AddPaymentCheckoutV2_19", 'type': 'add_payment_v2'},
              {'id': "invoicePreviewCheckoutV2_11", 'type': 'preview_invoice'},
              {'id': "CheckoutV2_160", 'type': 'checkout_v2'}], True, "early_checkin_block"),

            ("GetInventoryBlockDetails_07", 'Verify Inventory Block Details When Temporary Block Converts to '
                                            'an Early Check-In(ECI) Block post Confirmation',
             CREATE_TEMP_BLOCK_FOR_BOOKING_V1, 200, SUPER_ADMIN, "", "", "", False,
             [{'id': "Create_Expense_96", 'type': 'create_expense_V3'}], False, "temp_block"),

            ("GetInventoryBlockDetails_08", 'Verify Inventory Block Details When Temporary Block Converts to '
                                            'a Late Checkout(LCO) Block Post Confirmation',
             CREATE_TEMP_BLOCK_FOR_BOOKING_V2, 200, SUPER_ADMIN, "", "", "", False,
             [{'id': "Create_Expense_97", 'type': 'create_expense_V3'}], False, "temp_block"),

            ("GetInventoryBlockDetails_09", 'Verify Inventory Block Details When Temporary Block Converts to '
                                            'an Early Check-In(ECI) Block, Fulfilled Post Night Audit, '
                                            'Followed by Check-In and Check-Out',
             CONVERT_TEMP_TO_ECI_BLOCK_FOR_BOOKING, 200, SUPER_ADMIN, "", "", "", False,
             [{'id': "checkin_80", 'type': 'checkin_v2'},
              {'id': "AddPaymentCheckoutV2_19", 'type': 'add_payment_v2'},
              {'id': "invoicePreviewCheckoutV2_11", 'type': 'preview_invoice'},
              {'id': "CheckoutV2_160", 'type': 'checkout_v2'}], True, "early_checkin_block"),

            ("GetInventoryBlockDetails_10", 'Verify Inventory Block Details When Temporary Block Converts to '
                                            'a Late Checkout(LCO) Block, with Check-In, Night Audit, Normal Check-Out, '
                                            'and Block Fulfillment',
             CONVERT_TEMP_TO_LCO_BLOCK_CHECKIN_BOOKING, 200, SUPER_ADMIN, "", "", "", False,
             [{'id': "AddPaymentCheckoutV2_22", 'type': 'add_payment_v2'},
              {'id': "invoicePreviewCheckoutV2_17", 'type': 'preview_invoice'},
              {'id': "CheckoutV2_159", 'type': 'checkout_v2'}], True, "late_checkout_block"),

            ("GetInventoryBlockDetails_11", 'Verify Inventory Block Details for an Early Check-In (ECI) Block on a '
                                            'Single-Day, Single-Room Booking After Booking Edit(put booking)',
             CREATE_ECI_BLOCK_THEN_EDIT_BOOKING, 200, SUPER_ADMIN, "", "", "", False, [], False, "early_checkin_block"),

            ("GetInventoryBlockDetails_12", 'Verify Inventory Block Details for a Late Checkout(LCO) Block on a '
                                            'Single-Day, Single-Room Booking After Booking Edit(put booking)',
             CREATE_LCO_BLOCK_THEN_EDIT_BOOKING, 200, SUPER_ADMIN, "", "", "", False, [], False, "late_checkout_block"),

            ("GetInventoryBlockDetails_13", 'Verify Inventory Block Details for an Early Check-In (ECI) Block on a '
                                            'Single-Day, Single-Room Booking After ECI Charge Cancellation',
             CREATE_ECI_BLOCK_THEN_CANCEL_ECI_CHARGE, 200, SUPER_ADMIN, "", "", "", False, [], False,
             "early_checkin_block"),

            ("GetInventoryBlockDetails_14", 'Verify Inventory Block Details for a Late Checkout(LCO) Block on a '
                                            'Single-Day, Single-Room Booking After LCO Charge Cancellation',
             CREATE_LCO_BLOCK_THEN_CANCEL_LCO_CHARGE, 200, SUPER_ADMIN, "", "", "", False, [], False,
             "late_checkout_block"),

            ("GetInventoryBlockDetails_15", 'Verify Inventory Block Details When Temporary Block Converts to '
                                            'an Early Check-In(ECI) Block, Fulfilled Post Night Audit, '
                                            'Followed by Check-In, Check-Out then Reverse Checkout',
             CONVERT_TEMP_TO_ECI_BLOCK_FOR_BOOKING, 200, SUPER_ADMIN, "", "", "", False,
             [{'id': "checkin_80", 'type': 'checkin_v2'},
              {'id': "AddPaymentCheckoutV2_19", 'type': 'add_payment_v2'},
              {'id': "invoicePreviewCheckoutV2_11", 'type': 'preview_invoice'},
              {'id': "CheckoutV2_160", 'type': 'checkout_v2'},
              {'type': 'delete_booking_action'}], True, "early_checkin_block"),

            ("GetInventoryBlockDetails_16", 'Verify Inventory Block Details When Temporary Block Converts to '
                                            'a Late Checkout(LCO) Block, with Check-In, Night Audit, Normal Check-Out, '
                                            'and Block Fulfillment, Reverse Checkout',
             CONVERT_TEMP_TO_LCO_BLOCK_CHECKIN_BOOKING, 200, SUPER_ADMIN, "", "", "", False,
             [{'id': "AddPaymentCheckoutV2_22", 'type': 'add_payment_v2'},
              {'id': "invoicePreviewCheckoutV2_17", 'type': 'preview_invoice'},
              {'id': "CheckoutV2_159", 'type': 'checkout_v2'}], True, "late_checkout_block"),
        ])
    @pytest.mark.regression
    def test_get_inventory_blocks(self, client_, test_case_id, tc_description, previous_actions,
                                  status_code, user_type, error_code, error_message, dev_message,
                                  skip_message, actions, perform_night_audit, block_type):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        fulfill_virtual_blocks_service = locate_instance(InventoryBlockAsyncJobHandler)

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            self.common_request_caller(client_, previous_actions, hotel_id)
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))
            fulfill_virtual_blocks_service.fulfill_virtual_blocks(hotel_id=hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        if actions:
            self.common_request_caller(client_, actions, hotel_id)
        booking_id = self.booking_request.booking_id
        response = self.inventory_block_requests.get_inventory_blocks_details(client=client_, hotel_id=hotel_id,
                                                                              booking_id=booking_id, block_type=block_type,
                                                                              status_code=status_code, user_type=user_type)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, None)
        elif status_code in SUCCESS_CODES:
            self.validation(test_case_id, response)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(test_case_id, response):
        validation = ValidationGetInventoryBlockDetails(test_case_id, response)
        validation.validate_response()
