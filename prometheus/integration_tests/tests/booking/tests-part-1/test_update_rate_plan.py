from datetime import date, timedelta

import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_update_rate_plan import (
    ValidationUpdateRatePlan,
)
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.tests.mockers import mock_catalog_client
from ths_common.constants.catalog_constants import SellerType


class TestUpdateRatePlan(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added, "
        "perform_night_audit, action_after_night_audit, is_booking_with_rate_plan", [
            ("UpdateRatePlan_01", 'Update rate plan on check in date before check in', SINGLE_WALK_BOOKING_V2_01, 200,
             None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_02", 'Update rate plan on check in date after check in',
             WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2, 400, None, "04015966",
             "Cannot update room stay rate plan. Only RoomStays in reserved state allows rate plan update", "", "",
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_03", 'Update rate plan on future booking', FUTURE_WALK_BOOKING_V2_01_2_days, 200, None,
             "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_04", 'In multi room booking, update rate plan of one room',
             MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2, 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_05", 'Provide rate plan information only in price', SINGLE_WALK_BOOKING_V2_01, 200, None,
             "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_06", 'Provide rate plan information only in rate plan', SINGLE_WALK_BOOKING_V2_01, 200,
             None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_07", 'Does not provide rate plan in price as well as in rate plan',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015967",
             "Cannot update rate plan. Rate plan reference id not provided", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_08", 'Provide rate plan in price as well as in rate plan', SINGLE_WALK_BOOKING_V2_01, 200,
             None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_09",
             'Update rate plan which earlier have inclusion with rate plan which do not have inclusion',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, False, False, False, ""),
            ("UpdateRatePlan_10",
             "Update rate plan which earlier don't have inclusion with rate plan which have inclusion",
             [{'id': "booking_01_without_inclusions", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "",
             True, True, False, False, ""),
            ("UpdateRatePlan_11", "Update rate plan with same rate plan", SINGLE_WALK_BOOKING_V2_01, 200, None, "", "",
             "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_12", "Update rate plan with same rate plan but different price",
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_13", "Update rate plan with same rate plan but different inclusion",
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_14", "Update rate plan with same rate plan but different price and different inclusion",
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_15", "Update rate plan with inclusion out of check in and checkout date of room",
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015969",
             "Cannot add Rate Plan Inclusions. Rate Plan Inclusions provided are out of stay duration", "", "", False,
             "", True, True, False, False, ""),
            ("UpdateRatePlan_16", "Update rate plan with price out of check in and checkout date",
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", False, "",
             True, True, False, False, ""),
            ("UpdateRatePlan_17", "Update rate plan for two day booking but provide inclusion for 1 day",
             [{'id': "booking_06", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_18", "Update rate plan for two day booking and also provide inclusion for two day",
             [{'id': "booking_06", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_19", "Update rate plan 2-3 times",
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True},
              {'id': 'UpdateRatePlan_11', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True},
              {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan',
               'enable_rate_manager': True, 'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True,
             True, False, False, ""),
            ("UpdateRatePlan_20", "Update rate plan of a booking which does not have rate plan", SINGLE_BOOKING_01, 200,
             None, "", "", "", "", True, "Need to Confirm", True, True, False, False, ""),
            ("UpdateRatePlan_21", "Provide all the details to update rate plan", SINGLE_WALK_BOOKING_V2_01, 200, None,
             "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_22", "Update quantity of inclusion in rate plan", SINGLE_WALK_BOOKING_V2_01, 200, None,
             "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_23", "Remove all the rate plan details from a room which have rate plan in it",
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015967",
             "Cannot update rate plan. Rate plan reference id not provided", "", "", False, "", False, False, False,
             False, ""),
            ("UpdateRatePlan_24", "Update rate plan with multiple rate plan inclusion", SINGLE_WALK_BOOKING_V2_01, 200,
             None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_25", "Update rate plan for partial checked in room",
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'}, {'id': "checkin_11", 'type': 'checkin_v2'}],
             400, None, "04015966",
             "Cannot update room stay rate plan. Only RoomStays in reserved state allows rate plan update", "", "",
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_26", "Update rate plan for partial checked out room",
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_04', 'type': 'checkout_v2'}], 400, None, "04015966",
             "Cannot update room stay rate plan. Only RoomStays in reserved state allows rate plan update", "", "",
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_27", "Update rate plan for no show room", NO_SHOW_MARKED_ROOM, 400, None, "04015966",
             "Cannot update room stay rate plan. Only RoomStays in reserved state allows rate plan update", "", "",
             False, "", True, True, True, False, ""),
            ("UpdateRatePlan_28", "Update rate plan for cancelled room",
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "CancelAction_39", 'type': 'cancel'}], 400, None,
             "04015966", "Cannot update room stay rate plan. Only RoomStays in reserved state allows rate plan update",
             "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_29", "Update rate plan for room in which one guest is removed",
             [{'id': "booking_25", 'type': 'booking_v2'}, {'id': "MarkCancelled_21", 'type': 'mark_cancelled'}], 200,
             None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_30", "Update rate plan for room in which one guest is marked no show",
             NO_SHOW_MARKED_GUEST, 200, None, "", "", "", "", False, "", True, True, True, False, ""),
            ("UpdateRatePlan_31", "Update rate plan after performing reverse check in",
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_32", "Update rate plan after performing reverse checkout", FULL_CHECKOUT_01_V2 +
             [{'type': 'delete_booking_action'}], 400, None, "04015966",
             "Cannot update room stay rate plan. Only RoomStays in reserved state allows rate plan update", "", "",
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_33", "Update rate plan after performing reverse cancel",
             [{'id': "booking_10", 'type': 'booking_v2'}, {'id': "CancelAction_14", 'type': 'cancel'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_34", "Update rate plan after performing reverse no show",
             [{'id': 'booking_01_no_show', 'type': 'booking_v2'}, {'id': 'MarkNoShow_01', 'type': 'mark_no_show'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, True, False, ""),
            ("UpdateRatePlan_41", 'Provide invalid amount', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Pretax Amount] -> Failed creating money object with error: Unable to create money for amount: INVALID"
             " due to reason: [<class 'decimal.ConversionSyntax'>]", "", {"field": "prices.0.pretax_amount"}, False, "",
             True, True, False, False, ""),
            ("UpdateRatePlan_42", 'Provide invalid applicable date in price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Applicable Date] -> Not a valid datetime.", "", {"field": "prices.0.applicable_date"},
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_43", 'Provide bill to type as guest', SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "",
             "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_44", 'Provide bill to type as company for B2B booking',
             [{'id': "booking_43", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_45",
             'Provide bill to type as company and type as credit for booking without company details',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_46", 'Does not provide type in Price', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Type] -> Please provide price type.", "", {"field": "prices.0.type"}, False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_47", 'Provide type as NULL in Price', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Type] -> Price type may not be null.", "", {"field": "prices.0.type"}, False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_48", 'Provide type as empty in Price', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Type] -> '' is not a valid choice for Price Type", "", {"field": "prices.0.type"}, False, "", True,
             True, False, False, ""),
            ("UpdateRatePlan_49", 'Provide invalid type in Price', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Type] -> '!@#$%^' is not a valid choice for Price Type", "", {"field": "prices.0.type"}, False, "",
             True, True, False, False, ""),
            ("UpdateRatePlan_50", 'update rate plan but provide price as NULL', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Prices] -> Field may not be null.", "", {"field": "prices"}, False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_51", 'update rate plan but provide price as empty', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Prices] -> Invalid type.", "", {"field": "prices"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_52", 'Does not provide applicable date in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Applicable Date] -> Please provide applicable date.", "",
             {"field": "prices.0.applicable_date"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_53", 'Provide applicable date as NULL in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Applicable Date] -> Applicable date may not be null.", "",
             {"field": "prices.0.applicable_date"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_54", 'Provide applicable date empty in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Applicable Date] -> Not a valid datetime.", "", {"field": "prices.0.applicable_date"},
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_55", 'Does not provide bill to type in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Bill To Type] -> Please provide bill to type.", "", {"field": "prices.0.bill_to_type"},
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_56", 'Provide bill to type as NULL in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Bill To Type] -> Bill to type may not be null.", "", {"field": "prices.0.bill_to_type"},
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_57", 'Provide bill to type as empty in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Bill To Type] -> '' is not a valid choice for Bill-To Type", "",
             {"field": "prices.0.bill_to_type"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_58", 'Provide both pretax and posttax amount in price', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[ Schema] -> Please provide either pretax_amount or posttax_amount", "",
             {"field": "prices.0._schema"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_59", 'Does not provide pretax amount as well as posttax amount in price',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[ Schema] -> Please provide either pretax_amount or posttax_amount", "", {"field": "prices.0._schema"},
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_60", 'Provide rate plan reference id as NULL', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_61", 'Provide rate plan reference id as Empty', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_62", 'Delete rate plan reference id key', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_63", 'Provide start date in rate plan inclusion as NULL', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Start Date] -> Field may not be null.", "",
             {"field": "rate_plan_inclusions.0.start_date"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_64", 'Provide end date in rate plan inclusion as NULL', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[End Date] -> Field may not be null.", "", {"field": "rate_plan_inclusions.0.end_date"},
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_65", 'Provide sku id in rate plan inclusion as NULL', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Sku Id] -> sku id may not be null.", "", {"field": "rate_plan_inclusions.0.sku_id"}, False,
             "", True, True, False, False, ""),
            ("UpdateRatePlan_66", 'Provide start date in rate plan inclusion as Empty', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Start Date] -> Not a valid date.", "", {"field": "rate_plan_inclusions.0.start_date"},
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_67", 'Provide end date in rate plan inclusion as Empty', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[End Date] -> Not a valid date.", "", {"field": "rate_plan_inclusions.0.end_date"},
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_68", 'Provide sku id in rate plan inclusion as Empty', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04015942", "Sku not found", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_69", 'Does not Provide start date in rate plan inclusion', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Start Date] -> Missing data for required field.", "",
             {"field": "rate_plan_inclusions.0.start_date"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_70", 'Does not Provide end date in rate plan inclusion', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[End Date] -> Missing data for required field.", "",
             {"field": "rate_plan_inclusions.0.end_date"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_71", 'Does not Provide sku id in rate plan inclusion', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Sku Id] -> sku id data missing.", "", {"field": "rate_plan_inclusions.0.sku_id"},
             False, "", True, True, False, False, ""),
            ("UpdateRatePlan_72", 'Provide quantity in rate plan inclusion as negative', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Quantity] -> Value must be greater than 0", "",
             {"field": "rate_plan_inclusions.0.quantity"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_73", 'Provide both pretax and posttax amount in rate plan inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Rate Plan Inclusions] -> Please provide either of posttax_amount or pretax_amount", "",
             {"field": "rate_plan_inclusions.0.rate_plan_inclusions"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_74", 'Provide pretax or posttax amount negative in rate plan inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Rate Plan Inclusions] -> pretax_amount or posttax_amount cannot be negative", "",
             {"field": "rate_plan_inclusions.0.rate_plan_inclusions"}, False, "", True, True, False, False, ""),
            ("UpdateRatePlan_75", 'Does not provide any of the mandatory fields in rate plan inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_76", 'Provide Booking Id as Null', SINGLE_WALK_BOOKING_V2_01, 404, None, "04010007",
             "Aggregate: Booking with id: null missing.", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_77", 'Provide Booking Id as Empty', SINGLE_WALK_BOOKING_V2_01, 404, None, 404,
             "Exception occurred.", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_78", 'Provide Wrong Booking-Id', SINGLE_WALK_BOOKING_V2_01, 404, None, "04010007",
             "Aggregate: Booking with id: 0000-0000-0000 missing.", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_79", 'Provide Invalid format room stay id', SINGLE_WALK_BOOKING_V2_01, 404, None, 404,
             "Exception occurred.", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_80", 'Provide room stay id as Null', SINGLE_WALK_BOOKING_V2_01, 404, None, 404,
             "Exception occurred.", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_81", 'Provide room stay id as Empty', SINGLE_WALK_BOOKING_V2_01, 404, None, 404,
             "Exception occurred.", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_82", 'Provide Wrong room stay id', SINGLE_WALK_BOOKING_V2_01, 404, None, "04010004",
             "RoomStay not found. Please contact escalations.", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_83", 'Does not provide mandatory fields', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Rate Plan] -> Missing data for required field.", "", {"field": "rate_plan"}, True,
             "Gives 500", False, True, False, False, ""),
            ("UpdateRatePlan_84", 'Does not provide non-mandatory fields', SINGLE_WALK_BOOKING_V2_01, 200, None, "",
             "", "", "", False, "", True, False, False, False, ""),
            ("UpdateRatePlan_85", 'Send pretax price in Price while send posttax price in inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_87",
             'Provide sku id in rate plan inclusion which is not there in catalog as well as catalog',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015942", "Sku not found", "",
             "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_89", 'Provide skuid in rate plan inclusion that is not in catalog as well as catalog',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015942", "Sku not found", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_90", 'Provide invalid skuid in rate plan inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015942", "Sku not found", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateRatePlan_86", 'Provide sku id in rate plan inclusion which is not there in the crs db',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "",
             "", False, "", True, True, False, False, ""),

            ("UpdateRatePlan_91", 'Update rate plan with credit type after providing spot credit',
             BOOKING_WITH_SPOT_CREDIT_FOLIO, 400, None, "04010331", "Credit charges can not be billed to guest", "",
             "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_92", 'Update rate plan with non-credit type after providing spot credit',
             BOOKING_WITH_SPOT_CREDIT_FOLIO, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateRatePlan_131", 'Update rate plan on check in date before check in', SINGLE_WALK_BOOKING_V2_01, 200,
             None, "", "", "", "", False, "", True, True, False, False, ""),
        ])
    @pytest.mark.regression
    def test_update_rate_plan(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                              error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                              enable_rate_plan, is_inclusion_added, perform_night_audit, action_after_night_audit,
                              is_booking_with_rate_plan):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id, extra=is_booking_with_rate_plan)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=is_booking_with_rate_plan)

        if test_case_id in ('UpdateRatePlan_86', 'UpdateRatePlan_87', 'UpdateRatePlan_89', 'UpdateRatePlan_90'):
            query_execute(db_queries.UPDATE_EXPENSE_ITEM_SEQ)

        response = self.booking_request.update_rate_plan(client_, test_case_id, status_code,
                                                         self.booking_request.booking_id, enable_rate_plan,
                                                         is_inclusion_added, user_type)

        if test_case_id in ('UpdateRatePlan_86', 'UpdateRatePlan_87', 'UpdateRatePlan_89', 'UpdateRatePlan_90'):
            query_execute(db_queries.DELETE_EXPENSE_ITEM)

        if test_case_id in ('UpdateRatePlan_27', 'UpdateRatePlan_30', 'UpdateRatePlan_34'):
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.booking_request.bill_id, self.billing_request, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, bill_id, billing_request,
                   expense_request):
        validation = ValidationUpdateRatePlan(client_, test_case_id, response, booking_request)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id, booking_id)


class TestUpdateRatePlanAfterChargeSplit(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added, "
        "is_booking_with_rate_plan, has_slab_based_taxation, is_tax_clubbed", [
            ("UpdateRatePlan_93", 'Update rate plan having slab based taxation', SINGLE_WALK_BOOKING_V2_01_SLAB_BASED,
             200, None, "", "", "", "", False, "", True, True, "", True, []),
            ("UpdateRatePlan_94", 'Update rate plan having clubbed charge', SINGLE_WALK_BOOKING_V2_01_CLUBBED_CHARGE,
             200, None, "", "", "", "", False, "", True, True, "", False, CLUBBED_TAX_CONFIG),
            ("UpdateRatePlan_95", 'Update rate plan having clubbed charge and slab based taxation',
             SINGLE_WALK_BOOKING_V2_01_CLUBBED_AND_SLAB_BASED, 200, None, "", "", "", "", False, "", True, True, "",
             True, CLUBBED_TAX_CONFIG),
            ("UpdateRatePlan_96", 'Update rate plan after charge split',
             BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_03, 200, None, "", "", "", "", False, "", True, True, "",
             False, []),
            ("UpdateRatePlan_97", 'Update rate plan after charge split having slab based taxation',
             BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_02, 200, None, "", "", "", "", False, "", True, True, "",
             True, []),
            ("UpdateRatePlan_98", 'Update rate plan after charge split having clubbed charge and slab based taxation',
             BOOKING_WITH_CHARGE_SPLITS_CLUBBED_AND_SLAB_BASED, 200, None, "", "", "", "", False, "", True, True, "",
             True, CLUBBED_TAX_CONFIG),
        ])
    @pytest.mark.regression
    def test_update_rate_plan_after_charge_split(self, client_, test_case_id, tc_description, previous_actions,
                                                 status_code, user_type, error_code, error_message, dev_message,
                                                 error_payload, skip_case, skip_message, enable_rate_plan,
                                                 is_inclusion_added, is_booking_with_rate_plan, has_slab_based_taxation,
                                                 is_tax_clubbed):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=is_booking_with_rate_plan)

        response = self.booking_request.update_rate_plan(client_, test_case_id, status_code,
                                                         self.booking_request.booking_id, enable_rate_plan,
                                                         is_inclusion_added, user_type, has_slab_based_taxation,
                                                         is_tax_clubbed)
        self.billing_request.get_bill_charges(client_, self.booking_request.bill_id, 200)

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.booking_request.bill_id, self.billing_request, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, bill_id, billing_request,
                   expense_request):
        validation = ValidationUpdateRatePlan(client_, test_case_id, response, booking_request)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id, booking_id)


class TestUpdateRatePlanWithCommission(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added", [
            ("UpdateRatePlan_99",
             "Update rate plan on check in date before check in single room single day ota booking",
             [{'id': 'booking_212', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_100",
             "Update rate plan with inclusion on check in date before check in single room single day ota booking",
             [{'id': 'booking_212', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_101",
             "Update rate plan on check in date after check in single room single day ota booking",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'}], 400, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_102",
             "Update rate plan on check in date before check in single room multiple days ota booking",
             [{'id': 'booking_233', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_103", "Update rate plan of one room in a multiple rooms single day ota booking",
             [{'id': 'booking_232', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_104", "Update rate plan of one room in a multiple rooms multiple days ota booking",
             [{'id': 'booking_234', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_105", "Update rate plan of a single room single day ota booking in which a guest is added",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'AddGuest_01', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': False,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_106",
             "Update rate plan of a single room single day ota booking in which a guest is removed",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': False,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_107",
             "Update rate plan of a single room single day ota booking whose stay dates are updated",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_129', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_108",
             "Update rate plan of a single room single day ota booking whose charge is updated",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}], 200, None, "", "", "", "", False, "",
             True, False),
            ("UpdateRatePlan_109",
             "Update rate plan of a single room single day ota booking whose charge is updated and a guest is added",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'AddGuest_01', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': False,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_110",
             "Update rate plan of a single room single day ota booking whose charge is updated and a guest is removed",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': False,
               'is_inclusion_added': False}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_111",
             "Update rate plan of a single room single day ota booking whose TA commission is updated after creation",
             [{'id': 'booking_226', 'type': 'booking_v2'},
              {'id': 'put_booking_105', 'type': 'put_booking_v2'}], 200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_112", "Update rate plan of a single room single day ota reverse checked in booking",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'}, {'id': 'Delete_Checkin_01', 'type': 'delete_booking_action'}],
             200, None, "", "", "", "", False, "", True, False),
            ("UpdateRatePlan_113", "Update rate plan of a single room single day ota reverse cancelled booking",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': "MarkCancelled_95", 'type': 'mark_cancelled'},
              {'id': 'Delete_Cancel_01', 'type': 'reverse_cancel_action'}], 200, None, "", "", "", "", False, "",
             True, False),
            ("UpdateRatePlan_114",
             "Update rate plan of a single room single day ota booking where expense is added",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104', 'type': 'create_expense_V3'}], 200, None, "", "", "", "", False, "",
             True, False),
            ("UpdateRatePlan_129", "Edit commission then update rate plan of ota booking",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True, False),
        ])
    @pytest.mark.regression
    def test_update_rate_plan_with_commission(self, client_, test_case_id, tc_description, previous_actions,
                                              status_code, user_type, error_code, error_message, dev_message,
                                              error_payload, skip_case, skip_message, enable_rate_plan,
                                              is_inclusion_added):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.update_rate_plan(client_, test_case_id, status_code,
                                                         self.booking_request.booking_id, enable_rate_plan,
                                                         is_inclusion_added)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.booking_request.bill_id, self.billing_request, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, bill_id, billing_request,
                   expense_request):
        validation = ValidationUpdateRatePlan(client_, test_case_id, response, booking_request)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id, booking_id)
        validation.validate_commissions()


class TestUpdateRatePlanWithNewTaxCalc(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added", [
            ("UpdateRatePlan_115", "Update rate plan of a booking where sez and lut is false for company",
             [{'id': 'booking_with_lut_04', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_116", "Update rate plan of a booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_117", "Update rate plan of a booking where sez is false and lut is true for company",
             [{'id': 'booking_with_lut_03', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_118", "Update rate plan of a booking where sez and lut is true for company",
             [{'id': 'booking_with_lut_01', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_119", "Update rate plan of a booking where sez and lut is false for ta",
             [{'id': 'booking_with_lut_04_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_120", "Update rate plan of a booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_121", "Update rate plan of a booking where sez is false and lut is true for ta",
             [{'id': 'booking_with_lut_03_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_122", "Update rate plan of a booking where sez and lut is true for ta",
             [{'id': 'booking_with_lut_01_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_123", "Update rate plan of a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_124",
             "Update rate plan of a booking with company and travel agent where room charges are billed to company",
             [{'id': 'booking_with_lut_02_company_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_125",
             "Update rate plan of a booking with company and travel agent where room charges are billed to ta",
             [{'id': 'booking_with_lut_02_ta_company', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_126",
             "Update rate plan of a booking of multiple days where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_127",
             "Update rate plan of a booking of multiple days where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateRatePlan_128",
             "Update rate plan of a booking of multiple days where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
        ])
    @pytest.mark.regression
    def test_update_rate_plan_with_new_tax_calc(self, client_, test_case_id, tc_description, previous_actions,
                                                status_code, user_type, error_code, error_message, dev_message,
                                                error_payload, skip_case, skip_message, enable_rate_plan,
                                                is_inclusion_added):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.update_rate_plan(client_, test_case_id, status_code,
                                                         self.booking_request.booking_id, enable_rate_plan,
                                                         is_inclusion_added)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.booking_request.bill_id, self.billing_request, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, bill_id, billing_request,
                   expense_request):
        validation = ValidationUpdateRatePlan(client_, test_case_id, response, booking_request)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id, booking_id)
        validation.validate_commissions()
        validation.validate_charges(billing_request)
