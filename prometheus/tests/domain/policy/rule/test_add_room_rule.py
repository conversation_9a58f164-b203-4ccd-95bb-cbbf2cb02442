import pytest

from prometheus import crs_context
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.add_room_rule import AddRoomRule
from prometheus.tests.response.role_manager_privileges_response import (
    get_privilege_details_by_role,
)
from prometheus.tests.test_utils import today
from ths_common.constants.booking_constants import BookingChannels
from ths_common.exceptions import PolicyAuthException


def test_add_room_rule_fail_for_user_fdm_and_non_hotel_channel_booking(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.DIRECT.value
    )
    with pytest.raises(PolicyAuthException):
        privileges = get_privilege_details_by_role(role='fdm')
        add_room_allow = AddRoomRule().allow(
            Facts(
                user_type="fdm",
                booking_aggregate=valid_booking_aggregate_with_one_room_stay,
                hotel_context=crs_context.get_hotel_context(),
                action_payload=dict(checkin_date=today()),
            ),
            privileges=privileges,
        )


def test_add_room_rule_pass_for_user_cr_team(
    valid_booking_aggregate_with_one_room_stay,
):
    privileges = get_privilege_details_by_role(role='cr-team')
    add_room_allow = AddRoomRule().allow(
        Facts(
            user_type="cr-team",
            booking_aggregate=valid_booking_aggregate_with_one_room_stay,
            hotel_context=crs_context.get_hotel_context(),
            action_payload=dict(checkin_date=today()),
        ),
        privileges=privileges,
    )
    assert add_room_allow is True


def test_add_room_rule_pass_for_user_fdm_and_hotel_channel_booking(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.HOTEL.value
    )
    privileges = get_privilege_details_by_role(role='fdm')
    add_room_allow = AddRoomRule().allow(
        Facts(
            user_type="fdm",
            booking_aggregate=valid_booking_aggregate_with_one_room_stay,
            hotel_context=crs_context.get_hotel_context(),
            action_payload=dict(checkin_date=today()),
        ),
        privileges=privileges,
    )
    assert add_room_allow is True
