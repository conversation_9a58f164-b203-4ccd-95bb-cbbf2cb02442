import json
import logging
import os

import requests
from flask import current_app as app
from treebo_commons.request_tracing.context import (
    get_current_request_id,
    get_current_tenant_id,
)

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)

logger = logging.getLogger(__name__)


@register_instance(arguments=[os.environ.get('APP_ENV', 'local')])
class SlackAlertServiceClient(BaseExternalClient):
    def __init__(self, environment):
        self.environment = environment
        super().__init__(log_handler=logger)

    page_map = {
        'retrieve_user_id_from_email': dict(
            type=BaseExternalClient.CallTypes.GET, url_regex="/api/users.lookupByEmail"
        ),
        'post_message': dict(
            type=BaseExternalClient.CallTypes.POST, url_regex="/api/chat.postMessage"
        ),
    }

    def get_domain(self):
        return app.config['SLACK_URL']

    def record_event(self, event_type, event_payload):
        if self.environment != "production":
            return
        try:
            if hasattr(self, f'get_{event_type}_notifier'):
                url, payload = getattr(self, f'get_{event_type}_notifier')(
                    event_payload
                )
                self._send_slack_alert(payload, slack_url=url)
        except Exception as ex:
            logger.exception(
                "Error while sending slack error on channel. {ex}".format(ex=ex)
            )

    @staticmethod
    def _send_slack_alert(payload, slack_url):
        json_string = json.dumps(payload, default=lambda o: o.__dict__)
        headers = {"content-type": "application/json"}
        response = requests.post(slack_url, data=json_string, headers=headers)
        if response.status_code != 200:
            raise Exception(
                'Received response status: {status}'.format(status=response.status_code)
            )

    def get_payment_config_missing_notifier(self, event_payload):
        slack_hook = app.config['SUPERHERO_GENERIC_ALERTS_SLACK_WEBHOOK_URL']
        details = (
            f'[Config missing warning] payment_mode: {event_payload.payment_mode} - '
            f'paid_by {event_payload.paid_by} paid_to {event_payload.paid_to} type: {event_payload.payment_type}'
        )
        message = (
            f"`(Tenant: {get_current_tenant_id()}) CRS ({self.environment}) RequestId:"
            f" ({get_current_request_id()})`: ```{details}```"
        )
        return slack_hook, {
            "text": message,
            "username": 'CRS',
        }

    def get_company_details_update_notifier(self, event_payload):
        slack_hook = app.config['B2B_COMPANY_DETAILS_ALERTS_SLACK_WEBHOOK_URL']

        entity, old_value, new_value = (
            event_payload['entity'],
            event_payload['old_value'],
            event_payload['new_value'],
        )
        attributes = set(list(old_value.keys()) + list(new_value.keys()))
        changes = [f'{entity} Updated', "Attribute - Old value - New Value"]
        for attrib in attributes:
            changes.append(
                f'{attrib} - {old_value.get(attrib)} - {new_value.get(attrib)}'
            )
        changes = "\n".join(changes)
        payload = {
            "text": "`(Tenant: {0}) CRS ({1}) Booking: ({2}) Hotel ({3})`: ```{4}```".format(
                get_current_tenant_id(),
                self.environment,
                event_payload['booking_reference_id'],
                event_payload['hotel_id'],
                changes,
            ),
            "username": 'CRS',
        }
        return slack_hook, payload

    def get_tax_determiner_update_error_notifier(self, event_payload):
        slack_hook = app.config['B2B_COMPANY_DETAILS_ALERTS_SLACK_WEBHOOK_URL']
        payload = {
            "text": "`(Tenant: {0}) CRS ({1}) Booking: ({2}) Hotel ({3})`: ```{4}```".format(
                get_current_tenant_id(),
                self.environment,
                event_payload['booking_reference_id'],
                event_payload['hotel_id'],
                event_payload['error'],
            ),
            "username": 'CRS',
        }
        return slack_hook, payload

    def retrieve_poc_slack_user_id(self, email):
        if self.environment not in ['prod', 'production']:
            return
        if not email:
            return None
        page_name = "retrieve_user_id_from_email"
        custom_headers = {'Authorization': f'Bearer {os.environ.get("CORPBOT_TOKEN")}'}
        payload = dict(email=email)
        response = self.make_call(
            page_name, custom_headers=custom_headers, data=payload
        )
        if not response.is_success():
            logger.info(
                f"Retrieving user_id from email failed. Status Code: {response.response_code}, "
                f"Errors: {response.errors}"
            )
            return None

        if not response.data.get('ok'):
            logger.info(
                f"Retrieving user_id from email failed due to {response.data.get('error')}"
            )
            return None

        return response.data['user']['id']

    def send_alert_through_slackbot(self, payload):
        if self.environment not in ['prod', 'production']:
            return
        page_name = "post_message"
        custom_headers = {'Authorization': f'Bearer {os.environ.get("CORPBOT_TOKEN")}'}
        response = self.make_call(
            page_name, custom_headers=custom_headers, data=payload
        )
        if not response.is_success():
            logger.info(
                f"Sending alert failed. Status Code: {response.response_code}, "
                f"Errors: {response.errors}"
            )
            return

        if response.data.get('error'):
            logger.info(f"Sending alert failed due to {response.data.get('error')}")
            return

        return response.data.get('ok')
