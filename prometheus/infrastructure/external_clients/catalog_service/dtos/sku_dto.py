class SkuDTO(object):
    def __init__(self, expense_item_id, name, sku_category_id, linked):
        self.expense_item_id = expense_item_id
        self.name = name
        self.sku_category_id = sku_category_id
        self.linked = linked

    @staticmethod
    def create_from_catalog_data(sku_data):
        return SkuDTO(
            expense_item_id=sku_data.get('code'),
            name=sku_data.get('name'),
            sku_category_id=sku_data.get('sku_category_code'),
            linked=False,
        )
