import logging
from typing import List

from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus import crs_context
from prometheus.application.booking.helpers import (
    price_validators,
    rate_plan_validator,
    room_change_handler,
    room_stay_validators,
)
from prometheus.application.booking.helpers.expense_service import (
    ExpenseApplicationService,
)
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_from_billed_entity,
)
from prometheus.domain.billing.dto import EditChargeData
from prometheus.domain.booking.exceptions import PriceError
from prometheus.domain.catalog.repositories import RoomTypeRepository
from ths_common.constants.billing_constants import ChargeStatus, ChargeSubTypes
from ths_common.value_objects import NotAssigned, PriceData

logger = logging.getLogger(__name__)


class UpdateRoomStayRoomTypeCommand(object):
    def __init__(
        self,
        booking_aggregate,
        room_stay,
        bill_aggregate,
        room_stay_update_data,
        room_allocation_data,
        new_room_stay_config,
        hotel_aggregate,
        tax_service,
        room_stay_price_change_handler,
        ta_commission_helper,
    ):
        self.booking_aggregate = booking_aggregate
        self.room_stay = room_stay
        self.bill_aggregate = bill_aggregate
        self.room_stay_update_data = room_stay_update_data
        self.room_allocation_data = room_allocation_data
        self.new_room_stay_config = new_room_stay_config
        self.hotel_aggregate = hotel_aggregate
        self.tax_service = tax_service
        self.room_stay_price_change_handler = room_stay_price_change_handler
        self.charge_edit_service = room_stay_price_change_handler.charge_edit_service
        # TODO: Pass in __init__
        self.room_type_repository = locate_instance(RoomTypeRepository)
        self.expense_app_service = locate_instance(ExpenseApplicationService)
        self.ta_commission_helper = ta_commission_helper

    def execute(self):
        room_type_change = self.room_stay_update_data.get('room_type', dict())
        prices = self.room_stay_update_data.get('prices', list())
        rate_plan_inclusions = self.room_stay_update_data.get('rate_plan_inclusions')
        dates_for_price_change = []
        if (
            self.new_room_stay_config.room_type_id
            and self.new_room_stay_config.room_type_id != self.room_stay.room_type_id
            and room_type_change.get('price_change_required', True)
        ):
            start_date = dateutils.to_date(
                self.room_allocation_data.checkin_date
                if self.room_allocation_data
                else self.room_stay.checkin_date
            )
            end_date = dateutils.to_date(self.room_stay.checkout_date)
            dates_for_price_change = list(dateutils.date_range(start_date, end_date))

        old_room_type_id = self.room_stay.room_type_id
        (
            new_room_allocation,
            previous_room_allocation,
        ) = self.booking_aggregate.update_room_stay_type(
            room_stay_id=self.room_stay.room_stay_id,
            room_stay_config=self.new_room_stay_config,
            room_allocation_data=self.room_allocation_data,
        )

        room_stay_validators.validate_max_occupancy_for_room_stays(
            self.hotel_aggregate, [self.room_stay.room_stay_config]
        )

        if room_type_change.get('price_change_required', True):
            if not prices:
                raise PriceError(description='Price is required')
            edited_charges = self._update_room_stay_charge(
                self.bill_aggregate,
                dates_for_price_change,
                prices,
                self.room_stay,
                self.booking_aggregate,
            )

            self.booking_aggregate.update_room_rents(
                self.room_stay.room_stay_id, edited_charges
            )

            is_rate_plan_deletion_required = (
                True
                if old_room_type_id != self.new_room_stay_config.room_type_id
                else False
            )
            self.room_stay_price_change_handler.on_room_stay_charge_edit(
                self.booking_aggregate,
                self.bill_aggregate,
                self.room_stay,
                edited_charges,
                delete_rate_plan_addons=is_rate_plan_deletion_required,
            )

        room_change_handler.update_room_no_in_booked_charges(
            self.booking_aggregate,
            [self.room_stay.room_stay_id],
            self.bill_aggregate,
            self.room_type_repository.load_type_map(),
        )
        if (
            rate_plan_inclusions
            and len(rate_plan_inclusions) > 0
            and self.booking_aggregate.rate_plans
        ):
            price_validators.validate_price_and_inclusions(prices, rate_plan_inclusions)
            rate_plan_validator.validate_inclusion_with_duration(
                rate_plan_inclusions,
                self.room_stay.checkin_date,
                self.room_stay.checkout_date,
            )
            self.expense_app_service.create_expense_and_charge_for_inclusions(
                rate_plan_inclusions,
                self.booking_aggregate,
                self.bill_aggregate,
                self.room_stay,
            )

        if (
            crs_context.is_treebo_tenant()
            and self.booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_for_room_stays(
                self.booking_aggregate, [self.room_stay], self.bill_aggregate
            )

        return new_room_allocation, previous_room_allocation

    def _update_room_stay_charge(
        self,
        bill_aggregate,
        dates_for_price_change,
        new_prices: List[PriceData],
        room_stay,
        booking_aggregate,
    ):
        charge_ids = room_stay.get_charges_for_stay_dates(
            [dateutils.to_date(p.applicable_date) for p in new_prices]
        )
        non_consumed_charge_ids = self._get_non_consumed_charges(
            charge_ids, bill_aggregate
        )

        edit_charge_dtos = []
        for new_price in new_prices:
            charge_id = room_stay.charge_id_map.get(
                dateutils.date_to_ymd_str(new_price.applicable_date)
            )
            if charge_id not in non_consumed_charge_ids:
                continue
            if new_price.billing_instructions == NotAssigned:
                # Room stay charge has only one split
                room_stay_charge = bill_aggregate.get_charge(charge_id)
                existing_charge_split = room_stay_charge.charge_splits[0]
                if (
                    existing_charge_split.charge_sub_type == ChargeSubTypes.SPOT_CREDIT
                    and existing_charge_split.charge_type != new_price.type
                ):
                    new_price.billing_instructions = self.charge_edit_service.update_billing_instruction_to_convert_spot_credit_to_non_credit(
                        existing_charge_split, bill_aggregate
                    )
            new_price.charge_id = charge_id
            billed_entity = self.bill_aggregate.get_primary_billed_entity_of_charge(
                charge_id
            )
            edit_charge_dtos.append(
                EditChargeData(
                    charge_id=charge_id,
                    price_data=new_price,
                    buyer_gst_details=get_gst_details_from_billed_entity(
                        self.booking_aggregate, billed_entity
                    ),
                )
            )

        self.charge_edit_service.update_tax_amounts_of_charges(
            bill_aggregate,
            edit_charge_dtos,
            gst_details=booking_aggregate.booking_owner_gst_details(),
            seller_model=booking_aggregate.booking.seller_model,
        )

        edited_charges = self.charge_edit_service.edit_charges(
            bill_aggregate, edit_charge_dtos, None, None, None
        )
        return edited_charges

    @staticmethod
    def _get_non_consumed_charges(charge_ids, bill_aggregate):
        # Filter dates where charges are already CONSUMED. We don't want to change price for such charges
        consumed_charges = bill_aggregate.filter_and_get_charges(
            charge_ids, allowed_charge_status=[ChargeStatus.CONSUMED]
        )
        consumed_charge_ids = [c.charge_id for c in consumed_charges]
        allowed_charge_cancellation = list(set(charge_ids) - set(consumed_charge_ids))
        return allowed_charge_cancellation
