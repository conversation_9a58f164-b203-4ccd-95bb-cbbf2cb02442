from datetime import date
from typing import Optional

from marshmallow import Schema, fields
from marshmallow.validate import OneOf
from pydantic import ConfigDict

from prometheus.common.serializers.request.validators import UserDefinedEnumValidator
from prometheus.common.serializers.response.room_stay import RoomRatePlanSchema
from prometheus.common.serializers.response.shared import ApplicationSourceTraceSchema
from prometheus.core.api_docs import swag_schema
from prometheus.infrastructure.external_clients.catalog_service.user_defined_enums import (
    UserDefinedEnums,
)
from shared_kernel.api_helpers.apispec_pydantic_plugin.models import ApiBaseModel
from ths_common.constants.inventory_constants import (
    DNREffectiveStatus,
    InventoryBlockStatus,
    InventoryBlockType,
    RoomStatus,
)


@swag_schema
class RoomAllotmentResponseSchema(Schema):
    allotment_id = fields.String()
    start_time = fields.LocalDateTime()
    expected_end_time = fields.LocalDateTime()
    actual_end_time = fields.LocalDateTime()
    allotted_for = fields.String()


@swag_schema
class DateTimeSchema(Schema):
    date = fields.Date()
    time = fields.Time()


@swag_schema
class RoomAllotmentAvailabilityResponseSchema(Schema):
    room_id = fields.Integer()
    available_from = fields.LocalDateTime()
    available_till = fields.LocalDateTime()
    housekeeping_status = fields.String()


@swag_schema
class PhysicalInventoryResponseSchema(Schema):
    room_id = fields.Integer()
    room_type_id = fields.Method('get_room_type')
    date = fields.Date()
    status = fields.String(
        validate=OneOf(RoomStatus.all()),
        required=False,
        description='Status in Available, Busy...',
    )

    def get_room_type(self, data):
        room_type_mappings = self.context.get('room_type_mappings')
        return room_type_mappings.get(data.room_id)


@swag_schema
class PhysicalInventoryIntegrationEventSchema(PhysicalInventoryResponseSchema):
    hotel_id = fields.Method('get_hotel_id')

    def get_hotel_id(self, data):
        return self.context.get('hotel_id')


@swag_schema
class RoomTypeInventoryResponseSchema(Schema):
    room_type_id = fields.String()
    date = fields.Date()
    actual_count = fields.Integer()


@swag_schema
class RoomTypeInventoryIntegrationEventSchema(RoomTypeInventoryResponseSchema):
    hotel_id = fields.Method('get_hotel_id')

    def get_hotel_id(self, data):
        return self.context.get('hotel_id')


@swag_schema
class DNRResponseSchema(Schema):
    dnr_id = fields.String(
        required=True,
        description='Unique identifier generated at the time of creating a DNR',
    )
    hotel_id = fields.String()
    room_id = fields.Integer()
    from_date = fields.Date(
        attribute="start_date", required=True, description='DNR start date (inclusive)'
    )
    to_date = fields.Date(
        attribute="end_date", required=True, description='DNR end date (inclusive)'
    )
    source = fields.String(
        validate=UserDefinedEnumValidator(UserDefinedEnums.DNR_SOURCE),
        required=True,
        description='DNR created by, FDM, QAM, etc.',
    )
    type = fields.Method("get_dnr_type")
    subtype = fields.Method("get_dnr_subtype")
    comments = fields.Method("get_comments")
    assigned_by = fields.String(
        required=True, description='Email Address of the user creating the DNR'
    )
    status = fields.String()
    date_inactivated = fields.LocalDateTime()
    version = fields.Integer()
    room_allotment_id = fields.String()
    effective_status = fields.String(validate=[OneOf(DNREffectiveStatus.all())])
    effective_duration = fields.Integer()
    effective_end_date = fields.Date()
    deleted = fields.Boolean()

    def get_dnr_type(self, obj):
        return obj.dnr_type.type

    def get_dnr_subtype(self, obj):
        return obj.dnr_type.subtype

    def get_comments(self, obj):
        return obj.dnr_type.comments


@swag_schema
class BulkMarkDNRResponse(Schema):
    dnrs = fields.Nested(DNRResponseSchema, many=True)


@swag_schema
class DNRAuditTrailSchema(Schema):
    created_at = fields.LocalDateTime(attribute="timestamp")
    user = fields.String()
    user_type = fields.String()
    application = fields.String()
    application_trace = fields.Nested(ApplicationSourceTraceSchema, allow_none=True)
    audit_type = fields.String()
    audit_payload = fields.Dict()


@swag_schema
class DNRPaginatedResponseSchema(Schema):
    """
    DNR search response schema
    """

    dnrs = fields.Nested(
        DNRResponseSchema, many=True, required=True, description='DNR Search Results'
    )
    limit = fields.Integer(required=True, description='Number of results')
    offset = fields.Integer(
        required=True, description='Offset from which the results are fetched'
    )
    total = fields.Integer(
        required=True, description='Total number of dnrs for the query'
    )


@swag_schema
class CheckedInRoomResponseSchema(Schema):
    room_id = fields.Integer()
    hotel_id = fields.String()
    room_type_id = fields.String()
    room_number = fields.String()
    booking_id = fields.String()
    room_stay_id = fields.Integer()
    room_allocation_id = fields.Integer()
    rate_plans = fields.Nested(RoomRatePlanSchema, many=True)
    disallow_charge_addition = fields.Boolean()


@swag_schema
class InventoryBlockResponse(ApiBaseModel):
    block_id: str
    booking_id: str
    hotel_id: str
    room_type_id: str
    start_date: date
    end_date: date
    status: InventoryBlockStatus

    room_stay_id: Optional[int] = None
    block_type: Optional[InventoryBlockType] = None

    model_config = ConfigDict(
        from_attributes=True,
        frozen=True,
    )
