from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.mockers import mock_aws_service_client, mock_template_service
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.booking_constants import BookingStatus


def test_booking_confirmation_voucher(booking_repo, bill_repo, client, hotel_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=1)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=2)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=2,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        default_billed_entity_category=BilledEntityCategory.PRIMARY_GUEST.value,
    )

    payload = {"data": create_booking_payload}
    response = make_booking(
        client=client, payload=payload, return_complete_response=True
    )
    booking_id = response["data"]["booking_id"]

    url = f"/v2/bookings/{booking_id}/confirmation-voucher"
    with mock_template_service(), mock_aws_service_client():
        response = client.get(
            url.format(booking_id=booking_id),
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
    assert response.status_code == 200
