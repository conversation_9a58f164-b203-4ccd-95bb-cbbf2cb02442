import copy
import json

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking


def test_create_booking_without_guest_creates_and_assigns_dummy_guest(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    booking_action_repo,
    booking_audit_trail_repo,
):
    create_booking_payload = copy.deepcopy(json.loads(create_booking_payload))
    del create_booking_payload['room_stays'][0]['guest_stays'][0]['guest']
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)

    for room_stay in booking_aggregate.room_stays:
        for guest_stay in room_stay.guest_stays:
            assert guest_stay.guest_id is not None
            customer = booking_aggregate.get_customer(guest_stay.guest_id)
            assert customer.dummy


def test_created_booking_without_guest_should_have_consuming_guests_in_all_charges(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    booking_action_repo,
    booking_audit_trail_repo,
):
    create_booking_payload = copy.deepcopy(json.loads(create_booking_payload))
    del create_booking_payload['room_stays'][0]['guest_stays'][0]['guest']
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    for charge in bill_aggregate.charges:
        assert charge.charge_to is not None
        assert len(charge.charge_to) == 1
        assert charge.item.details.get('occupancy') == 1
        assert charge.item.details.get('room_type_code') == 'rt01'


def test_created_booking_with_guest_should_have_consuming_guests_in_all_charges(
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    booking_action_repo,
    booking_audit_trail_repo,
):
    create_booking_payload = copy.deepcopy(json.loads(create_booking_payload))
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    for charge in bill_aggregate.charges:
        assert charge.charge_to is not None
        assert len(charge.charge_to) == 1
        assert charge.item.details.get('occupancy') == 1
        assert charge.item.details.get('room_type_code') == 'rt01'
