from ths_common.exceptions import ValidationException


class GuestCheckinData(object):
    def __init__(self, guest_stay_id, checkin_date, guest=None, guest_id=None):
        if not guest and not guest_id:
            raise ValidationException(
                message="Either guest or guest_id is required for performing checkin operation"
            )
        self.guest_stay_id = guest_stay_id
        self.checkin_date = checkin_date
        self.guest = guest
        self.guest_id = guest_id
