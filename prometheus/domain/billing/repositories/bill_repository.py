# coding=utf-8
"""
bill repository
"""
from collections import defaultdict
from datetime import time
from decimal import Decimal
from typing import Dict, List

from sqlalchemy import and_, func, or_, tuple_
from sqlalchemy.orm.exc import MultipleResultsFound, NoResultFound
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.dto.ar_reports_dto import PaymentDataDto
from prometheus.domain.billing.dto.bill_summary_dto import (
    BillSummaryDto,
    CreditSummaryDto,
    DebitSummaryDto,
)
from prometheus.domain.billing.dto.erp_event_details_dto import (
    ErpChargeDetailsDto,
    ErpPaymentDetailsDto,
)
from prometheus.domain.billing.dto.finance_erp_dtos import (
    BilledEntityDetailsDto,
    CreditShellRefundChargeDetailsDto,
    PaymentChargeDetailsDto,
)
from prometheus.domain.billing.dto.payment_data import PaymentDetailsDto
from prometheus.domain.billing.entities.bill import Bill
from prometheus.domain.billing.models import (
    AccountModel,
    AllowanceModel,
    BilledEntityModel,
    BillModel,
    ChargeModel,
    ChargeSplitModel,
    CreditShellModel,
    CreditShellRefundModel,
    FolioModel,
    PaymentModel,
    PaymentSplitModel,
)
from prometheus.domain.billing.repositories.adaptors.account_adaptor import (
    AccountDBAdaptor,
)
from prometheus.domain.billing.repositories.adaptors.allowance_adaptor import (
    AllowanceDBAdapter,
)
from prometheus.domain.billing.repositories.adaptors.bill_adaptor import BillDBAdapter
from prometheus.domain.billing.repositories.adaptors.billed_entity_adaptor import (
    BilledEntityDBAdaptor,
)
from prometheus.domain.billing.repositories.adaptors.charge_adaptor import (
    ChargeDBAdapter,
)
from prometheus.domain.billing.repositories.adaptors.charge_split_adaptor import (
    ChargeSplitDBAdapter,
)
from prometheus.domain.billing.repositories.adaptors.folio_adaptor import FolioDBAdaptor
from prometheus.domain.billing.repositories.adaptors.payment_adaptor import (
    PaymentDBAdapter,
)
from prometheus.domain.billing.repositories.adaptors.payment_split_adaptor import (
    PaymentSplitDBAdapter,
)
from prometheus.domain.billing.repositories.adaptors.raw_allowance_adaptor import (
    RawAllowanceDBAdaptor,
)
from prometheus.domain.billing.repositories.adaptors.raw_charge_adaptor import (
    RawChargeDBAdapter,
)
from prometheus.domain.billing.repositories.adaptors.raw_charge_split_adaptor import (
    RawChargeSplitDBAdapter,
)
from prometheus.domain.catalog.models import SkuCategoryModel
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.access_entity_facts import AccessEntityFacts
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.billing_constants import (
    BillAppId,
    BillStatus,
    ChargeStatus,
    ChargeSubTypes,
    ChargeTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.catalog_constants import SkuCategoryHSN
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import (
    AggregateNotFound,
    DatabaseError,
    DatabaseLockError,
    InvalidOperationError,
    OutdatedVersion,
    ResourceNotFound,
    UnableToObtainLockOnBill,
)
from ths_common.utils.common_utils import group_list
from ths_common.value_objects import ItemCode


@register_instance()
class BillRepository(BaseRepository):
    """
    Bill repository
    """

    def verify_version_id(self, bill_id, version_id):
        bill = self.get(BillModel, bill_id=bill_id)
        return version_id == bill.version_id

    def validate_bill_hotel_id(self, bill_id):
        bill = self.get(BillModel, bill_id=bill_id)
        if not bill:
            raise AggregateNotFound("BillAggregate", bill_id)
        if not crs_context.should_bypass_access_entity_checks():
            RuleEngine.action_allowed(
                action='access_entity',
                facts=AccessEntityFacts(
                    user_data=crs_context.user_data,
                    entity_vendor_id=bill.vendor_id,
                    entity_type="bill",
                ),
                fail_on_error=True,
            )

    def mark_clean(self, bill_aggregate: BillAggregate):
        charges = bill_aggregate._charges
        for c in charges:
            c.mark_clean()

            for split in c._charge_splits:
                split.mark_clean()

    def save(self, bill_aggregate):
        """
        Saves the bill_aggregate in DB

        :param bill_aggregate:
        """
        bill_aggregate.check_invariance()
        bill_model = BillDBAdapter.to_db_model(bill_aggregate.bill)
        bill_id = bill_aggregate.bill.bill_id
        charge_models = ChargeDBAdapter.to_db_models(bill_id, bill_aggregate._charges)
        billed_entity_models = BilledEntityDBAdaptor.to_db_models(
            bill_id, bill_aggregate._billed_entities
        )
        folio_models = []
        folio_models.extend(FolioDBAdaptor.to_db_model(bill_id, bill_aggregate._folios))
        account_models = []
        for billed_entity in bill_aggregate._billed_entities:
            account_models.extend(
                AccountDBAdaptor.to_db_model(
                    bill_id, billed_entity.billed_entity_id, billed_entity.accounts
                )
            )
        allowance_models, charge_split_models = [], []
        for charge in bill_aggregate._charges:
            charge_split_models.extend(
                ChargeSplitDBAdapter.to_db_model(
                    bill_id, charge.charge_id, charge._charge_splits
                )
            )
            for charge_split in charge._charge_splits:
                allowance_models.extend(
                    AllowanceDBAdapter.to_db_model(
                        bill_id,
                        charge.charge_id,
                        charge_split.charge_split_id,
                        charge_split._allowances,
                    )
                )
        payment_models = PaymentDBAdapter.to_db_models(
            bill_id, bill_aggregate._payments
        )
        payment_split_models = []
        for payment in bill_aggregate._payments:
            payment_split_models.extend(
                PaymentSplitDBAdapter.to_db_models(
                    bill_id, payment.payment_id, payment.payment_splits
                )
            )

        self._save_all(charge_split_models)
        self._save_all(allowance_models)
        self._save_all(charge_models)
        self._save_all(payment_split_models)
        self._save_all(payment_models)
        self._save_all(account_models)
        self._save_all(billed_entity_models)
        self._save_all(folio_models)
        self._save(bill_model)
        self.flush_session()
        self.mark_clean(bill_aggregate)

    def update(self, bill_aggregate, flush_session=True):
        """
        updates the given bill aggregate
        :param bill_aggregate:
        :param flush_session:
        :return:
        """
        bill_aggregate.check_invariance()
        current_bill_version = self.get_current_bill_version(bill_aggregate.bill_id)
        if bill_aggregate.bill.version != current_bill_version:
            raise OutdatedVersion(
                'Bill', bill_aggregate.bill.version, current_bill_version
            )
        bill_aggregate.increment_version()
        bill_id = bill_aggregate.bill.bill_id

        # Handle bill
        bill_model = BillDBAdapter.to_db_model(bill_aggregate.bill)

        dirty_charges = [c for c in bill_aggregate._charges if c.is_dirty()]
        # Handle charges
        updated_charge_models = ChargeDBAdapter.to_db_models(
            bill_id, [c for c in dirty_charges if c.is_dirty() and not c.is_new()]
        )
        created_charge_models = ChargeDBAdapter.to_db_models(
            bill_id, [c for c in dirty_charges if c.is_dirty() and c.is_new()]
        )
        updated_charge_split_models, created_charge_split_models = [], []
        updated_allowance_models, created_allowance_models = [], []
        for charge in bill_aggregate._charges:
            dirty_charge_splits = [cs for cs in charge._charge_splits if cs.is_dirty()]
            updated_charge_split_models.extend(
                ChargeSplitDBAdapter.to_db_model(
                    bill_id,
                    charge.charge_id,
                    [cs for cs in dirty_charge_splits if not cs.is_new()],
                )
            )
            created_charge_split_models.extend(
                ChargeSplitDBAdapter.to_db_model(
                    bill_id,
                    charge.charge_id,
                    [cs for cs in dirty_charge_splits if cs.is_new()],
                )
            )
            for charge_split in charge._charge_splits:
                dirty_allowances = [
                    allowance
                    for allowance in charge_split._allowances
                    if allowance.is_dirty()
                ]
                updated_allowance_models.extend(
                    AllowanceDBAdapter.to_db_model(
                        bill_id,
                        charge.charge_id,
                        charge_split.charge_split_id,
                        [
                            allowance
                            for allowance in dirty_allowances
                            if not allowance.is_new()
                        ],
                    )
                )
                created_allowance_models.extend(
                    AllowanceDBAdapter.to_db_model(
                        bill_id,
                        charge.charge_id,
                        charge_split.charge_split_id,
                        [
                            allowance
                            for allowance in dirty_allowances
                            if allowance.is_new()
                        ],
                    )
                )

        dirty_billed_entities = [
            be for be in bill_aggregate._billed_entities if be.is_dirty()
        ]
        updated_billed_entity_models = BilledEntityDBAdaptor.to_db_models(
            bill_id,
            [be for be in dirty_billed_entities if be.is_dirty() and not be.is_new()],
        )
        created_billed_entity_models = BilledEntityDBAdaptor.to_db_models(
            bill_id,
            [be for be in dirty_billed_entities if be.is_dirty() and be.is_new()],
        )
        deleted_folios = [
            folio
            for folio in bill_aggregate._folios
            if folio.is_dirty() and folio.deleted is True
        ]

        if deleted_folios:
            self.query(FolioModel).filter(FolioModel.bill_id == bill_id).delete(
                synchronize_session=False
            )
        updated_folio_models, created_folio_models = [], []
        dirty_folios = [folio for folio in bill_aggregate._folios if folio.is_dirty()]
        updated_folio_models.extend(
            FolioDBAdaptor.to_db_model(
                bill_id,
                [f for f in dirty_folios if not f.is_new() and f.deleted is False],
            )
        )
        created_folio_models.extend(
            FolioDBAdaptor.to_db_model(
                bill_id,
                [f for f in dirty_folios if f.is_new()],
            )
        )
        updated_account_models, created_account_models = [], []
        for billed_entity in bill_aggregate._billed_entities:
            dirty_accounts = [ac for ac in billed_entity.accounts if ac.is_dirty()]
            updated_account_models.extend(
                AccountDBAdaptor.to_db_model(
                    bill_id,
                    billed_entity.billed_entity_id,
                    [ac for ac in dirty_accounts if not ac.is_new()],
                )
            )
            created_account_models.extend(
                AccountDBAdaptor.to_db_model(
                    bill_id,
                    billed_entity.billed_entity_id,
                    [ac for ac in dirty_accounts if ac.is_new()],
                )
            )

        # Handle payments
        dirty_payments = [p for p in bill_aggregate._payments if p.is_dirty()]
        updated_payment_models = PaymentDBAdapter.to_db_models(
            bill_id, [p for p in dirty_payments if p.is_dirty() and not p.is_new()]
        )
        created_payment_models = PaymentDBAdapter.to_db_models(
            bill_id, [p for p in dirty_payments if p.is_dirty() and p.is_new()]
        )
        updated_payment_split_models, created_payment_split_models = [], []
        for payment in bill_aggregate._payments:
            dirty_payment_splits = [
                ps for ps in payment.all_payment_splits if ps.is_dirty()
            ]
            updated_payment_split_models.extend(
                PaymentSplitDBAdapter.to_db_models(
                    bill_id,
                    payment.payment_id,
                    [ps for ps in dirty_payment_splits if not ps.is_new()],
                )
            )
            created_payment_split_models.extend(
                PaymentSplitDBAdapter.to_db_models(
                    bill_id,
                    payment.payment_id,
                    [ps for ps in dirty_payment_splits if ps.is_new()],
                )
            )

        self._bulk_update_mappings(
            ChargeModel, [ch.mapping_dict() for ch in updated_charge_models]
        )
        self._bulk_update_mappings(
            ChargeSplitModel, [cs.mapping_dict() for cs in updated_charge_split_models]
        )
        self._bulk_update_mappings(
            AllowanceModel,
            [allowance.mapping_dict() for allowance in updated_allowance_models],
        )
        self._bulk_update_mappings(
            BilledEntityModel,
            [be.mapping_dict() for be in updated_billed_entity_models],
        )
        self._bulk_update_mappings(
            FolioModel, [f.mapping_dict() for f in updated_folio_models]
        )
        self._bulk_update_mappings(
            AccountModel, [ac.mapping_dict() for ac in updated_account_models]
        )
        self._bulk_update_mappings(
            PaymentModel, [p.mapping_dict() for p in updated_payment_models]
        )
        self._bulk_update_mappings(
            PaymentSplitModel,
            [ps.mapping_dict() for ps in updated_payment_split_models],
        )

        self._bulk_insert_mappings(
            ChargeModel, [ch.mapping_dict() for ch in created_charge_models]
        )
        self._bulk_insert_mappings(
            ChargeSplitModel, [cs.mapping_dict() for cs in created_charge_split_models]
        )
        self._bulk_insert_mappings(
            AllowanceModel,
            [allowance.mapping_dict() for allowance in created_allowance_models],
        )
        self._bulk_insert_mappings(
            BilledEntityModel,
            [be.mapping_dict() for be in created_billed_entity_models],
        )
        self._bulk_insert_mappings(
            FolioModel, [f.mapping_dict() for f in created_folio_models]
        )
        self._bulk_insert_mappings(
            AccountModel, [ac.mapping_dict() for ac in created_account_models]
        )
        self._bulk_insert_mappings(
            PaymentModel, [p.mapping_dict() for p in created_payment_models]
        )
        self._bulk_insert_mappings(
            PaymentSplitModel,
            [ps.mapping_dict() for ps in created_payment_split_models],
        )

        self._update(bill_model)
        if flush_session:
            self.flush_session()
        self.mark_clean(bill_aggregate)

    def update_charge_model(self, bill_aggregate, flush_session=True):
        bill_id = bill_aggregate.bill.bill_id
        bill_model = BillDBAdapter.to_db_model(bill_aggregate.bill)
        dirty_charges = [c for c in bill_aggregate._charges if c.is_dirty()]
        # Handle charges
        updated_charge_models = ChargeDBAdapter.to_db_models(
            bill_id, [c for c in dirty_charges if c.is_dirty() and not c.is_new()]
        )
        self._bulk_update_mappings(
            ChargeModel, [ch.mapping_dict() for ch in updated_charge_models]
        )
        self._update(bill_model)
        if flush_session:
            self.flush_session()
        self.mark_clean(bill_aggregate)

    def update_all(self, bill_aggregates):
        for bill_aggregate in bill_aggregates:
            self.update(bill_aggregate, flush_session=False)
        self.flush_session()

    def _create_bill_aggregates(self, bill_models, skip_billed_entities=False):
        bill_ids = [bill_model.bill_id for bill_model in bill_models]

        charge_split_models = (
            self.query(ChargeSplitModel)
            .filter(ChargeSplitModel.bill_id.in_(bill_ids))
            .all()
        )
        allowance_models = (
            self.query(AllowanceModel)
            .filter(AllowanceModel.bill_id.in_(bill_ids))
            .all()
        )
        charge_models = (
            self.query(ChargeModel)
            .filter(ChargeModel.bill_id.in_(bill_ids))
            .order_by(ChargeModel.applicable_date.asc())
            .all()
        )
        payment_split_models = (
            self.query(PaymentSplitModel)
            .filter(PaymentSplitModel.bill_id.in_(bill_ids))
            .all()
        )
        payment_models = (
            self.query(PaymentModel).filter(PaymentModel.bill_id.in_(bill_ids)).all()
        )
        folio_models = (
            self.query(FolioModel).filter(FolioModel.bill_id.in_(bill_ids)).all()
        )
        account_models = (
            self.query(AccountModel).filter(AccountModel.bill_id.in_(bill_ids)).all()
        )
        billed_entity_models = []
        if not skip_billed_entities:
            billed_entity_models = (
                self.query(BilledEntityModel)
                .filter(BilledEntityModel.bill_id.in_(bill_ids))
                .all()
            )
        grouped_charge_split_models = group_list(charge_split_models, 'bill_id')
        grouped_allowance_models = group_list(allowance_models, 'bill_id')
        grouped_charge_models = group_list(charge_models, 'bill_id')
        grouped_payment_models = group_list(payment_models, 'bill_id')
        grouped_folio_models = group_list(folio_models, 'bill_id')
        grouped_account_models = group_list(account_models, 'bill_id')
        grouped_billed_entity_models = group_list(billed_entity_models, 'bill_id')
        grouped_payment_split_models = group_list(payment_split_models, 'bill_id')

        bill_aggregates = list()
        for bill_model in bill_models:
            bill = BillDBAdapter.to_entity(bill_model)
            allowance_map = AllowanceDBAdapter.to_entities(
                grouped_allowance_models[bill_model.bill_id], bill.base_currency
            )
            charge_split_map = ChargeSplitDBAdapter.to_entities(
                grouped_charge_split_models[bill_model.bill_id],
                allowance_map,
                bill.base_currency,
            )
            folio_map = FolioDBAdaptor.to_entities(
                grouped_folio_models[bill_model.bill_id]
            )
            account_map = AccountDBAdaptor.to_entities(
                grouped_account_models[bill_model.bill_id], folio_map
            )
            charges = ChargeDBAdapter.to_entities(
                grouped_charge_models[bill_model.bill_id],
                charge_split_map,
                bill.base_currency,
            )
            payment_split_map = PaymentSplitDBAdapter.to_entities(
                grouped_payment_split_models[bill_model.bill_id], bill.base_currency
            )
            payments = PaymentDBAdapter.to_entities(
                grouped_payment_models[bill_model.bill_id],
                payment_split_map,
                bill.base_currency,
            )
            billed_entities = BilledEntityDBAdaptor.to_entities(
                grouped_billed_entity_models[bill_model.bill_id], account_map
            )
            bill_aggregates.append(
                BillAggregate(
                    bill,
                    payments,
                    charges,
                    billed_entities,
                    list(folio_map.values()) if folio_map else [],
                )
            )
        return bill_aggregates

    def _load(self, bill_model, use_raw_query=None, exclude_fields=None):
        bill_id = bill_model.bill_id
        bill = BillDBAdapter.to_entity(bill_model)

        if use_raw_query:
            charge_split_rows = (
                self.query(*RawChargeSplitDBAdapter.columns)
                .filter(ChargeSplitModel.bill_id == bill_id)
                .filter(ChargeSplitModel.deleted == False)
                .all()
            )
            charge_rows = (
                self.query(*RawChargeDBAdapter.columns)
                .filter(ChargeModel.bill_id == bill_id)
                .filter(ChargeModel.deleted == False)
                .order_by(ChargeModel.applicable_date)
                .all()
            )

            # Charge Row, 10th index is status, as per RawChargeDBAdapter.columns order
            if any(
                charge_row[10] == ChargeStatus.CONSUMED.value
                for charge_row in charge_rows
            ):
                allowance_rows = (
                    self.query(*RawAllowanceDBAdaptor.columns)
                    .filter(AllowanceModel.bill_id == bill_id)
                    .filter(AllowanceModel.deleted.is_(False))
                )

                allowances = RawAllowanceDBAdaptor.to_entities(
                    allowance_rows, bill.base_currency
                )
            else:
                allowances = defaultdict(list)

            charge_splits = RawChargeSplitDBAdapter.to_entities(
                charge_split_rows, allowances, bill.base_currency
            )
            charges = RawChargeDBAdapter.to_entities(
                charge_rows, charge_splits, bill.base_currency
            )
        else:
            charge_split_models = self.filter(
                ChargeSplitModel, ChargeSplitModel.bill_id == bill_id
            ).all()
            charge_models = (
                self.filter(ChargeModel, ChargeModel.bill_id == bill_id)
                .order_by(ChargeModel.applicable_date)
                .all()
            )

            if any(
                charge_model.status == ChargeStatus.CONSUMED.value
                for charge_model in charge_models
            ):
                allowance_models = self.filter(
                    AllowanceModel, AllowanceModel.bill_id == bill_id
                ).all()
                allowance_map = AllowanceDBAdapter.to_entities(
                    allowance_models, bill.base_currency
                )
            else:
                allowance_map = defaultdict(list)
            charge_split_map = ChargeSplitDBAdapter.to_entities(
                charge_split_models, allowance_map, bill.base_currency
            )
            charges = ChargeDBAdapter.to_entities(
                charge_models, charge_split_map, bill.base_currency
            )

        payment_models = self.filter(
            PaymentModel, PaymentModel.bill_id == bill_id
        ).all()
        payment_split_models = self.filter(
            PaymentSplitModel, PaymentSplitModel.bill_id == bill_id
        ).all()
        payment_split_map = PaymentSplitDBAdapter.to_entities(
            payment_split_models, bill.base_currency
        )
        payments = PaymentDBAdapter.to_entities(
            payment_models, payment_split_map, bill.base_currency
        )

        if exclude_fields and 'billed_entities' in exclude_fields:
            billed_entities = None
            folio_map = None
        else:
            billed_entity_models = self.filter(
                BilledEntityModel, BilledEntityModel.bill_id == bill_id
            ).all()
            folio_map = FolioDBAdaptor.to_entities(
                self.filter(FolioModel, FolioModel.bill_id == bill_id).all()
            )
            account_map = AccountDBAdaptor.to_entities(
                self.filter(AccountModel, AccountModel.bill_id == bill_id).all(),
                folio_map,
            )
            billed_entities = BilledEntityDBAdaptor.to_entities(
                billed_entity_models, account_map
            )

        return BillAggregate(
            bill,
            payments,
            charges,
            billed_entities,
            list(folio_map.values()) if folio_map else [],
        )

    def load(
        self, bill_id, use_raw_query=None, exclude_fields=None, shallow_response=False
    ):
        # load bill
        bill_model = self.get(BillModel, bill_id=bill_id)
        if not bill_model:
            raise AggregateNotFound("BillAggregate", bill_id)
        return self._load(
            bill_model,
            use_raw_query=use_raw_query,
            exclude_fields=exclude_fields,
        )

    def load_for_update(self, bill_id, version=None):
        try:
            bill_model = self.get_for_update(BillModel, bill_id=bill_id)
        except DatabaseLockError as exc:
            raise UnableToObtainLockOnBill(description=str(exc))

        if not bill_model:
            # 0353
            raise AggregateNotFound("BillAggregate", bill_id)
        if version is not None and bill_model.version != version:
            # 0354
            raise OutdatedVersion('BillAggregate', version, bill_model.version)
        return self._load(bill_model)

    def delete_all(self, bill_ids, user_data=None):
        if not user_data or user_data.user_type != UserType.CRS_MIGRATION_USER.value:
            raise InvalidOperationError("Only CRS Migration User can delete bill_ids")

        deleted_bill_count = (
            self.query(BillModel)
            .filter(BillModel.bill_id.in_(bill_ids))
            .delete(synchronize_session=False)
        )
        self.query(ChargeModel).filter(ChargeModel.bill_id.in_(bill_ids)).delete(
            synchronize_session=False
        )
        self.query(ChargeSplitModel).filter(
            ChargeSplitModel.bill_id.in_(bill_ids)
        ).delete(synchronize_session=False)
        self.query(PaymentModel).filter(PaymentModel.bill_id.in_(bill_ids)).delete(
            synchronize_session=False
        )
        self.flush_session()
        return deleted_bill_count

    def load_charges(self, bill_id, charge_id_gt=None, limit=None, charge_ids=None):
        # load charges
        charge_models = self.filter(ChargeModel, ChargeModel.bill_id == bill_id).filter(
            ChargeModel.deleted == False
        )
        if charge_id_gt is not None and limit is not None:
            charge_models = (
                charge_models.filter(ChargeModel.charge_id > charge_id_gt)
                .order_by(ChargeModel.charge_id)
                .limit(limit)
            )

        if charge_ids is not None:
            charge_models = charge_models.filter(ChargeModel.charge_id.in_(charge_ids))
            if limit is not None:
                charge_models = charge_models.order_by(ChargeModel.charge_id).limit(
                    limit
                )

        charge_ids = [cm.charge_id for cm in charge_models]
        charge_split_models = (
            self.filter(ChargeSplitModel, ChargeSplitModel.bill_id == bill_id)
            .filter(ChargeSplitModel.deleted == False)
            .filter(ChargeSplitModel.charge_id.in_(charge_ids))
        )
        allowance_models = (
            self.filter(AllowanceModel, AllowanceModel.bill_id == bill_id)
            .filter(AllowanceModel.deleted == False)
            .filter(AllowanceModel.charge_id.in_(charge_ids))
        )
        base_currency = self.get_bill_base_currency(bill_id)
        allowance_map = AllowanceDBAdapter.to_entities(allowance_models, base_currency)
        charge_split_map = ChargeSplitDBAdapter.to_entities(
            charge_split_models, allowance_map, base_currency
        )
        charges = ChargeDBAdapter.to_entities(
            charge_models, charge_split_map, base_currency
        )
        return charges

    def load_charge(self, bill_id, charge_id):
        try:
            charge_model = (
                self.filter(
                    ChargeModel,
                    ChargeModel.bill_id == bill_id,
                    ChargeModel.charge_id == charge_id,
                )
                .filter(ChargeModel.deleted == False)
                .one()
            )
            charge_split_models = (
                self.filter(ChargeSplitModel, ChargeSplitModel.bill_id == bill_id)
                .filter(ChargeSplitModel.charge_id == charge_id)
                .filter(ChargeSplitModel.deleted == False)
            )
            allowance_models = (
                self.filter(AllowanceModel, AllowanceModel.bill_id == bill_id)
                .filter(AllowanceModel.charge_id == charge_id)
                .filter(AllowanceModel.deleted == False)
            )
            base_currency = self.get_bill_base_currency(bill_id)
            allowance_map = AllowanceDBAdapter.to_entities(
                allowance_models, base_currency
            )
            charge_split_map = ChargeSplitDBAdapter.to_entities(
                charge_split_models, allowance_map, base_currency
            )
            charges = ChargeDBAdapter.to_entities(
                [charge_model], charge_split_map, base_currency
            )
            return charges[0]
        except NoResultFound:
            raise ResourceNotFound(
                "Charge",
                description="Charge with charge_id: {} not found in Bill: {}".format(
                    charge_id, bill_id
                ),
            )
        except MultipleResultsFound:
            message = "Multiple objects %s found" % str(ChargeModel)
            raise DatabaseError(description=message)

    def get_current_bill_version(self, bill_id):
        current_bill_version = self.filter(
            BillModel.version, BillModel.bill_id == bill_id
        ).one()[0]
        return current_bill_version

    def get_bill_base_currency(self, bill_id):
        bill_base_currency = self.filter(
            BillModel.base_currency, BillModel.bill_id == bill_id
        ).one()[0]
        return bill_base_currency

    def get_bill_base_currencies(self, bill_ids):
        bill_base_currencies = (
            self.query(BillModel.bill_id, BillModel.base_currency)
            .filter(BillModel.bill_id.in_(bill_ids))
            .all()
        )
        return {
            base_currency[0]: CurrencyType(
                base_currency[1]
                if base_currency[1]
                else crs_context.hotel_context.base_currency
            )
            for base_currency in bill_base_currencies
        }

    def load_all_with_yield_per(self, bill_ids, skip_billed_entities=False):
        # TODO: fix this yield will not work if .all() is called
        bill_models = (
            self.query(BillModel)
            .filter(BillModel.bill_id.in_(bill_ids))
            .yield_per(1000)
            .all()
        )
        return self._create_bill_aggregates(bill_models, skip_billed_entities)

    def get_max_charge_id(self, bill_id):
        max_charge_id = (
            self.query(func.max(ChargeModel.charge_id))
            .filter(ChargeModel.bill_id == bill_id)
            .scalar()
        )
        return max_charge_id

    def get_all_bills_for_night_audit(self, vendor_ids, current_business_date):
        """
        Index needed:

        CREATE INDEX ix_bill_vendor_id ON bill (vendor_id, deleted);
        CREATE INDEX ix_payment_night_audit ON payment (bill_id, status, date_of_payment);
        CREATE INDEX ix_charge_night_audit ON charge (bill_id, status, deleted, applicable_date);
        """
        switch_over_time = crs_context.get_hotel_context().switch_over_time

        # Next day switch over time
        max_time_for_current_business_date = dateutils.datetime_at_given_time(
            dateutils.add(current_business_date, days=1), switch_over_time
        )

        import datetime

        least_billed_entity_migrated_booking_date = datetime.date(2020, 11, 1)
        q = (
            self.query(BillModel)
            .filter(BillModel.vendor_id.in_(vendor_ids), BillModel.deleted.is_(False))
            .filter(
                or_(
                    (
                        self.query(ChargeModel.bill_id)
                        .filter(ChargeModel.bill_id == BillModel.bill_id)
                        .filter(ChargeModel.status == ChargeStatus.CREATED.value)
                        .filter(ChargeModel.deleted.is_(False))
                        .filter(
                            ChargeModel.applicable_date
                            < max_time_for_current_business_date
                        )
                        .filter(
                            ChargeModel.applicable_date
                            > least_billed_entity_migrated_booking_date
                        )
                        .exists()
                    ),
                    (
                        self.query(AllowanceModel.bill_id)
                        .filter(AllowanceModel.bill_id == BillModel.bill_id)
                        .filter(AllowanceModel.status.in_([ChargeStatus.CREATED.value]))
                        .filter(AllowanceModel.deleted.is_(False))
                        .exists()
                    ),
                    (
                        self.query(PaymentModel.bill_id)
                        .filter(PaymentModel.bill_id == BillModel.bill_id)
                        .filter(
                            PaymentModel.status.in_(
                                [
                                    PaymentStatus.DONE.value,
                                    PaymentStatus.UNCONFIRMED.value,
                                ]
                            )
                        )
                        .filter(PaymentModel.deleted.is_(False))
                        .filter(
                            PaymentModel.date_of_payment
                            < max_time_for_current_business_date
                        )
                        .filter(
                            PaymentModel.date_of_payment
                            > least_billed_entity_migrated_booking_date
                        )
                        .exists()
                    ),
                )
            )
            .order_by(BillModel.bill_id.desc())
            .with_for_update(nowait=False, of=BillModel)
        )

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def cashier_report_query(self, hotel_id, start_date, end_date):
        end_date = dateutils.add(dateutils.ymd_str_to_date(end_date), days=1)
        q = self.query(BillModel).yield_per(1000)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = (
            q.join(PaymentModel, BillModel.bill_id == PaymentModel.bill_id)
            .filter(PaymentModel.date_of_payment >= start_date)
            .filter(PaymentModel.date_of_payment <= end_date)
        )
        q = q.order_by(PaymentModel.created_at.desc())

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def load_with_payment_ref_id(self, payment_ref_id):
        q = self.query(BillModel)
        q = q.join(PaymentModel, BillModel.bill_id == PaymentModel.bill_id).filter(
            PaymentModel.payment_ref_id == payment_ref_id
        )
        bill_model = q.all()

        return self._create_bill_aggregates(bill_model)

    def load_all_for_update(self, bill_ids):
        bill_models = self.filter(
            BillModel, BillModel.bill_id.in_(bill_ids), for_update=True
        ).order_by(BillModel.created_at)
        return self._create_bill_aggregates(bill_models.all())

    def load_billed_entities(self, bill_id, include_deleted=False):
        # load billed entities
        billed_entity_models = self.filter(
            BilledEntityModel, BilledEntityModel.bill_id == bill_id
        )
        if not include_deleted:
            billed_entity_models = billed_entity_models.filter(
                BilledEntityModel.deleted == False
            )
        account_models = self.filter(AccountModel, AccountModel.bill_id == bill_id)
        if not include_deleted:
            account_models = account_models.filter(AccountModel.deleted == False)
        folio_map = FolioDBAdaptor.to_entities(
            self.filter(FolioModel, FolioModel.bill_id == bill_id).all()
        )
        account_map = AccountDBAdaptor.to_entities(account_models, folio_map)
        billed_entities = BilledEntityDBAdaptor.to_entities(
            billed_entity_models.all(), account_map
        )
        return billed_entities

    def trail_balance_past_dated_charge_addition_query(self, hotel_id, start_date):
        q = self.query(BillModel)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = (
            q.join(ChargeModel, BillModel.bill_id == ChargeModel.bill_id)
            .filter(func.Date(ChargeModel.created_at) == start_date)
            .filter(ChargeModel.applicable_date < start_date)
        )

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def trail_balance_payments_added_on_running_date(
        self, hotel_id, start_date, end_date
    ):
        q = self.query(BillModel)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = q.join(PaymentModel, BillModel.bill_id == PaymentModel.bill_id).filter(
            func.Date(PaymentModel.date_of_payment) == start_date
        )

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def trail_balance_payments_modified_on_running_date(
        self, hotel_id, start_date, end_date
    ):
        q = self.query(BillModel)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = q.join(PaymentModel, BillModel.bill_id == PaymentModel.bill_id).filter(
            func.Date(PaymentModel.modified_at) == start_date
        )

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def trail_balance_allowance_added_on_running_date(self, hotel_id, running_date):
        q = self.query(BillModel)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = q.join(AllowanceModel, BillModel.bill_id == AllowanceModel.bill_id).filter(
            dateutils.to_date(AllowanceModel.posting_date) == running_date,
            AllowanceModel.deleted.is_(False),
        )

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def trail_balance_pos_bills(self, start_date, end_date, vendor_ids):
        q = self.query(BillModel).yield_per(1000)
        q = q.filter(
            BillModel.vendor_id.in_(vendor_ids),
            BillModel.deleted.is_(False),
            BillModel.status == BillStatus.CREATED.value,
        ).filter(BillModel.app_id == BillAppId.POS_APP.value)

        q = q.join(ChargeModel, BillModel.bill_id == ChargeModel.bill_id).filter(
            ChargeModel.posting_date >= start_date,
            ChargeModel.posting_date <= end_date,
            ChargeModel.status == ChargeStatus.CONSUMED.value,
            ChargeModel.deleted == False,
        )
        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def manager_flash_report_query_for_pos(self, report_date, vendor_ids):
        q = self.query(BillModel).yield_per(1000)
        q = q.filter(
            BillModel.vendor_id.in_(vendor_ids),
            BillModel.deleted.is_(False),
            BillModel.status == BillStatus.CREATED.value,
        )
        q = q.join(ChargeModel, BillModel.bill_id == ChargeModel.bill_id).filter(
            ChargeModel.posting_date == report_date,
            ChargeModel.status == ChargeStatus.CONSUMED.value,
            ChargeModel.deleted == False,
        )
        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def manager_flash_report_query_for_allowances(self, hotel_id, posting_date):
        q = self.query(BillModel)
        q = q.filter(BillModel.vendor_id == hotel_id, BillModel.deleted.is_(False))
        q = q.join(AllowanceModel, BillModel.bill_id == AllowanceModel.bill_id).filter(
            AllowanceModel.posting_date == posting_date,
            AllowanceModel.deleted.is_(False),
        )

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def in_touch_charge_query(self, report_date, hotel_id):
        q = self.query(BillModel.bill_id)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = q.join(ChargeModel, BillModel.bill_id == ChargeModel.bill_id)
        q = q.filter(
            or_(
                func.Date(ChargeModel.created_at) == report_date,
                func.Date(ChargeModel.modified_at) == report_date,
            ),
        )

        bill_ids = q.all()
        return bill_ids

    def in_touch_payment_query(self, report_date, hotel_id):
        q = self.query(BillModel.bill_id)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = q.join(PaymentModel, BillModel.bill_id == PaymentModel.bill_id).filter(
            func.Date(PaymentModel.date_of_payment) == report_date
        )
        q = q.filter(
            or_(
                func.Date(PaymentModel.created_at) == report_date,
                func.Date(PaymentModel.modified_at) == report_date,
            )
        )

        bill_ids = q.all()
        return bill_ids

    def get_total_room_revenue_projected_for_business_date(
        self, hotel_id, business_date
    ) -> Decimal:
        """
        Return total revenue for Stay SKU Category HSN Code, or other categories having same HSN Code as Stay category,
        for given business date.
        Either posting_date, or applicable_date should match the business_date, and charge status should be CREATED
        or CONSUMED

        Subtract Allowance value from revenue

        total_room_revenue = total_charge (for room hsn) - total_allowance (on charge with room hsn)
        """
        total_charge = (
            self.query(func.sum(ChargeModel.pretax_amount))
            .join(BillModel, BillModel.bill_id == ChargeModel.bill_id)
            .join(
                SkuCategoryModel,
                ChargeModel.sku_category_id == SkuCategoryModel.sku_category_id,
            )
            .filter(
                BillModel.vendor_id == hotel_id,
                BillModel.deleted.is_(False),
                ChargeModel.status.in_(
                    [ChargeStatus.CREATED.value, ChargeStatus.CONSUMED.value]
                ),
                or_(
                    ChargeModel.posting_date == business_date,
                    ChargeModel.applicable_business_date == business_date,
                ),
                ChargeModel.deleted.is_(False),
                SkuCategoryModel.item_code
                == ItemCode.from_string(SkuCategoryHSN.STAY.value),
            )
            .first()[0]
        )

        total_allowance = (
            self.query(func.sum(AllowanceModel.pretax_amount))
            .join(BillModel, BillModel.bill_id == AllowanceModel.bill_id)
            .join(
                ChargeSplitModel,
                and_(
                    ChargeSplitModel.bill_id == AllowanceModel.bill_id,
                    ChargeSplitModel.charge_id == AllowanceModel.charge_id,
                    ChargeSplitModel.charge_split_id == AllowanceModel.charge_split_id,
                ),
            )
            .join(
                ChargeModel,
                and_(
                    ChargeModel.bill_id == AllowanceModel.bill_id,
                    ChargeModel.charge_id == AllowanceModel.charge_id,
                ),
            )
            .join(
                SkuCategoryModel,
                ChargeModel.sku_category_id == SkuCategoryModel.sku_category_id,
            )
            .filter(
                BillModel.vendor_id == hotel_id,
                BillModel.deleted.is_(False),
                ChargeModel.status.in_([ChargeStatus.CONSUMED.value]),
                or_(
                    ChargeModel.posting_date == business_date,
                    ChargeModel.applicable_business_date == business_date,
                ),
                ChargeSplitModel.deleted.is_(False),
                AllowanceModel.posting_date == business_date,
                AllowanceModel.status.in_(
                    [ChargeStatus.CREATED.value, ChargeStatus.CONSUMED.value]
                ),
                AllowanceModel.deleted.is_(False),
                SkuCategoryModel.item_code
                == ItemCode.from_string(SkuCategoryHSN.STAY.value),
            )
            .first()[0]
        )

        total_charge = total_charge if total_charge is not None else Decimal('0')
        total_allowance = (
            total_allowance if total_allowance is not None else Decimal('0')
        )
        return total_charge - total_allowance

    def get_bill_posttax_amounts(self, bill_charge_mappings):
        return (
            self.query(
                ChargeModel.bill_id, ChargeModel.charge_id, ChargeModel.posttax_amount
            )
            .filter(ChargeModel.deleted == False)
            .filter(
                tuple_(ChargeModel.bill_id, ChargeModel.charge_id).in_(
                    bill_charge_mappings
                )
            )
            .all()
        )

    def trial_balance_allowance_posted_on_business_date(self, hotel_id, business_date):
        q = self.query(BillModel)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = q.join(AllowanceModel, BillModel.bill_id == AllowanceModel.bill_id).filter(
            AllowanceModel.posting_date == business_date,
            AllowanceModel.deleted.is_(False),
        )

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def trial_balance_charges_posted_on_business_date(self, hotel_id, business_date):
        q = self.query(BillModel)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = q.join(ChargeModel, BillModel.bill_id == ChargeModel.bill_id).filter(
            ChargeModel.posting_date == business_date, ChargeModel.deleted.is_(False)
        )

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def trial_balance_payments_made_on_business_date(self, hotel_id, business_date):
        q = self.query(BillModel)
        q = q.filter(BillModel.vendor_id == hotel_id)
        q = q.join(PaymentModel, BillModel.bill_id == PaymentModel.bill_id).filter(
            func.date(
                func.timezone(
                    dateutils.get_timezone().zone, PaymentModel.date_of_payment
                )
            )
            == business_date
        )

        bill_models = q.all()
        return self._create_bill_aggregates(bill_models)

    def get_payments_posted_before_given_business_date_on_bills(
        self, bill_ids, business_date
    ) -> List[Money]:
        base_currency = crs_context.get_hotel_context().base_currency
        switch_over_time = crs_context.get_hotel_context().switch_over_time
        min_time_for_current_business_date = dateutils.datetime_at_given_time(
            business_date, switch_over_time
        )

        q = self.query(PaymentModel.payment_type, PaymentModel.amount)
        q = q.filter(
            PaymentModel.bill_id.in_(bill_ids),
            PaymentModel.status == PaymentStatus.POSTED.value,
        )
        payment_amounts = (
            q.filter(PaymentModel.date_of_payment < min_time_for_current_business_date)
            .filter(
                PaymentModel.deleted.is_(False),
                PaymentModel.payment_type.in_(
                    [PaymentTypes.PAYMENT.value, PaymentTypes.REFUND.value]
                ),
            )
            .all()
        )

        return [
            Money(payment_amount, base_currency)
            if payment_type == PaymentTypes.PAYMENT.value
            else Money(-payment_amount, base_currency)
            for payment_type, payment_amount in payment_amounts
        ]

    def get_payments_posted_on_given_business_date_on_bills(
        self, business_date, hotel_id
    ):
        switch_over_time = crs_context.get_hotel_context().switch_over_time
        min_time_for_current_business_date = dateutils.datetime_at_given_time(
            business_date, switch_over_time
        )
        max_time_for_current_business_date = dateutils.datetime_at_given_time(
            dateutils.add(business_date, days=1), switch_over_time
        )

        q = self.query(
            PaymentModel.bill_id,
            PaymentModel.payment_type,
            PaymentModel.payment_mode,
            PaymentModel.amount,
        )
        q = q.filter(PaymentModel.status == PaymentStatus.POSTED.value).join(
            BillModel, BillModel.bill_id == PaymentModel.bill_id
        )
        payments = (
            q.filter(
                BillModel.vendor_id == hotel_id,
                PaymentModel.date_of_payment > min_time_for_current_business_date,
                PaymentModel.date_of_payment < max_time_for_current_business_date,
            )
            .filter(
                PaymentModel.deleted.is_(False),
                PaymentModel.payment_type.in_(
                    [PaymentTypes.PAYMENT.value, PaymentTypes.REFUND.value]
                ),
            )
            .all()
        )

        return payments

    def get_charges_posted_on_given_business_date_on_bills(
        self, business_date, hotel_id
    ):
        q = self.query(
            ChargeModel.bill_id,
            ChargeModel.pretax_amount,
            ChargeModel.posttax_amount,
            ChargeModel.sku_category_id,
            ChargeModel.tax_details,
        )
        q = q.join(BillModel, BillModel.bill_id == ChargeModel.bill_id)
        q = q.filter(
            BillModel.vendor_id == hotel_id,
            ChargeModel.posting_date == business_date,
            ChargeModel.status == ChargeStatus.CONSUMED.value,
            ChargeModel.deleted.is_(False),
        )
        charges = q.all()

        return charges

    def get_allowances_posted_on_given_business_date_on_bills(
        self, business_date, hotel_id
    ):
        q = self.query(
            AllowanceModel.bill_id,
            AllowanceModel.pretax_amount,
            AllowanceModel.posttax_amount,
            ChargeModel.sku_category_id,
            AllowanceModel.tax_details,
        ).join(
            AllowanceModel,
            and_(
                ChargeModel.bill_id == AllowanceModel.bill_id,
                ChargeModel.charge_id == AllowanceModel.charge_id,
            ),
        )
        q = q.join(BillModel, BillModel.bill_id == AllowanceModel.bill_id)
        q = q.filter(
            BillModel.vendor_id == hotel_id,
            AllowanceModel.posting_date == business_date,
            AllowanceModel.status == ChargeStatus.CONSUMED.value,
            ChargeModel.deleted.is_(False),
            BillModel.deleted.is_(False),
            AllowanceModel.deleted.is_(False),
        )
        allowances = q.all()

        return allowances

    def _load_bill_wise_credit_summary(
        self, bill_ids, bill_wise_base_currency: Dict[str, CurrencyType]
    ) -> Dict[str, CreditSummaryDto]:
        total_payment_group_by_type = (
            self.query(
                PaymentModel.bill_id,
                PaymentModel.payment_type,
                PaymentModel.confirmed,
                func.sum(PaymentSplitModel.amount),
            )
            .join(
                PaymentModel,
                and_(
                    PaymentModel.bill_id == PaymentSplitModel.bill_id,
                    PaymentSplitModel.payment_id == PaymentModel.payment_id,
                ),
            )
            .filter(
                PaymentSplitModel.bill_id.in_(bill_ids),
                PaymentSplitModel.deleted.is_(False),
                PaymentModel.deleted.is_(False),
                PaymentModel.status != PaymentStatus.CANCELLED.value,
            )
            .group_by(
                PaymentModel.bill_id, PaymentModel.payment_type, PaymentModel.confirmed
            )
            .all()
        )

        bill_wise_payments_grouped_by_type_and_confirmed = defaultdict(
            lambda: defaultdict(Decimal)
        )
        for total_payment in total_payment_group_by_type:
            bill_wise_payments_grouped_by_type_and_confirmed[total_payment[0]][
                (total_payment[1], total_payment[2])
            ] = total_payment[3]

        bill_wise_credit_summary = dict()
        for bill_id in bill_ids:
            base_currency = bill_wise_base_currency[bill_id]
            payments_grouped_by_type_and_confirmed = (
                bill_wise_payments_grouped_by_type_and_confirmed[bill_id]
            )
            total_confirmed_payment = payments_grouped_by_type_and_confirmed[
                (PaymentTypes.PAYMENT.value, True)
            ]
            total_unconfirmed_payment = payments_grouped_by_type_and_confirmed[
                (PaymentTypes.PAYMENT.value, False)
            ]
            total_refund = (
                payments_grouped_by_type_and_confirmed[
                    (PaymentTypes.REFUND.value, True)
                ]
                + payments_grouped_by_type_and_confirmed[
                    (PaymentTypes.REFUND.value, False)
                ]
            )
            total_credit = (
                total_confirmed_payment + total_unconfirmed_payment - total_refund
            )
            bill_wise_credit_summary[bill_id] = CreditSummaryDto(
                total_confirmed_payment=Money(total_confirmed_payment, base_currency),
                total_unconfirmed_payment=Money(
                    total_unconfirmed_payment, base_currency
                ),
                total_credit_offered=Money("0", base_currency),
                total_refund=Money(total_refund, base_currency),
                total_credit=Money(total_credit, base_currency),
            )

        return bill_wise_credit_summary

    def _load_bill_wise_debit_summary(
        self, bill_ids, bill_wise_base_currency: Dict[str, CurrencyType]
    ) -> Dict[str, DebitSummaryDto]:
        total_charge_group_by_type = (
            self.query(
                ChargeSplitModel.bill_id,
                ChargeSplitModel.charge_type,
                func.sum(ChargeSplitModel.post_tax),
                func.sum(ChargeSplitModel.pre_tax),
            )
            .join(
                ChargeModel,
                and_(
                    ChargeModel.bill_id == ChargeSplitModel.bill_id,
                    ChargeModel.charge_id == ChargeSplitModel.charge_id,
                ),
            )
            .filter(
                ChargeModel.bill_id.in_(bill_ids),
                ChargeModel.deleted.is_(False),
                ChargeModel.status != ChargeStatus.CANCELLED.value,
                ChargeSplitModel.deleted.is_(False),
            )
            .group_by(ChargeSplitModel.bill_id, ChargeSplitModel.charge_type)
            .all()
        )

        total_spot_credit_group_by_bill = (
            self.query(
                ChargeSplitModel.bill_id,
                func.sum(ChargeSplitModel.post_tax),
                func.sum(ChargeSplitModel.pre_tax),
            )
            .join(
                ChargeModel,
                and_(
                    ChargeModel.bill_id == ChargeSplitModel.bill_id,
                    ChargeModel.charge_id == ChargeSplitModel.charge_id,
                ),
            )
            .filter(
                ChargeModel.bill_id.in_(bill_ids),
                ChargeModel.deleted.is_(False),
                ChargeModel.status != ChargeStatus.CANCELLED.value,
                ChargeSplitModel.charge_type == ChargeTypes.CREDIT.value,
                ChargeSplitModel.charge_sub_type == ChargeSubTypes.SPOT_CREDIT.value,
                ChargeSplitModel.deleted.is_(False),
            )
            .group_by(ChargeSplitModel.bill_id)
            .all()
        )

        total_allowance_group_by_type = (
            self.query(
                ChargeSplitModel.bill_id,
                ChargeSplitModel.charge_type,
                func.sum(func.coalesce(AllowanceModel.posttax_amount, Decimal('0'))),
                func.sum(func.coalesce(AllowanceModel.pretax_amount, Decimal('0'))),
            )
            .join(
                ChargeModel,
                and_(
                    ChargeModel.bill_id == ChargeSplitModel.bill_id,
                    ChargeModel.charge_id == ChargeSplitModel.charge_id,
                ),
            )
            .join(
                AllowanceModel,
                and_(
                    ChargeSplitModel.bill_id == AllowanceModel.bill_id,
                    ChargeSplitModel.charge_id == AllowanceModel.charge_id,
                    ChargeSplitModel.charge_split_id == AllowanceModel.charge_split_id,
                ),
            )
            .filter(
                ChargeModel.bill_id.in_(bill_ids),
                ChargeModel.deleted.is_(False),
                ChargeModel.status != ChargeStatus.CANCELLED.value,
                ChargeSplitModel.deleted.is_(False),
                AllowanceModel.deleted.is_(False),
                AllowanceModel.status != ChargeStatus.CANCELLED.value,
            )
            .group_by(ChargeSplitModel.bill_id, ChargeSplitModel.charge_type)
            .all()
        )

        bill_wise_allowance_sum_group_by_charge_type_posttax = defaultdict(
            lambda: defaultdict(Decimal)
        )
        bill_wise_allowance_sum_group_by_charge_type_pretax = defaultdict(
            lambda: defaultdict(Decimal)
        )
        for total_charge_and_allowance in total_allowance_group_by_type:
            bill_wise_allowance_sum_group_by_charge_type_posttax[
                total_charge_and_allowance[0]
            ][total_charge_and_allowance[1]] = total_charge_and_allowance[2]
            bill_wise_allowance_sum_group_by_charge_type_pretax[
                total_charge_and_allowance[0]
            ][total_charge_and_allowance[1]] = total_charge_and_allowance[3]

        bill_wise_charge_split_sum_group_by_charge_type_posttax = defaultdict(
            lambda: defaultdict(Decimal)
        )
        bill_wise_charge_split_sum_group_by_charge_type_pretax = defaultdict(
            lambda: defaultdict(Decimal)
        )
        for total_charge_and_allowance in total_charge_group_by_type:
            bill_wise_charge_split_sum_group_by_charge_type_posttax[
                total_charge_and_allowance[0]
            ][total_charge_and_allowance[1]] = total_charge_and_allowance[2]
            bill_wise_charge_split_sum_group_by_charge_type_pretax[
                total_charge_and_allowance[0]
            ][total_charge_and_allowance[1]] = total_charge_and_allowance[3]

        bill_wise_spot_credit_sums_posttax = defaultdict(Decimal)
        bill_wise_spot_credit_sums_pretax = defaultdict(Decimal)
        for spot_credit_group_by_bill in total_spot_credit_group_by_bill:
            bill_wise_spot_credit_sums_posttax[
                spot_credit_group_by_bill[0]
            ] = spot_credit_group_by_bill[1]
            bill_wise_spot_credit_sums_pretax[
                spot_credit_group_by_bill[0]
            ] = spot_credit_group_by_bill[2]

        bill_wise_debit_summary = dict()
        for bill_id in bill_ids:
            base_currency = bill_wise_base_currency[bill_id]
            # Post-tax calculations
            posttax_allowance_sum_group_by_charge_type = (
                bill_wise_allowance_sum_group_by_charge_type_posttax[bill_id]
            )
            posttax_charge_split_sum_group_by_charge_type = (
                bill_wise_charge_split_sum_group_by_charge_type_posttax[bill_id]
            )
            total_posttax_credit_type_allowance = (
                posttax_allowance_sum_group_by_charge_type[ChargeTypes.CREDIT.value]
            )
            total_posttax_non_credit_type_allowance = (
                posttax_allowance_sum_group_by_charge_type[ChargeTypes.NON_CREDIT.value]
            )
            total_posttax_allowance = (
                total_posttax_credit_type_allowance
                + total_posttax_non_credit_type_allowance
            )

            total_posttax_debit_payable_after_checkout = (
                posttax_charge_split_sum_group_by_charge_type[ChargeTypes.CREDIT.value]
                - total_posttax_credit_type_allowance
            )
            total_posttax_debit_payable_at_checkout = (
                posttax_charge_split_sum_group_by_charge_type[
                    ChargeTypes.NON_CREDIT.value
                ]
                - total_posttax_non_credit_type_allowance
            )
            total_posttax_debit = (
                total_posttax_debit_payable_after_checkout
                + total_posttax_debit_payable_at_checkout
            )
            total_posttax_credit_charge = (
                total_posttax_debit_payable_after_checkout
                + total_posttax_credit_type_allowance
            )
            total_posttax_non_credit_charge = (
                total_posttax_debit_payable_at_checkout
                + total_posttax_non_credit_type_allowance
            )
            total_posttax_charge = (
                total_posttax_credit_charge + total_posttax_non_credit_charge
            )

            # Pre-tax calculations
            pretax_allowance_sum_group_by_charge_type = (
                bill_wise_allowance_sum_group_by_charge_type_pretax[bill_id]
            )
            pretax_charge_split_sum_group_by_charge_type = (
                bill_wise_charge_split_sum_group_by_charge_type_pretax[bill_id]
            )
            total_pretax_credit_type_allowance = (
                pretax_allowance_sum_group_by_charge_type[ChargeTypes.CREDIT.value]
            )
            total_pretax_non_credit_type_allowance = (
                pretax_allowance_sum_group_by_charge_type[ChargeTypes.NON_CREDIT.value]
            )
            total_pretax_debit_payable_after_checkout = (
                pretax_charge_split_sum_group_by_charge_type[ChargeTypes.CREDIT.value]
                - total_pretax_credit_type_allowance
            )
            total_pretax_debit_payable_at_checkout = (
                pretax_charge_split_sum_group_by_charge_type[
                    ChargeTypes.NON_CREDIT.value
                ]
                - total_pretax_non_credit_type_allowance
            )
            total_debit_pretax = (
                total_pretax_debit_payable_after_checkout
                + total_pretax_debit_payable_at_checkout
            )
            bill_wise_debit_summary[bill_id] = DebitSummaryDto(
                total_charge=Money(total_posttax_charge, base_currency),
                total_allowance=Money(total_posttax_allowance, base_currency),
                total_debit=Money(total_posttax_debit, base_currency),
                total_debit_pretax=Money(total_debit_pretax, base_currency),
                total_credit_charge=Money(total_posttax_credit_charge, base_currency),
                total_non_credit_charge=Money(
                    total_posttax_non_credit_charge, base_currency
                ),
                total_credit_allowance=Money(
                    total_posttax_credit_type_allowance, base_currency
                ),
                total_non_credit_allowance=Money(
                    total_posttax_non_credit_type_allowance, base_currency
                ),
                total_debit_payable_after_checkout=Money(
                    total_posttax_debit_payable_after_checkout, base_currency
                ),
                total_debit_payable_at_checkout=Money(
                    total_posttax_debit_payable_at_checkout, base_currency
                ),
                total_spot_credit=Money(
                    bill_wise_spot_credit_sums_posttax[bill_id], base_currency
                ),
            )
        return bill_wise_debit_summary

    def load_bill_wise_summary(
        self, bill_ids, base_currency=None
    ) -> Dict[str, BillSummaryDto]:
        if not base_currency:
            bill_wise_base_currency = self.get_bill_base_currencies(bill_ids)
        else:
            bill_wise_base_currency = {bill_id: base_currency for bill_id in bill_ids}

        bill_wise_credit_summary = self._load_bill_wise_credit_summary(
            bill_ids, bill_wise_base_currency
        )
        bill_wise_debit_summary = self._load_bill_wise_debit_summary(
            bill_ids, bill_wise_base_currency
        )

        bill_wise_summary = dict()
        for bill_id in bill_ids:
            credit_summary = bill_wise_credit_summary[bill_id]
            debit_summary = bill_wise_debit_summary[bill_id]
            bill_wise_summary[bill_id] = BillSummaryDto(credit_summary, debit_summary)
        return bill_wise_summary

    def get_bills(self, bill_ids) -> List[Bill]:
        bill_models = (
            self.query(BillModel).filter(BillModel.bill_id.in_(bill_ids)).all()
        )
        bills = [BillDBAdapter.to_entity(bill_model) for bill_model in bill_models]
        return bills

    def get_app_id(self, bill_id):
        return (
            self.query(BillModel.app_id).filter(BillModel.bill_id == bill_id).first()[0]
        )

    def get_charge_details_for_erp_service(
        self, hotel_id=None, business_date=None, bill_ids=None
    ):
        q = self.query(
            ChargeModel.bill_id,
            ChargeModel.sku_category_id,
            ChargeSplitModel.pre_tax,
            ChargeSplitModel.tax_details,
            ChargeSplitModel.charge_type,
            ChargeSplitModel.billed_entity_id,
            FolioModel.folio_number,
        )
        q = q.join(ChargeSplitModel, ChargeSplitModel.bill_id == ChargeModel.bill_id)
        q = q.join(FolioModel, FolioModel.bill_id == ChargeModel.bill_id)
        q = q.filter(
            ChargeModel.status == ChargeStatus.CONSUMED.value,
            ChargeModel.charge_id == ChargeSplitModel.charge_id,
            FolioModel.billed_entity_id == ChargeSplitModel.billed_entity_id,
            FolioModel.account_number == ChargeSplitModel.billed_entity_account_number,
            ChargeModel.deleted.is_(False),
            ChargeSplitModel.deleted.is_(False),
        )

        if bill_ids:
            q = q.filter(ChargeModel.bill_id.in_(bill_ids))
        elif hotel_id and business_date:
            q = q.join(BillModel, BillModel.bill_id == ChargeModel.bill_id)
            q = q.filter(
                BillModel.vendor_id == hotel_id,
                BillModel.deleted.is_(False),
                ChargeModel.posting_date == business_date,
            )

        charge_splits_details = q.all()

        charge_details = [
            ErpChargeDetailsDto(charge_split_detail)
            for charge_split_detail in charge_splits_details
        ]

        return charge_details

    def get_allowance_details_for_erp_service(
        self, hotel_id=None, business_date=None, bill_ids=None
    ):
        q = self.query(
            AllowanceModel.bill_id,
            ChargeModel.sku_category_id,
            AllowanceModel.pretax_amount.label("pre_tax"),
            AllowanceModel.tax_details,
            ChargeSplitModel.charge_type,
            AllowanceModel.billed_entity_id,
            FolioModel.folio_number,
            ChargeSplitModel.invoice_id,
            AllowanceModel.credit_note_id,
        )
        q = q.join(AllowanceModel, AllowanceModel.bill_id == ChargeModel.bill_id)
        q = q.join(ChargeSplitModel, AllowanceModel.bill_id == ChargeSplitModel.bill_id)
        q = q.join(FolioModel, AllowanceModel.bill_id == FolioModel.bill_id)
        q = q.filter(
            AllowanceModel.status == ChargeStatus.CONSUMED.value,
            AllowanceModel.billed_entity_id == FolioModel.billed_entity_id,
            AllowanceModel.billed_entity_account_number == FolioModel.account_number,
            AllowanceModel.charge_id == ChargeModel.charge_id,
            AllowanceModel.charge_id == ChargeSplitModel.charge_id,
            AllowanceModel.charge_split_id == ChargeSplitModel.charge_split_id,
            AllowanceModel.deleted.is_(False),
            ChargeModel.deleted.is_(False),
            ChargeSplitModel.deleted.is_(False),
        )

        if bill_ids:
            q = q.filter(AllowanceModel.bill_id.in_(bill_ids))
        elif hotel_id and business_date:
            q = q.join(BillModel, AllowanceModel.bill_id == BillModel.bill_id)
            q = q.filter(
                BillModel.vendor_id == hotel_id,
                BillModel.deleted.is_(False),
                AllowanceModel.posting_date == business_date,
            )

        allowances = q.all()
        allowance_details = [ErpChargeDetailsDto(allowance) for allowance in allowances]
        return allowance_details

    def get_payment_splits_for_erp_service(
        self, hotel_id=None, business_date=None, payment_type=None
    ):
        payment_type = PaymentTypes.REFUND if payment_type else PaymentTypes.PAYMENT
        q = self.query(
            PaymentModel.bill_id,
            PaymentModel.date_of_payment,
            PaymentSplitModel.amount,
            PaymentModel.payment_mode,
            PaymentModel.payment_ref_id,
            PaymentModel.payor_billed_entity_id,
            FolioModel.folio_number,
        )
        q = q.join(PaymentSplitModel, PaymentModel.bill_id == PaymentSplitModel.bill_id)
        q = q.join(FolioModel, PaymentModel.bill_id == FolioModel.bill_id)
        q = q.join(BillModel, PaymentModel.bill_id == BillModel.bill_id)
        q = q.filter(
            PaymentModel.status == PaymentStatus.POSTED.value,
            PaymentModel.payment_type == payment_type.value,
            PaymentModel.payment_id == PaymentSplitModel.payment_id,
            FolioModel.billed_entity_id == PaymentSplitModel.billed_entity_id,
            FolioModel.account_number == PaymentSplitModel.billed_entity_account_number,
            BillModel.vendor_id == hotel_id,
            BillModel.deleted.is_(False),
            PaymentModel.posting_date == business_date,
            PaymentModel.deleted.is_(False),
            PaymentSplitModel.deleted.is_(False),
        )

        payment_splits_details = q.all()

        payment_details = [
            ErpPaymentDetailsDto(payment_splits_detail)
            for payment_splits_detail in payment_splits_details
        ]

        return payment_details

    def get_folio_details_for_bill(self, bill_id):
        return self.query(FolioModel).filter(FolioModel.bill_id == bill_id).all()

    def get_payments_for_bills(self, bill_ids):
        q = self.query(PaymentModel)
        q = q.filter(PaymentModel.bill_id.in_(bill_ids))
        return q.all()

    def update_payments(self, payment_models):
        self._update_all(payment_models)
        self.flush_session()

    def get_payments_posted_on_given_date(self, posted_date):
        q = self._build_payment_data_query()
        q = q.join(
            PaymentSplitModel,
            and_(
                PaymentSplitModel.bill_id == PaymentModel.bill_id,
                PaymentSplitModel.payment_id == PaymentModel.payment_id,
                PaymentSplitModel.deleted.is_(False),
            ),
        ).filter(
            PaymentModel.status == PaymentStatus.POSTED.value,
            PaymentModel.posted_date == posted_date,
            PaymentModel.deleted.is_(False),
        )

        payment_charge_details = q.all()

        payment_charge_details = [
            PaymentChargeDetailsDto(payment_charge_detail)
            for payment_charge_detail in payment_charge_details
        ]

        return payment_charge_details

    def get_billed_entities_for_bills(self, bill_ids):
        q = self.query(
            BilledEntityModel.bill_id,
            BilledEntityModel.billed_entity_id,
            BilledEntityModel.category,
            BilledEntityModel.first_name,
            BilledEntityModel.last_name,
        ).filter(
            BilledEntityModel.bill_id.in_(bill_ids),
            BilledEntityModel.deleted.is_(False),
        )

        billed_entities_details = q.all()

        billed_entities_details = [
            BilledEntityDetailsDto(billed_entity_details)
            for billed_entity_details in billed_entities_details
        ]

        return billed_entities_details

    def get_payments_with_reference_id(self, payment_ref_id):
        q = self.query(PaymentModel)
        q = q.filter(
            PaymentModel.payment_ref_id == payment_ref_id,
            PaymentModel.status != PaymentStatus.CANCELLED.value,
            PaymentModel.deleted.is_(False),
        )
        payments_details = q.all()
        payments_details = [
            PaymentDetailsDto(payment_details) for payment_details in payments_details
        ]

        return payments_details

    def get_payments_posted_on_given_date_for_given_pay_modes(
        self, posted_date, payment_modes
    ):
        q = (
            self.query(
                PaymentModel.posted_date,
                PaymentModel.date_of_payment,
                PaymentModel.bill_id,
                PaymentModel.payment_type,
                PaymentModel.payment_mode,
                PaymentModel.payment_id,
                PaymentModel.payment_ref_id,
                PaymentModel.created_at,
                PaymentSplitModel.payment_split_id,
                PaymentSplitModel.amount,
                PaymentSplitModel.billed_entity_account_number,
            )
            .join(
                PaymentSplitModel,
                and_(
                    PaymentSplitModel.bill_id == PaymentModel.bill_id,
                    PaymentSplitModel.payment_id == PaymentModel.payment_id,
                ),
            )
            .filter(
                PaymentModel.status == PaymentStatus.POSTED.value,
                PaymentModel.posted_date == posted_date,
                PaymentModel.payment_mode.in_(payment_modes),
                PaymentModel.deleted.is_(False),
            )
        )

        payment_details = q.all()

        payment_details = [
            PaymentDataDto(payment_detail) for payment_detail in payment_details
        ]

        return payment_details

    def get_payments_under_given_payment_modes_for_bills(self, bill_ids, payment_modes):
        q = (
            self.query(
                PaymentModel.posted_date,
                PaymentModel.date_of_payment,
                PaymentModel.bill_id,
                PaymentModel.payment_type,
                PaymentModel.payment_mode,
                PaymentModel.payment_id,
                PaymentModel.payment_ref_id,
                PaymentSplitModel.payment_split_id,
                PaymentSplitModel.amount,
                PaymentModel.created_at,
                PaymentSplitModel.billed_entity_account_number,
            )
            .join(
                PaymentSplitModel,
                and_(
                    PaymentSplitModel.bill_id == PaymentModel.bill_id,
                    PaymentSplitModel.payment_id == PaymentModel.payment_id,
                ),
            )
            .filter(
                PaymentModel.status == PaymentStatus.POSTED.value,
                PaymentModel.payment_mode.in_(payment_modes),
                PaymentModel.deleted.is_(False),
                PaymentModel.bill_id.in_(bill_ids),
            )
        )

        payment_details = q.all()

        payment_details = [
            PaymentDataDto(payment_detail) for payment_detail in payment_details
        ]

        return payment_details

    def get_posted_payments_on_given_bills_for_given_payment_modes(
        self, bill_ids, payment_modes=None
    ):
        q = self._build_payment_data_query()
        q = q.join(
            PaymentSplitModel,
            and_(
                PaymentSplitModel.bill_id == PaymentModel.bill_id,
                PaymentSplitModel.payment_id == PaymentModel.payment_id,
                PaymentSplitModel.deleted.is_(False),
            ),
        ).filter(
            PaymentModel.status == PaymentStatus.POSTED.value,
            PaymentModel.bill_id.in_(bill_ids),
            PaymentModel.deleted.is_(False),
        )

        if payment_modes:
            q = q.filter(PaymentModel.payment_mode.in_(payment_modes))

        payment_charge_details = q.all()

        payment_charge_details = [
            PaymentChargeDetailsDto(payment_charge_detail)
            for payment_charge_detail in payment_charge_details
        ]

        return payment_charge_details

    def _create_charge_subquery(self, query_model):
        return self.query(func.sum(ChargeModel.posttax_amount)).filter(
            ChargeModel.bill_id == query_model.bill_id,
            ChargeModel.status != ChargeStatus.CANCELLED.value,
            ChargeModel.deleted.is_(False),
        )

    def _create_allowance_subquery(self, query_model):
        return self.query(func.sum(AllowanceModel.posttax_amount)).filter(
            AllowanceModel.bill_id == query_model.bill_id,
            AllowanceModel.status != ChargeStatus.CANCELLED.value,
            AllowanceModel.deleted.is_(False),
        )

    def _build_payment_data_query(self):
        subquery_charge = self._create_charge_subquery(PaymentModel)
        subquery_allowance = self._create_allowance_subquery(PaymentModel)
        payment_data_query = self.query(
            PaymentModel.posted_date,
            PaymentModel.created_at,
            PaymentModel.bill_id,
            PaymentModel.payment_type,
            PaymentModel.payment_channel,
            PaymentModel.payment_mode,
            PaymentModel.payment_mode_sub_type,
            PaymentModel.payment_id,
            PaymentModel.payment_ref_id,
            PaymentModel.payor_billed_entity_id,
            PaymentModel.paid_by,
            PaymentModel.paid_to,
            PaymentModel.refund_reason,
            PaymentSplitModel.payment_split_id,
            PaymentSplitModel.amount,
            PaymentSplitModel.billed_entity_id,
            PaymentSplitModel.billed_entity_account_number,
            subquery_charge.label('total_charges'),
            subquery_allowance.label('total_allowances'),
        )
        return payment_data_query

    def get_cs_refunds_posted_for_given_bills_or_date(
        self, posted_date=None, bill_ids=None
    ):
        subquery_charge = self._create_charge_subquery(CreditShellRefundModel)
        subquery_allowance = self._create_allowance_subquery(CreditShellRefundModel)

        q = self.query(
            CreditShellRefundModel.created_at,
            CreditShellRefundModel.bill_id,
            CreditShellRefundModel.payment_mode,
            CreditShellRefundModel.payment_ref_id,
            CreditShellRefundModel.credit_shell_refund_id,
            CreditShellRefundModel.amount,
            CreditShellRefundModel.paid_by,
            CreditShellRefundModel.paid_to,
            CreditShellModel.billed_entity_id,
            subquery_charge.label('total_charges'),
            subquery_allowance.label('total_allowances'),
        ).join(
            CreditShellModel,
            CreditShellModel.credit_shell_id == CreditShellRefundModel.credit_shell_id,
        )

        if posted_date:
            created_at_min = dateutils.datetime_at_given_time(posted_date, time.min)
            created_at_max = dateutils.datetime_at_given_time(posted_date, time.max)
            q = q.filter(
                and_(
                    CreditShellRefundModel.created_at >= created_at_min,
                    CreditShellRefundModel.created_at <= created_at_max,
                )
            )

        if bill_ids:
            q = q.filter(CreditShellRefundModel.bill_id.in_(bill_ids))

        cs_refund_charge_details = q.all()

        cs_refund_charge_details = [
            CreditShellRefundChargeDetailsDto(cs_refund_charge_detail)
            for cs_refund_charge_detail in cs_refund_charge_details
        ]

        return cs_refund_charge_details

    def load_for_update_v2(self, bill_id, version=None):
        try:
            bill_model = self.filter(
                BillModel, BillModel.bill_id == bill_id, for_update=True
            ).one()
        except DatabaseLockError as exc:
            raise UnableToObtainLockOnBill(description=str(exc))
        except NoResultFound as exc:
            raise AggregateNotFound("BillAggregate", bill_id)

        if not bill_model:
            raise AggregateNotFound("BillAggregate", bill_id)

        if version is not None and version != bill_model.version:
            raise OutdatedVersion(
                "Bill",
                requested_version=version,
                current_version=bill_model.version,
            )

        return self._create_bill_aggregates([bill_model])[0]

    def load_bill_by_booking_id(
        self,
        booking_id,
    ):
        bill_model = self.get(BillModel, parent_reference_number=booking_id)
        if not bill_model:
            raise AggregateNotFound("BillAggregate", booking_id)
        return self._load(bill_model)
