from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts import Guest<PERSON>tayFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class AddGuestRule(BaseRule):
    def allow(self, facts: GuestStayFacts, privileges=None):
        if (
            PrivilegeCode.EDIT_BOOKING_FOR_CH not in privileges
            or facts.get_channel_code()
            not in privileges[PrivilegeCode.EDIT_BOOKING_FOR_CH]
        ):
            raise PolicyAuthException(
                error=PolicyError.ADD_GUEST_NOT_ALLOWED_FOR_BOOKING_FROM_THIS_CHANNEL
            )

        if (
            not facts.hotel_live()
            and PrivilegeCode.FULL_ACCESS_IF_HOTEL_NOT_LIVE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.ADD_GUEST_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE
            )

        if (
            facts.is_credit_folio()
            and PrivilegeCode.ADD_CHARGE_TO_CREDIT_FOLIO not in privileges
        ):
            raise PolicyAuthException(error=PolicyError.NOT_AUTHORIZED_TO_ADD_GUEST)
        return True
