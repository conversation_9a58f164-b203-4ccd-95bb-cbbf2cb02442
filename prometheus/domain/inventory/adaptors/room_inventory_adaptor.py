from prometheus.domain.inventory.entities.room_inventory import RoomInventory
from prometheus.domain.inventory.models import RoomInventoryModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.inventory_constants import (
    RoomCurrentStatus,
    RoomReservationStatus,
)


class RoomInventoryAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity, **kwargs):
        return RoomInventoryModel(
            hotel_id=domain_entity.hotel_id,
            room_id=domain_entity.room_id,
            current_status=domain_entity.current_status.value,
            reservation_statuses=[
                reservation_status.value
                for reservation_status in domain_entity.reservation_statuses
            ]
            if domain_entity.reservation_statuses
            else [],
        )

    def to_domain_entity(self, db_entity):
        reservation_statuses = (
            set(
                [
                    RoomReservationStatus(reservation_status)
                    for reservation_status in db_entity.reservation_statuses
                ]
            )
            if db_entity.reservation_statuses
            else set()
        )

        return RoomInventory(
            hotel_id=db_entity.hotel_id,
            room_id=db_entity.room_id,
            current_status=RoomCurrentStatus(db_entity.current_status),
            reservation_statuses=reservation_statuses,
            dirty=False,
            new=False,
        )
