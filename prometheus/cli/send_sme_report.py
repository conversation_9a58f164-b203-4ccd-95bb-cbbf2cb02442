import logging

import click
from flask.cli import with_appcontext
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.reporting.reporting_service import ReportingApplicationService

logger = logging.getLogger(__name__)


@click.command()
@click.option('--start_date', help="start_date value in isoformat")
@click.option('--end_date', help="end_date value in isoformat")
@with_appcontext
@session_manager(commit=True)
@inject(reporting_app_service=ReportingApplicationService)
def generate_sme_report(reporting_app_service, start_date, end_date):
    start_date = dateutils.ymd_str_to_date(start_date)
    end_date = dateutils.ymd_str_to_date(end_date)
    reporting_app_service.generate_between_date_range_and_trigger_sme_report_email(
        start_date=start_date, end_date=end_date
    )
