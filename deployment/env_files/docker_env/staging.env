# Used as .env_file in docker compose. Sets environment variable for application
FLASK_APP=tenant_gateway_app
APP_NAME=tenant_gateway_service
APP_ENV=staging

LOG_ROOT=/var/log/tenant_gateway/
STATIC_ROOT=/var/www/static/
CONFIG_FILE_PATH=/usr/src/app/deployment/config_files/staging.cfg

THSC_ENVIRONMENT=staging

AWS_SECRET_PREFIX=apps/tenant_gateway_service
AWS_REGION=ap-southeast-1

TENANT_SERVICE_HOST=https://tenants.treebo.be

LC_ALL=C.UTF-8
LANG=C.UTF-8

SU_INTEGRATION_ENDPOINT_URL = 'https://stayinn.suissu.com'
SU_BASIC_AUTH_HEADER = 'Basic eU84NmZBVng6S1FaSzJOQ3Y'
CATALOG_SERVICE_ENDPOINT_URL = 'http://catalog.treebo.be'
CRS_SERVICE_ENDPOINT_URL = 'http://crs.treebo.be'
RATE_MANAGER_SERVICE_ENDPOINT_URL = 'http://rackrate.treebo.be'
NOTIFICATION_SERVICE_ENDPOINT_URL = 'http://notification-staging.treebo.be'
COMPANY_PROFILE_SERVICE_ENDPOINT_URL = 'http://company-profiles.treebo.be'

# New Relic Configuration
NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED=1000