Flask==2.3.3
SQLAlchemy==2.0.20
Flask-SQLAlchemy==3.1.1
flasgger==0.9.7.1
flask-swagger-ui==4.11.1
git-pylint-commit-hook==2.6.1
requests==2.31.0
python-dateutil==2.8.2
click==8.1.7
lxml==4.9.3
Flask-Caching==2.0.2
Flask-HTTPAuth==4.8.0
apispec==6.3.0
apispec-webframeworks==0.5.2
paramiko==2.7.2
PyJWT==2.8.0
pydantic==2.3.0
kombu==5.3.2
marshmallow==3.20.1
marshmallow-enum==1.5.1

psycopg2==2.9.7
psycogreen==1.0.2
gevent==23.9.1
greenlet==3.0.0rc3
gunicorn==21.2.0

phonenumbers==8.10.17
logstash-formatter==0.5.17
redis==5.0.0
flaskhealthcheck==1.4.3
newrelic==9.1.0
sentry-sdk[flask]==1.31.0

#Dependencies
aenum==3.1.15
amqp==5.1.1
annotated-types==0.5.0
astroid==2.15.6
attrs==23.1.0
Babel==2.12.1
bcrypt==4.0.1
blinker==1.6.2
boto3==1.28.48
botocore==1.31.48
cachelib==0.9.0
certifi==2023.7.22
cffi==1.15.1
charset-normalizer==3.2.0
countryinfo==0.1.2
cryptography==41.0.3
dill==0.3.7
enum34==1.1.10
factory-boy==2.10.0
Faker==19.6.1
future==0.18.3
idna==3.4
IMAPClient==2.3.1
isort==5.12.0
itsdangerous==2.1.2
Jinja2==3.1.2
jmespath==1.0.1
jsonschema==4.19.0
jsonschema-specifications==2023.7.1
lazy-object-proxy==1.9.0
mail-parser==3.15.0
MarkupSafe==2.1.3
mccabe==0.7.0
mistune==3.0.1
mock==2.0.0
packaging==23.1
pbr==5.11.1
platformdirs==3.10.0
pycountry==23.12.11
pycparser==2.21
pydantic_core==2.6.3
pylint==2.17.5
PyNaCl==1.5.0
pytz==2023.3.post1
PyYAML==6.0.1
python-slugify==8.0.4
referencing==0.30.2
rpds-py==0.10.3
s3transfer==0.6.2
simplejson==3.19.1
six==1.16.0
tomlkit==0.12.1
typing_extensions==4.7.1
urllib3==1.26.16
vine==5.0.0
Werkzeug==2.3.7
wrapt==1.15.0
zope.event==5.0
zope.interface==6.0
