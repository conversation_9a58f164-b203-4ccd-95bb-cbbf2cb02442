# -*- coding: utf-8 -*-
"""Defines fixtures available to all tests."""

import os
import pytest
from treebo_commons.multitenancy.sqlalchemy import db_engine

from app import create_app
from object_registry import finalize_app_initialization
from rackrate_service.config import TestConfig
from tests.factories.user_factory import UserFactory

pytest_plugins = [
    "tests.rate_manager.fixtures.conftest_repositories",
    "tests.rate_manager.fixtures.conftest_rate_plan",
    "tests.rate_manager.fixtures.conftest_package",
    "tests.rate_manager.fixtures.conftest_rate_plan_rates",
    "tests.rate_manager.fixtures.conftest_room_rates",
    "tests.rate_manager.fixtures.conftest_inclusion_rates"
]

_app = create_app(TestConfig)
finalize_app_initialization()


def ensure_valid_test_env_setup(app):
    assert app.config['DEBUG']
    assert app.config['TESTING'], "App Config 'TESTING' must be True for running tests"
    assert os.environ.get('APP_ENV') == 'testing', "APP_ENV should be 'testing' for running tests"
    database_uris = db_engine.get_database_uris()

    assert all(db_creds.dbname == 'rate_manager_test' for tenant_id, db_creds in
               database_uris.items()), "Database name should be 'rate_manager_test' for running " \
                                       "tests. Found: {0}".format(list(database_uris.values())[0].db_uri)
    assert all(db_creds.host == 'localhost' for tenant_id, db_creds in
               database_uris.items()), "Database host should be 'localhost' for running tests. Found: {0}".format(
        list(database_uris.values())[0].db_uri)


ensure_valid_test_env_setup(_app)


@pytest.fixture(scope="session", autouse=True)
def app():
    """An application for the tests."""
    print("==> Creating TestConfig app context")
    ensure_valid_test_env_setup(_app)
    ctx = _app.test_request_context()
    print("======> Pushing test app context")
    ctx.push()

    print("Using db engine: {0}".format(db_engine.get_engine(None)))
    print("===========> Dropping and re-creating tables")
    db_engine.Base.metadata.drop_all(bind=db_engine.get_engine(None))
    db_engine.Base.metadata.create_all(bind=db_engine.get_engine(None))
    print("============> Created tables")

    yield _app

    print("=======> Popping test app context")
    ctx.pop()


@pytest.fixture(scope="function", autouse=True)
def db():
    yield

    db_engine.remove_session(None)

    import contextlib

    with contextlib.closing(db_engine.get_engine(None).connect()) as con:
        trans = con.begin()
        for table in reversed(db_engine.Base.metadata.sorted_tables):
            con.execute(table.delete())
        trans.commit()

    # db_engine.get_session(None).query(Role).delete()
    # db_engine.get_session(None).query(User).delete()
    # db_engine.get_session(None).query(SKU).delete()
    # db_engine.get_session(None).query(Property).delete()
    # db_engine.get_session(None).query(Policy).delete()
    # db_engine.get_session(None).query(EnabledPolicy).delete()


@pytest.fixture
def user():
    """A user for the tests."""
    user = UserFactory(password="myprecious")
    db_engine.get_session(None).commit()
    return user


@pytest.fixture
def client(app, request):
    test_client = app.test_client()

    def teardown():
        pass

    request.addfinalizer(teardown)
    return test_client
