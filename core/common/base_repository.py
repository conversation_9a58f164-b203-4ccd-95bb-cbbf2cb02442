import abc
import logging
from collections.abc import Sequence
from contextlib import ContextDecorator
from dataclasses import dataclass
from typing import List, Union

from flask import g
from flask_sqlalchemy import Pagination
from marshmallow import fields
from sqlalchemy import cast, String
from sqlalchemy.exc import DBAPIError
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

logger = logging.getLogger(__name__)

MAX_RESULTS_PER_PAGE = 200


class PaginationSchema(object):
    total = fields.Integer(data_key="total_records", dump_only=True)
    pages = fields.Integer(data_key="total_pages", dump_only=True)
    per_page = fields.Integer(missing=20)
    page = fields.Integer(missing=1)
    next_num = fields.Integer(data_key="next_page", dump_only=True)
    previous_num = fields.Integer(data_key="previous_page", dump_only=True)
    has_next = fields.Boolean(dump_only=True)
    has_prev = fields.Boolean(dump_only=True)
    max_results_per_page = fields.Integer(missing=MAX_RESULTS_PER_PAGE, dump_only=True)


class Result:
    """ Container of result to work with pagination and holding large amounts of records as an iterator"""

    def __init__(self, query, to_data_class_callback):
        self._query = query
        self.pagination = None
        self.callback = to_data_class_callback
        records = query
        if isinstance(query, Pagination):
            self.pagination = PaginationSchema().dump(query)
            records = query.items

        self.records = (to_data_class_callback(r) for r in records)

    def __iter__(self):
        return self.records

    def __next__(self):
        next(self.records)

    def __bool__(self):
        return bool(len(self))

    def __len__(self):
        return self.query.count()

    def __getitem__(self, index):
        return list(self.records)[index]

    def first(self):
        first_item = self.query.first()
        return self.callback(first_item) if first_item else None

    def last(self):
        last_item = self.query.last()
        return self.callback(last_item) if last_item else None

    @property
    def query(self):
        return self._query.query if isinstance(self._query, Pagination) else self._query


class BaseRepository(metaclass=abc.ABCMeta):
    primary_key_name = 'id'

    def __init__(self, model: Base, data_class: dataclass, use_soft_delete=False):
        self.data_class = data_class
        self.model = model

        self.use_soft_delete = use_soft_delete

        if self.use_soft_delete:
            self._original_delete = self.delete
            self._original_delete_many = self.delete_many
            self.delete = self.soft_delete
            self.delete_many = self.soft_delete_many

    @abc.abstractmethod
    def to_data_class(self, record):
        pass

    @abc.abstractmethod
    def to_record(self, data_class, existing_record=None):
        pass

    @property
    def _session(self):
        return db_engine.get_session()

    def save(self, data_class):
        record = self._save(data_class)
        return self.to_data_class(record)

    def _save(self, data_class):
        record = self.to_record(data_class)
        self._session.add(record)
        self._session.flush()
        return record

    def update(self, id, data_class):
        record = self._update(id, data_class)
        return self.to_data_class(record)

    def _update(self, id, data_class):
        existing_record = self.model.query.get(id)
        record = self.to_record(data_class, existing_record)
        setattr(record, self.primary_key_name, id)
        record = self._session.merge(record)
        self._session.flush()
        return record

    def delete(self, id):
        self.model.query.filter(self._primary_key() == id).delete()
        self._session.flush()

    def soft_delete(self, id):
        record = self.model.query.filter(self._primary_key() == id)
        record.is_deleted = True
        self._session.add(record)
        self._session.flush()

    def save_many(self, data_classes):
        records = [self.to_record(dc) for dc in data_classes]
        self._session.add_all(records)
        self._session.flush()
        return records

    def delete_many(self, ids: List):
        self.model.query.filter(self._primary_key().in_(ids)).delete()
        self._session.flush()

    def soft_delete_many(self, ids: List):
        records = self.model.query.filter(self._primary_key().in_(ids))
        for r in records:
            r.is_deleted = True
        self._session.add(records)
        self._session.flush()

    def all(self, pagination_details: dict = None):
        records = self.model.query.filter()
        if pagination_details:
            records = self._paginate(records, pagination_details)
        return Result(records, self.to_data_class)

    def _paginate(self, query, pagination_details):
        page, per_page, raise_exc, max_per_page = (
            pagination_details.get("page") or 1,
            pagination_details.get("per_page") or 20,
            pagination_details.get("raise_exc") or False,
            pagination_details.get("max_per_page") or 100,
        )
        query = query.paginate(page, per_page, error_out=raise_exc, max_per_page=max_per_page)
        return query

    def commit(self):
        if not g.get("single_commit"):
            self._session.commit()

    def from_id(self, id: str):
        record = self._from_ids(id)
        return self.to_data_class(record) if record else None

    def _from_ids(self, ids: Union[Sequence, str]):
        field = self._primary_key()
        if isinstance(ids, Sequence) and not isinstance(ids, str):
            # str is also counted as sequence so excluding it
            return self.model.query.filter(field.in_(ids))
        else:
            cond = field == ids
            if isinstance(ids, str):
                cond = cast(field, String) == ids
            return self.model.query.filter(cond).first()

    def from_ids(self, ids: List):
        records = self._from_ids(ids)
        return Result(records, self.to_data_class)

    def _primary_key(self):
        return getattr(self.model, self.primary_key_name)


class cleanup_session(ContextDecorator):
    def __init__(self, session=db_engine.get_session()):
        self.session = session

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type and issubclass(exc_type, DBAPIError):
            self.session.remove()


class single_commit(ContextDecorator):
    def __enter__(self):
        setattr(g, "single_commit", True)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        session = db_engine.get_session()
        if not exc_type:
            session.commit()
            setattr(g, "single_commit", False)
        else:
            session.rollback()
