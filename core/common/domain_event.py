import logging
import uuid
from collections import defaultdict
from typing import Type

logger = logging.getLogger(__name__)


class DomainEventHandler:

    def __init__(self, event, **kwargs):
        self.event = event
        self.kwargs = kwargs

    def run(self):
        pass


handler_map = defaultdict(list)


class DomainEvent:

    def __init__(self, reason='', handler_options: dict = None):
        self.uid = str(uuid.uuid4())
        self.reason = reason
        self.handler_options = handler_options or {}

    @property
    def handlers(self):
        return handler_map[self.__class__]

    def trigger(self):
        logger.info(f'Triggering domain event {self}')
        for handler in self.handlers:
            logger.info(f"Executing domain event handler: {handler} for event {repr(self)}")
            handler(self, **self.handler_options).run()

    @classmethod
    def subscribe(cls, handler: Type[DomainEventHandler]):
        handlers = handler_map[cls]
        if handler not in handlers:
            handlers.append(handler)
            handler_map[cls] = handlers

    def __str__(self):
        return f"{self.__class__.__name__}({self.uid})"

    def __repr__(self):
        return f"{self.__class__.__name__}({vars(self)})"
