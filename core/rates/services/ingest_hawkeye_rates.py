from datetime import timedelta
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

from dateutil.parser import parser
import newrelic.agent
from treebo_commons.utils import dateutils

from core.c_sc_p.data_classes.c_sc_p import AnyAnyAny
from core.property.repository.property import PropertyRepository
from core.rates.data_classes.rate import SellRate
from core.rates.data_classes.rate_request import RateRequest
from core.rates.data_classes.sources import DataScience
from core.rates.respository.guardrail import GuardRailRepository
from core.rates.respository.rate import DefaultRateRepository
from core.rates.respository.rate_request import DataScienceRateRequestRepository
from rackrate_service.application.rate_publisher_service import RatePublisherService
from rackrate_service.cache.cache_rackrate_response import CacheDeleteLayer
from rackrate_service.decorators import session_manager

logger = logging.getLogger('rackrate_service')


class HawkeyeRateIngestionService:

    def __init__(self, property_code, stay_prices):
        """
        :param property_code: str
        :param stay_prices: {stay_date: {<sku_code>: <price>}}
        """
        self.property = PropertyRepository.get_by_code(code=property_code)
        start_time = dateutils.current_datetime()
        self.stay_prices = stay_prices
        self.property_sku = self.property.supported_skus
        self.incoming_sku_codes = set()
        for sku_prices in stay_prices.values():
            self.incoming_sku_codes.update(sku_prices.keys())
        self.sku_name_to_id = {
            sku.name: sku.uid
            for sku in self.property_sku
        }
        self.relevant_skus = []
        self.sku_code_to_sku = {}
        for sku in self.property_sku:
            if sku.uid in self.incoming_sku_codes:
                self.sku_code_to_sku[sku.uid] = sku
                self.relevant_skus.append(sku)
                continue

            segments = sku.name.split('-')
            if len(segments) >= 2 and segments[1] != '1':
                base_sku = f"{segments[0]}-{segments[1]}"
            else:
                base_sku = segments[0]
            base_sku_id = self.sku_name_to_id.get(base_sku)
            if base_sku_id in self.incoming_sku_codes:
                self.relevant_skus.append(sku)
                self.sku_name_to_id[sku.name] = sku.uid
                self.sku_code_to_sku[sku.uid] = sku
        self.relevant_sku_codes = [sku.uid for sku in self.relevant_skus]
        logger.info(f'Time to build sku info {(dateutils.current_datetime() - start_time).total_seconds():.2f} sec')

    @staticmethod
    def get_stay_start_end_dates(stay_date):
        stay_start = parser().parse(stay_date)
        return stay_start, stay_start + timedelta(days=1)

    @staticmethod
    def get_date_ranges(dates):
        dates.sort()
        date_ranges = []

        start_date = None
        end_date = None
        for date in dates:
            if start_date is None:
                start_date = date
            elif date != end_date + timedelta(days=1):
                date_ranges.append((start_date, end_date+timedelta(days=1)))
                start_date = date
            end_date = date

        date_ranges.append((start_date, end_date+timedelta(days=1)))
        return date_ranges

    def get_guard_rails_map(self):
        guard_rails = GuardRailRepository.for_property_and_sku_codes(
            property=self.property,
            sku_codes=self.relevant_sku_codes,
            c_sc_p=AnyAnyAny,
        )
        return {guard_rail.sku[0].uid: guard_rail for guard_rail in guard_rails if guard_rail.sku}

    def get_existing_sale_rates_map(self):
        existing_sale_rates = DefaultRateRepository.get_default_sale_rates_for_property_skus_dates(
            property_code=self.property.uid,
            sku_codes=self.relevant_sku_codes,
            stay_dates=list(self.stay_prices.keys())
        )

        existing_sale_rates_map = dict()
        for rate in existing_sale_rates:
            key = (rate.sku_code, rate.stay_date)
            if key in existing_sale_rates_map:
                continue
            existing_sale_rates_map[key] = rate
        return existing_sale_rates_map

    def get_base_sku_name(self, sku):
        """
        Get base SKU name from a SKU code using direct dictionary lookup.
        """
        # Direct dictionary lookup instead of loop
        segments = sku.name.split('-')
        if len(segments) >= 2 and segments[1] != '1':
            base_sku = f"{segments[0]}-{segments[1]}"
        else:
            base_sku = f"{segments[0]}"
        return self.sku_name_to_id.get(base_sku)

    def process_rate_request(self, rate_request, guard_rails_map, existing_sale_rates_map):
        """Process a single rate request and return the results"""
        results = {
            'rate_and_request_ids': [],
            'existing_rates': []
        }

        sell_price_rates = SellRate.from_rate_request(
            rate_request,
            DataScience,
            guard_rails_map.get(rate_request.sku.uid)
        )

        for rate in sell_price_rates:
            key = (rate.sku.uid, rate.stay_date.date())
            if key not in existing_sale_rates_map:
                results['rate_and_request_ids'].append((rate, rate_request.id))
                continue

            existing_rate = existing_sale_rates_map[key]
            existing_rate.rate_request_id = rate_request.id
            existing_rate.price = rate.price
            existing_rate.override_source = rate.source.name
            results['existing_rates'].append(existing_rate)

        return results

    @newrelic.agent.background_task(name='ingest_hawkeye_rates')
    @session_manager
    def ingest(self):
        rate_requests, stay_dates = list(), list()
        start_time = dateutils.current_datetime()
        for stay_date, sku_prices in self.stay_prices.items():
            stay_dates.append(stay_date)
            stay_start, stay_end = self.get_stay_start_end_dates(stay_date)
            for sku in self.relevant_skus:
                base_sku_id = self.get_base_sku_name(sku)
                if not base_sku_id or base_sku_id not in sku_prices:
                    continue
                rate_requests.append(RateRequest(
                        property=self.property,
                        sku=sku,
                        c_sc_p=AnyAnyAny,
                        stay_start=stay_start,
                        stay_end=stay_end,
                        sell_price=sku_prices[base_sku_id],
                        list_price=None,
                    )
                )
        logger.info(f'Time to build rate-request {(dateutils.current_datetime() - start_time).total_seconds():.2f} sec')
        guard_rails_map = self.get_guard_rails_map()
        existing_sale_rates_map = self.get_existing_sale_rates_map()
        rate_and_request_ids = []
        existing_rates = []
        start_time = dateutils.current_datetime()
        rate_requests = self.filter_events_having_active_persistent_override(existing_sale_rates_map, rate_requests)
        rate_request_records = DataScienceRateRequestRepository.save_many(rate_requests)
        rate_requests = DataScienceRateRequestRepository.to_data_classes(
            rate_request_records, self.property, self.sku_code_to_sku
        )
        logger.info(
            f'Time to save rate requests {(dateutils.current_datetime() - start_time).total_seconds():.2f} sec'
        )
        start_time = dateutils.current_datetime()
        with ThreadPoolExecutor() as executor:
            futures = []
            for rate_request in rate_requests:
                future = executor.submit(
                    self.process_rate_request,
                    rate_request,
                    guard_rails_map,
                    existing_sale_rates_map
                )
                futures.append(future)

            for future in as_completed(futures):
                try:
                    results = future.result()
                    rate_and_request_ids.extend(results['rate_and_request_ids'])
                    existing_rates.extend(results['existing_rates'])
                except Exception as e:
                    logger.info(f"Error processing rate request: {e}")

        logger.info(
            f'Time to process ds request{(dateutils.current_datetime() - start_time).total_seconds():.2f} sec'
        )
        start_time = dateutils.current_datetime()
        if rate_and_request_ids:
            DefaultRateRepository.save_rates_in_bulk(rate_and_request_ids)

        if existing_rates:
            DefaultRateRepository.update_rates_in_bulk(existing_rates)
        logger.info(
            f'Time to save n update default rate{(dateutils.current_datetime() - start_time).total_seconds():.2f} sec'
        )
        CacheDeleteLayer.delete_rate_cache_data(self.property.uid, stay_dates)
        start_time = dateutils.current_datetime()
        try:
            RatePublisherService().create_price_payload_and_publish(existing_rates, rate_and_request_ids)
        except Exception as e:
            logger.info(f'Failed to publish rates: {e}')
        logger.info(
            f'Time to publish price payload {(dateutils.current_datetime() - start_time).total_seconds():.2f} sec'
        )

    @staticmethod
    def filter_events_having_active_persistent_override(existing_sale_rates_map, rate_requests):
        eligible_rate_requests = rate_requests.copy()
        for rate_request in rate_requests:
            existing_sale_rate = existing_sale_rates_map.get((rate_request.sku.uid, rate_request.stay_start.date()))
            if existing_sale_rate and existing_sale_rate.persistent_override:
                eligible_rate_requests.remove(rate_request)

        return eligible_rate_requests
