from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Any, Union

from core.c_sc_p.data_classes.channel import AnyC
from core.linked_rate_plans.respository.linked_rate_plan import SalePriceLinkedRatePlanRepository, \
    ListPriceLinkedRatePlanRepository


@dataclass
class RateRequest:
    property: Any
    sku: Any
    c_sc_p: Any

    stay_start: Union[str, datetime]
    stay_end: Union[str, datetime]

    list_price: Union[Decimal, None]
    sell_price: Union[Decimal, None]

    booking_start: Union[str, None, datetime] = None
    booking_end: Union[str, None, datetime] = None

    reason: str = ''

    id: Union[str, int, None] = None

    persistent: bool = False

    def __post_init__(self):
        self.sell_price = Decimal(self.sell_price) if self.sell_price is not None else None
        self.list_price = Decimal(self.list_price) if self.list_price is not None else None

        if self.c_sc_p.channel != AnyC:
            # this is a business rule to check for an LRP before if a request comes in for specific channel
            # Rationale:
            #     The preference is to control rates using LRP for specific channel rather than overrides
            #     if the lrp is not sufficient / producing desirable values then proceed to create overrides
            #     The lack of this check will let people use overrides as a substitute for lrp which
            #     introduces chaos in maintaining prices.
            if self.sell_price is not None:
                lrp_exists = SalePriceLinkedRatePlanRepository.exists_for_rate_request(self)
                if not lrp_exists:
                    raise RuntimeError(f'No LRP exists for SP {self.property} {self.sku} {self.c_sc_p}')

            if self.list_price is not None:
                lrp_exists = ListPriceLinkedRatePlanRepository.exists_for_rate_request(self)
                if not lrp_exists:
                    raise RuntimeError(f'No LRP exists for LP {self.property} {self.sku} {self.c_sc_p}')
