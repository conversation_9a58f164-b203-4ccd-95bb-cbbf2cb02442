from datetime import timedelta

from core.c_sc_p.data_classes.c_sc_p import AnyAnyAny
from core.common.domain_event import DomainEventHandler
from core.common.utils.date import daterange, ist_now, maybe_convert_string_to_datetime
from core.rates.services.get_rates import get_prices
from core.workers import external_rate_sync_exchange, external_rate_sync_rk
from rackrate_service.extensions import rmq


class RateSyncDomainEventHandler(DomainEventHandler):
    sync_window = 400
    async_mode = True

    def run(self):
        if self.event.c_sc_p != AnyAnyAny:
            # only sync for AnyAnyAny for now
            return
        start_date = maybe_convert_string_to_datetime(self.kwargs.get('start_date') or ist_now())
        end_date = self.kwargs.get('end_date') or (start_date + timedelta(days=self.sync_window))
        async_mode = self.kwargs.get('async_mode') or self.async_mode

        dates = daterange(start_date=start_date, end_date=end_date)

        skus = self.event.property.supported_skus

        sku_prices = get_prices(
            property=self.event.property,
            skus=skus,
            c_sc_p=self.event.c_sc_p,
            from_date=start_date,
            to_date=end_date,
            refresh_cache=True
        )

        if async_mode:
            for date in dates:
                sku_date_prices = RateSyncDomainEventHandler.per_date_price_for_skus(sku_prices, skus, date)
                message = {
                    "hotel_code": self.event.property.uid,
                    "stay_date": date.strftime('%Y-%m-%d'),
                    "sku_prices": sku_date_prices
                }
                rmq.publish(
                    exchange_or_queue=external_rate_sync_exchange,
                    routing_key=external_rate_sync_rk,
                    data=message
                )
        else:
            raise RuntimeError("non Async is not supported")

    @classmethod
    def per_date_price_for_skus(cls, sku_prices, skus, date):

        def get_sku_price_payload_from_sku_and_date(sku, date):
            try:
                if sku.is_modular:
                    # ideally it needs only stay skus to convert to room config for cm and old pricing
                    return

                sku_price = [price for price in sku_prices if price.sku == sku][0]
                price = sku_price[date.date()]
                if not price.list_price and not price.sell_price:
                    return

                return {
                    'sale_price': price.sell_price,
                    'list_price': price.list_price,
                    'sku_code': sku.uid,
                    'rate_id': price.uid,
                }
            except (IndexError, KeyError):
                return

        sku_date_prices = [get_sku_price_payload_from_sku_and_date(sku, date) for sku in skus]
        sku_date_prices = [sku_date_price for sku_date_price in sku_date_prices if sku_date_price]  # filter Nones
        return sku_date_prices
