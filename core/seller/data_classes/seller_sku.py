from dataclasses import dataclass
from decimal import Decimal
from typing import Union

from core.property.data_classes.sku import Sku


@dataclass(eq=True, frozen=True)
class SellerSku(Sku):
    seller_id: str = None
    is_active: bool = False
    is_saleable: bool = False

    default_list_price: Union[Decimal, None] = None
    default_sale_price: Union[Decimal, None] = None

    def __post_init__(self):

        if self.default_list_price is not None and not isinstance(self.default_list_price, Decimal):
            self.set_default_list_price(Decimal(self.default_list_price))
        if self.default_sale_price is not None and not isinstance(self.default_sale_price, Decimal):
            self.set_default_sale_price(Decimal(self.default_sale_price))

    def set_default_list_price(self, price):
        # ref: https://stackoverflow.com/a/54119384/6323666 on usage of __setattr__
        object.__setattr__(self, 'default_list_price', price)

    def set_default_sale_price(self, price):
        # ref: https://stackoverflow.com/a/54119384/6323666 on usage of __setattr__
        object.__setattr__(self, 'default_sale_price', price)



