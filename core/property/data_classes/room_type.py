from dataclasses import dataclass

from core.property.data_classes.sku import Sku


@dataclass
class RoomTypeIdAndPax:
    room_type_id: str
    adults: int
    children: int

    @property
    def pax_config(self):
        return f"{self.adults}-{self.children}"

    def __hash__(self):
        return hash(tuple([self.room_type_id, self.pax_config]))


@dataclass
class RoomTypePax(RoomTypeIdAndPax):
    room_type_id: str
    room_type_code: str
    adults: int
    children: int
    sku: Sku
