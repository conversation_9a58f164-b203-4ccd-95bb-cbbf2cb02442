from typing import Iterable

from core.common.api.client.api_request import Json<PERSON><PERSON><PERSON><PERSON>
from core.common.api.client.exceptions import APIClientError
from core.common.api.client.response_handlers import <PERSON>sonResponseHandler
from core.property.data_classes.sku import Sku
from core.property.data_classes.room_type import RoomTypeIdAndPax, RoomTypePax
from rackrate_service.extensions import cache


class _RoomTypeDetailsToSkuResponseHandler(JsonResponseHandler):
    def success(self, data):
        property_sku_mapping = [
            mapping
            for d in data
            for mapping in d["property_sku_room_mapping"]
            if d["property_id"] == self.request.property_code
        ]

        room_type_pax_sku_mapping = {}
        for rtp in self.request.room_type_paxes:
            rtp: RoomTypeIdAndPax

            matched_sku_mapping = None
            for sku_mapping in property_sku_mapping:
                if (
                    sku_mapping["room_code"] == rtp.room_type_id
                    and sku_mapping["room_config"] == rtp.pax_config
                ):
                    matched_sku_mapping = sku_mapping
                    break

            if matched_sku_mapping is None:
                raise APIClientError(
                    f"unable to find a property_sku_mapping in response for {rtp}"
                )

            sku_details = matched_sku_mapping["property_sku"]["sku"]
            room_type_pax_sku_mapping[rtp] = RoomTypePax(
                room_type_id=rtp.room_type_id,
                adults=rtp.adults,
                children=rtp.children,
                room_type_code=matched_sku_mapping["room_type"],
                sku=Sku(
                    uid=sku_details["code"],
                    name=sku_details["name"],
                    category=sku_details["sku_category_code"],
                    is_modular=sku_details["is_modular"],
                ),
            )

        return room_type_pax_sku_mapping


class RoomTypeDetailsToSkuAPIRequest(JsonAPIRequest):
    url = "api/v1/sku/room_config/"
    method = "get"
    response_handler = _RoomTypeDetailsToSkuResponseHandler

    def __init__(self, property_code, room_type_paxes: Iterable[RoomTypeIdAndPax]):
        self.property_code = property_code
        self.room_type_paxes = room_type_paxes

    def data(self):
        return None

    def params(self) -> dict:
        unique_room_codes, unique_configs = set(), set()
        for rtp in self.room_type_paxes:
            unique_room_codes.add(rtp.room_type_id)
            unique_configs.add(rtp.pax_config)

        return {
            "hotels": self.property_code,
            "configs": ",".join(unique_configs),
            "room_types": ",".join(unique_room_codes),
        }
