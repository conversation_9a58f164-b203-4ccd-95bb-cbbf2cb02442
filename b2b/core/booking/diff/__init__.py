from common.diff_and_patch.differ import Differ
from .add_addon import AddAddonDiff
from .add_charge import AddChargeDiff
from .add_payment import AddPaymentDiff
from .add_room import AddRoomDiff
from .cancel_payment import CancelPaymentDiff
from .change_addon import ChangeAddonDiff
from .change_charge_type import ChangeChargeTypeDiff
from .change_payment import ChangePaymentDiff
from .change_payment_type import ChangePaymentTypeDiff
from .change_room import ChangeRoomDiff
from .comments import CommentsDiff
from .gstin import GstinDiff
from .guest_details import GuestDetailsDiff
from .remove_addon import RemoveAddonDiff
from .remove_charge import RemoveChargesDiff
from .remove_room import RemoveRoomDiff
from .soft_block import SoftBlockDiff
from .source import SourceDiff
from .validators import *  # pylint: disable=wildcard-import


class BookingDiffer(Differ):

    def __init__(self, old_booking, new_booking):
        self.old_booking = old_booking
        self.new_booking = new_booking
        super(BookingD<PERSON><PERSON>, self).__init__(lhs=old_booking, rhs=new_booking)

    diffs = [
        # basic
        SoftBlockDiff,
        GstinDiff,
        # SourceDiff, # cannot modify in CRS and need not modify as per product
        GuestDetailsDiff,
        CommentsDiff,

        # rooms
        AddRoomDiff,
        ChangeRoomDiff,
        RemoveRoomDiff,

        # addons
        AddAddonDiff,
        ChangeAddonDiff,
        RemoveAddonDiff,

        # other charges
        ChangeChargeTypeDiff,
        ChangePaymentTypeDiff,
        AddChargeDiff,
        # RemoveChargesDiff,
        ChangePaymentDiff,
        AddPaymentDiff,
        CancelPaymentDiff,
    ]
