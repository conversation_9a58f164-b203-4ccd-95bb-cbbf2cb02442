from common.diff_and_patch.delta import Delta
from common.diff_and_patch.diff_item import DiffItem


class ChangePaymentTypeDiff(DiffItem):

    def deltas(self):
        if self.rhs.payment_type != self.lhs.payment_type:
            return Delta(self,
                         self.lhs.payment_type,
                         self.rhs.payment_type,
                         current_booking=self.lhs,
                         new_payment_type=self.rhs.payment_type,
                         new_gst_details=self.rhs.gst_details,
                         )
        return None
