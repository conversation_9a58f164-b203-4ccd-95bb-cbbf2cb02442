import uuid

from common.data_classes.base import BaseDataClass
from common.data_classes.container import Container


class TaxDetail(BaseDataClass):

    def __init__(self, amount, tax_type):
        self.uid = uuid.uuid4()
        self.amount = amount
        self.tax_type = tax_type

    def __hash__(self):
        return hash(tuple((self.tax_type, self.uid)))

class TaxDetails(Container):
    pass
