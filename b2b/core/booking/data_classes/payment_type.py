# -*- coding: utf-8 -*-
# pylint: disable=invalid-name
from common.data_classes.base import BaseDataClass


class PaymentType(BaseDataClass):

    def __init__(self, uid, name, category, short_name, description, is_credit_type=False):
        self.uid = str(uid)
        self.name = name
        self.category = category
        self.short_name = short_name
        self.description = description
        self.is_credit_type = is_credit_type

    def __eq__(self, other):
        if isinstance(other, self.__class__):
            return hash(self) == hash(other)
        return NotImplemented

    def __hash__(self):
        return hash(tuple([self.uid, self.name, self.category]))

    def __repr__(self):
        return '<{kls} ({u}-{n} category:{c})>'.format(
            kls=self.__class__.__name__,
            u=self.uid,
            n=self.name,
            c=self.category,
        )

    def __str__(self):
        return self.name


CorpCategory = 'corp'
TaCategory = 'ta'

CorporateDirect = PaymentType(
    uid=1,
    name='Corporate (Direct)',
    category=CorpCategory,
    short_name='Pay at Hotel',
    description='The guest has to pay the balance amount at checkout'
)

CorporateBTC = PaymentType(
    uid=2,
    name='Corporate (BTC)',
    category=CorpCategory,
    short_name='BTC',
    description='The guest need not pay for things that have been booked',
    is_credit_type=True,
)

CorporateBTT = PaymentType(
    uid=3,
    name='Corporate (BTT)',
    category=CorpCategory,
    short_name='BTT',
    description='This booking is completely paid for, by Treebo',
)

TaPaidByTa = PaymentType(
    uid=4,
    name='TA - PAID BY TA',
    category=TaCategory,
    short_name='Pay at Hotel',
    description='The guest has to pay the balance amount at checkout',
)

TaPaidByGuest = PaymentType(
    uid=5,
    name='TA - PAID BY GUEST',
    category=TaCategory,
    short_name='Pay at Hotel',
    description='The guest has to pay the balance amount at checkout',
)


class PaymentTypesContainer(object):
    items = [
        CorporateDirect,
        CorporateBTC,
        CorporateBTT,
        TaPaidByTa,
        TaPaidByGuest,
    ]

    def from_name(self, name):
        try:
            return [item for item in self.items if str(item.name).lower() == str(name).lower()][0]
        except IndexError:
            raise ValueError("Invalid Payment Type Name: {n}".format(n=name))

    def from_uid(self, uid):
        try:
            return [item for item in self.items if str(item.uid) == str(uid)][0]
        except IndexError:
            raise ValueError("Invalid Payment Type uid: {n}".format(n=uid))

    def __iter__(self):
        return iter(self.items)


PaymentTypes = PaymentTypesContainer()

CorpPaymentTypes = [pt for pt in PaymentTypes if pt.category == CorpCategory]
TaPaymentTypes = [pt for pt in PaymentTypes if pt.category == TaCategory]
