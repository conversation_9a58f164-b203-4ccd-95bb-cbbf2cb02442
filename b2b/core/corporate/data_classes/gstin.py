# -*- coding: utf-8 -*-
from common.data_classes.address import Address
from common.data_classes.base import BaseDataClass


class Gstin(BaseDataClass):
    def __init__(self, number, addresses, is_sez, has_provided_lut, default=False):
        self.number = number or ''
        self.default = default
        self.is_sez = is_sez
        self.has_provided_lut = has_provided_lut

        _addresses = []
        for address in addresses:
            assert isinstance(address, Address)
            _addresses.append(address)

        self.addresses = frozenset(_addresses)

    def __repr__(self):
        return "<{kls}:({u} default:{d}, {a})>".format(
            kls=self.__class__.__name__,
            u=self.number,
            d=self.default,
            a='\n,'.join(map(str, self.addresses))
        )

    def __str__(self):
        return "{kls}:{u}".format(
            kls=self.__class__.__name__,
            u=self.number,
        )

    def __hash__(self):
        return hash(tuple(sorted(self.__dict__.items())))
