# -*- coding: utf-8 -*-
import warnings

from b2b import constants
from common.data_classes.base import BaseDataClass
from core.booking.data_classes.payment_type import CorpPaymentTypes, TaPaymentTypes
from .legal_entity import LegalEntity


# pylint: disable=too-many-instance-attributes
class Corporate(BaseDataClass):

    def __init__(self, uid, name, agency_type, legal_entities, sales_poc):
        self.uid = uid
        self.name = name
        self.trading_name = name
        self.agency_type = agency_type
        self.legal_entities = set()

        for entity in legal_entities:
            assert isinstance(entity, LegalEntity)
            self.legal_entities.add(entity)

        self._available_payment_types = None

        self.email = None
        self.phone_number = None
        self.btc_enabled = False

        self.sales_poc = sales_poc

    @property
    def treebo_poc(self):
        warnings.warn('treebo_poc is deprecated. Use sales_poc instead.')
        return self.sales_poc

    @property
    def available_payment_types(self):
        if self._available_payment_types is None:
            self._available_payment_types = self._get_available_payment_types()
        return self._available_payment_types

    @available_payment_types.setter
    def available_payment_types(self, value):
        self._available_payment_types = value

    def _get_available_payment_types(self):

        if self.is_ta():
            pay_types = TaPaymentTypes
        else:
            pay_types = CorpPaymentTypes

        if not self.btc_enabled:
            pay_types = [item for item in pay_types if not item.is_credit_type]

        return pay_types

    def is_ta(self):
        return self.agency_type in constants.CorporateAttributes.AgencyTypes.TA_CATEGORIES

    def __repr__(self):
        return "<{kls}:({u} {n})>".format(kls=self.__class__.__name__,
                                          u=self.uid,
                                          n=self.name,
                                          )

    def __str__(self):
        return "{n} ({u})".format(n=self.name,
                                  u=self.uid,
                                  )
