# -*- coding: utf-8 -*-
from common.data_classes.base import BaseDataClass
from .gstin import Gstin


class LegalEntity(BaseDataClass):
    def __init__(self, name, gstins, default=False):
        self.name = name
        self.default = default

        self.crs_id = None

        _gstins = []
        for gstin in gstins:
            assert isinstance(gstin, Gstin)
            _gstins.append(gstin)
        self.gstins = frozenset(_gstins)

    def __repr__(self):
        return "<{kls}:({u} default:{d},{g})>".format(kls=self.__class__.__name__,
                                                      u=self.name,
                                                      d=self.default,
                                                      g='\n,'.join(map(str, self.gstins))
                                                      )

    def __str__(self):
        return "{kls}:{u}".format(kls=self.__class__.__name__,
                                  u=self.name,
                                  )

    def __hash__(self):
        return hash(tuple(sorted(self.__dict__.items())))