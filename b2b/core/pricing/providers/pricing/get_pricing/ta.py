# -*- coding: utf-8 -*-
import sys
from decimal import Decimal

from b2b.domain.services.ta_corporate import TAPricingService
from core.pricing.data_classes import Price
from core.pricing.interfaces.get_pricing import GetPricingInterface



class TAPricingScheme(GetPricingInterface):

    def get_prices(self, room_type=None):
        from core.pricing.providers.pricing.get_pricing import WebPricingScheme

        ta_pricing_svc = TAPricingService(
            legal_entity_id=self.legal_entity.legal_entity_id,
            room_config=self.room_config.to_str(),
            check_in=self.from_date.date().strftime('%Y-%m-%d'),
            check_out=self.to_date.date().strftime('%Y-%m-%d'),
        )

        web_scheme = WebPricingScheme(
            hotel=self.hotel,
            from_date=self.from_date,
            to_date=self.to_date,
            room_config=self.room_config,
            legal_entity=self.legal_entity,
        )

        web_prices = web_scheme.get_prices(room_type)

        prices = {}
        for _room_type in self.hotel.room_types:

            if room_type and room_type != _room_type:
                continue  # populating a certain room type if it is present

            try:
                web_price = web_prices[_room_type].per_day.pre_tax
            except KeyError:
                web_price = Decimal(sys.maxsize)

            web_price = ta_pricing_svc.get_discounted_price(web_price)
            pre_tax = ta_pricing_svc.apply_room_night_discount(web_price)
            price = Price.from_per_day_price(pre_tax, 0, self.from_date, self.to_date)
            prices[_room_type] = price

        return prices
