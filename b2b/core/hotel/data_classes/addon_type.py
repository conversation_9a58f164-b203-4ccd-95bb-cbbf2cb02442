# -*- coding: utf-8 -*-
# pylint: disable=invalid-name
from functools import partial

from b2b import constants
from common.data_classes.base import BaseDataClass
from core.hotel.data_classes.posting_rhythm import (
    AllPostingRhythms,
    EveryNightRhythm,
    EveryNightExceptCheckinRhythm,
    EveryNightIncludeCheckoutRhythm,
    OnlyCheckinRhythm,
    OnlyCheckoutRhythm
)
from core.hotel.data_classes.sku_category import (LunchSku, DinnerSku, LaundrySku,
                                                  CabSku, ConferenceRoomSku)

__all__ = [
    'VegLunch',
    'VegDinner',
    'NonVegLunch',
    'NonVegDinner',
    'Laundry',
    'Cab',
    'ConferenceRoom',
    'ConferenceLunch',
    'ConferenceDinner',

    'AllAddonTypes',
    'get_addon_from_name',
    'get_addon_from_old_name',

    'AddonType'
]


class AddonType(BaseDataClass):

    def __init__(self, uid, name, sku, available_posting_rhythms=None):
        self.uid = uid
        self.name = name
        self.sku = sku

        self.available_posting_rhythms = available_posting_rhythms

    def __repr__(self):
        return "<{kls}: {n} ({u}, category:{c})>".format(kls=self.__class__.__name__,
                                                         n=self.name,
                                                         u=self.uid,
                                                         c=self.sku,
                                                         )

    def __str__(self):
        return self.name


_Meal = partial(AddonType,
                available_posting_rhythms=[
                    EveryNightRhythm,
                    EveryNightExceptCheckinRhythm,
                    EveryNightIncludeCheckoutRhythm,
                    OnlyCheckinRhythm, # for hx compatibility. not in requirements by product.
                    OnlyCheckoutRhythm
                ]
                )

VegLunch = _Meal('V-LNCH', 'Veg Lunch', LunchSku)
VegDinner = _Meal('V-DNNR', 'Veg Dinner', DinnerSku)

NonVegLunch = _Meal('NV-LNCH', 'Non-Veg Lunch', LunchSku)
NonVegDinner = _Meal('NV-DNNR', 'Non-Veg Dinner', DinnerSku)

ConferenceLunch = _Meal('CONF-LNCH', 'Conference Lunch', LunchSku)
ConferenceDinner = _Meal('CONF-DNNR', 'Conference Dinner', DinnerSku)

Laundry = AddonType('LNDRY', 'Laundry', LaundrySku, available_posting_rhythms=AllPostingRhythms)
Cab = AddonType('CAB', 'Cab', CabSku, available_posting_rhythms=AllPostingRhythms)
ConferenceRoom = AddonType('CONF-RM', 'Conference Room', ConferenceRoomSku, available_posting_rhythms=AllPostingRhythms)

VegMeal = VegLunch
NonVegMeal = NonVegLunch
ConferenceMeal = ConferenceLunch

AllAddonTypes = [
    VegLunch,
    VegDinner,
    NonVegLunch,
    NonVegDinner,
    Laundry,
    Cab,
    ConferenceRoom,
    ConferenceLunch,
    ConferenceDinner,
]


def get_addon_from_name(name):
    try:
        return [item for item in AllAddonTypes if item.name == name][0]
    except IndexError:
        raise ValueError('Addon with name: {n} not found/configured'.format(n=name))


_old_name_map = {
    constants.Inclusions.VegMealCharges: VegMeal,
    constants.Inclusions.NonVegMealCharges: NonVegMeal,
    constants.Inclusions.LaundryCharges: Laundry,
    constants.Inclusions.CabCharges: Cab,
    constants.Inclusions.ConferenceRoomCharges: ConferenceRoom,
    constants.Inclusions.ConferenceMealCharges: ConferenceMeal,
}


def get_addon_from_old_name(old_name):
    try:
        return _old_name_map[old_name]
    except KeyError:
        raise ValueError('Addon with name: {n} not found/configured'.format(n=old_name))
