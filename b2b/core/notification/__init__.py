from django.conf import settings

from core.notification.data_classes.base import BaseNotifyItem
from core.notification.interfaces.handlers import NotificationHandlerInterface
from . import providers
from .data_classes import Email, Slack, Sms


class Notify(object):
    email_provider = getattr(settings, 'NOTIFY_EMAIL_BACKEND', providers.email.TreeboEmailNotify)
    sms_provider = getattr(settings, 'NOTIFY_SMS_BACKEND', None)
    slack_provider = getattr(settings, 'NOTIFY_SLACK_BACKEND', providers.slack.DefaultSlackNotify)

    def dispatch(self, item, **kwargs):
        if isinstance(item, Email):
            tracking_id = self.email_provider(item).send(**kwargs)
        elif isinstance(item, Slack):
            tracking_id = self.slack_provider(item).send(**kwargs)
        elif isinstance(item, Sms):
            tracking_id = self.sms_provider(item).send(**kwargs)
        else:
            raise RuntimeError('Unsupported notify item received: {i}'.format(i=repr(item)))
        return tracking_id


notify = Notify().dispatch  # pylint: disable=invalid-name
