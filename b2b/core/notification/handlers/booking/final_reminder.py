from core.notification.data_classes import Email
from core.notification.interfaces.handlers import NotificationHandlerInterface


class BookingFinalReminderHandler(NotificationHandlerInterface):
    email_template = ''
    slack_template = ''
    sms_template = ''

    def __init__(self, booking, **kwargs):
        self.booking = booking
        super(BookingFinalR<PERSON>inderHandler, self).__init__(booking, **kwargs)

    def email(self):
        email = Email(subject='Hi', to_list=['<EMAIL>'])
        email.text = 'hahaha'
        return email

    def sms(self):
        raise NotImplementedError

    def slack(self):
        raise NotImplementedError
