from django.db.models import Q

from b2b.domain.services import AddressService
from b2b.models import Corporate, Hotel, Booking, Account
from dal import autocomplete

from b2b.models import State, User
from b2b.models.corporate import LegalEntity


class CorporatesAutocomplete(autocomplete.Select2QuerySetView):

    def get_queryset(self):
        # Don't forget to filter out results depending on the visitor !
        if not self.request.user.is_authenticated:
            return Corporate.objects.none()

        qs_all = Corporate.objects.filter(active=True)
        if not self.q:
            return qs_all

        query_text = self.q.strip()
        qs = qs_all.filter(trading_name__icontains=query_text)
        if not qs:
            qs = qs_all.filter(corporate_id__iexact=query_text)
            return qs

        return qs


class HotelsAutocomplete(autocomplete.Select2QuerySetView):

    def get_queryset(self):
        # Don't forget to filter out results depending on the visitor !
        if not self.request.user.is_authenticated:
            return Hotel.objects.none()

        qs = Hotel.objects.filter(active=True)
        if self.q:
            qs = qs.filter(name__icontains=self.q)

        return qs


class BookingsAutocomplete(autocomplete.Select2QuerySetView):

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return Booking.objects.none()

        qs = Booking.objects.all()
        if self.q:
            qs = qs.filter(booking_id__icontains=self.q)

        return qs


class UsersAutocomplete(autocomplete.Select2QuerySetView):

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return User.objects.none()

        qs = User.objects.filter(is_active=True)
        if self.q:
            query_text = self.q.strip()
            qs = qs.filter(Q(first_name__icontains=query_text) | Q(email__icontains=query_text))

        return qs


class LegalEntityAutocomplete(autocomplete.Select2QuerySetView):

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return LegalEntity.objects.none()

        qs = LegalEntity.objects.filter(active=True)
        corporate = self.forwarded.get('corporate')
        if corporate:
            qs = qs.filter(corporatelegalentity__corporate=corporate)
        if self.q:
            query_text = self.q.strip()
            qs = qs.filter(Q(name__icontains=query_text) | Q(legal_entity_id__iexact=query_text)
                           | Q(gstin__gstin__iexact=query_text))

        return qs


class StateAutocomplete(autocomplete.Select2QuerySetView):

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return State.objects.none()

        qs = State.objects.all()
        if self.q:
            qs = qs.filter(Q(name__icontains=self.q))

        return qs


class AddressAutocomplete(autocomplete.Select2QuerySetView):

    def get_queryset(self):
        qs = AddressService.get_all_addresses()
        if self.q:
            qs = qs.filter(Q(building__icontains=self.q) | Q(street__icontains=self.q) | Q(city__icontains=self.q))
        return qs


class AccountAutocomplete(autocomplete.Select2QuerySetView):

    def get_queryset(self):
        if not self.request.user.is_authenticated:
            return Account.objects.none()

        qs = Account.objects.all()
        corporate = self.forwarded.get('corporate')
        if corporate:
            qs = qs.filter(corporate=corporate)
        if self.q:
            qs = qs.filter(Q(corporate__trading_name__icontains=self.q) | Q(treebo_poc__first_name__icontains=self.q)
                           | Q(primary_admin__first_name__icontains=self.q) | Q(account_id=self.q))

        return qs
