import logging

import sentry_sdk
from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from b2b.domain.services import CorporateService
from b2b.domain.services.legal_entity import LegalEntityService
from b2b.dto.legal_entity_details import UserDTO
from b2b.models import AccountLegalEntity, AccountAdmin
from loyalty.exceptions import LoyaltyDisabled
from loyalty.services.loyalty_service import LoyaltyService


logger = logging.getLogger(__name__)

NOT_APPLICABLE = "N/A"


@method_decorator(login_required(login_url='/profiles/login/'), name='get')
class LegalEntityDetails(APIView):

    @staticmethod
    def _make_user_details(poc):
        if not poc:
            return NOT_APPLICABLE
        return dict(name=poc['first_name']+" "+poc['last_name'], email=poc['email'],
                    phone=poc['phone_number'])

    def _get_user_details(self, primus_admin, legal_entity_details):
        return dict(primus_admin=self._make_user_details(primus_admin.data) if primus_admin else NOT_APPLICABLE,
                    primary_admin=self._make_user_details(legal_entity_details.get('primary_admin')),
                    primary_finance_admin=self._make_user_details(legal_entity_details.get('primary_finance_admin')),
                    treebo_poc=self._make_user_details(legal_entity_details.get('treebo_poc')),
                    inside_sales_poc=self._make_user_details(legal_entity_details.get('inside_sales_poc')),
                    hotel_finance_poc=self._make_user_details(legal_entity_details.get('hotel_finance_poc')))

    @staticmethod
    def _get_basic_legal_details(legal_entity_details):
        return dict(name=legal_entity_details['legal_name'],
                    gstin=legal_entity_details.get('gstin_number', NOT_APPLICABLE),
                    address=dict(legal_entity_details['address']),
                    sector=legal_entity_details.get('sector'),
                    agency_type=legal_entity_details.get('agency_type'),
                    account_id=legal_entity_details.get('account_id'))

    @staticmethod
    def _get_pricing_and_payment_details(legal_entity_details):
        return dict(discount=legal_entity_details['slab_type'].split('@')[1] if legal_entity_details.get('slab_type') else "0",
                    web_pricing=legal_entity_details.get('show_web_prices'),
                    ta_pricing_discount=legal_entity_details.get('ta_pricing_discount', '0'),
                    payment_source=legal_entity_details.get('payment_source'),
                    btc_enabled=legal_entity_details.get('btc_enabled')
                    )

    @staticmethod
    def _get_loyalty_and_other_details(legal_entity_details, loyalty_points):
        return dict(tac_commission=legal_entity_details.get('tac_commission', '0'),
                    loyalty_enabled=legal_entity_details['loyalty_enabled'],
                    loyalty_level=legal_entity_details.get('loyalty_level', NOT_APPLICABLE),
                    loyalty_points=(loyalty_points['rupee'] if legal_entity_details['loyalty_enabled']
                                    else NOT_APPLICABLE))

    @staticmethod
    def _get_corporate_details(corporate_details, legal_entity_details, sibling_legal_entity_count):
        return dict(name=corporate_details.get('trading_name'),
                    corporate_id=corporate_details['corporate_id'],
                    pan_number=legal_entity_details.get('pan_number', NOT_APPLICABLE),
                    tan_number=legal_entity_details.get('tan_number', NOT_APPLICABLE),
                    sibling_legal_entity_count=sibling_legal_entity_count)

    def _make_response(self, legal_entity_details, sibling_legal_entity_count, primus_admin, corporate_details, user):
        loyalty_points = {'rupee': '0'}
        legal_entity_details['loyalty_enabled'] = True
        try:
            loyalty_points = LoyaltyService(legal_entity_details['legal_entity_id']).get_points(legal_entity_details['legal_entity_id'])
        except LoyaltyDisabled:
            legal_entity_details['loyalty_enabled'] = False
        except Exception:
            # Bypass exceptions such as wallet does not exist, error in fetching loyal points
            # etc. We will show 0 loyalty points in such cases.
            pass

        return dict(
            basic_details=self._get_basic_legal_details(legal_entity_details),
            point_of_contacts=self._get_user_details(primus_admin, legal_entity_details),
            pricing_and_payment=self._get_pricing_and_payment_details(legal_entity_details),
            loyalty_and_other_details=self._get_loyalty_and_other_details(legal_entity_details, loyalty_points),
            corporate_details=self._get_corporate_details(corporate_details, legal_entity_details, sibling_legal_entity_count),
            is_user_poc=user.email == legal_entity_details['treebo_poc'].get("email")
        )

    def get(self, request, corporate_id, legal_entity_id):
        try:
            legal_entity_details = CorporateService.fetch_legal_entities(legal_entity_ids=legal_entity_id)
            response = {}
            if legal_entity_details and legal_entity_details.context['legal_entity_id_to_corporate_map'].values():
                corporate_details = list(legal_entity_details.context['legal_entity_id_to_corporate_map'].values())[0]

                if corporate_details['corporate_id'] != corporate_id:
                    return Response(data={'error': f'{corporate_id} is not a valid corporate_id'},
                                    status=status.HTTP_400_BAD_REQUEST)

                sibling_legal_entity_count = LegalEntityService.get_sibling_legal_entities_by_id(legal_entity_id).count()
                account_admin = AccountAdmin.objects.select_related('auth_user').filter(
                    account__accountlegalentity__legal_entity__legal_entity_id=legal_entity_id).order_by('-modified_at').first()
                primus_admin = None
                if account_admin:
                    primus_admin = UserDTO(account_admin.auth_user)
                response = self._make_response(dict(legal_entity_details.data[0]), sibling_legal_entity_count,
                                               primus_admin, corporate_details, request.user)
        except Exception as e:
            error = f'Error while fetching legal entity details. error: {str(e)}'
            logger.info(error)
            sentry_sdk.capture_exception(e)
            return Response(data={'error': error}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return render(request, 'legal_entity_details.html', {'data': response})
