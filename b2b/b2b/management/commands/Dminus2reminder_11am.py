import datetime
import os
import logging
from datetime import timedelta

from django.conf import settings
from django.core.management.base import BaseCommand

from b2b.models.booking import Booking
from b2b.domain.services.notifications import NotificationService as B2BNotificationService
from b2b import constants

JSON_DIR = os.path.join(settings.BASE_DIR, 'b2b/management/commands/')
TWO_DAYS = timedelta(days=2)


class Command(BaseCommand):
    help = 'Add the given corporates as corp admin'

    def handle(self, *args, **options):

        current_date = datetime.datetime.now().date()
        logger = logging.getLogger(self.__class__.__name__)
        failed_bookings = []
        for booking in Booking.objects.filter(status='Confirmed'):
            # check if all guest details are present or not
            if booking.is_temporary_booking():
                # 11 AM
                if booking.check_in - current_date == TWO_DAYS and booking.channel == constants.Booking.Channel.ATHENA:
                    logger.debug('Email Trigger at %s on D-2: %s %s', datetime.datetime.now().isoformat(),
                                 booking.booking_id, booking.check_in)
                    try:
                        B2BNotificationService.booking_reminder(booking.booking_id)
                    except Exception as e:
                        logging.error(e, exc_info=True)
                        failed_bookings.append(booking.booking_id)

        if len(failed_bookings) > 0:
            logger.info(
                'Dminus2reminder_11am failed while sending the reminder mail for bookings with booking_ids %s',
                ','.join(failed_bookings))
