import csv
import logging
from typing import List
from django.core.management import BaseCommand
from django.db import transaction
from b2b.admin.utils.make_id import make_id
from b2b.constants import B2BEventType, IntegrationEventStatus
from b2b.csv_actions.utils.common_utils import generate_event_id
from b2b.dto.integration_event import IntegrationEventDTO
from b2b.models import Account, AccountAdmin, AccountLegalEntity, LegalEntity, Corporate
from b2b.models.integration_event import IntegrationEvent
from b2b.utils.legal_entity_utils import create_legal_entity_dto

logger = logging.getLogger(__name__)


class AccountSchema(object):
    def __init__(
            self,
            row_id,
            account_id,
            corp_id,
            treebo_poc_id,
            primary_admin_id,
            inside_sales_poc_id,
            primary_finance_admin_id,
            is_active: bool = False,
    ):
        self.row_id = row_id
        self.account_id = account_id
        self.corp_id = corp_id
        self.treebo_poc_id = treebo_poc_id
        self.primary_admin_id = primary_admin_id
        self.inside_sales_poc_id = inside_sales_poc_id
        self.primary_finance_admin_id = primary_finance_admin_id
        self.is_active = is_active
        self.error = None

    def to_dict(self):
        return dict(
            row_id=self.row_id,
            account_id=self.account_id,
            corp_id=self.corp_id,
            inside_sales_poc_id=self.inside_sales_poc_id,
            primary_admin_id=self.primary_admin_id,
            primary_finance_admin_id=self.primary_finance_admin_id,
            treebo_poc_id=self.treebo_poc_id,
            is_active=self.is_active,
            error=self.error,
        )


class AccountLegalEntitySchema(object):
    def __init__(self, row_id, legal_entity_id, is_default: bool = False):
        self.row_id = row_id
        self.legal_entity_id = legal_entity_id
        self.is_default = is_default
        self.error = None

    def to_dict(self):
        return dict(
            row_id=self.row_id,
            legal_entity_id=self.legal_entity_id,
            is_default=self.is_default,
            error=self.error,
        )


class AccountAdminSchema(object):
    def __init__(
            self,
            row_id,
            admin_user_id,
            loyalty_enabled: bool,
            default_recommended_hotels,
    ):
        self.row_id = row_id
        self.admin_user_id = admin_user_id
        self.loyalty_enabled = loyalty_enabled
        self.default_recommended_hotels = default_recommended_hotels
        self.error = None

    def to_dict(self):
        return dict(
            row_id=self.row_id,
            admin_user_id=self.admin_user_id,
            loyalty_enabled=self.loyalty_enabled,
            default_recommended_hotels=self.default_recommended_hotels,
            error=self.error,
        )


class AccountAggregate(object):
    def __init__(
            self,
            account: AccountSchema,
            account_admin_ids=None,
            legal_entity_ids=None,
            account_admins: List[AccountAdminSchema] = None,
            account_legal_entities: List[AccountLegalEntitySchema] = None
    ):
        self.account = account
        self.account_admin_ids = account_admin_ids if account_admin_ids else []
        self.legal_entity_ids = legal_entity_ids if legal_entity_ids else []
        self.account_admins = account_admins if account_admins else []
        self.account_legal_entities = account_legal_entities if account_legal_entities else []

    def to_dict(self):
        return dict(
            account=self.account.to_dict(),
            account_admin_ids=self.account_admin_ids,
            legal_entity_ids=self.legal_entity_ids,
            account_admins=[admin.to_dict() for admin in self.account_admins],
            account_legal_entities=[legal_entity.to_dict() for legal_entity in self.account_legal_entities],
        )


class Command(BaseCommand):
    def handle(self, *args, **options):
        row_id_to_account_aggregate, data_count = self.create_account_aggregate()
        with transaction.atomic():
            self.create_accounts(row_id_to_account_aggregate)
            self.create_account_legal_entities(row_id_to_account_aggregate)
            self.create_account_admins(row_id_to_account_aggregate)
            self.save_ingestion_logs(row_id_to_account_aggregate, data_count)

    @staticmethod
    def invalid_row_id(row_id):
        if not row_id:
            return True
        if row_id == '#N/A':
            return True
        return False

    @staticmethod
    def create_unique_account_id(preexisting_account_ids, corporate_name):
        account_id = f"acc-{make_id(corporate_name)}"
        while account_id in preexisting_account_ids:
            account_id = f"acc-{make_id(corporate_name)}"
        preexisting_account_ids.add(account_id)
        return account_id

    def create_accounts(self, row_id_to_account_aggregate):
        preexisting_account_ids = set()
        for row_id, account_aggregate in row_id_to_account_aggregate.items():
            account_dto = account_aggregate.account
            try:
                corporate = Corporate.objects.get(id=account_dto.corp_id)
                account_id = self.create_unique_account_id(preexisting_account_ids, corporate.trading_name)
                account = Account(
                    account_id=account_id,
                    treebo_poc_id=account_dto.treebo_poc_id,
                    primary_admin_id=account_dto.primary_admin_id,
                    corporate=corporate,
                    inside_sales_poc_id=account_dto.inside_sales_poc_id,
                    primary_finance_admin_id=account_dto.primary_finance_admin_id,
                    is_active=account_dto.is_active,
                    updated_by_id=26626,
                )
                account.save()
                account_aggregate.account.account_id = account_id
            except Exception as e:
                logger.info(f" Failed to create Account for Account ID {account_dto.row_id}. Error: {str(e)}")
                account_aggregate.account.error = str(e)
            row_id_to_account_aggregate[row_id] = account_aggregate

    def create_account_legal_entities(self, row_id_to_account_aggregate):
        for row_id, account_aggregate in row_id_to_account_aggregate.items():
            account = account_aggregate.account
            for account_legal_entity in account_aggregate.account_legal_entities:
                if not account.account_id:
                    account_legal_entity.error = account.error
                    continue
                try:
                    legal_entity = LegalEntity.objects.get(
                        legal_entity_id=account_legal_entity.legal_entity_id, active=True)
                    AccountLegalEntity.objects.create(
                        account_id=account.account_id,
                        legal_entity=legal_entity,
                        is_default=account_legal_entity.is_default,
                    )
                    self.create_integration_event(
                        legal_entity=legal_entity,
                        account=legal_entity.account if account_legal_entity.is_default else None,
                    )
                except Exception as e:
                    logger.info(
                        f" Failed to create Account LE mapping for Account ID {account.row_id}, "
                        f"LE ID: {account_legal_entity.legal_entity_id} Error: {str(e)}"
                    )
                    account_legal_entity.error = str(e)
            row_id_to_account_aggregate[row_id] = account_aggregate

    @staticmethod
    def create_account_admins(row_id_to_account_aggregate):
        for row_id, account_aggregate in row_id_to_account_aggregate.items():
            account = account_aggregate.account
            for account_admin in account_aggregate.account_admins:
                if not account.account_id:
                    account_admin.error = account.error
                    continue
                try:
                    AccountAdmin.objects.create(
                        account_id=account.account_id,
                        auth_user_id=account_admin.admin_user_id,
                        default_recommended_hotels=account_admin.default_recommended_hotels,
                        loyalty_enabled=account_admin.loyalty_enabled,
                    )
                except Exception as e:
                    logger.info(
                        f" Failed to create Account Admin mapping for Account ID {account.row_id}, "
                        f" Primus Admin ID: {account_admin.admin_user_id}. Error: {str(e)}"
                    )
                    account_admin.error = str(e)
            row_id_to_account_aggregate[row_id] = account_aggregate

    @staticmethod
    def create_integration_event(legal_entity, account=None):
        event_dto = IntegrationEventDTO(
            data=dict(
                event_id=generate_event_id(),
                event_type=B2BEventType.LEGAL_ENTITY_UPDATED,
                body=create_legal_entity_dto(legal_entity, account),
                status=IntegrationEventStatus.UNPUBLISHED,
                generated_by_id=26626,
            )
        )
        if event_dto.is_valid():
            IntegrationEvent.objects.create(**event_dto.data)

    @staticmethod
    def save_ingestion_logs(row_id_to_account_aggregate, data_count):
        logs_csv_file = 'ingestion_logs.csv'
        summary_csv_file = 'ingestion_summary.csv'

        logs_field_names = [
            'ExternalAccountID', 'InternalAccountID', 'AdminsSupposedToBeMapped',
            'AdminsWhichGotMapped', 'AdminsWhichWereNotMapped', 'LegalEntitiesWhichWereNotMapped',
            'LegalEntitiesSupposedToBeMapped', 'LegalEntitiesWhichGotMapped', 'DidAccountGetCreated',
            'ErrorInAccountCreation', 'AccountData', 'AccountAdminsData', 'AccountLegalEntitiesData'
        ]
        summary_field_names = [
            'TotalAccountsSupposedToBeMapped', 'TotalLegalEntitiesSupposedToBeMapped', 'TotalAdminsSupposedToBeMapped'
        ]
        row = {
            'TotalAccountsSupposedToBeMapped': data_count['accounts'],
            'TotalLegalEntitiesSupposedToBeMapped': data_count['account_legal_entities'],
            'TotalAdminsSupposedToBeMapped': data_count['account_admins'],
        }

        with open(summary_csv_file, mode='w', newline='') as csv_file:
            csv_writer = csv.DictWriter(csv_file, fieldnames=summary_field_names)
            csv_writer.writeheader()
            csv_writer.writerow(row)

        with open(logs_csv_file, mode='w', newline='') as csv_file:
            csv_writer = csv.DictWriter(csv_file, fieldnames=logs_field_names)
            csv_writer.writeheader()
            for row_id, account_aggregate in row_id_to_account_aggregate.items():
                account = account_aggregate.account
                account_admins = account_aggregate.account_admins
                account_legal_entities = account_aggregate.account_legal_entities
                admins_which_got_mapped, legal_entities_which_got_mapped = [], []

                for account_admin in account_admins:
                    if account_admin.error:
                        continue
                    admins_which_got_mapped.append(account_admin.admin_user_id)

                for account_legal_entity in account_legal_entities:
                    if account_legal_entity.error:
                        continue
                    legal_entities_which_got_mapped.append(account_legal_entity.legal_entity_id)

                row = {
                    'ExternalAccountID': account.row_id,
                    'InternalAccountID': account.account_id,
                    'AdminsSupposedToBeMapped': account_aggregate.account_admin_ids,
                    'AdminsWhichGotMapped': admins_which_got_mapped,
                    'AdminsWhichWereNotMapped': set(account_aggregate.account_admin_ids) - set(
                        admins_which_got_mapped),
                    'LegalEntitiesSupposedToBeMapped': account_aggregate.legal_entity_ids,
                    'LegalEntitiesWhichGotMapped': legal_entities_which_got_mapped,
                    'LegalEntitiesWhichWereNotMapped': set(account_aggregate.legal_entity_ids) - set(
                        legal_entities_which_got_mapped),
                    'DidAccountGetCreated': True if account.account_id and not account.error else False,
                    'ErrorInAccountCreation': account.error,
                    'AccountData': account.to_dict(),
                    'AccountAdminsData': [admin.to_dict() for admin in account_admins],
                    'AccountLegalEntitiesData': [legal_entity.to_dict() for legal_entity in account_legal_entities],
                }

                csv_writer.writerow(row)

        logger.info(f'Ingestion logs has been written to {summary_csv_file} and {logs_csv_file}')

    def create_account_aggregate(self):
        b2b_accounts_data = 'b2b_accounts.csv'
        b2b_account_admins_data = 'b2b_account_admins.csv'
        b2b_account_legal_entity_data = 'b2b_account_legal_entity.csv'

        row_id_to_account_aggregate = dict()
        total_accounts, total_account_admins, total_account_legal_entities = 0, 0, 0
        with open(b2b_accounts_data, mode='r', newline='') as csv_file:
            csv_reader = csv.DictReader(csv_file)
            for row in csv_reader:
                if row_id_to_account_aggregate.get(row['ID']):
                    continue
                if self.invalid_row_id(row['ID']):
                    continue
                row_id_to_account_aggregate[row['ID']] = AccountAggregate(
                    account=AccountSchema(
                        row_id=row['ID'],
                        account_id=None,
                        treebo_poc_id=row['treebo_poc_id'],
                        primary_admin_id=row['primary_admin_id'],
                        corp_id=row['Corp ID'],
                        inside_sales_poc_id=row['inside_sales_poc_id'],
                        primary_finance_admin_id=row['primary_finance_admin_id'],
                        is_active=False,
                    )
                )
                total_accounts += 1
        with open(b2b_account_admins_data, mode='r', newline='') as csv_file:
            csv_reader = csv.DictReader(csv_file)
            for row in csv_reader:
                account_aggregate = row_id_to_account_aggregate.get(row['Account ID'])
                if not account_aggregate:
                    continue
                if row['admin_user_id'] in account_aggregate.account_admin_ids:
                    continue
                account_aggregate.account_admin_ids.append(row['admin_user_id'])
                account_aggregate.account_admins.append(
                    AccountAdminSchema(
                        row_id=row['Account ID'],
                        admin_user_id=row['admin_user_id'],
                        loyalty_enabled=eval(row['loyalty_enabled'].capitalize()),
                        default_recommended_hotels=row['default_recommended_hotels'],
                    )
                )
                row_id_to_account_aggregate[row['Account ID']] = account_aggregate
                total_account_admins += 1

        with open(b2b_account_legal_entity_data, mode='r', newline='') as csv_file:
            csv_reader = csv.DictReader(csv_file)
            for row in csv_reader:
                account_aggregate = row_id_to_account_aggregate.get(row['Account ID'])
                if not account_aggregate:
                    continue
                if row['legal_entity_id'] in account_aggregate.legal_entity_ids:
                    continue
                account_aggregate.legal_entity_ids.append(row['legal_entity_id'])
                account_aggregate.account_legal_entities.append(
                    AccountLegalEntitySchema(
                        row_id=row['Account ID'],
                        legal_entity_id=row['legal_entity_id'],
                        is_default=eval(row['is_default'].capitalize()),
                    )
                )
                if row['is_default'] == "TRUE":
                    account_aggregate.account.is_active = True
                row_id_to_account_aggregate[row['Account ID']] = account_aggregate
                total_account_legal_entities += 1

        data_count = dict(
            accounts=total_accounts,
            account_admins=total_account_admins,
            account_legal_entities=total_account_legal_entities,
        )
        return row_id_to_account_aggregate, data_count
