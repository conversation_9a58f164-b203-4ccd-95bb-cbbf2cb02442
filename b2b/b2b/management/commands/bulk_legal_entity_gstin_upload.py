# -*- coding: utf-8 -*-
import csv
import logging

from django.core.management import BaseCommand
from django.db import transaction

from b2b.domain.services import AddressService
from b2b.dto import AddressDTO
from b2b.models import Corporate, Address, State
from b2b.models.corporate import LegalEntity, Gstin, CorporateLegalEntity, LegalEntityGstinAddress

logger = logging.getLogger('b2b')


class Command(BaseCommand):
    help = 'Bulk update of Legal Entity and GSTIN for all Corporates'

    def add_arguments(self, parser):
        parser.add_argument('csv_file',
                            type=str,
                            default='',
                            help='CSV file with legal entities, gstins and addresses for each corporate')

        parser.add_argument('error_csv_file',
                            type=str,
                            default='',
                            help='CSV file containing failures.')

    def handle(self, *args, **options):
        try:

            with open(options['error_csv_file'], 'wb') as ef:

                print("STEP 1: Ingesting DATA for CSV File")
                # Step 1: Ingest Data from CSV file
                default_exists = set()
                with open(options['csv_file'], 'rb') as f:
                    csv_data = csv.DictReader(f)
                    headers = csv_data.fieldnames
                    headers.append('reason')
                    writer = csv.DictWriter(ef, fieldnames=headers)
                    writer.writeheader()
                    for row in csv_data:
                        try:
                            with transaction.atomic():
                                corp = Corporate.objects.get(corporate_id__iexact=row['corporate_id'].strip())

                                _legal_entity, created = LegalEntity.objects.get_or_create(name=row['legal_name'].strip())

                                _corp_legal_entity, created = CorporateLegalEntity.objects.get_or_create(
                                    corporate=corp,
                                    legal_entity=_legal_entity)
                                if created:
                                    _corp_legal_entity.default = not (corp in default_exists)
                                    _corp_legal_entity.save()

                                # TODO: We take first legal entity as default. Change below if change in default selection
                                default_exists.add(corp)

                                if row['gstin'] and row['gstin'].upper().strip() != 'NA':
                                    _gstin, created = Gstin.objects.get_or_create(gstin=row['gstin'].strip(),
                                                                                  legal_entity=_legal_entity)

                                    if created:
                                        _gstin.default = row['default_gstin'].upper().strip() == 'YES'
                                        _gstin.save()

                                    _state = State.objects.get(
                                        name=self._sanitize_data(row=row, key='address_state').title())

                                    address_dto = AddressDTO(data=dict(
                                        building=self._sanitize_data(row=row, key='address_building'),
                                        street=self._sanitize_data(row=row, key='address_street'),
                                        locality=self._sanitize_data(row=row, key='address_locality'),
                                        landmark=self._sanitize_data(row=row, key='address_landmark'),
                                        city=self._sanitize_data(row=row, key='address_city'),
                                        state=_state.name,
                                        pincode=self._sanitize_data(row=row, key='address_zip_code'),
                                        country=self._sanitize_data(row=row, key='address_country').title()
                                    ))
                                    address_dto.fill_required_fields_with_dummy_values()

                                    _address, created = AddressService.get_or_create_address(address_dto=address_dto)

                                    _gstin_address, created = LegalEntityGstinAddress.objects.get_or_create(gstin=_gstin,
                                                                                                            address=_address)
                                    if created:
                                        _gstin_address.default = row['default_address'].upper().strip() == 'YES'
                                        _gstin_address.save()
                                else:
                                    logger.warn(
                                        'Incorrect GSTIN for legal entity {le} associated with corporate {c}'.format(
                                            le=_legal_entity.name, c=corp.corporate_id
                                        ))

                        except Corporate.DoesNotExist:
                            logger.info('Bulk update unable to find {corp} : {line}'.format(corp=row['corporate_id'],
                                                                                            line=csv_data.line_num))
                            row['reason'] = 'Corporate does not exists'
                            writer.writerow(rowdict=row)
                        except Exception as e:
                            logger.info(
                                'Bulk update failed for {corp}:{line} due to {e}'.format(corp=row['corporate_id'],
                                                                                         line=csv_data.line_num,
                                                                                         e=str(e)))
                            row['reason'] = str(e)
                            writer.writerow(rowdict=row)
                            print(('Bulk update failed for {corp}:{line} due to {e}'.format(corp=row['corporate_id'],
                                                                                           line=csv_data.line_num,
                                                                                           e=str(e))))

            print("STEP 2: Check if any legal entity not assigned default GSTIN")
            # Step 2: Check if any legal entity not assigned default GSTIN
            with transaction.atomic():
                for legal_entity in LegalEntity.objects.all():
                    gstins = legal_entity.gstin_set.all()
                    if gstins.count() > 0:
                        gstin = gstins.filter(default=True)
                        if not gstin:
                            print(("Default GSTIN not found for legal entity {}".format(legal_entity.name)))
                            gstin = gstins[0]
                            gstin.default = True
                            gstin.save()
                            print(("Default GSTIN for legal entity {} set to {}".format(legal_entity.name, gstin.gstin)))

            print("STEP 3: Check if any GSTIN not assigned default Address")
            # Step 3: Check if any GSTIN not assigned default Address
            with transaction.atomic():
                for gstin in Gstin.objects.all():
                    addresses = gstin.legalentitygstinaddress_set.all()
                    if addresses.count() > 0:
                        address = addresses.filter(default=True)
                        if not address:
                            print(("Default Address not found for GSTIN {}".format(gstin.gstin)))
                            address = addresses[0]
                            address.default = True
                            address.save()
                            print(("Default Address for GSTIN {} set to {}".format(gstin.gstin, address.address)))

        except KeyError:
            print("The CSV file doesn't specify all fields required or is specified incorrectly")
        except Exception as e:
            logger.info('Bulk upload of Legal Entity GSTIN failed due to {exc}'.format(exc=e))
            print((str(e)))

    def _sanitize_data(self, row, key):
        return row[key].decode('utf8').encode('ascii', 'ignore').replace('\n', ' ').replace('\r', ' ').strip()
