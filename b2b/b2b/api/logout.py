from rest_framework.exceptions import MethodNotAllowed
from .api import Tree<PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import logout
from .auth_classes import CsrfExemptSessionAuthentication


class LogoutAPI(TreeboAPI):

    authentication_classes = (CsrfExemptSessionAuthentication,)

    def get(self, request, format=None):
        raise MethodNotAllowed("GET")

    def post(self, request, format=None):
        """
        Logout a user
        :type request: django.http.request.HttpRequest
        :param request:
        :param format:
        """
        try:
            logout(request)
            response = Response({"success": {
                'msg': "user logged out"
                }
            },
            status=status.HTTP_200_OK)

            response.set_cookie('authenticated_user', 'false')
            return response
        except Exception as e:
            return Response({
                "error": {
                    "msg": "Unable to logout user",
                    "code": "1001"
                }
            }, status=status.HTTP_400_BAD_REQUEST)
