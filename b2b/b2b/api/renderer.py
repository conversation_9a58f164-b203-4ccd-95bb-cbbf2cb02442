import logging
from treebo_commons.utils.dateutils import current_datetime
from rest_framework.renderers import JSONRenderer
logger = logging.getLogger(__name__)

class TreeboJSONRenderer(JSONRenderer):
    def render(self, data, accepted_media_type=None, renderer_context=None):
        start_time = current_datetime()
        status_code = renderer_context['response'].status_code
        status_code = status_code if status_code else 200
        if 200 <= status_code < 300:
            data = {'status': status_code, 'data': data}
        elif 400 <= status_code < 500:
            if 'errors' in data:
                data = {'status': status_code, 'errors': data['errors']}
            else:
                data = {'status': status_code, 'errors': {'validation_errors': data}}
        else:
            if 'errors' in data:
                data = {'status': status_code, 'errors': data['errors']}
            else:
                data = {'status': status_code, 'errors': data}
        response = super(TreeboJSONRenderer, self).render(data, accepted_media_type,
                                                          renderer_context)
        logger.debug("JSON rendering --- %s milliseconds ---" % (self.elapsed(start_time)))

        return response

    def elapsed(self, start_time):
        return (current_datetime() - start_time).total_seconds() * 1000
