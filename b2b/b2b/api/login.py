from rest_framework.authentication import BasicAuthentication
from rest_framework.exceptions import MethodNotAllowed
from .api import TreeboAPI
from django.contrib.auth import login, authenticate
from rest_framework.response import Response
import urllib.request, urllib.parse, urllib.error
import json
from rest_framework import status
from b2b.dto import LoginRequestD<PERSON>
from b2b.models import AccountAdmin, AccountLegalEntity
from b2b.api.api_error_response import APIErrorResponse


class LoginAPI(TreeboAPI):

    # authentication_classes = (CsrfExemptSessionAuthentication,)
    authentication_classes = (BasicAuthentication, )

    def get(self, request):
        raise MethodNotAllowed("GET")

    def post(self, request):
        """
        Login a user
        :type request: django.http.request.HttpRequest
        :param request:
        :param format:
        ---
        parameters:
        - name: username
          description: Username
          required: true
          type: string
          paramType: form
        - name: password
          description: Password
          required: true
          type: string
          paramType: form
        consumes:
        - application/json
        produces:
        - application/json
        """

        lr_dto = LoginRequestDTO(data=request.data)
        if not lr_dto.is_valid():
            return Response(lr_dto.errors, status.HTTP_400_BAD_REQUEST)
        lr_data = lr_dto.data
        user = authenticate(username=lr_data['username'], password=lr_data['password'])
        if user and user.is_active:
            login(request, user)

            account_legal_entities = AccountLegalEntity.objects.select_related('legal_entity').filter(
                    account__accountadmin__auth_user=user, is_default=True).distinct()
            legalentities = []
            for account_legal_entity in account_legal_entities:
                legal_entity = account_legal_entity.legal_entity
                legalentities.append({
                    'name': legal_entity.name,
                    'legal_entity_id': legal_entity.legal_entity_id,
                    'city': legal_entity.address.city,
                    'locality': legal_entity.address.locality,
                    'is_local_ta': legal_entity.is_local_ta(),
                    'is_ta': legal_entity.is_ta(),
                    'web_price_enabled': legal_entity.is_web_prices_enabled(),
                    'relationship_manager': {
                        'name': getattr(legal_entity.account_treebo_poc, 'name', None),
                        'email': getattr(legal_entity.account_treebo_poc, 'email', None),
                        'phone': getattr(legal_entity.account_treebo_poc, 'phone_number', None),

                    },
                    'is_posttax_enabled': legal_entity.posttax_enabled
                })

            response = Response({
                'status': 'success',
                'msg': 'User logged in successfully',
                'userId': user.id,
                'email': user.email,
                'phone': user.phone_number,
                'name': user.first_name,
                'last_name': user.last_name,
                'isSignUp': False,
                'legal_entities': legalentities
            })
            authenticated_user_cookie_map = {
                "userId": user.id,
                "email": user.email,
                "phone": user.phone_number,
                "name": user.first_name,
            }

            response.set_cookie('authenticated_user', urllib.parse.quote(json.dumps(authenticated_user_cookie_map)))
            return response
        else:
            return APIErrorResponse.invalid_login_credentials(lr_data['username'])
