from b2b.api.api import TreeboAPI
from rest_framework.response import Response
from rest_framework import status, exceptions
from .api_responses import DashboardAPIResponses
from .exception import CorporateAuthorizationError, GroupAuthorizationError


class DashboardAPI(TreeboAPI):

    def handle_exception(self, exc):
        if isinstance(exc, CorporateAuthorizationError):
            return DashboardAPIResponses.corporate_authorization_failed(message=str(exc))

        if isinstance(exc, GroupAuthorizationError):
            return DashboardAPIResponses.unauthorized_group()

        if isinstance(exc, exceptions.AuthenticationFailed):
            return DashboardAPIResponses.anonymous_user()

        else:
            error_response = {"errors": [
                {"code": 5002,
                 "message": "Unexpected exception ({err})".format(err=exc.__class__.__name__),
                 "developer_message": str(exc)}]}

            response = Response(data=error_response, status=500)
            return response
