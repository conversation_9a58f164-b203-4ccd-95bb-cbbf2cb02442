import logging

from rest_framework import status
from rest_framework.response import Response

from b2b.api import CsrfExemptSessionAuthentication
from b2b.api.api import TreeboAP<PERSON>
from b2b.api.api_error_response import APIErrorResponse
from b2b.auth.services import OTPService
from b2b.dto.otp_login import OtpLoginDTO
from b2b.models import User, CorpAdmin

logger = logging.getLogger(__name__)


class OTPLoginAPI(TreeboAPI):
    authentication_classes = [CsrfExemptSessionAuthentication, ]
    otp_service = OTPService()

    def post(self, request):
        """
        :param request: phone number
        :return: OTP is sent to the requested phone number and success response is sent back.
        """
        try:
            otp_login_dto = OtpLoginDTO(data=request.data)
            if not otp_login_dto.is_valid():
                return Response(otp_login_dto.errors, status.HTTP_400_BAD_REQUEST)
            if not otp_login_dto.is_valid():
                logger.info("otp login invalid request %s ", request.data)
                raise Exception("Invalid Request")

            # Check if user is a valid primus user
            request_data = request.data
            user = User.objects.get(phone_number=request_data['phone_number'])
            corporate_admins = CorpAdmin.objects.filter(auth_user=user)
            if not corporate_admins:
                raise Exception("Primus account not found")

            self.otp_service.send_otp(request_data['phone_number'], 'phone_number')

            response = Response({
                'status': 'success',
                'msg': 'OTP sent successfully',
            })

            return response
        except User.DoesNotExist as e:
            logger.info("internal error otp login exception %s - %s" % (request_data, str(e)))
            response = APIErrorResponse.invalid_login_credentials(request_data['phone_number'], message="Invalid user")
        except Exception as e:
            logger.info("internal error otp login exception %s - %s" % (request_data, str(e)))
            response = APIErrorResponse.invalid_login_credentials(request_data['phone_number'], message=str(e))
        return response
