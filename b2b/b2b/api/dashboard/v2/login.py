import json
import logging
import urllib.request, urllib.parse, urllib.error
from datetime import datetime, timedelta

import pytz
from django.conf import settings
from rest_framework import status
from rest_framework.response import Response

from b2b.api import CsrfExemptSessionAuthentication
from b2b.api.api import TreeboAP<PERSON>
from b2b.api.api_error_response import APIErrorResponse
from b2b.auth.treebo.auth_client import TreeboAuthClient
from b2b.dto import LoginRequestDTO
from b2b.models import User, AccountAdmin, AccountLegalEntity
from common.auth.token_helper import TokenHelper
from common.exceptions import CorporateDataUnavailable, AuthException

logger = logging.getLogger(__name__)


class LoginAPI(TreeboAPI):
    authentication_classes = (CsrfExemptSessionAuthentication,)
    token_helper = TokenHelper()

    def post(self, request):
        """
        Login a user
        :type request: django.http.request.HttpRequest

        """

        lr_dto = LoginRequestDTO(data=request.data)
        if not lr_dto.is_valid():
            return Response(lr_dto.errors, status.HTTP_400_BAD_REQUEST)
        lr_data = lr_dto.data
        try:
            # Fetch user
            user = User.objects.get(email=lr_data['username'])

            account_legal_entities = AccountLegalEntity.objects.select_related('legal_entity').filter(
                account__accountadmin__auth_user=user, is_default=True).distinct()
            if not account_legal_entities:
                raise CorporateDataUnavailable(corporate_id=user.id,
                                               error="No Active AccountAdmin found for this user")

            # Authenticate User
            user_data = TreeboAuthClient().login(email=lr_data['username'], password=lr_data['password'])

            # Save access token for refreshing it when it expires
            self.token_helper.save_access_token(token_info=user_data, user=user)

            if user and user.is_active and user_data:
                legal_entities = []
                for account_legal_entity in account_legal_entities:
                    legal_entity = account_legal_entity.legal_entity
                    legal_entity_gstin = legal_entity.gstin()
                    legal_entities.append({
                        'name': legal_entity.name,
                        'legal_entity_id': legal_entity.legal_entity_id,
                        'city': legal_entity.address.city,
                        'locality': legal_entity.address.locality,
                        'is_local_ta': legal_entity.is_local_ta(),
                        'is_ta': legal_entity.is_ta(),
                        'web_price_enabled': legal_entity.is_web_prices_enabled(),
                        'gstin': legal_entity_gstin.gstin if legal_entity_gstin else None,
                        'state': legal_entity.address.state,
                        'relationship_manager': {
                            'name': getattr(legal_entity.account_treebo_poc, 'name', None),
                            'email': getattr(legal_entity.account_treebo_poc, 'email', None),
                            'phone': getattr(legal_entity.account_treebo_poc, 'phone_number', None),
                        },
                        'corporate_id': legal_entity.corporate.corporate_id
                    })

                response = Response({
                    'status': 'success',
                    'msg': 'User logged in successfully',
                    'userId': user.id,
                    'email': user.email,
                    'phone': user.phone_number,
                    'name': user.first_name,
                    'last_name': user.last_name,
                    'isSignUp': False,
                    'legal_entities': legal_entities

                })

                # Instead of django auth login, we will just set token attribute of request.user
                # Treebo Auth Middleware will later set it in response header and Cookies
                token = user_data['access_token']
                user.token = token
                request.user = user

                authenticated_user_cookie_map = {
                    "userId": user.id,
                    "email": user.email,
                    "phone": user.phone_number,
                    "name": user.first_name,
                }

                local_tz = pytz.timezone(settings.TIME_ZONE)
                local_date = datetime.utcnow().replace(tzinfo=pytz.utc).astimezone(local_tz)

                response.set_cookie(key='authenticated_user',
                                    value=urllib.parse.quote(json.dumps(authenticated_user_cookie_map)),
                                    expires=local_date + timedelta(seconds=settings.DASHBOARD_SESSION_COOKIE_AGE))
                return response
        except User.DoesNotExist:
            logger.info("Unable to log user: {user}. {error}".format(user=lr_data['username'],
                                                                     error="User not found"))
            return APIErrorResponse.invalid_login_credentials(
                user=None,
                message="Please enter your email ID registered with Treebo for Business.")
        except CorporateDataUnavailable:
            logger.info("Unable to log user: {user}. {error}".format(user=lr_data['username'],
                                                                     error="Primus User not found"))
            return APIErrorResponse.invalid_login_credentials(
                user=None,
                message="Please enter your email ID registered with Treebo for Business.")
        except AuthException as err:
            logger.info("Login failed for user {email} - {message}".format(email=user.email,
                                                                           message=str(err)))
            return APIErrorResponse.invalid_login_credentials(user=None,
                                                              message="Incorrect Username or Password")
        except Exception as err:
            logger.info("Login failed for user {email} - {message}".format(email=user.email,
                                                                           message=str(err)))
            return APIErrorResponse.invalid_login_credentials(
                user=None,
                message="We're sorry that your request failed. Please refresh the page and try again")
