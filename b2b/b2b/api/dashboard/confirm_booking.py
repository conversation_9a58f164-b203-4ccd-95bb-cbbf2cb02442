import json
import logging
import traceback

from rest_framework import status
from rest_framework.response import Response

from b2b import constants
from b2b.domain.services import BookingService, NotificationService
from b2b.dto.booking_request import ConfirmBookingDTO
from rest_framework.authentication import BasicAuthentication

from b2b.dto.verify_payment import VerifyPaymentRequestDTO
from b2b.orchestrator.booking_payments import update_payment_in_crs
from b2b.payments.services.payments import PaymentService
from common.utils.slack import send_slack_notif
from .api import DashboardAPI
from .api_responses import DashboardAPIResponses
from .authorization import DashboardAuthorization
from b2b.api import CsrfExemptSessionAuthentication

logger = logging.getLogger(__name__)


class ConfirmBookingAPI(DashboardAPI):
    permission_classes = (DashboardAuthorization,)
    authentication_classes = (BasicAuthentication, CsrfExemptSessionAuthentication)

    def post(self, request):
        logger.debug('Confirm Booking API called with payload: {0}'.format(json.dumps(request.data)))
        confirm_booking_request_dto = ConfirmBookingDTO(data=request.data)
        if not confirm_booking_request_dto.is_valid():
            return Response(confirm_booking_request_dto.errors, status.HTTP_400_BAD_REQUEST)

        try:
            logger.debug('Payment Verification initiated')
            verify_payment_request_dto = VerifyPaymentRequestDTO(data=request.data.get('payment'))
            PaymentService.verify_payment(verify_payment_request_dto)
            logger.debug('Payment Verified')
            BookingService.confirm_temp_booking(confirm_booking_request_dto)
            update_payment_in_crs(confirm_booking_request_dto.data.get('payment').get('order_id'),
                                  confirm_booking_request_dto.data.get('booking_id'))
            NotificationService.booking_confirmed(confirm_booking_request_dto.data.get('booking_id'),
                                                  force_notify=True, pay_now=True)
            return Response(data={"response": "success"}, status=status.HTTP_200_OK)

        except Exception as e:
            err_msg = "Error Confirming Booking"
            send_slack_notif(constants.Slack.B2B_PAYNOW_BOOKING_ALERTS, message=str(traceback.format_exc()),
                             raise_exception=False)
            logger.info(err_msg)
            return DashboardAPIResponses.error_response(err_msg, status.HTTP_500_INTERNAL_SERVER_ERROR)
