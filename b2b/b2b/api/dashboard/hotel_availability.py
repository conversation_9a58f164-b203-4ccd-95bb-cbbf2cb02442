import json
import logging
from rest_framework.response import Response
from b2b.api.dashboard.api import DashboardAPI
from b2b.domain.services import AvailabiltyService
from b2b.dto import CheckAvailabilityDTO
from b2b.models.hotel import Hotel

logger = logging.getLogger(__name__)


class HotelAvailability(DashboardAPI):
    """
    Calls the Availability Backend for this hotel
    """

    def get(self, request, external_hotel_id):
        request_data = request.query_params

        logger.info(json.dumps(request_data))

        hotel_info = Hotel.objects.filter(external_id=external_hotel_id)[:1]
        if hotel_info and hotel_info.count():
            hotel_info = hotel_info[0]
            hotel_id = hotel_info.hotel_id
            get_availability_request_dto = CheckAvailabilityDTO(data=dict(hotel_ids=hotel_info.hotel_id,
                                                                          checkin=request_data['checkin'],
                                                                          checkout=request_data['checkout'],
                                                                          roomconfig=request_data['roomconfig']
                                                                          ))

            availability_data = AvailabiltyService.get_availability(get_availability_request_dto)['data']

            if not availability_data or (hotel_id in availability_data and not availability_data[hotel_id]):
                data = {
                    "available": False,
                    "availability": {}
                }
            else:
                if hotel_id in availability_data:
                    data = {
                        "available": True,
                        "availability": availability_data[hotel_id]
                    }
            logger.info("Availability Data : %s", json.dumps(availability_data))
        else:
            msg = "Hotel with external_id : {ext_id} doesn't exist".format(ext_id=external_hotel_id)
            logger.info(msg)
            raise Exception(msg)

        logger.info("Response: %s", json.dumps(data))
        return Response(data)
