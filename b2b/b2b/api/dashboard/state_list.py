import logging
from rest_framework import status
from rest_framework.authentication import BasicAuthentication
from rest_framework.response import Response

from b2b.api import CsrfExemptSessionAuthentication
from b2b.api.api_error_response import APIErrorResponse
from b2b.api.dashboard.api import DashboardAP<PERSON>
from b2b.api.dashboard.authorization import DashboardAuthorization
from b2b.models import State

logger = logging.getLogger(__name__)

class StateListAPI(DashboardAPI):
    permission_classes = (DashboardAuthorization,)
    authentication_classes = (CsrfExemptSessionAuthentication,)

    def get(self, request):
        """
        Will return the list of all states of India
        :param request:
        :return: ['UP', 'Bihar']
        """
        try:
            states = State.objects.all().values_list('name', flat=True)
            return Response(data=states, status=status.HTTP_200_OK)
        except Exception as e:
            logger.info(
                'Error occurred while getting states. Error {error}'.format(error=str(e)))
            return APIErrorResponse.error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
