import logging

from rest_framework import status
from rest_framework.authentication import BasicAuthentication
from rest_framework.response import Response

from .api import DashboardAPI
from .api_responses import DashboardAPIResponses
from .authorization import DashboardAuthorization
from b2b.api import CsrfExemptSessionAuthentication
from b2b.domain.services.exceptions import InvalidDTO
from b2b.dto.verify_payment import VerifyPaymentRequestDTO
from b2b.orchestrator.booking_payments import finalise_payment

logger = logging.getLogger(__name__)


class PaymentVerificationAPI(DashboardAPI):
    permission_classes = (DashboardAuthorization,)

    authentication_classes = (BasicAuthentication, CsrfExemptSessionAuthentication)

    def get(self, request):
        return DashboardAPIResponses.error_response(message="GET method is not allowed",
                                                    resp_code=status.HTTP_405_METHOD_NOT_ALLOWED)

    def post(self, request):
        verify_payment_request_dto = VerifyPaymentRequestDTO(data=request.data, context=request)
        if not verify_payment_request_dto.is_valid():
            raise InvalidDTO(verify_payment_request_dto)

        try:
            finalise_payment(verify_payment_request_dto)
            return Response(data={"response": "success"}, status=status.HTTP_200_OK)

        except Exception as e:
            err_msg = "Error verifying payments for user {u}: {err}".format(err=str(e),
                                                                            u=request.user.email)
            logger.info(err_msg)
            return DashboardAPIResponses.error_response(err_msg, status.HTTP_500_INTERNAL_SERVER_ERROR)
