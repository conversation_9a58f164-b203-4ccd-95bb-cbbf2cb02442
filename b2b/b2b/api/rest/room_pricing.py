import json
import logging
import traceback

from django.conf import settings
from rest_framework import status
from rest_framework.response import Response

from b2b.api.api import TreeboAPI
from b2b.api.api_error_response import APIErrorResponse
from b2b.domain.pricing.exceptions import RoomPricesNotFound
from b2b.domain.services import PricingService
from b2b.dto import RoomPriceDTO
from b2b.dto import RoomPriceRequestDTO


class RoomPricingAPI(TreeboAPI):
    def get(self, request):
        logger = logging.getLogger(self.__class__.__name__)

        # dto = RoomPriceRequestDTO(data=request.data)
        dto = RoomPriceRequestDTO(data=request.query_params)
        if not dto.is_valid():
            return Response(dto.errors, status.HTTP_400_BAD_REQUEST)

        # todo: we need to convert from external to internal ids for hotel and corporate

        try:
            logger.debug('Received room price request: %s', json.dumps(dto.data))
            pricing_svc = PricingService(dto.data['legal_entity_id'])

            room_price_dto = pricing_svc.get_room_price(room_price_request_dto=dto)
            assert type(room_price_dto) is RoomPriceDTO
            if not room_price_dto.is_valid():
                return Response(room_price_dto.errors, status.HTTP_500_INTERNAL_SERVER_ERROR)

        except RoomPricesNotFound as rpnf:
            logger.info(rpnf.message)
            return APIErrorResponse.price_not_found(price_type='room')

        except Exception as e:
            traceback.print_exc()

            msg = "Error finding room prices: %s", str(e)
            logger.info(msg)
            return APIErrorResponse.error_response(message=msg, resp_code=status.HTTP_404_NOT_FOUND)

        # get rid of the audit log before sending off the response
        price_data = room_price_dto.data
        del price_data['audit_log']
        del price_data['slab_type']
        response = Response(data=price_data,
                            status=status.HTTP_200_OK)

        return response
