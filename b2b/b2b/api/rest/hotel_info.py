from rest_framework import status
from rest_framework.response import Response

from b2b.api.api import TreeboAPI
from b2b.dto.hotel_info import HotelInfoDTO
from b2b.models.hotel import Hotel


class HotelInfoAPI(TreeboAPI):
    def get(self, request, *args, **kwargs):
        try:
            hotel = Hotel.objects.get(hotelogix_id=kwargs.get('hotelogix_id'))
            serializer = HotelInfoDTO(hotel, many=False)
            return Response(data=serializer.data, status=status.HTTP_200_OK)
        except Hotel.DoesNotExist as e:
            return Response(str(e), status=status.HTTP_404_NOT_FOUND)
