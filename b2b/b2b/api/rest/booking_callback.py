import json
import logging
import traceback

from django.conf import settings
from rest_framework import status
from rest_framework.response import Response

from b2b import constants
from b2b.api.api import TreeboAPI
from b2b.domain.services import BookingService, NotificationService
from b2b.dto.booking_request import ConfirmBookingDTO

from b2b.models import Booking
from b2b.orchestrator.booking_payments import update_payment_in_crs
from b2b.payments.integrations import get_payment_backend
from b2b.payments.models import Payment
from b2b.payments.services.payments import PaymentService
from common.utils.slack import send_slack_notif

logger = logging.getLogger(__name__)


class BookingCallBackAPI(TreeboAPI):

    def post(self, request):
        logger.debug('Confirm Booking API called with payload: {0}'.format(json.dumps(request.data)))
        confirm_booking_request_dto = ConfirmBookingDTO(data=request.data)
        if not confirm_booking_request_dto.is_valid():
            return Response(confirm_booking_request_dto.errors, status.HTTP_400_BAD_REQUEST)
        booking_id = confirm_booking_request_dto.data.get('booking_id')

        try:
            booking = Booking.objects.filter(booking_id=booking_id).first()
            if booking and booking.status != constants.Booking.CONFIRMED:
                payments_on_booking = PaymentService.get_all_payments(booking_id)
                pg_order_ids_of_b2b_payments = [payment.order_id for payment in payments_on_booking]
                payment_backend = get_payment_backend(settings.PAYMENT_CONF['backend'])
                captured_payment = payment_backend.get_captured_payment(pg_order_ids=pg_order_ids_of_b2b_payments)
                if captured_payment:
                    payments = Payment.objects.filter(order_id=captured_payment.get('order_id'))
                    payments.update(status=constants.Payments.VERIFIED,
                                    payment_id=captured_payment.get('payment_service_id'))
                    BookingService.confirm_temp_booking(confirm_booking_request_dto)
                    update_payment_in_crs(captured_payment.get('order_id'), booking_id)
                    NotificationService.booking_confirmed(booking_id, force_notify=True, pay_now=True)
                else:
                    BookingService.cancel_booking(booking_id)
            else:
                BookingService.cancel_booking(booking_id)
            return Response(data={"response": "success"}, status=status.HTTP_200_OK)

        except Exception as e:
            BookingService.cancel_booking(booking_id)
            err_msg = "Error Confirming Booking {0}".format(booking_id)
            send_slack_notif(constants.Slack.B2B_PAYNOW_BOOKING_ALERTS,
                             message=str(traceback.format_exc()) + str(booking_id), raise_exception=False)
            logger.exception(err_msg)
            raise e
