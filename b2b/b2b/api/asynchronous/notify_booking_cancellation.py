# -*- coding: utf-8 -*-
import logging
import traceback

from b2b.api.asynchronous.api_responses import AsyncAPIResponse
from b2b.api.api import TreeboAP<PERSON>
from b2b.api.request_dtos import GetBookingRequest
from b2b.domain.services import NotificationService

logger = logging.getLogger(__name__)

class NotifyBookingCancellationAPI(TreeboAPI):
    """
    used for notifying booking cancellation

    """

    def post(self, request):
        """
        given a booking-id we send the required confirmation notifications
        :param request: GetBookingRequest DTO
        """

        dto = GetBookingRequest(data=request.data)
        if not dto.is_valid():
            errors = ["{k}: {v}".format(k=k, v=';'.join(dto.errors[k])) for k in dto.errors]
            return AsyncAPIResponse.unable_to_notify_booking_cancellation(booking_id='<missing>',
                                                                          errors=errors)
        else:
            booking_id = dto.data['booking_id']

        try:
            NotificationService.booking_cancelled(booking_id, force_notify=True)

        except Exception as e:
            traceback.print_exc()
            logger.info("error occurred while notifying cancellation for booking {bid} : {err}".format(err=str(e),
                                                                                                        bid=booking_id))
            return AsyncAPIResponse.unable_to_notify_booking_cancellation(booking_id=booking_id,
                                                                          errors=[str(e)])

        response = AsyncAPIResponse.success_response(
            message="Notified cancellation booking: {bid}".format(bid=booking_id))
        return response
