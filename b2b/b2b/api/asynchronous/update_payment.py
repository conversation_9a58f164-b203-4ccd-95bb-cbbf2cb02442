# -*- coding: utf-8 -*-
import logging

from b2b.api.api import TreeboAP<PERSON>
from b2b.api.asynchronous.api_responses import AsyncAPIResponse
from b2b.domain.services.exceptions import ResourceUnavailable, UnableToUpdateMapping, InvalidDTO
from b2b.api.dtos.request.update_payment import UpdatePaymentDTO
from b2b.orchestrator.booking_payments import update_payment_in_crs

logger = logging.getLogger(__name__)

class UpdatePaymentAPI(TreeboAPI):
    def post(self, request):
        try:
            dto = UpdatePaymentDTO(data=request.data)
            if not dto.is_valid():
                raise InvalidDTO(dto)
            booking_id = dto.data['booking_id']
            order_id = dto.data['order_id']
        except InvalidDTO as e:
            return AsyncAPIResponse.invalid_request(str(e))

        try:
            logger.info(
                "Updating payment in Hx with order id {oid} and booking id {bid}".format(oid=order_id, bid=booking_id))
            update_payment_in_crs(order_id, booking_id)
            return AsyncAPIResponse.success_response(
                message="Payment updated successfully for booking id {bid}.".format(bid=booking_id))

        except UnableToUpdateMapping as e:
            return AsyncAPIResponse.unable_to_update_mapping(errors=[str(e)])

        except ResourceUnavailable as e:
            return AsyncAPIResponse.resource_unavailable(errors=[str(e)])

        except Exception as e:
            logger.info("Failed to update payments for {booking}:{err}".format(err=str(e), booking=booking_id))
            return AsyncAPIResponse.failed_to_update_mapping(errors=[str(e)])
