# -*- coding: utf-8 -*-
from rest_framework.response import Response


class APIResponse(object):
    """
    base class for generating success and error responses
    helps keep the format consistent
    """

    @classmethod
    def _prep_error_response(cls, error_code, messages, response_code):
        """
        prepares a Response object in the standard format
        :param errors: error codes and messages explaining/accompanying the error
        :return: Response object
        """
        error_response = {
            "errors": [{
                "code": error_code,
                "messages": messages if type(messages) is list else [messages]
            }]
        }

        response = Response(data=error_response, status=response_code)
        return response

    @classmethod
    def _prep_success_response(cls, message, response_code):
        """
        vanilla Response object on success
        :param message: what message to embed as part of the response
        :param response_code: what http response code to use
        :return: Response object
        """
        response = {
            "message": message
        }

        response = Response(data=response, status=response_code)
        return response
