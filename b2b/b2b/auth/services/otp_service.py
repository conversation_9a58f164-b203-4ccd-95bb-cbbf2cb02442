import logging
from datetime import datetime, timedelta
from random import randint

import pytz
from django.conf import settings

from b2b.auth.treebo.auth_client import TreeboAuthClient
from b2b.auth.treebo.user_registration import UserRegistration
from b2b.domain.services.notifications import NotificationService
from b2b.models import EmailOTP
from b2b.models import User
from common.auth.token_helper import TokenHelper
from django.core.exceptions import ValidationError
logger = logging.getLogger(__name__)


class OTPService(object):
    auth_client = TreeboAuthClient()
    token_helper = TokenHelper()
    user_registration = UserRegistration()

    def get_request_type(self, otp_login_data):
        username = otp_login_data['phone_number']

        if username.isdigit() and len(username) == 10:
            return "phone_number"
        elif '@' in username:
            return 'email'
        else:
            raise ValidationError('Invalid username')

    def send_otp(self, otp_login_dto, request_type):
        if request_type == 'phone_number':
            self.send_otp_mobile(otp_login_dto)
        else:
            self.send_otp_email(otp_login_dto)

    def verify_otp(self, otp_verify_dto, request_type):
        phone_number = otp_verify_dto['phone_number']
        if request_type == 'phone_number':
            token_info, user = self.verify_otp_mobile(otp_verify_dto)
        else:
            token_info, user = self.verify_otp_email(otp_verify_dto)
        logger.debug("OTP verfiy success for  %s , token_info %s ", phone_number, token_info)

        self.token_helper.save_access_token(token_info, user)
        return user, token_info

    def generate_otp(self, email):
        generated_otp_list = EmailOTP.objects.filter(email=email)
        otp = None
        for email_otp in generated_otp_list:
            if email_otp.created_at <= (datetime.utcnow()-timedelta(
                    minutes=settings.EMAIL_OTP_VALIDITY_MINUTES)).replace(tzinfo=pytz.UTC):
                email_otp.delete()
            else:
                otp = email_otp.otp

        if not otp:
            otp = str(randint(1000, 9999))
            EmailOTP.objects.create(email=email, otp=otp, created_at=datetime.utcnow())
        return otp

    def send_otp_mobile(self, otp_login_dto):
        phone_number = otp_login_dto['phone_number']
        logger.debug("OTP sending otp to %s ", phone_number)
        self.auth_client.login_via_otp(phone_number)

    def verify_otp_mobile(self, otp_verify_dto):
        phone_number = otp_verify_dto['phone_number']
        verification_code = otp_verify_dto['verification_code']
        logger.debug("OTP verfiy for %s , code %s ", phone_number, verification_code)
        token_info = self.auth_client.login_via_otp_verify(phone_number, verification_code)
        user = self.user_registration.register_user_with_token(token_info['access_token'])
        return token_info, user

    def send_otp_email(self, otp_login_dto):
        email = otp_login_dto['phone_number']
        logger.debug("Sending OTP to %s ", email)

        otp = self.generate_otp(email.lower())
        NotificationService.corporate_login_otp(email, otp)

    def verify_otp_email(self, otp_verify_dto):
        email = otp_verify_dto['phone_number']
        verification_code = otp_verify_dto['verification_code']
        logger.debug("OTP verfiy for %s , code %s ", email, verification_code)

        try:
            email_otp = EmailOTP.objects.get(email=email.lower(), otp=verification_code)
            if email_otp.created_at >= (datetime.utcnow() - timedelta(
                    minutes=settings.EMAIL_OTP_VALIDITY_MINUTES)).replace(tzinfo=pytz.UTC):
                user = User.objects.get(email=email, is_active=True)
                token_info = TreeboAuthClient().auto_login(email)
        except EmailOTP.DoesNotExist:
            raise Exception("Invalid OTP")
        except User.DoesNotExist:
            raise Exception("Invalid User")
        except Exception as e:
            logging.exception("Failed to Verify Email OTP {message}".format(message=str(e)))
            raise

        email_otp.delete() # Invalidate OTP if verification is successful

        return token_info, user
