import calendar
import logging
from collections import OrderedDict
from time import strftime

import htmlmin
from django.conf import settings
from django.template import loader

from b2b import constants
from b2b.consumer.notif import get_notification_backend
from b2b.domain.services.epos import EposService
from b2b.domain.utils import count_room_nights
from b2b.dto import EmailDTO
from b2b.constants import SIKKIM_STATE_CODE

# pylint: disable=too-many-locals, no-self-use, deprecated-lambda
from core.booking.helpers import get_applicable_rate_plan

logger = logging.getLogger(__name__)


class BookingConfirmationHandler(object):
    def get_confirmation_context(self, booking, rooms_list, payment_link_url):
        try:
            prices = booking.get_price_details()
            logger.info('Amount received for voucher {amt}'.format(amt=prices))

        except Exception as e:
            msg = "Error in fetching booking price for building context while sending '{type}'" \
                  " confirmation email for booking '{bid}': {err}"
            logger.info(msg.format(type=type, bid=booking.booking_id, err=str(e)))
            raise

        try:
            from b2b.domain.services import NotificationService, BookingService
            legal_entity = BookingService.get_legal_entity_for_mail(booking=booking)

            billing_address = legal_entity.get('address')
            hotel_gstin = None
            should_show_tourist_fee_policy = False
            try:
                from b2b.models import HotelAttribute
                from b2b.constants import HotelAttributes as HotelAttributesConstants, DEFAULT_CHECKOUT_TIME

                hotel_gstin = HotelAttribute.objects.get(key=HotelAttributesConstants.GSTIN_CODE,
                                                         hotel_id=booking.hotel_id).value
            except HotelAttribute.DoesNotExist:
                logger.info("Error fetching gstin for: {hid}".format(hid=booking.hotel_id))
            hotel_state_code = booking.hotel.get_attr(constants.HotelAttributes.LEGAL_STATE_CODE)
            if hotel_state_code == SIKKIM_STATE_CODE:
                should_show_tourist_fee_policy = True
            rate_plan_applied = get_applicable_rate_plan(booking=booking)

            context = dict(
                corporate=legal_entity.get('name'),
                gstin=legal_entity.get('gstin'),
                gstin_address=billing_address,
                corporate_street=",".join((billing_address['building'], \
                                           billing_address['street'], billing_address['locality'])),
                corporate_city=billing_address['city'],
                corporate_state=billing_address['state'],
                corporate_zipcode=billing_address['pincode'],
                hotel_name=booking.hotel.name,
                hotel_gstin=hotel_gstin,
                address=booking.hotel.address(),
                phone_number=booking.hotel.phone_number,
                image_url='',
                booking_code=booking.booking_id,
                voucher_code=BookingConfirmationHandler.epos_voucher_code(booking),
                booking_date=str(booking.created_at.date()),
                checkin=str(strftime('%d %b, %Y ', booking.check_in.timetuple())),
                checkin_day=calendar.day_name[booking.check_in.weekday()],
                checkout=str(strftime('%d %b, %Y ', booking.check_out.timetuple())),
                checkout_day=calendar.day_name[booking.check_out.weekday()],
                days=count_room_nights(str(booking.check_in), str(booking.check_out)),
                bookingStatus="Confirmed",
                pah=0 if booking.is_btc_booking() else 1,
                booking_type=booking.booking_type,
                # Need to ask what exactly to put
                total_guests=len(booking.get_all_guests()),
                guest_without_detail=booking.guest_without_details(),
                expiry_date=str(strftime('%d %b, %Y ', booking.check_in.timetuple())),
                rooms_list=rooms_list,
                prices=OrderedDict(prices),
                inclusion_details=NotificationService().get_inclusion_context(booking),
                totalCost=BookingService.total_booking_amount(booking=booking),
                is_subchannnel_ta=booking.is_subchannnel_ta(),
                payment_link_url=payment_link_url,
                checkout_time=DEFAULT_CHECKOUT_TIME,
                room_night_cost=booking.get_room_charge_per_night(),
                rate_plan_applied=rate_plan_applied,
                should_show_tourist_fee_policy=should_show_tourist_fee_policy,
            )

        except Exception as e:
            msg = "Error building context while sending '{type}' confirmation email for booking '{bid}': {err}"
            logger.info(msg.format(type=type, bid=booking.booking_id, err=str(e)))
            raise

        return context

    def get_confimation_email_dto(self, booking, context):
        booker_email = booking.created_by.email if booking.created_by is not None else '<EMAIL>'
        final_guest_emails = booking.get_not_empty_emails()
        final_guest_emails = [email for email in final_guest_emails if email]

        if not booking.booker_company.is_ta():
            final_guest_emails = final_guest_emails if final_guest_emails else []
        elif not final_guest_emails:
            final_guest_emails = final_guest_emails if final_guest_emails else []
        else:
            final_guest_emails = []
        # in case this is not prod system, prepend the subject with environment name
        email_subject = "Booking Confirmation - {hotel} - {bid}".format(hotel=booking.hotel.name,
                                                                        bid=booking.booking_id)
        # build the email dto
        email_tmpl_config = settings.NOTIFICATION_CONF['email_templates']['booking']['confirmation']
        email_html = loader.render_to_string(template_name=email_tmpl_config['template'], context=context)

        # in case it's a non-prod environment, let the subject say which
        # pylint: disable=len-as-condition
        if settings.ENV_NAME != 'prod' or len(settings.ENV_NAME.strip()) == 0:
            email_subject = "[{e}] {s}".format(e=settings.ENV_NAME, s=email_subject)

        from b2b.domain.services import NotificationService
        try:
            htmlmin.minify(email_html, remove_empty_space=True)
        except IndexError as e:
            raise Exception('Context has changed but template has not been changed due to {exc}'.format(exc=e))
        treebo_poc_email = []
        if booking.booker_company.account_treebo_poc:
            treebo_poc_email = [booking.booker_company.account_treebo_poc.email]
        email = EmailDTO(data=dict(
            sender=email_tmpl_config['sender'],
            to_list=email_tmpl_config['to'] + [booker_email] + final_guest_emails,
            cc_list=email_tmpl_config['cc'] + treebo_poc_email + booking.additional_notification_emails(),
            subject=email_subject,
            content=email_html,
            sender_name=email_tmpl_config['sender_name'],
            is_urgent=NotificationService.is_priority(constants.NotificationPriority.BOOKING_CONFIRMATION)
        ))

        return email

    def send_confimation_email(self, booking, email_dto):
        notif_backend = get_notification_backend(settings.NOTIFICATION_CONF['backend'])

        try:
            # the backend needs to return a tracking id on success
            tracking_id = notif_backend.send_email(email_dto)

        except Exception as e:
            # todo: we need to catch specific exceptions here
            logger.info("Error notifying booking confirmation for %s: %s", booking.booking_id, str(e))
            raise

        return tracking_id

    def create_booking_attribute(self, booking, booking_attr, tracking_id):
        booking.bookingattributes_set.create(key=booking_attr,
                                             value=tracking_id)
        msg = "saved '{type}' email tracking id '{tid}' to booking-attribs for booking '{bid}'"
        logger.info(msg.format(type='booking_confirmation', tid=tracking_id, bid=booking.booking_id))

    def notify(self, booking, payment_link_url=None):

        rooms_list = []

        def build_guest_name_list(record):
            return [guest for guest in record.additional_guests() if guest.name not in constants.Booking.STUB_NAMES]

        for record in booking.bookedroom_set.all():
            all_guests = [record.guest] + record.additional_guests()

            guest_without_names = [guest for guest in all_guests if guest.name in constants.Booking.STUB_NAMES]

            rooms_list.append(dict(room_type=record.room_type,
                                   adults=record.guest_count(),
                                   primary_guest=record.guest,
                                   additional_guests=[guest.name for guest in build_guest_name_list(record)],
                                   guest_without_names=len(guest_without_names),
                                   guest_with_names=list(set(all_guests) - set(guest_without_names))))

        context = self.get_confirmation_context(booking, rooms_list, payment_link_url)
        email_dto = self.get_confimation_email_dto(booking, context)
        tracking_id = self.send_confimation_email(booking, email_dto)
        self.create_booking_attribute(booking, constants.BookingAttributes.BOOKING_CONFIRM_MAIL,
                                      tracking_id)

    @classmethod
    def epos_voucher_code(cls, booking):
        return EposService.get_voucher_code(booking.booking_id) if booking.is_epos_required() else None
