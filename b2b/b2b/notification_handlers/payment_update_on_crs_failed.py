import copy
import logging

from django.conf import settings
from django.template import loader

from b2b.dto import EmailDTO
from .interface import NotificationHandler

logger = logging.getLogger(__name__)


class PaymentUpdateOnCRSFailed(NotificationHandler):
    def notify(self, hotel_name, booking_id, amount):
        context = dict(
            booking_id=booking_id,
            amount=amount,
            hotel_name=hotel_name
        )
        email_tmpl_config = copy.deepcopy(
            settings.NOTIFICATION_CONF['email_templates']['payment_crs_update']['failure'])
        email_dto = EmailDTO(data=dict(
            sender=email_tmpl_config['sender'],
            to_list=email_tmpl_config['to'],
            cc_list=email_tmpl_config['cc'],
            subject="Update payment in Hx | {booking_id}".format(booking_id=booking_id),
            content=loader.render_to_string(template_name=email_tmpl_config['template'], context=context)
        ))
        try:
            tracking_id = self._backend.send_email(email_dto)
            logger.info('Notification tracking_id: {tr_id} for Payment Update Failure on CRS'.format(tr_id=tracking_id))
            return tracking_id
        except Exception as e:
            logger.info(
                "Error sending payment update on crs failed email , booking_id: {booking_id}, amount: {amount}"
                    .format(booking_id=booking_id, amount=amount))
