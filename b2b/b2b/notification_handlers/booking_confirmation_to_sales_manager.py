import logging
from time import strftime

import htmlmin
from django.conf import settings
from django.template import loader

from b2b import constants
from b2b.consumer.notif import get_notification_backend
from b2b.domain.utils import count_room_nights
from b2b.dto import EmailDTO
from b2b.models import SalesPocHierarchy

logger = logging.getLogger(__name__)

class FlexiBookingConfirmationHandler(object):
    def get_context(self, booking, rooms_list):

        from athena.models import FlexiPriceBookingStatus
        try:
            flexi_data = FlexiPriceBookingStatus.objects.get(booking_id=booking.booking_id)
        except Exception as e:
            logger.info("Error getting flexi price status data '{type}' while building context " \
                             "for booking '{bid}': {err}".format(type=type, bid=booking.booking_id, err=str(e)))
            raise

        treebo_poc = booking.booker_company.account_treebo_poc
        try:
            sales_hierarchy = SalesPocHierarchy.objects.filter(poc=treebo_poc)

        except Exception as e:
            logger.info("Error getting sales_hierarchy for '{sales_poc}' while getting data from " \
                             "SalesPocHierarchy and for booking '{bid}': {err}".format(sales_poc=getattr(treebo_poc,'email', None),
                                                                                       bid=booking.booking_id,
                                                                                       err=str(e)))
            sales_hierarchy = []

        try:
            from b2b.domain.services import NotificationService

            sales_poc_manager_email = [hierachy.manager.email for hierachy in sales_hierarchy]
            if not sales_poc_manager_email:
                sales_poc_manager_email = settings.SALES_POC_EMAIL

            context = dict(
                corporate=booking.legal_entity.name,
                sales_manager_email=sales_poc_manager_email,
                hotel_name=booking.hotel.name,
                treebo_poc_name=getattr(treebo_poc, 'name', None),
                treebo_poc_email=getattr(treebo_poc, 'email', None),
                booking_code=booking.booking_id,
                booking_date=str(booking.created_at.date()),
                checkin=str(strftime('%d %b, %Y ', booking.check_in.timetuple())),
                checkout=str(strftime('%d %b, %Y ', booking.check_out.timetuple())),
                days=count_room_nights(str(booking.check_in), str(booking.check_out)),
                no_of_romms=len(rooms_list),
                actual_price=round(flexi_data.actual_price),
                offered_price=round(flexi_data.flexible_price),
                discount=round(flexi_data.discount),
                reason=flexi_data.reason
            )

        except Exception as e:
            logger.info("Error building context while sending '{type}' email " \
                             "for booking '{bid}': {err}".format(type='flexi_price_confirmation_to_sales',
                                                                 bid=booking.booking_id, err=str(e)))
            raise

        return context

    def get_email_dto(self, booking, context):
        # in case this is not prod system, prepend the subject with environment name
        email_subject = "{name} <{email}> has given a discount of ({discount}) - {hotel} - {bid}".format(
            name=context['treebo_poc_name'], email=context['treebo_poc_email'], discount=context['discount'],
            hotel=booking.hotel.name,
            bid=booking.booking_id)
        # build the email dto
        email_tmpl_config = settings.NOTIFICATION_CONF['email_templates']['booking']['flexi_confirmation_tosales']
        email_html = loader.render_to_string(template_name=email_tmpl_config['template'], context=context)

        # in case it's a non-prod environment, let the subject say which
        if settings.ENV_NAME != 'prod' or len(settings.ENV_NAME.strip()) == 0:
            email_subject = "[{e}] {s}".format(e=settings.ENV_NAME, s=email_subject)

        from b2b.domain.services import NotificationService
        try:
            htmlmin.minify(email_html, remove_empty_space=True)
        except IndexError as e:
            raise Exception('Context has changed but template has not been changed due to {exc}'.format(exc=e))

        email = EmailDTO(data=dict(
            sender=email_tmpl_config['sender'],
            to_list=email_tmpl_config['to'] + context['sales_manager_email'],
            cc_list=email_tmpl_config['cc'],
            subject=email_subject,
            content=email_html,
            is_urgent=NotificationService.is_priority(
                constants.NotificationPriority.BOOKING_CONFIRMATION_TO_SALES_MANAGER)
        ))

        return email

    def send_email(self, booking, email_dto):
        notif_backend = get_notification_backend(settings.NOTIFICATION_CONF['backend'])

        try:
            # the backend needs to return a tracking id on success
            tracking_id = notif_backend.send_email(email_dto)

        except Exception as e:
            # todo: we need to catch specific exceptions here
            logger.info("Error notifying flexi price sales booking confirmation for %s: %s", booking.booking_id,
                             str(e))
            raise

        return tracking_id

    def create_booking_attribute(self, booking, booking_attr, tracking_id):
        logger = logging.getLogger(self.__class__.__name__)
        booking.bookingattributes_set.create(key=booking_attr,
                                             value=tracking_id)
        msg = "saved '{type}' flexi price sales confirmation email tracking id " \
              "'{tid}' to booking-attribs for booking '{bid}'"
        logger.info(msg.format(type='flexi_price_confirmation_to_sales', tid=tracking_id, bid=booking.booking_id))

    def notify(self, booking):

        rooms_list = [dict(room_type=record.room_type,
                           adults=record.guest_count(),
                           primary_guest=record.guest)
                      for record in booking.bookedroom_set.all()]
        context = self.get_context(booking, rooms_list)
        email_dto = self.get_email_dto(booking, context)
        tracking_id = self.send_email(booking, email_dto)
        self.create_booking_attribute(booking, constants.BookingAttributes.FLEXI_BOOKING_CONFIRM_MAIL,
                                      tracking_id)
