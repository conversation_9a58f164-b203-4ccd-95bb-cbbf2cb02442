# -*- coding: utf-8 -*-
import logging

from django.conf import settings
from django.template import loader

from b2b.dto import EmailDTO
from b2b.models import LegalEntity, AccountLegalEntity, AccountAdmin
from b2b.models import Corporate
from .interface import NotificationHandler

logger = logging.getLogger(__name__)


class GiftCardOrderConfirmationHandler(NotificationHandler):
    def notify(self, legal_entity_id, order_data, user, order_id, **kwargs):
        legal_entity = LegalEntity.objects.get(legal_entity_id=legal_entity_id)

        account_ids = AccountLegalEntity.objects.filter(legal_entity=legal_entity).values_list('account_id', flat=True)

        if not (account_ids and AccountAdmin.objects.filter(account_id__in=account_ids, auth_user=user).exists()):
            return

        context = dict(name=user.get_short_name(),
                       email=user.email,
                       order_id=order_id,
                       products=order_data['products'])
        # build the email dto
        email_tmpl_config = settings.NOTIFICATION_CONF['email_templates']['offline_gift_card']['success']

        email_subject = "Loyalty gift card order for corporate - {legal_entity_name}".format(
            legal_entity_name=legal_entity.name)

        # in case it's a non-prod environment, let the subject say which
        if settings.ENV_NAME != 'prod' or len(settings.ENV_NAME.strip()) == 0:
            email_subject = "[{e}] {s}".format(e=settings.ENV_NAME, s=email_subject)

        email_dto = EmailDTO(data=dict(
            sender=email_tmpl_config['sender'],
            to_list=email_tmpl_config['to'],
            cc_list=email_tmpl_config['cc'],
            subject=email_subject,
            content=loader.render_to_string(template_name=email_tmpl_config['template'], context=context)
        ))

        # send email
        try:
            # the backend needs to return a tracking id on success
            tracking_id = self._backend.send_email(email_dto)
            logger.info(
                'Notification tracking_id: {tr_id} for Gift card order success'.format(tr_id=tracking_id))

        except Exception as e:
            logger.info("Error notifying gift card order for {legal_entity_id}", legal_entity_id)
