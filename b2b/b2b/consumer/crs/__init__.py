# -*- coding: utf-8 -*-
# pylint: disable=invalid-name

from b2b.consumer.crs.crs_update_booking_txn_mgr import CRSUpdateBookingTxnManager

from b2b.consumer.crs.dummy import DummyCRS
from b2b.consumer.crs.treebo_crs.booking_backend import TreeboCRSWrapper
import b2b.consumer.crs.crs_order

_crs_backends = [DummyCRS, TreeboCRSWrapper]


def get_crs_backend(name):
    """
    :param name: name of the chosen class (that also implements CRSBackend interface)
    :return: the requested CRS backend
    """
    for cls in _crs_backends:
        if name == cls.__name__:
            return cls

    raise RuntimeError("The CRS backend requested ('{name}') is not available".format(name=name))
