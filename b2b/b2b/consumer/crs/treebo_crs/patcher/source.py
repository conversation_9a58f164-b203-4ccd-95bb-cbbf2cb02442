import logging

from ths_common.value_objects import BookingSource

from b2b.consumer.crs.treebo_crs.utils_mixin import thsc_exception_transformer
from common.diff_and_patch.patch_item import PatchItem
from core.booking.diff.source import SourceDiff

logger = logging.getLogger(__name__)


class SourcePatch(PatchItem):

    @classmethod
    def diff_class(cls):
        return SourceDiff

    @thsc_exception_transformer
    def apply(self):
        new_source = self.delta.new_state

        thsc_booking = self.patcher.thsc_booking_for_update()
        thsc_booking.source = BookingSource(channel_code=new_source.channel.uid,
                                            subchannel_code=new_source.sub_channel.uid,
                                            application_code=new_source.application.uid,
                                            )
        thsc_booking.update()

        return thsc_booking
