import logging

from b2b.consumer.crs.treebo_crs.utils_mixin import thsc_exception_transformer, ThscUtils
from common.diff_and_patch.patch_item import PatchItem
from core.booking.diff.add_payment import AddPaymentDiff

logger = logging.getLogger(__name__)


class AddPaymentsPatch(PatchItem):

    @classmethod
    def diff_class(cls):
        return AddPaymentDiff

    @thsc_exception_transformer
    def apply(self):
        thsc_payments = ThscUtils.get_thsc_payments_from_payments([self.delta.new_state])
        thsc_bill = self.patcher.thsc_bill_for_update()

        for thsc_payment in thsc_payments:
            thsc_bill.add_payment(thsc_payment)
