import logging

from b2b.consumer.crs.treebo_crs.utils_mixin import thsc_exception_transformer
from core.booking.diff import RemoveAddonDiff
from common.diff_and_patch.patch_item import PatchItem

logger = logging.getLogger(__name__)


class RemoveAddonPatch(PatchItem):

    @classmethod
    def diff_class(cls):
        return RemoveAddonDiff

    @thsc_exception_transformer
    def apply(self):
        thsc_booking = self.patcher.thsc_booking_for_update()
        thsc_booking.delete_addon(self.delta.current_state.uid)
