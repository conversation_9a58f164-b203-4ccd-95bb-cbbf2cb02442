import json
import logging

from django.conf import settings

from b2b.consumer.hotel_pricing.interface import BaseHotelsCheapestPricingBackend
from b2b.consumer.hotel_pricing.pricing_orchestrator.po_pricing_details import WebsitePriceDetails
from b2b.domain.services.exceptions import InvalidDTO
from common.utils.slack import send_goonj

logger = logging.getLogger(__name__)


class HotelsCheapestPricingBackend(BaseHotelsCheapestPricingBackend):

    @classmethod
    def get_hotel_pricing(cls, dto):
        if not dto.is_valid():
            logger.info("Invalid dto:- for website hotel pricing backend")
            raise InvalidDTO(dto)
        hotel_pricing_request = dto.data

        try:
            hotel_ids = hotel_pricing_request['hotels']

            web_price_details_cls = WebsitePriceDetails(check_in=hotel_pricing_request['from_date'],
                                                        checkout=hotel_pricing_request['to_date'],
                                                        hotel_ids=hotel_ids,
                                                        room_config=hotel_pricing_request['room_config'],
                                                        pricing_type=settings.PRICING_TYPES['CHEAPEST'],
                                                        ta_pricing_applicable=hotel_pricing_request[
                                                            'ta_pricing_applicable'],
                                                        gstin=hotel_pricing_request['gstin']
                                                        )

            response = web_price_details_cls.get_website_prices()

        except Exception as e:
            data = {'response': response}
            logger.info(
                "Getting an error  {err} while fetching price from PO for request {request} and "
                "response {response}".format(
                    request=hotel_pricing_request, response=data, err=str(e)))
            send_goonj(logger, "pricing_alert", "Error in getting cheapest price", json.dumps(data), "pricing")
            raise

        return response
