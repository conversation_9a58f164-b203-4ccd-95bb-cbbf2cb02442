from rest_framework import serializers
from django.conf import settings


class _NightsBreakupDTO(serializers.Serializer):
    tax = serializers.DecimalField(max_digits=20, decimal_places=2)
    date = serializers.DateField(format=settings.BOOKING['date_format'])
    pre_tax_price = serializers.DecimalField(max_digits=20, decimal_places=2)
    post_tax_price = serializers.DecimalField(max_digits=20, decimal_places=2)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass


class TaxDTO(serializers.Serializer):
    nights_breakup = _NightsBreakupDTO(many=True)
    pretax_price = serializers.DecimalField(max_digits=20, decimal_places=2)
    tax = serializers.DecimalField(max_digits=20, decimal_places=2)
    post_tax_price = serializers.DecimalField(max_digits=20, decimal_places=2)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass
