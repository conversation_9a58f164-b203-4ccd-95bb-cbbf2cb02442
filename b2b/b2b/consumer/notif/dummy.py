# -*- coding: utf-8 -*-
import logging
import random

from .interface import NotificationBackend


class DummyNotificationBackend(NotificationBackend):
    @classmethod
    def send_sms(cls, phone_number, content):
        logger = logging.getLogger(__name__)
        super(DummyNotificationBackend, cls).send_sms(phone_number, content)

        logger.info('sms sent ... %s, %s',phone_number, content)

        # return some random tracking-id; it's a dummy notif backend anyway
        return random.randint(10**6, 10**7)

    @classmethod
    def send_email(cls, email_dto):
        logger = logging.getLogger(__name__)
        super(DummyNotificationBackend, cls).send_email(email_dto)

        if not email_dto.is_valid():
            raise RuntimeError("Invalid EmailDTO received")

        email_data = email_dto.data
        logger.info('email sent ... %s', email_data)

        # return some random tracking-id; it's a dummy notif backend anyway
        return random.randint(10**6, 10**7)
