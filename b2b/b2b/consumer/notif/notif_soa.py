# -*- coding: utf-8 -*-
#pylint: disable=too-many-locals,unidiomatic-typecheck,too-many-format-args,singleton-comparison,unsubscriptable-object,ungrouped-imports
import logging

import time
import requests
from django.conf import settings

from b2b.dto import EmailDTO
from b2b.consumer.notif.interface import NotificationBackend
from common.utils.slack import send_slack_notif
from b2b.constants import Slack, Timeouts


class NotificationSOA(NotificationBackend):
    """
    talks to the notification soa to send emails, sms, slack notifc etc
    """

    @classmethod
    def send_sms(cls, phone_number, content):
        logger = logging.getLogger(__name__)
        super(NotificationSOA, cls).send_sms(phone_number, content)
        try:
            data = {
                "data": {
                    "message_type": "Whitelisted",
                    "body": content,
                    "sender": "",  # Optional as of now. Can be used to send mask to be used for sending SMS
                    "receivers": phone_number,
                    "consumer": settings.SMS_SERVICE_CONSUMER,
                }
            }

            logger.debug('Sending SMS url: %s, phone_number: %s, data: %s',
                         settings.SMS_SERVICE_API,
                         phone_number,
                         data)
            response = requests.post(settings.SMS_SERVICE_API, json=data,
                                     timeout=(Timeouts.CONNECTION_TIMEOUT, Timeouts.RESPONSE_TIMEOUT))

            logger.info("Sending SMS response: %s", response.text)
            json_response = response.json()

            if not json_response['data']['status'] == "success":
                raise Exception('Sending SMS response failed')

            tracking_id = json_response['data']['data']['notification_id']
            logger.info('Notification tracking id: %s', tracking_id)

        except Exception as e:
            logger.info('Error sending SMS:- for %s, %s due to %s', phone_number, str(content[:10] + '...'), e,
                            exc_info=True)
            raise

        return tracking_id

    @classmethod
    def send_email(cls, email_dto):
        """
        two things to keep in mind to avoid silent failures in NotifSOA
            - emails shouldn't repeat in to/cc/bcc
            - 'to' shouldn't be empty

        :param email_dto: EmailDTO
        :return: nothing
        """
        assert type(email_dto) is EmailDTO

        logger = logging.getLogger(__name__)
        super(NotificationSOA, cls).send_email(email_dto)

        if not email_dto.is_valid():
            raise RuntimeError("Invalid EmailDTO: {e}".format(e=email_dto.errors))
        response = None
        data = None
        try:
            # ensure we don't have any repeating emails in to/cc/bcc - otherwise the notif-soa fails
            to_set = set(email_dto.data['to_list'])
            cc_set = set(email_dto.data['cc_list'])
            bcc_set = set(email_dto.data['bcc_list'])
            sender = email_dto.data['sender'] if email_dto.data['sender'] else settings.SERVER_EMAIL.email

            data = {
                "data": {
                    "subject": email_dto.data['subject'],
                    "body_text": email_dto.data['content'] if email_dto.data['content_type'] == 'text' else '',
                    "body_html": email_dto.data['content'] if email_dto.data['content_type'] == 'html' else '',
                    "sender": sender,
                    "sender_name":email_dto.data.get('sender_name', ""),
                    "reply_to": email_dto.data['reply_to'] if email_dto.data['reply_to'] else sender,
                    "receivers": {
                        "to": list(to_set),
                        "cc": list(cc_set - to_set),
                        "bcc": list(bcc_set - to_set)
                    },
                    "consumer": "b2b",
                    "attachments": email_dto.data['attachments']
                },
                "attributes":{
                    "message_priority": 'priority' if email_dto.data['is_urgent'] == True else 'normal',
                }
            }
            url = settings.NOTIFICATION_CONF['backend_configs']['soa']['email']['url']
            logger.info('Sending email (to:{t}, cc:{c}, bcc:{b}, subject:{s}) '
                        'using Notification SOA (url: {u})'.format(u=url, s=email_dto.data['subject'],
                                                                   t=', '.join(to_set).encode('utf-8').strip(),
                                                                   c=', '.join(cc_set).encode('utf-8').strip(),
                                                                   b=', '.join(bcc_set).encode('utf-8').strip()))
            start_time = time.time()
            response = requests.post(url, json=data, timeout=(Timeouts.CONNECTION_TIMEOUT, Timeouts.RESPONSE_TIMEOUT))
            json_response = response.json()
            logger.info('Response time: {t}s; response: {resp}'.format(resp=response.text,
                                                                       t=round(time.time() - start_time, 2)))
            if not json_response['data']['status'] == "success":
                # todo: do we get error message in case NotifSOA fails? add that to the exception
                raise RuntimeError('response status is {s}'.format(s=json_response['data']['status']))

            tracking_id = json_response['data']['data']['notification_id']
            logger.info('Notification tracking id: %s', tracking_id)
        except Exception as e:
            logger.info('Error sending email:- for %s due to %s', email_dto.data, e, exc_info=True)
            message_info = ''
            response_info = ''
            if data:
                message_info = "Subject: {s}, to: {to}".format(s=data['data']['subject'],
                                                               to=data['data']['receivers']['to'])
            if response:
                response_info = "status_code: {s}, resp_text: {r}, ".format(s=response.status_code,
                                                                            r=response.text)

            send_slack_notif(Slack.B2B_APP_ALERTS, "Notification Service Error : "
                                                   "message_info: {m}, response_info: {r}, error_message: {e_msg}".
                             format(m=message_info, r=response_info, e_msg=str(e)), raise_exception=False)
            raise

        return tracking_id
