import logging

import requests
from django.conf import settings

from b2b import constants
from b2b.domain.services.exceptions import InvalidDTO
from b2b.dto.hotel_sync import PropertyDTO
from b2b.constants import CatalogServiceEndPoints
from common.dtos.config_dto import ConfigDto
from common.utils.slack import send_slack_notif
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

logger = logging.getLogger(__name__)


class CatalogServiceClient(object):
    def __init__(self, catalog_id):
        self.cs_id = catalog_id

    @staticmethod
    def get_headers():
        headers = {
            "referrer": "/",
            "content-type": "application/json"
        }
        enrich_outgoing_request(headers)
        return headers

    def get_property(self):
        url = '{host}/{path}/{cs_id}'.format(host=settings.CATALOG_SERVICE_HOST, path=CatalogServiceEndPoints.PROPERTY_DETAILS,
                                             cs_id=self.cs_id)
        logger.info('Calling API : {url}'.format(url=url))
        try:
            response = requests.get(url, headers=self.get_headers())
            response.raise_for_status()
            if response and response.status_code == 200:
                property_dto = PropertyDTO(data=response.json())
                if not property_dto.is_valid():
                    logger.info("Invalid data format received from Catalog Service, data={response}, error: {e}"
                                 .format(response=response.json, e=property_dto.errors))
                    raise InvalidDTO(property_dto)
            return property_dto.data
        except Exception as e:
            message = 'Unable to get property {e}'.format(e = str(e))
            send_slack_notif(constants.Slack.B2B_APP_ALERTS,message,raise_exception=False)
            logger.info(e)
            raise e

    def get_property_configs(self, property_id=None):
        url = '{host}/{path}'.format(host=settings.CATALOG_SERVICE_HOST,
                                     path=CatalogServiceEndPoints.TENANT_CONFIGS)
        if property_id:
            url = url + '?property_id={0}'.format(property_id)

        try:
            response = requests.get(url, headers=self.get_headers())
            response.raise_for_status()
            if response and response.status_code == 200:
                config_dtos = self._get_property_config_from_json(response.json())
                property_config = {config.config_name: config for config in config_dtos}
                return property_config
            return {}
        except Exception as e:
            message = 'Unable to get property config {e}'.format(e=str(e))
            send_slack_notif(constants.Slack.B2B_APP_ALERTS,message,raise_exception=False)
            logger.info(e)
            raise e

    @staticmethod
    def _get_property_config_from_json(property_config_json):
        config_list = []
        for config in property_config_json:
            config_list.append(ConfigDto(config.get('config_name'), config.get('config_value'),
                                         config.get('value_type')))
        return config_list
