import logging

from b2b.constants import TenantConfigConstants
from b2b.consumer.hotel_details.cataloging_service.catalog_service_client import CatalogServiceClient
from b2b.consumer.hotel_details.cataloging_service.formatter import CatalogPropertyFormatter

logger = logging.getLogger(__name__)


class CatalogService():
    def __init__(self):
        pass

    def get_property(self, catalog_id):
        client = CatalogServiceClient(catalog_id=catalog_id)
        property_data = client.get_property()
        property_formatter = CatalogPropertyFormatter(property_data=property_data)
        formatted_data = property_formatter.reformat()
        return formatted_data

    def has_seller_provided_lut(self, catalog_id):
        client = CatalogServiceClient(catalog_id=catalog_id)
        property_data = client.get_property()
        property_details = CatalogPropertyFormatter(property_data=property_data)
        return property_details.property_data['property_details']['has_lut']

    @staticmethod
    def get_acq_config_for_hotel(hotel_id=None):
        client = CatalogServiceClient(catalog_id=hotel_id)
        configs = client.get_property_configs(property_id=hotel_id)
        return configs.get(TenantConfigConstants.ACQ_CONFIGS)
