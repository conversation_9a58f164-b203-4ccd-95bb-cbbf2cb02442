# pylint: disable=no-member,unidiomatic-typecheck

import logging
import traceback

import requests
from requests import <PERSON><PERSON>P<PERSON>rror, ConnectionError, ConnectTimeout, ReadTimeout, Timeout
from django.conf import settings
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from b2b import constants
from b2b.constants import Timeouts
from b2b.consumer.hotel_details import BaseHotelDetailsBackend
from b2b.models import Hotel
from common.utils.slack import send_slack_notif


class WebsiteHotelDetailsBackend(BaseHotelDetailsBackend):

    @classmethod
    def get_headers(cls):
        headers = {
            "referrer": "/",
            "content-type": "application/json"
        }
        enrich_outgoing_request(headers)
        return headers

    @classmethod
    def get_hotel_details(cls, dto):
        logger = logging.getLogger(cls.__name__)
        if not dto.is_valid():
            logger.info("Invalid dto:- for hotel details")
            raise Exception
        hotel_details_request = dto.data
        try:
            hotel_ids = hotel_details_request['hotels'].split(',')
            hotel_ext_ids = Hotel.objects.filter(hotel_id__in=hotel_ids).values_list('external_id', flat=True)
            if hotel_ext_ids.count() == 0:
                traceback.print_exc()
                logger.info("Hotels does not exist:- for "
                                "hotel_ids {hotel_ids}".format(hotel_ids=hotel_details_request['hotels']))
                raise Exception
            hotel_external_ids = ','.join(hotel_ext_ids)
            hotel_details_response = requests.get(cls._get_hotel_details_url(hotel_external_ids),
                                                  headers=cls.get_headers(),
                                                  timeout=(Timeouts.CONNECTION_TIMEOUT, Timeouts.RESPONSE_TIMEOUT))

            response_data = hotel_details_response.json()
            response_data = cls._convert_to_b2b_id(response_data, hotel_external_ids)
            return response_data
        except Exception as e:
            message = "WebsiteHotelDetails:- error occurred while getting hotel details for " \
                      "hotel id {hotel_id}: {err}".format(hotel_id=hotel_details_request['hotel_id'], err=str(e))
            logger.info(message)
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, message, raise_exception=False)
            raise e

    @classmethod
    def _get_hotel_details_url(cls, hotel_ids):
        hotel_details_url = 'api/v2/hotels/details/?hotels={hotel_ids}'.format(hotel_ids=hotel_ids)
        return settings.WEBSITE_HOST + '/' + hotel_details_url

    @classmethod
    def _get_hotels_status_url(cls):
        hotels_status_url = 'api/v2/hotels/status/'
        return settings.WEBSITE_HOST + '/' + hotels_status_url

    @classmethod
    def _get_all_hotels_url(cls):
        hotels_status_url = 'api/v2/hotels/'
        return settings.WEBSITE_HOST + '/' + hotels_status_url

    @classmethod
    def _convert_to_b2b_id(cls, response_data, hotel_external_ids):
        hotels_details = response_data['data']
        ext_id_to_b2b_id = cls._ext_id_to_b2b_id(hotel_external_ids.split(','))
        new_hotel_details = []
        for hotel_details in hotels_details:
            hotel_id = ext_id_to_b2b_id.get(str(hotel_details['id']))
            if hotel_id:
                hotel_details['id'] = hotel_id
                new_hotel_details.append(hotel_details)
        response_data['data'] = new_hotel_details
        return response_data

    @classmethod
    def _ext_id_to_b2b_id(cls, hotel_external_ids):

        assert type(hotel_external_ids) is list

        hotels = Hotel.objects.filter(external_id__in=hotel_external_ids)
        return {hotel.external_id: hotel.hotel_id for hotel in hotels}

    @classmethod
    def get_all_hotels_status(cls):
        logger = logging.getLogger(cls.__name__)
        try:
            hotel_details_response = requests.get(cls._get_hotels_status_url(), headers=cls.get_headers())
            response_data = hotel_details_response.json()
            hotels_status = response_data['data']
            hotel_ids = list(hotels_status.keys())
            ext_id_to_b2b_id = cls._ext_id_to_b2b_id(hotel_ids)

            # Hotel ids not present in b2b DB. Log those hotels
            unavail_hotel_ids = list(set(hotel_ids) - set(ext_id_to_b2b_id.keys()))
            if unavail_hotel_ids:
                logger.debug('hotel ids not present in b2b DB: {ids}'.format(ids=', '.join(unavail_hotel_ids)))

            return {ext_id_to_b2b_id[hotel_id]: hotels_status.get(hotel_id, False) for hotel_id in hotels_status if
                    ext_id_to_b2b_id.get(hotel_id, False)}
        except Exception as e:
            msg = "WebsiteAllHotelStatus:- error occurred while getting hotel status for all hotel : {err}".format(
                err=str(e))
            logger.info(msg)
            raise e

    @classmethod
    def get_all_hotels(cls):
        logger = logging.getLogger(cls.__name__)
        try:
            hotel_details_response = requests.get(cls._get_all_hotels_url(), headers=cls.get_headers())
            hotel_external_ids = [str(hotel['id']) for hotel in hotel_details_response.json()['data']]

            hotel_details_response = cls._convert_to_b2b_id(hotel_details_response.json(), ','.join(hotel_external_ids))
            return hotel_details_response['data']
        except Exception as e:
            msg = "WebsiteAllHotelStatus:- error occurred while getting hotel status for all hotel : {err}".format(
                err=str(e))
            logger.info(msg)
            raise e

    @classmethod
    def _url_for_hotel_by_hx_id(cls, hx_id):
        return "{website_host}/{hotel_by_hx_id_path}/{hx_id}/".format(website_host=settings.WEBSITE_HOST,
                                                                      hotel_by_hx_id_path='api/v2/hotels',
                                                                      hx_id=hx_id)

    @classmethod
    def get_hotel_id_by_hx_id(cls, hx_id):
        logger = logging.getLogger(cls.__name__)
        try:
            target_url = cls._url_for_hotel_by_hx_id(hx_id=hx_id)
            logger.info('Calling {url}'.format(url=target_url))
            response = requests.get(target_url, headers=cls.get_headers())
            response.raise_for_status()
            if response and response.status_code == 200 and 'data' in response.json() and \
                    response.json()['data'] and 'id' in response.json()['data'][0]:
                return response.json()['data'][0]['id']
            return None
        except Exception as e:
            msg = "WebsiteDetailsBackend:- error occurred while getting external_id for hotel with hx_id {h} : {err}" \
                .format(err=str(e), h=hx_id)
            logger.info(msg)
            raise e

    @classmethod
    def get_hotel_details_from_direct(cls, hotel_detail_dto):
        logger = logging.getLogger(cls.__name__)
        hotel_id = hotel_detail_dto.data['hotels']

        try:
            hotel_search = settings.WEBSITE_HOST + settings.WEBSITE_HOTEL_DETAIL
            hotel_search_url = hotel_search.format(h_id=hotel_id)
            hotel_detail_response = requests.get(hotel_search_url, headers=cls.get_headers())
            hotel_detail_response.raise_for_status()
            if hotel_detail_response and hotel_detail_response.status_code == 200 \
                    and hotel_detail_response.json().get('data'):
                return hotel_detail_response.text
            return None
        except (ConnectTimeout, HTTPError, ReadTimeout, Timeout, ConnectionError) as e:
            message = "HotelDetailFailure: Network error"
            logger.info(message)
            raise
        except Exception as e:
            message = "HotelDetailFailure: Error occured while loading hotel details for hotel_id {h_id}::{err}"
            logger.info(message.format(h_id=hotel_id, err=str(e)))
            raise
