# pylint: disable=invalid-name

import logging

import requests
from django.conf import settings
from requests import HTTP<PERSON>rror, ConnectionError, ConnectTimeout, ReadTimeout, Timeout
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from b2b.api.api import TreeboAPI

logger = logging.getLogger(__name__)


class SearchHotelAPI(TreeboAPI):

    @classmethod
    def get_headers(cls):
        headers = {
            "referrer": "/",
            "content-type": "application/json"
        }
        enrich_outgoing_request(headers)
        return headers

    @classmethod
    def search(cls, search_dto):

        data = search_dto.data
        try:
            website_search = settings.WEBSITE_HOTEL_SEARCH.format(n=data['nearby'],
                                                                  c=data['city'],
                                                                  s=data['state'],
                                                                  country=data['country'],
                                                                  saleable=data['meta_key'])
            hotel_search_url = settings.WEBSITE_HOST + website_search
            hotel_search_response = requests.get(hotel_search_url, headers=cls.get_headers())
            hotel_search_response.raise_for_status()
            if hotel_search_response and hotel_search_response.status_code == 200 \
                    and hotel_search_response.json().get('data'):
                return hotel_search_response.text
            return None
        except (ConnectTimeout, HTTPError, ReadTimeout, Timeout, ConnectionError) as e:
            message = "TreeboWebsiteSearch: Network error"
            logger.info(message)
            raise
        except Exception as e:
            message = "TreeboWebsiteSearch: Error occurred while connecting to direct website for hotel search."
            logger.info(message)
            raise
