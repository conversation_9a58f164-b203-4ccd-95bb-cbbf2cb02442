import logging
from collections import defaultdict

from b2b import constants
from b2b.consumer.availability import BaseAvailabilityBackend
from b2b.consumer.availability.inventorythrottleservice.its_service import ITSService
from b2b.domain.services.exceptions import InvalidDTO
from b2b.models import Hotel
from common.utils.slack import send_slack_notif

logger = logging.getLogger(__name__)


class InventoryThrottleServiceBackend(BaseAvailabilityBackend):
    # pylint: disable=invalid-name
    DEFAULT_CHANNEL = 'b2b'
    DEFAULT_SUBCHANNEL = 'athena'

    @classmethod
    def get_availability(cls, dto):

        if not dto.is_valid():
            raise InvalidDTO(dto)

        try:
            data = dto.data
            cs_ids_to_hotel_ids_map = cls.get_cs_ids_to_hotel_ids_map(hotel_ids=data['hotel_ids'].split(','))

            its_svc = ITSService(cs_ids=list(cs_ids_to_hotel_ids_map.keys()), room_configs=data['roomconfig'])

            its_available_rooms = its_svc.get_availability(checkin=data['checkin'],
                                                           checkout=data['checkout'],
                                                           channel=data.get('channel') or cls.DEFAULT_CHANNEL,
                                                           subchannel=data.get('sub_channel') or cls.DEFAULT_SUBCHANNEL)

            availability, occupancy = cls.format_rooms(its_available_rooms=its_available_rooms,
                                                       cs_ids_to_hotel_ids_map=cs_ids_to_hotel_ids_map)
            return {'data': availability, 'occupancy': occupancy}

        except Exception as e:
            msg = "ITSAvailability: Error occurred while getting availability due to {e}".format(e=str(e))
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
            logger.info(msg)
            raise

    @classmethod
    def get_all_room_types_availability(cls, dto):

        if not dto.is_valid():
            raise InvalidDTO(dto)

        try:
            data = dto.data
            cs_ids_to_hotel_ids_map = cls.get_cs_ids_to_hotel_ids_map(hotel_ids=data['hotel_ids'].split(','))

            its_svc = ITSService(cs_ids=list(cs_ids_to_hotel_ids_map.keys()), room_configs=data['roomconfig'])

            its_available_rooms = its_svc.get_all_rooms_availability(checkin=data['checkin'],
                                                                     checkout=data['checkout'],
                                                                     channel=data.get('channel') or cls.DEFAULT_CHANNEL,
                                                                     subchannel=data.get(
                                                                         'sub_channel') or cls.DEFAULT_SUBCHANNEL)

            availability, occupancy = cls.format_rooms(its_available_rooms=its_available_rooms,
                                                       cs_ids_to_hotel_ids_map=cs_ids_to_hotel_ids_map)
            return {'data': availability, 'occupancy': occupancy}

        except Exception as e:
            msg = "ITSAvailability: Error occurred while getting availability due to {e}".format(e=str(e))
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
            logger.info(msg)
            raise

    @classmethod
    def get_cs_ids_to_hotel_ids_map(cls, hotel_ids):
        # pylint: disable=no-member
        hotels = Hotel.objects.filter(hotel_id__in=hotel_ids,
                                      catalogue_id__isnull=False).values('catalogue_id', 'hotel_id')
        cs_ids_to_hotel_ids_map = {hotel['catalogue_id']: hotel['hotel_id'] for hotel in hotels}

        if not cs_ids_to_hotel_ids_map:
            msg = "ITS: Hotels {h} does not exist".format(h=hotel_ids)
            raise RuntimeError(msg)

        return cs_ids_to_hotel_ids_map

    @classmethod
    def format_rooms(cls, its_available_rooms, cs_ids_to_hotel_ids_map):
        availability, occupancy = defaultdict(dict), defaultdict(list)
        for room in its_available_rooms:
            b2b_hotel_id = cs_ids_to_hotel_ids_map[room.hotel_cs_id]

            availability[b2b_hotel_id][room.type] = room.availability
            occupancy[b2b_hotel_id].append(room.max_occupancy_details())

        # pylint: disable=fixme
        # TODO: remove dependency on occupancy from availability svc: CORPAPI-1408
        return availability, occupancy
