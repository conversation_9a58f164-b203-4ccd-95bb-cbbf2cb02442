from .interface import BaseAvailabilityBackend
from .website.availability import WebsiteAvailabilityBackend
from .inventorythrottleservice.availability import InventoryThrottleServiceBackend

_availability_backends = [WebsiteAvailabilityBackend,InventoryThrottleServiceBackend]


def get_availability_backend(name):
    """
    :param name: name of the chosen class
    :return: the requested Availability backend
    """
    for cls in _availability_backends:
        if name == cls.__name__:
            return cls

    raise RuntimeError("The Availability backend requested ('{name}') is not available".format(name=name))
