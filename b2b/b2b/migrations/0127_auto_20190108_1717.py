# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0126_auto_20181214_1713'),
    ]

    operations = [
        migrations.AddField(
            model_name='booking',
            name='owner_email',
            field=models.CharField(max_length=400, null=True, blank=True),
        ),
        migrations.AddField(
            model_name='booking',
            name='owner_name',
            field=models.CharField(max_length=400, null=True, blank=True),
        ),
        migrations.AddField(
            model_name='booking',
            name='owner_phone',
            field=models.CharField(max_length=100, null=True, blank=True),
        ),
        migrations.AddField(
            model_name='booking',
            name='room_night_count',
            field=models.IntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='payment',
            name='payment_source',
            field=models.CharField(blank=True, max_length=20, null=True, choices=[(b'HX', b'HX'), (b'CRS', b'CRS'), (b'AUTOMATION', b'TA_AUTOMATION'), (b'TA_PORTAL', b'TA_PORTAL')]),
        ),
    ]
