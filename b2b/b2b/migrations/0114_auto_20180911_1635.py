# -*- coding: utf-8 -*-
from django.db import models, migrations


def insert_automation_config(apps, schema_editor):
    AutomationConfig = apps.get_model("b2b", "automationconfig")
    for config in config_json:
        AutomationConfig(days_to_checkin=config['days_to_checkin'],
                         room_nights_start_range=config['room_night_start'],
                         room_nights_end_range=config['room_night_stop'],
                         advanced_booking_window_start=config['abw_start'],
                         advanced_booking_window_end=config['abw_stop'], payment_type=config['payment_type'],
                         config_type=config['config_type'],
                         percentage_to_be_paid=config['percentage_to_be_paid']).save()


class Migration(migrations.Migration):
    dependencies = [
        ('b2b', '0113_auto_20180905_1725'),
    ]

    operations = [
        migrations.RunPython(insert_automation_config, reverse_code=migrations.RunPython.noop)
    ]


config_json = [
    {
        "days_to_checkin": 1,
        "room_night_start": 50,
        "room_night_stop": 10000,
        "abw_start": 2,
        "abw_stop": 30,
        "payment_type": "TA - PAID BY GUEST",
        "percentage_to_be_paid": 30,
        "config_type": "REMINDER"
    },
    {
        "days_to_checkin": 3,
        "room_night_start": 0,
        "room_night_stop": 20,
        "abw_start": 8,
        "abw_stop": 10000,
        "payment_type": "TA - PAID BY GUEST",
        "percentage_to_be_paid": 30,
        "config_type": "REMINDER"
    },
    {
        "days_to_checkin": 4,
        "room_night_start": 0,
        "room_night_stop": 50,
        "abw_start": 8,
        "abw_stop": 10000,
        "payment_type": "TA - PAID BY GUEST",
        "percentage_to_be_paid": 30,
        "config_type": "REMINDER"
    },
    {
        "days_to_checkin": 5,
        "room_night_start": 0,
        "room_night_stop": 20,
        "abw_start": 8,
        "abw_stop": 10000,
        "payment_type": "TA - PAID BY GUEST",
        "percentage_to_be_paid": 30,
        "config_type": "REMINDER"
    },
    {
        "days_to_checkin": 8,
        "room_night_start": 50,
        "room_night_stop": 10000,
        "abw_start": 8,
        "abw_stop": 10000,
        "payment_type": "TA - PAID BY GUEST",
        "percentage_to_be_paid": 30,
        "config_type": "REMINDER"
    }
]
