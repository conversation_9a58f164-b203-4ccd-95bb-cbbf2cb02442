# -*- coding: utf-8 -*-
from django.db import models, migrations
import datetime
from django.utils.timezone import utc


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0095_salespocpromotedhotelsmapping'),
    ]

    operations = [
        migrations.CreateModel(
            name='PromotedHotel',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('hotel_id', models.CharField(unique=True, max_length=20, db_index=True)),
                ('sales_poc_incentive_factor', models.DecimalField(default=0, max_digits=4, decimal_places=2)),
            ],
            options={
                'default_permissions': ('add', 'change', 'read', 'delete'),
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='salespocpromotedhotelsmapping',
            name='created_at',
            field=models.DateTimeField(default=datetime.datetime(2018, 5, 24, 12, 39, 52, 828713, tzinfo=utc), verbose_name='Created at', auto_now_add=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='salespocpromotedhotelsmapping',
            name='modified_at',
            field=models.DateTimeField(default=datetime.datetime(2018, 5, 24, 12, 39, 58, 668712, tzinfo=utc), verbose_name='Modified at', auto_now=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='salespocpromotedhotelsmapping',
            name='sales_poc_email',
            field=models.CharField(default='<EMAIL>', max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='salespocpromotedhotelsmapping',
            name='hotel',
            field=models.ForeignKey(to='b2b.PromotedHotel', on_delete=models.CASCADE),
        ),
        migrations.AlterUniqueTogether(
            name='salespocpromotedhotelsmapping',
            unique_together=set([('sales_poc_email', 'hotel')]),
        ),
        migrations.RemoveField(
            model_name='salespocpromotedhotelsmapping',
            name='sales_poc',
        ),
    ]
