# -*- coding: utf-8 -*-
from django.db import migrations

from b2b import constants


def assign_btc_to_attribs(apps, schema_editor):
    print('Migrating btc_enabled flag')
    corporates = apps.get_model("b2b", "Corporate").objects.all()
    CorpAttrib = apps.get_model("b2b", "CorporateAttributes")
    for corporate in corporates:
        attrib, created = CorpAttrib.objects.get_or_create(key=constants.CorporateAttributes.BTC_ENABLED,
                                                           corporate=corporate)
        attrib.value = 1 if corporate.btc_enabled else 0
        attrib.save()



def unassign_btc_from_attribs(apps, schema_editor):
    print('\n')
    print('Nothing to undo as we just create corporate attributes in the migration.')
    print('The existence of this method is to guarantee reversibility of migrations.')


class Migration(migrations.Migration):
    dependencies = [
        ('b2b', '0048_remove_corporate_treebo_poc_person'),
    ]

    operations = [
        migrations.RunPython(assign_btc_to_attribs, reverse_code=unassign_btc_from_attribs),
    ]
