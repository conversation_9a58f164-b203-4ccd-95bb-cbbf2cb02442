# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0067_auto_20170904_1833'),
    ]

    operations = [
        migrations.CreateModel(
            name='State',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('ext_state_id', models.CharField(max_length=25, null=True)),
                ('state_abbreviation', models.Char<PERSON>ield(default=b'', max_length=10, blank=True)),
                ('legal_state_code', models.CharField(unique=True, max_length=5)),
                ('name', models.CharField(unique=True, max_length=100)),
            ],
            options={
                'default_permissions': ('add', 'change', 'read', 'delete'),
                'abstract': False,
            },
        ),
        migrations.AlterModelOptions(
            name='legalentity',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'verbose_name': 'Legal Entities', 'verbose_name_plural': 'Legal Entities'},
        ),
        migrations.RenameField(
            model_name='corporate',
            old_name='legal_name',
            new_name='_legal_name',
        ),
    ]
