# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0089_merge'),
    ]

    operations = [
        migrations.CreateModel(
            name='CorporateRestrictedHotels',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('corporate', models.ForeignKey(to='b2b.Corporate', to_field='corporate_id', on_delete=models.CASCADE)),
                ('hotel', models.ForeignKey(to='b2b.Hotel', to_field='hotel_id', on_delete=models.CASCADE)),
            ],
            options={
                'abstract': False,
                'default_permissions': ('add', 'change', 'read', 'delete'),
            },
        ),
        migrations.AlterUniqueTogether(
            name='corporaterestrictedhotels',
            unique_together=set([('corporate', 'hotel')]),
        ),
    ]
