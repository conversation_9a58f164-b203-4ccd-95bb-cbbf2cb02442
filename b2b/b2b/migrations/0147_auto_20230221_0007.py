# -*- coding: utf-8 -*-
# Generated by Django 1.10.7 on 2023-02-20 18:37
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0146_auto_20230217_1106'),
    ]

    operations = [
        migrations.CreateModel(
            name='LegalEntityAdmin',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('default_recommended_hotels', models.CharField(default='', max_length=200)),
                ('loyalty_enabled', models.BooleanField(default=True, help_text='Designates whether loyalty is enabled for this user or not. Unselect this instead of deleting accounts.', verbose_name='loyalty enabled')),
                ('auth_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
                'default_permissions': ('add', 'change', 'read', 'delete'),
            },
        ),
        migrations.RemoveField(
            model_name='corpadmin',
            name='auth_user',
        ),
        migrations.RemoveField(
            model_name='corpadmin',
            name='corporate',
        ),
        migrations.DeleteModel(
            name='CorpAdmin',
        ),
        migrations.AddField(
            model_name='legalentityadmin',
            name='legal_entity',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='b2b.LegalEntity'),
        ),
        migrations.AlterUniqueTogether(
            name='legalentityadmin',
            unique_together=set([('legal_entity', 'auth_user')]),
        ),
    ]
