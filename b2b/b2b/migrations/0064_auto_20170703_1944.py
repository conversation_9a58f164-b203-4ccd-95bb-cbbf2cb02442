# -*- coding: utf-8 -*-
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0063_auto_20170628_1756'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='additionalguests',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='address',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='auditlog',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='bookedroom',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='booking',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='bookingattributes',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'get_latest_by': 'modified_at'},
        ),
        migrations.AlterModelOptions(
            name='bookinginclusions',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='corpadmin',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='corporate',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='corporateattributes',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='countercreds',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'get_latest_by': 'modified_at'},
        ),
        migrations.AlterModelOptions(
            name='custommealpricing',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'get_latest_by': 'modified_at'},
        ),
        migrations.AlterModelOptions(
            name='customroompricing',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'get_latest_by': 'modified_at'},
        ),
        migrations.AlterModelOptions(
            name='featuretoggle',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='guest',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='hotel',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'verbose_name': 'Hotel', 'verbose_name_plural': 'Hotels'},
        ),
        migrations.AlterModelOptions(
            name='hotelattribute',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'verbose_name': 'Hotel Attribute', 'verbose_name_plural': 'Hotel Attributes'},
        ),
        migrations.AlterModelOptions(
            name='hxmapping',
            options={'default_permissions': ('add', 'change', 'read', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='payment',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'get_latest_by': 'modified_at', 'verbose_name': 'Payments', 'verbose_name_plural': 'Payments'},
        ),
        migrations.AlterModelOptions(
            name='standardinclusionspricing',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'get_latest_by': 'modified_at'},
        ),
        migrations.AlterModelOptions(
            name='standardmealpricing',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'get_latest_by': 'modified_at'},
        ),
        migrations.AlterModelOptions(
            name='standardroompricing',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'get_latest_by': 'modified_at'},
        ),
        migrations.AlterModelOptions(
            name='user',
            options={'default_permissions': ('add', 'change', 'read', 'delete'), 'verbose_name': 'user', 'verbose_name_plural': 'users'},
        ),
    ]
