# -*- coding: utf-8 -*-
from django.db import models, migrations
import b2b.models.managers.usermanager
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0006_require_contenttypes_0002'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(null=True, verbose_name='last login', blank=True)),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('first_name', models.CharField(max_length=254, verbose_name='First Name')),
                ('last_name', models.CharField(default=b'', max_length=254, verbose_name='Last Name', blank=True)),
                ('email', models.EmailField(unique=True, max_length=254, verbose_name='email address')),
                ('phone_number', models.CharField(default=b'', max_length=20, verbose_name='phone_number', blank=True)),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=False, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('is_guest', models.BooleanField(default=False, verbose_name='is guest')),
                ('groups', models.ManyToManyField(related_query_name='user', related_name='user_set', to='auth.Group', blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(related_query_name='user', related_name='user_set', to='auth.Permission', blank=True, help_text='Specific permissions for this user.', verbose_name='user permissions')),
            ],
            options={
                'default_permissions': ('add', 'change', 'delete', 'read'),
                'abstract': False,
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
            },
            managers=[
                ('objects', b2b.models.managers.usermanager.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='AdditionalGuests',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
            ],
        ),
        migrations.CreateModel(
            name='BookedRoom',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('room_type', models.CharField(max_length=50, choices=[(b'Acacia', b'Acacia'), (b'Maple', b'Maple'), (b'Oak', b'Oak'), (b'Mahogany', b'Mahogany')])),
                ('pre_tax_price', models.DecimalField(default=0, max_digits=20, decimal_places=2)),
                ('post_tax_price', models.DecimalField(default=0, max_digits=20, decimal_places=2)),
                ('meal_plan', models.CharField(default=b'MAP', max_length=50, choices=[(b'MAP', b'MAP'), (b'AP', b'AP'), (b'EP', b'EP'), (b'CP', b'CP')])),
                ('meal_type', models.CharField(default=b'veg', max_length=50, null=True, blank=True, choices=[(b'veg', b'Veg'), (b'non-veg', b'Non-Veg')])),
                ('pre_tax_meal_charges', models.DecimalField(default=0, max_digits=20, decimal_places=2)),
                ('tmc_commission', models.DecimalField(default=0, max_digits=20, decimal_places=2)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Booking',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('booking_id', models.CharField(unique=True, max_length=30, editable=False)),
                ('check_in', models.DateField()),
                ('check_out', models.DateField()),
                ('tmc_commission', models.DecimalField(default=0, max_digits=8, decimal_places=2)),
                ('channel', models.CharField(max_length=50, null=True, blank=True)),
                ('sub_channel', models.CharField(max_length=50, null=True, blank=True)),
                ('comments', models.CharField(max_length=300, null=True, blank=True)),
                ('status', models.CharField(default=b'In Progress', max_length=50, editable=False, choices=[(b'In Progress', b'In Progress'), (b'Saved', b'Saved'), (b'Confirmed', b'Confirmed'), (b'Cancelled', b'Cancelled'), (b'Processed', b'Processed'), (b'Failed', b'Failed')])),
                ('agent', models.ForeignKey(to=settings.AUTH_USER_MODEL, null=True, on_delete=models.CASCADE)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Corporate',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('corporate_id', models.CharField(unique=True, max_length=20)),
                ('legal_name', models.CharField(help_text=b'Corporate will be registered in Hx with this name', max_length=200)),
                ('trading_name', models.CharField(help_text=b'Alias for the corporate. Eg: ANI Ltd and Ola', unique=True, max_length=200)),
                ('address', models.CharField(max_length=200, null=True, blank=True)),
                ('city', models.CharField(max_length=200, null=True, blank=True)),
                ('state', models.CharField(max_length=200, null=True, blank=True)),
                ('zipcode', models.CharField(max_length=200, null=True, blank=True)),
                ('email', models.EmailField(max_length=200, null=True, blank=True)),
                ('phone', models.CharField(max_length=200, null=True, blank=True)),
                ('mop', models.CharField(default=b'Direct', max_length=200, null=True, blank=True)),
                ('slab_type', models.CharField(blank=True, max_length=200, null=True, choices=[(b'quote', b'Quote'), (b'default', b'Default'), (b'floor', b'Floor'), (b'custom', b'Custom')])),
                ('meal_plan', models.CharField(default=b'MAP', max_length=10, null=True, blank=True, choices=[(b'MAP', b'MAP'), (b'AP', b'AP'), (b'EP', b'EP'), (b'CP', b'CP')])),
                ('is_tmc', models.BooleanField(default=False)),
                ('tmc_commission_percentage', models.DecimalField(default=0, max_digits=4, decimal_places=2)),
                ('active', models.BooleanField(default=True)),
                ('parent', models.CharField(default=b'', max_length=200, null=True, blank=True)),
                ('sector', models.CharField(default=b'', max_length=200, null=True, blank=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Hotel',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('hotel_id', models.CharField(unique=True, max_length=20)),
                ('name', models.CharField(default=b'', max_length=200)),
                ('hotelogix_id', models.CharField(max_length=200, null=True, blank=True)),
                ('locality', models.CharField(default=b'', max_length=200, null=True)),
                ('street', models.CharField(default=b'', max_length=200, null=True)),
                ('city', models.CharField(default=b'', max_length=200, null=True)),
                ('phone_number', models.CharField(default=b'', max_length=20, null=True)),
                ('active', models.BooleanField(default=True)),
            ],
            options={
                'default_permissions': ('add', 'change', 'delete', 'read'),
                'abstract': False,
                'verbose_name': 'Hotel',
                'verbose_name_plural': 'Hotels',
            },
        ),
        migrations.CreateModel(
            name='Person',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('salutation', models.CharField(default=b'Mr', max_length=200, null=True, blank=True, choices=[(b'Mr', b'Mr'), (b'Mrs', b'Mrs'), (b'Ms', b'Ms')])),
                ('name', models.CharField(unique=True, max_length=200)),
                ('gender', models.CharField(default=b'M', max_length=200, null=True, blank=True, choices=[(b'M', b'Male'), (b'F', b'Female')])),
                ('email', models.EmailField(max_length=200, null=True, blank=True)),
                ('phone', models.CharField(max_length=200, null=True, blank=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='corporate',
            name='admin',
            field=models.ForeignKey(related_name='corporate_admin', to='b2b.Person', on_delete=models.CASCADE),
        ),
        migrations.AddField(
            model_name='corporate',
            name='created_by',
            field=models.ForeignKey(related_name='created_by', blank=True, to=settings.AUTH_USER_MODEL, null=True, on_delete=models.CASCADE),
        ),
        migrations.AddField(
            model_name='corporate',
            name='treebo_poc',
            field=models.ForeignKey(related_name='treebo_poc', to=settings.AUTH_USER_MODEL, on_delete=models.CASCADE),
        ),
        migrations.AddField(
            model_name='booking',
            name='corporate',
            field=models.ForeignKey(to='b2b.Corporate', on_delete=models.CASCADE),
        ),
        migrations.AddField(
            model_name='booking',
            name='hotel',
            field=models.ForeignKey(to='b2b.Hotel', on_delete=models.CASCADE),
        ),
        migrations.AddField(
            model_name='bookedroom',
            name='booking',
            field=models.ForeignKey(to='b2b.Booking', on_delete=models.CASCADE),
        ),
        migrations.AddField(
            model_name='bookedroom',
            name='guest',
            field=models.ForeignKey(to='b2b.Person', on_delete=models.CASCADE),
        ),
        migrations.AddField(
            model_name='additionalguests',
            name='booked_room',
            field=models.ForeignKey(to='b2b.BookedRoom', on_delete=models.CASCADE),
        ),
        migrations.AddField(
            model_name='additionalguests',
            name='booking_id',
            field=models.ForeignKey(to='b2b.Booking', on_delete=models.CASCADE),
        ),
        migrations.AddField(
            model_name='additionalguests',
            name='guest',
            field=models.ForeignKey(to='b2b.Person', on_delete=models.CASCADE),
        ),
    ]
