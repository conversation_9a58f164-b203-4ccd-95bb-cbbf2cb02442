# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0035_auto_20170428_1947'),
    ]

    operations = [
        migrations.CreateModel(
            name='HotelAttribute',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('key', models.CharField(max_length=100)),
                ('value', models.CharField(max_length=100, blank=True)),
                ('category', models.CharField(max_length=100, null=True)),
                ('sub_category', models.CharField(max_length=100, null=True)),
                ('hotel_id', models.ForeignKey(to='b2b.Hotel', on_delete=models.CASCADE)),
            ],
            options={
                'abstract': False,
                'verbose_name': 'Hotel Attribute',
                'verbose_name_plural': 'Hotel Attributes',
                'default_permissions': ('add', 'change', 'delete', 'read'),
            },
        ),
        migrations.AlterField(
            model_name='booking',
            name='status',
            field=models.CharField(default=b'Received', max_length=50, editable=False, choices=[(b'Received', b'Received'), (b'Initiated', b'Initiated'), (b'Saved', b'Saved'), (b'Confirmed', b'Confirmed'), (b'Cancellation Initiated', b'Cancellation Initiated'), (b'Cancelled', b'Cancelled'), (b'Processed', b'Processed'), (b'Failed', b'Failed'), (b'Update In Progress', b'Update In Progress'), (b'Updated', b'Updated'), (b'Update Failed', b'Update Failed'), (b'Update Discarded', b'Update Discarded')]),
        ),
        migrations.AlterUniqueTogether(
            name='hotelattribute',
            unique_together=set([('hotel_id', 'key')]),
        ),
    ]
