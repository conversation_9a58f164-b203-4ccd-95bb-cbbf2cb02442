# -*- coding: utf-8 -*-
# Generated by Django 1.10.7 on 2023-02-27 04:00
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0149_auto_20230223_1832'),
    ]

    operations = [
        migrations.CreateModel(
            name='LegalEntityAttributes',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('key', models.CharField(max_length=100)),
                ('value', models.TextField(blank=True, max_length=100, null=True)),
                ('legal_entity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='b2b.LegalEntity')),
            ],
            options={
                'abstract': False,
                'default_permissions': ('add', 'change', 'read', 'delete'),
            },
        ),
        migrations.AlterUniqueTogether(
            name='legalentityattributes',
            unique_together=set([('legal_entity', 'key')]),
        ),
        migrations.AlterIndexTogether(
            name='legalentityattributes',
            index_together=set([('legal_entity', 'key')]),
        ),
    ]
