# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0082_merge'),
    ]

    operations = [
        migrations.AlterField(
            model_name='hotel',
            name='external_id',
            field=models.Cha<PERSON><PERSON><PERSON>(max_length=20, null=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='hotel',
            name='hotel_id',
            field=models.CharField(unique=True, max_length=20, db_index=True),
        ),
        migrations.AlterField(
            model_name='hotel',
            name='hotelogix_id',
            field=models.CharField(db_index=True, max_length=200, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='hotelattribute',
            name='key',
            field=models.CharField(max_length=100, db_index=True),
        ),
        migrations.AlterField(
            model_name='hotelattribute',
            name='value',
            field=models.Char<PERSON>ield(db_index=True, max_length=100, blank=True),
        ),
        migrations.AlterIndexTogether(
            name='hotelattribute',
            index_together=set([('hotel', 'key')]),
        ),
    ]
