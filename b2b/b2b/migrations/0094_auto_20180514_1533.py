# * coding: utf8 *

#pylint: disable=invalid-name
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0090_corpadmin_loyalty_enabled'),
        ('b2b', '0093_merge'),
    ]

    operations = [
        migrations.AlterField(
            model_name='corpadmin',
            name='loyalty_enabled',
            field=models.BooleanField(default=True, help_text=
                                      'Designates whether loyalty is enabled for this user or not. '
                                      'Unselect this instead of deleting accounts.'
                                      , verbose_name='loyalty enabled'),
        ),
    ]
