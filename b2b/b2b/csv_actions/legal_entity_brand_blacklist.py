from ast import literal_eval

from csv_uploader.handler import CsvHandlerRegisty
from csv_uploader.validator import CsvValidatorRegistry

from b2b.admin import CustomPermissionsMixin
from b2b.models.corporate import LegalEntityBrandBlacklist

UploaderName = 'legal_entity_brand_blacklist'  # pylint: disable=invalid-name

# pylint: disable=invalid-name
UploaderHeaders = [
    'legal_entity_id',
    'brand',
    'active'
]


@CsvHandlerRegisty.sync_handler(UploaderName)
def handler(row, csv_job_item_id):
    # populating defaults
    row['legal_entity_id'] = row['legal_entity_id'].strip()
    row['brand'] = row['brand'].strip()
    row['active'] = literal_eval(row['active'].strip().title())

    try:
        legal_entity_brand_blacklist_info = LegalEntityBrandBlacklist.objects.get(legal_entity_id=row['legal_entity_id'],
                                                                            brand=row['brand'])
        legal_entity_brand_blacklist_info.active = row['active']
        legal_entity_brand_blacklist_info.save()
    except LegalEntityBrandBlacklist.DoesNotExist:
        LegalEntityBrandBlacklist.objects.create(**row)


@CsvValidatorRegistry.header_validator(UploaderName, UploaderHeaders)
def header_validator(user, header):
    if not CustomPermissionsMixin.has_upload_permission(user, LegalEntityBrandBlacklist):
        raise RuntimeError('Permission Denied')
    return header == UploaderHeaders


# pylint: disable=unused-argument
@CsvValidatorRegistry.row_validator(UploaderName)
def row_validator(user, row):
    return True
