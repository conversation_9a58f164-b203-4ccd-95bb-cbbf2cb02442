import logging

from csv_uploader.handler import CsvHandlerRegisty
from csv_uploader.validator import CsvValidatorRegistry

from b2b.admin import CustomPermissionsMixin
from b2b.models import HotelAttribute, Hotel

logger = logging.getLogger(__name__)

UploaderName = 'corporate_ratings_upload'  # pylint: disable=invalid-name

UploaderHeaders = [
    'hotel_cs_id',
    'category'
]


@CsvHandlerRegisty.sync_handler(UploaderName)
def handler(row, csv_job_item_id):
    data_dict = {}
    for key, value in row.items():
        data_dict[key.lower().strip()] = value.strip()
    if not data_dict['category']:
        return
    hotel_cs_id = data_dict['hotel_cs_id']
    if len(hotel_cs_id) < 7:
        hotel_cs_id.zfill(7)
    hotel_info = Hotel.objects.get(catalogue_id=hotel_cs_id, active=True)
    data = {'hotel_id': hotel_info.id, 'value': data_dict['category'], 'key': "corporate_rating"}
    HotelAttribute.objects.update_or_create(hotel_id=data['hotel_id'],
                                            key="corporate_rating", defaults=data)


@CsvValidatorRegistry.header_validator(UploaderName, UploaderHeaders)
def header_validator(user, headers):
    if not CustomPermissionsMixin.has_upload_permission(user, HotelAttribute):
        raise RuntimeError('Permission Denied')
    if len(headers) != len(UploaderHeaders):
        return False
    # Making headers case-insensitive, removing leading and trailing spaces
    headers = [header.lower().strip() for header in headers]
    # Making headers position insensitive
    final_headers = []
    if {'hotel_cs_id', 'category'}.issubset(set(headers)):
        final_headers = ['hotel_cs_id', 'category']

    return final_headers == UploaderHeaders


# pylint: disable=unused-argument
@CsvValidatorRegistry.row_validator(UploaderName)
def row_validator(user, row):
    return True
