import requests
import json
import logging
from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import MultipleObjectsReturned

from b2b.constants import Timeouts
from b2b.models.token import Token


class GoogleAuthService(object):
    LOGGER = logging.getLogger('GoogleAuthService')
    CONFIG = settings.GOOGLE_AUTH

    @classmethod
    def generate_access_token_using_refresh_token(cls, refresh_token):
        """
        Given a refresh token, generate an access token

        Arguments:
            refresh_token (string)  -- Corresponding refresh token

        Returns:
            access_token/None
        """
        assert(isinstance(refresh_token, str))
        access_token = None

        try:
            payload = dict(
                client_secret=cls.CONFIG['client_secret'],
                client_id=cls.CONFIG['client_id'],
                refresh_token=refresh_token,
                grant_type='refresh_token',
            )
            response = requests.post(url=cls.CONFIG['token_urls']['refresh'],
                                     headers={'content-type': 'application/x-www-form-urlencoded'},
                                     data=payload,timeout=(Timeouts.CONNECTION_TIMEOUT,Timeouts.RESPONSE_TIMEOUT))

            if response.status_code == 200:
                cls.LOGGER.info("HTTP Response :: {r}".format(r=response.json()))
                access_token = response.json().get("access_token")
                cls.LOGGER.info("Access token {t} generated".format(t=access_token))
            else:
                cls.LOGGER.error("Failed to generate new access token :: {msg}".format(
                    msg=response.json().get("error_description")))

        except (requests.exceptions.HTTPError,
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout) as err:
            cls.LOGGER.exception("Request error :: {err}".format(err=err))

        except Exception as e:
            cls.LOGGER.exception("Failed to revoke token {t}".format(t=access_token))

        return str(access_token) if access_token else None

    @classmethod
    def revoke_access_token(cls, access_token):
        """Revoke the user granted to the app (he needs to give the consent again
        upon the net login)

        Arguments:
            access_token {str} -- Access token

        Returns:
            [True/False] -- IF the access token got successfully revoked
        """

        assert(isinstance(access_token, str))

        try:
            url = "{url}?token={token}".format(url=cls.CONFIG['token_urls']['revoke'], token=access_token)
            response = requests.post(url=url,
                                     headers={'content-type': 'application/x-www-form-urlencoded'})

            if response.status_code == 200:
                cls.LOGGER.info("Token {t} revoked successfully".format(t=access_token))
            else:
                cls.LOGGER.error("Failed to revoke token {t} :: {res}".format(
                    t=access_token, res=response.text))
                return False

        except (requests.exceptions.HTTPError,
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout) as err:
            cls.LOGGER.exception("Request error :: {err}".format(err=err))
            return False

        except Exception as e:
            cls.LOGGER.exception("Failed to revoke token {t}".format(t=access_token))
            return False

        return True

    @classmethod
    def check_access_token_validity(cls, access_token):
        """This is just a wrapper over google's token info api to
        check if a token is still valid

        Arguments:
            access_token {str} -- Access token

        Returns:
            True/False
        """

        assert(isinstance(access_token, str))

        try:
            url = "{url}?access_token={token}".format(url=cls.CONFIG['token_urls']['access'], token=access_token)
            response = requests.post(url=url,
                                     headers={'content-type': 'application/x-www-form-urlencoded'})

            if response.status_code == 200:
                cls.LOGGER.info("Access token {t} is valid".format(t=access_token))
            else:
                cls.LOGGER.warning("Access token {t} is invalid :: {res}".format(
                    t=access_token, res=response.json().get('error')))
                return False
        except (requests.exceptions.HTTPError,
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout) as err:
            cls.LOGGER.exception("Request error :: {err}".format(err=err))
            return False

        except Exception as e:
            cls.LOGGER.exception("Failed to check the validity of access token {t}".format(t=access_token))
            return False

        return True

    @classmethod
    def get_user_email_from_access_token(cls, access_token):
        """Use google's token info api to get the user email

        Arguments:
            access_token {str} -- Access token

        Returns:
            str/None -- email if present, else none
        """

        email = None
        assert(isinstance(access_token, str))

        try:
            response = requests.get(url="{url}?access_token={acc}".format(
                url=cls.CONFIG['token_urls']['access'], acc=access_token))

            if response.status_code == 200:
                cls.LOGGER.info("HTTP Response :: {r}".format(r=response.json()))
                email = response.json().get('email')
                if not email:
                    cls.LOGGER.error("Could not fetch email for acc token {t}".format(t=access_token))
            else:
                cls.LOGGER.error("Failed to fetch access token info :: {msg}".format(
                    msg=response.text))

        except (requests.exceptions.HTTPError,
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout) as err:
            cls.LOGGER.exception("Request error :: {err}".format(err=err))

        except Exception as e:
            cls.LOGGER.exception("Failed to fetch access token info :: {e}".format(e=str(e)))

        return str(email) if email else None

    @classmethod
    def generate_refresh_token_and_access_token(cls, authcode):
        """Generate a new refresh token and access token for a given authcode

        Arguments:
            authcode {str} -- Authcode is needed to generate a refresh token
                              This is provided by google upon authenticating from ui
        Returns:
            refresh_token, access_token
        """
        refresh_token, access_token = None, None
        assert(isinstance(authcode, str))

        try:
            payload = dict(
                client_secret=cls.CONFIG['client_secret'],
                client_id=cls.CONFIG['client_id'],
                code=authcode,
                grant_type='authorization_code',
                redirect_uri=cls.CONFIG['redirect_uri']
            )
            headers = {'content-type': 'application/x-www-form-urlencoded'}
            url = cls.CONFIG['token_urls']['refresh']

            response = requests.post(url=url,
                                     headers=headers,
                                     data=payload)
            cls.LOGGER.info("request details :: {url} :: {header} :: {payload}".format(url=url, header=headers, payload=payload))

            if response.status_code == 200:
                refresh_token = response.json().get("refresh_token")
                access_token = response.json().get("access_token")

                if not refresh_token:
                    cls.LOGGER.warn(
                        "No refresh token found for auth code {code} :: revoke access first".format(code=authcode))
                if not access_token:
                    cls.LOGGER.warn(
                        "No access token found for auth code {code}".format(code=authcode))
            else:
                cls.LOGGER.error("Failed to generate new refresh+access token :: {msg}".format(
                    msg=response.text))

        except (requests.exceptions.HTTPError,
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout) as err:
            cls.LOGGER.exception("Request error :: {err}".format(err=err))

        except Exception as e:
            cls.LOGGER.exception("Failed to generate new refresh+access token :: {e}".format(e=str(e)))

        return str(refresh_token) if refresh_token else None, str(access_token) if access_token else None

    @classmethod
    def set_refresh_token(cls, email, refresh_token):
        """Persist the token in cache

        Arguments:
            refresh_token {str} -- token for a user(email)
            email {str} -- email of the user

        Returns:
            true/false -- success/failure
        """
        assert(isinstance(refresh_token, str))
        assert(isinstance(email, str))

        try:
            key = "{prefix}:{email}".format(prefix=cls.CONFIG['cache_prefix'], email=email)
            token = Token.objects.get(category=Token.Categories.REFRESH_TOKEN, key=key)
            cls.LOGGER.info("Refresh token found for {e} in cache:: updating".format(e=email))
            token.value = refresh_token
            token.save()
            return True

        except Token.DoesNotExist:
            cls.LOGGER.info("No refresh token found for {e} in cache:: creating new one".format(e=email))
            new_token = Token(category=Token.Categories.REFRESH_TOKEN, key=key, value=refresh_token)
            new_token.save()
            return True

        except Token.MultipleObjectsReturned:
            cls.LOGGER.error("Multiple token found for {e} :: continuing without any action".format(e=email))

        except Exception as e:
            cls.LOGGER.exception("Failed to store refresh token {t} in cache for {e}".format(e=email,
                                                                                             t=refresh_token))
        return False

    @classmethod
    def get_refresh_token(cls, email):
        """Fetch the refresh token from cache

        Arguments:
            email {str} -- email of the user

        Returns:
            str/none - refresh token from cache
        """
        assert(isinstance(email, str))

        try:
            key = "{prefix}:{email}".format(prefix=cls.CONFIG['cache_prefix'], email=email)
            token = Token.objects.get(category=Token.Categories.REFRESH_TOKEN, key=key)
            cls.LOGGER.info("Found refresh token for user {e} :: {t}".format(e=email, t=token.value))
            return str(token.value) if token.value else None

        except Token.DoesNotExist:
            cls.LOGGER.info("No token found for {e}".format(e=email))

        except Token.MultipleObjectsReturned:
            cls.LOGGER.error("Multiple token found for {e}".format(e=email))

        except Exception as e:
            cls.LOGGER.exception("Failed to get cached refresh token for {e}".format(e=email))

        return None

    @classmethod
    def delete_refresh_token(cls, email):
        """Rare case of refresh token being invalid/outdated

        Arguments:
            email {str} -- email of the user (that's the cache key)

        Returns:
            True/False
        """
        assert(isinstance(email, str))

        try:
            key = "{prefix}:{email}".format(prefix=cls.CONFIG['cache_prefix'], email=email)
            Token.objects.filter(category=Token.Categories.REFRESH_TOKEN, key=key).delete()
            cls.LOGGER.info("Deleted refresh tokens for {e}".format(e=email))
            return True

        except Exception as e:
            cls.LOGGER.exception("Failed to get cached refresh token for {e}".format(e=email))

        return False
