import logging
from datetime import datetime, timedelta

import jwt
import pytz
from django.conf import settings

from b2b.constants import Booking as BookingConstants, UserAutoLoginParameters
from b2b.models.booking import Booking
from b2b.models.user import User


class AutoLoginService(object):

    @classmethod
    def generate_auto_login_url(cls, email, redirect_url, error_message='', **kwargs):
        try:
            # Check if redirect URL is empty
            if not redirect_url:
                raise Exception("Invalid redirect URL")

            # Check if user exists
            User.objects.get(email=email, is_active=True)
        except Exception as e:
            error_message = "Unable to generate auto-login URL. {message}".format(message=str(e))
            logging.exception(error_message)
            raise Exception(error_message)

        booking_id = kwargs.get("booking_id", None)
        cta = kwargs.get("cta", None)
        cta_text = kwargs.get("cta_text", None)
        token = cls.encode_auth_token(email, redirect_url, error_message, booking_id, cta, cta_text)
        base_url = settings.B2B_HOST + UserAutoLoginParameters.URL

        return '{base_url}?token={token}'.format(base_url=base_url, token=token)

    @classmethod
    def encode_auth_token(cls, user_id, redirect_url, error_message='', booking_id='', cta='', cta_text=''):
        """
        Generates the Auth Token
        user_id: string (User Id)
        extension: Integer (Extension factor to increase the expiry)
        :return: string
        """
        try:
            payload = {
                'default_expiry': (datetime.utcnow().date() +
                        timedelta(days=UserAutoLoginParameters.DEFAULT_TOKEN_EXPIRY_DAYS)).strftime("%Y-%m-%d"),
                'user': user_id,
                'url': redirect_url,
                'bid': booking_id,
                'msg': error_message,
                'cta': cta,
                'cta_text': cta_text
            }
            return jwt.encode(
                payload,
                UserAutoLoginParameters.SECRET_KEY,
                algorithm='HS256'
            )
        except Exception as e:
            logging.exception(str(e))
            raise e

    @classmethod
    def is_booking_checked_out(cls, booking_id, default_expiry_date):
        local_tz = pytz.timezone(settings.TIME_ZONE)
        local_date = datetime.utcnow().replace(tzinfo=pytz.utc).astimezone(local_tz).date()
        if not booking_id:
            expiry_date = default_expiry_date
        else:
            try:
                booking = Booking.objects.get(booking_id=booking_id)
                if booking.status not in (BookingConstants.CONFIRMED,
                                          BookingConstants.PAYMENT_FAILED,
                                          BookingConstants.INITIATED,
                                          BookingConstants.SAVED):
                    raise Exception("Invalid Booking Status - {status}".format(status=booking.status))
                # Invalidate after midnight(EOD) of checkout date
                expiry_date = booking.check_out
            except Exception as e:
                logging.exception("Booking is invalid. {msg}".format(msg=str(e)))
                raise Exception("Unable to calculate token expiry")
        if expiry_date < local_date:
            logging.error("Token has expired")
            return True
        logging.info("Token is valid")
        return False


    @classmethod
    def decode_auth_token(cls, auth_token):
        """
        Decodes the auth token
        :param auth_token:
        :return: integer|string
        """
        try:
            payload = jwt.decode(auth_token, UserAutoLoginParameters.SECRET_KEY)
            return payload
        except jwt.ExpiredSignatureError:
            raise jwt.ExpiredSignatureError(
                'Session expired! Please log in again.')
        except jwt.InvalidTokenError:
            raise jwt.InvalidTokenError('Invalid token! Please log in again.')
        except Exception:
            raise
