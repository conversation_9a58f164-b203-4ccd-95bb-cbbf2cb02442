import logging
import datetime
import random

import sentry_sdk

from b2b.constants import B2BEventType, IntegrationEventStatus, CorporateAttributes
from b2b.dto.integration_event import IntegrationEventDTO
from b2b.messaging.publisher.company_profile_publisher import CorporateProfileEventPublisher
from b2b.models.integration_event import IntegrationEvent

logger = logging.getLogger(__name__)


class IntegrationEventService(object):
    @classmethod
    def _generate_event_id(cls, prefix="EVT"):
        created_at = datetime.datetime.utcnow()
        part2 = created_at.strftime("%H%M%S")

        number = random.randint(0, *********)
        part4 = int(number % 10000)
        number = (number - part4) / 10000
        part3 = int(number % 10000)
        part3 += (created_at.month * 10)
        parts = [part2, str(part3).rjust(3, '0'), str(part4).rjust(4, '0')]
        if prefix:
            parts.insert(0, prefix)
        return '-'.join(parts)

    @staticmethod
    def _populate_legal_entity(form, obj):
        communication_address = None
        if obj.address:
            communication_address = dict(building=obj.address.building,
                                         street=obj.address.street,
                                         locality=obj.address.locality,
                                         landmark=obj.address.landmark,
                                         city=obj.address.city,
                                         state=obj.address.state,
                                         pincode=obj.address.pincode,
                                         country=obj.address.country)
        registered_address = communication_address
        if form.cleaned_data.get('gstin'):
            registered_address = dict(building=form.cleaned_data['gstin_building'],
                                      street=form.cleaned_data['gstin_street'],
                                      locality=form.cleaned_data['gstin_locality'],
                                      landmark=form.cleaned_data['gstin_landmark'],
                                      city=form.cleaned_data['gstin_city'],
                                      state=form.cleaned_data['gstin_state'].name,
                                      pincode=form.cleaned_data['gstin_pincode'],
                                      country=form.cleaned_data['gstin_country'])

        return dict(
            corporate_id=form.cleaned_data['corporate'].corporate_id if form.cleaned_data.get('corporate') else
            form.instance.corporate.corporate_id,
            legal_entity_id=obj.legal_entity_id,
            agency_type=obj.agency_type,
            registered_address=registered_address,
            communication_address=communication_address,
            legal_entity_name=obj.name,
            btc_enabled=obj.btc_enabled,
            has_lut=form.cleaned_data.get('gstin_has_lut') or False,
            credit_limit=obj.credit_limit,
            gstin=form.cleaned_data.get('gstin'),
            credit_period=obj.credit_period,
            billing_period=obj.billing_period,
            invoice_dispatch_option_on_checkout=obj.invoice_dispatch_option_on_checkout,
            invoice_dispatch_option_with_stay_summary=obj.invoice_dispatch_option_with_stay_summary,
            should_dispatch_booking_request_with_stay_summary=obj.should_dispatch_booking_request_with_stay_summary,
            is_sez=form.cleaned_data.get('gstin_is_sez') or False,
            active=obj.active,
            is_test=obj.is_test,
            inside_sales_poc=dict(
                name=getattr(obj.account_inside_sales_poc, 'name'),
                phone_number=obj.account_inside_sales_poc.phone_number,
                email=obj.account_inside_sales_poc.email,
            ) if obj.account_inside_sales_poc and obj.is_default_for_account else None,
            treebo_poc=dict(
                name=getattr(obj.account_treebo_poc, 'name'),
                phone_number=obj.account_treebo_poc.phone_number,
                email=obj.account_treebo_poc.email,
            ) if obj.account_treebo_poc and obj.is_default_for_account else None,
            hotel_finance_poc=dict(
                name=getattr(obj.account_hotel_finance_poc, 'name'),
                phone_number=obj.account_hotel_finance_poc.phone_number,
                email=obj.account_hotel_finance_poc.email,
            ) if obj.account_hotel_finance_poc and obj.is_default_for_account else None,
            finance_admin=dict(
                name=getattr(obj.finance_admin, 'name'),
                phone_number=obj.finance_admin.phone_number,
                email=obj.finance_admin.email
            ) if obj.finance_admin and obj.is_default_for_account else None,
            primary_admin=dict(
                name=getattr(obj.primary_admin, 'name'),
                phone_number=obj.primary_admin.phone_number,
                email=obj.primary_admin.email
            ) if obj.primary_admin and obj.is_default_for_account else None,
            ota_commission=dict(
                tac_commission=obj.tac_commission,
                post_commission_amount=obj.post_commission_amount
            ),
            account_id=obj.account.account_id if obj.is_default_for_account else None,
            account_version=obj.account.latest_account_version.version if obj.is_default_for_account else None,
        )

    @staticmethod
    def _populate_corporate_data(form, obj):
        return dict(corporate_id=obj.corporate_id,
                    active=obj.active,
                    is_test=obj.is_test,
                    agency_type=form.cleaned_data['agency_type'],
                    billing_address=dict(building=obj.billing_address.building,
                                         street=obj.billing_address.street,
                                         locality=obj.billing_address.locality,
                                         landmark=obj.billing_address.landmark,
                                         city=obj.billing_address.city,
                                         state=obj.billing_address.state,
                                         pincode=obj.billing_address.pincode,
                                         country=obj.billing_address.country),
                    contact_address=dict(building=obj.contact_address.building,
                                         street=obj.contact_address.street,
                                         locality=obj.contact_address.locality,
                                         landmark=obj.contact_address.landmark,
                                         city=obj.contact_address.city,
                                         state=obj.contact_address.state,
                                         pincode=obj.contact_address.pincode,
                                         country=obj.contact_address.country),
                    legal_name=form.cleaned_data['trading_name'],
                    email=form.cleaned_data['email'],
                    phone=form.cleaned_data['phone'],
                    sector=form.cleaned_data['sector'],
                    trading_name=form.cleaned_data['trading_name'],
                    pan_number=form.cleaned_data['pan_number'],
                    virtual_account_number=form.cleaned_data['virtual_account_number'],
                    tan_number=form.cleaned_data['tan_number'],
                    cin_number=form.cleaned_data['cin_number'],
                    )

    def _convert_to_data(self, form, obj, for_legal_entity):
        if not for_legal_entity:
            return self._populate_corporate_data(form, obj)

        return self._populate_legal_entity(form, obj)

    def create_integration_event(self, form, obj, change, generated_by, for_legal_entity=False):
        data = self._convert_to_data(form, obj, for_legal_entity)

        if change:
            if for_legal_entity:
                event_type = B2BEventType.LEGAL_ENTITY_UPDATED
            else:
                event_type = B2BEventType.CORPORATE_UPDATED
        else:
            if for_legal_entity:
                event_type = B2BEventType.LEGAL_ENTITY_CREATED
            else:
                event_type = B2BEventType.CORPORATE_CREATED

        if not form.changed_data:
            return None

        event_dto = IntegrationEventDTO(data=dict(
            event_id=self._generate_event_id(),
            event_type=event_type,
            body=data,
            status=IntegrationEventStatus.UNPUBLISHED,
            generated_by_id=generated_by.id
        ))

        if event_dto.is_valid():
            IntegrationEvent.objects.create(**event_dto.data)
        return event_dto.initial_data

    @staticmethod
    def get_oldest_unpublished_event():
        return IntegrationEvent.objects.filter(status__in=[
            IntegrationEventStatus.UNPUBLISHED, IntegrationEventStatus.FAILED]).order_by(
            'created_at').first()

    @staticmethod
    def count_unpublished_events():
        return IntegrationEvent.objects.filter(status__in=[
            IntegrationEventStatus.UNPUBLISHED, IntegrationEventStatus.FAILED]).order_by(
            'created_at').count()

    def publish_oldest_unpublished_event_to_queue(self):
        event = self.get_oldest_unpublished_event()

        if not event:
            return False

        pending_event_count = self.count_unpublished_events()

        logger.info(f"pending_integration_events:{pending_event_count}")
        try:
            logger.info(
                'Fetched event_id: %s to be published', event.event_id,
            )
            event_details = dict(event_type=event.event_type,
                                 body=event.body,
                                 event_id=event.event_id)
            CorporateProfileEventPublisher().publish(event_details)
            event.status = IntegrationEventStatus.PUBLISHED
        except Exception as ex:
            logger.debug(
                'Error while publishing integration event with id: %s', event.event_id)
            sentry_sdk.capture_exception(ex)
            event.status = IntegrationEventStatus.FAILED

        event.save()
        return event.status == IntegrationEventStatus.PUBLISHED
