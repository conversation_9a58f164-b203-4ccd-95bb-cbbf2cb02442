import logging
import os
from operator import attrgetter

from django.conf import settings
from ths_common.constants.booking_constants import AttachmentFileType, AttachmentGroup
from thsc.crs import context
from thsc.crs.entities.booking import Attachment, Booking as THSCBooking, BookingSearchQuery
from treebo_commons.treebo_email.email_parser import EmailParser
from treebo_commons.treebo_email.file_uploader.aws_s3 import AWSS3Uploader
from weasyprint import HTML

logger = logging.getLogger(__name__)


class BookingRequestService(object):
    BASE_S3_UPLOAD_DIR = "attachments/email-parser"
    BOOKING_REQUEST_PREFIX = "Booking Request"
    MAIL_SUMMARY_ATTACHMENT_COUNT = 5

    @classmethod
    def parse_booking_request_and_add_to_attachments(cls, booking_request_parser_dto):
        logger.info("Running parser for data: %s", booking_request_parser_dto)
        parser_failure = []
        parser_success = []
        for parser_data in booking_request_parser_dto:
            try:
                thsc_booking = cls._get_thsc_booking(parser_data["booking_id"])

                parser = EmailParser(email_id=os.environ.get("BOOKING_REQUEST_EMAIL"),
                                     password=os.environ.get("BOOKING_REQUEST_PASSWORD"),
                                     uploader=AWSS3Uploader)

                parsed_emails = parser.parse_emails(subject=parser_data["email_subject"], delete_source=True,
                                                    label="OutgoingBookingRequests", parse_attachments=True)

                logger.info(
                    "parsing booking request by subject: {subject}".format(subject=parser_data["email_subject"]))
                booking_request_name = cls._get_booking_request_name(thsc_booking)
                latest_email = max(parsed_emails, key=attrgetter("date"))
                booking_attachments = [
                    Attachment(original_url=att.link, file_type=AttachmentFileType(att.attachment_type.value),
                               display_name=att.name, attachment_group=AttachmentGroup.BOOKING_REQUEST) for att in
                    latest_email.attachments] if latest_email.attachments else []
                booking_request_attachment = cls._upload_booking_request(latest_email.text_html[0],
                                                                         latest_email.address_headers.subject,
                                                                         booking_request_name)
                booking_attachments.append(booking_request_attachment)
                if booking_attachments:
                    thsc_booking.add_attachments(booking_attachments)
                parser_success.append(parser_data)
            except Exception as e:
                logger.info("Exception %s occurred while processing booking request for %s", e, parser_data)
                parser_data["error"] = e.__str__()
                parser_failure.append(parser_data)
                continue
        logger.info("Parsing completed, success: %s, failure %s", parser_success, parser_failure)
        return parser_success, parser_failure

    @classmethod
    def _get_thsc_booking(cls, booking_id):
        context.auth_id = "email_parser"
        context.application = "b2b_backend"
        thsc_result = THSCBooking.search(BookingSearchQuery(query=booking_id))
        if not thsc_result.bookings:
            raise Exception("No booking found")
        if thsc_result.total > 1:
            raise Exception("Multiple bookings found")
        return thsc_result.bookings[0]

    @classmethod
    def _upload_booking_request(cls, body_html, subject, booking_request_name):
        subject = subject.replace("/", "")
        booking_request_local_path = os.path.join("/tmp", booking_request_name)
        s3_upload_directory = os.path.join(cls.BASE_S3_UPLOAD_DIR, subject)
        doc = HTML(string=body_html)
        doc.write_pdf(target=booking_request_local_path)
        booking_request_s3link = AWSS3Uploader().upload_file(booking_request_local_path, s3_upload_directory)
        return Attachment(original_url=booking_request_s3link, file_type=AttachmentFileType.PDF,
                          display_name=booking_request_name,
                          attachment_group=AttachmentGroup.BOOKING_REQUEST)

    @classmethod
    def _get_booking_request_name(cls, thsc_booking):
        """
        Booking requests are of the pattern "Booking Request <counter> - <booking_id>"
        So creating the next name by incrementing the counter
        :param thsc_booking:
        :return:
        """
        last_name = max([att.display_name for att in thsc_booking.get_attachments() if
                         att.attachment_group == AttachmentGroup.BOOKING_REQUEST and
                         cls.BOOKING_REQUEST_PREFIX in att.display_name], default=None)
        if last_name:
            last_counter = last_name.split(cls.BOOKING_REQUEST_PREFIX)[1].split("-")[0].strip()

            if last_counter and int(last_counter) >= cls.MAIL_SUMMARY_ATTACHMENT_COUNT:
                attachment_exception = f'Mail Summary Attachments Limit Exceeded , current limit is: {cls.MAIL_SUMMARY_ATTACHMENT_COUNT}'
                raise Exception(attachment_exception)
            return f'{cls.BOOKING_REQUEST_PREFIX} {int(last_counter) + 1 if last_counter else 2} -' \
                   f' {thsc_booking.reference_number}.pdf'
        else:
            return f'{cls.BOOKING_REQUEST_PREFIX} - {thsc_booking.reference_number}.pdf'
