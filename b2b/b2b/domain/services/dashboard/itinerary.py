import logging

from django.conf import settings

from b2b.constants import HotelAttributes as HotelAttributesConstants
from b2b.constants import Tax
from b2b.consumer.itinerary import get_itinerary_backend
from b2b.domain.services.hotel import HotelService
from b2b.domain.services.ta_corporate import TAPricingService
from b2b.domain.services.tax import TaxService
from b2b.domain.utils import DTOExchange
from b2b.domain.utils.date_utils import count_room_nights
from b2b.dto import HotelDetailsDTO
from b2b.dto import RoomPricingAvailabilityDTO
from b2b.dto import TaxRequestDTO
from b2b.dto.itinerary import ItineraryDTO
from b2b.models import Corporate, HotelAttribute, Hotel

logger = logging.getLogger(__name__)


class ItineraryService(object):
    """
    Get and construct dict for itinerary api used for dashboard
    """
    _corp = None
    _website_price = None

    @classmethod
    def get_itinerary_data(cls, itinerary_request_dto):

        if not itinerary_request_dto.is_valid():
            logger.info("Invalid Itinerary DTO")
            raise Exception('Invalid Itinerary DTO')

        rpa_req_data = itinerary_request_dto.data
        hotel_id = rpa_req_data['hotel_id']
        hd_dto = HotelDetailsDTO(data=dict(hotels=hotel_id))
        hotels_details_response = HotelService.get_hotel_details(hd_dto)
        hotel_details_response = hotels_details_response['data'][0]

        hotel = Hotel.objects.get(hotel_id=hotel_id)

        try:
            hotel_attribute = HotelAttribute.objects.get(key=HotelAttributesConstants.LEGAL_STATE_CODE,
                                                         hotel_id=hotel.id)
            legal_state_code = hotel_attribute.value
        except HotelAttribute.DoesNotExist:
            logger.info('HotelAttribute LEGAL_STATE_CODE does not exist for hotel {hotel}'.format(hotel=hotel_id))
            legal_state_code = None

        hotel_details = {
            'name': hotel_details_response['name'],
            'id': hotel_id,
            'image_url': hotel_details_response.get('image_url', ''),
            'legal_state_code': legal_state_code
        }
        hotel_details.update(hotel_details_response['address'])
        room_pricing_availability_dto = DTOExchange.convert(itinerary_request_dto, RoomPricingAvailabilityDTO)

        room_pricing = HotelService.get_room_pricing(room_pricing_availability_dto)
        room_pricing = room_pricing[rpa_req_data['hotel_id']][rpa_req_data['roomtype'].title()]

        tax_req_dto = DTOExchange.convert(itinerary_request_dto, TaxRequestDTO,
                                          room_pricing=room_pricing,
                                          charge_type=Tax.RoomNight)

        tax_dto = TaxService.get_tax(tax_req_dto)
        if not tax_dto.is_valid():
            raise RuntimeError('Invalid Tax DTO')

        price_details = cls._convert_prices_to_float(tax_dto.data)
        nights = count_room_nights(rpa_req_data['checkin'], rpa_req_data['checkout'])

        corporate = Corporate.objects.get(corporate_id=rpa_req_data['corporate_id'])

        try:
            cls._corp = Corporate.objects.get(corporate_id=rpa_req_data['corporate_id'])
        except Corporate.DoesNotExist:
            logger.info(
                "Corporate does not exist for corp id {corp_id}".format(corp_id=rpa_req_data['corporate_id']))
            raise

        # If corporate is TA then show min prices between website and B2B
        if cls._corp.is_web_pricing_for_ta_or_tmc_applicable():
            price_details = cls._min_itinerary_price(rpa_req_data, price_details)

        return {
            'hotel': hotel_details,
            'price': price_details,
            'nights': nights,
            'date': {
                'checkin': rpa_req_data['checkin'],
                'checkout': rpa_req_data['checkout']
            },
            'room': cls._get_room_details(rpa_req_data['roomconfig'], rpa_req_data['roomtype']),
            'btc_allowed': corporate.btc_enabled
        }

    @classmethod
    def _min_itinerary_price(cls, rpa_req_data, corp_price_details):
        cls._website_price = cls.get_website_itinerary(rpa_req_data)

        # Compare corp and website prices and take min
        ta_pricing_svc = TAPricingService(rpa_req_data['corporate_id'], rpa_req_data['roomconfig'],
                                          rpa_req_data['checkin'],
                                          rpa_req_data['checkout'])
        factor = ta_pricing_svc.get_ta_discount_factor()

        web_pretax_amount = cls._get_price_details_from_website_itinerary(
            cls._website_price)[0]

        if cls._website_price and web_pretax_amount and web_pretax_amount * factor < corp_price_details[
            'pretax_price']:
            corp_price_details = cls._convert_website_itinerary_data(cls._website_price, factor)

        corp_price_details = cls._apply_room_night_discount(ta_pricing_svc, corp_price_details)

        return corp_price_details

    @classmethod
    def _apply_room_night_discount(cls, ta_pricing_svc, corp_price_details):
        if not corp_price_details:
            return None
        rnd_factor = ta_pricing_svc.room_night_discount_factor()
        corp_price_details['pretax_price'] = round(rnd_factor * corp_price_details['pretax_price'], 2)
        corp_price_details['post_tax_price'] = round(rnd_factor * corp_price_details['post_tax_price'], 2)
        corp_price_details['total_tax'] = round(rnd_factor * corp_price_details['total_tax'], 2)

        for night_breakup in corp_price_details['nights_breakup']:
            night_breakup['tax'] = round(rnd_factor * night_breakup['tax'], 2)
            night_breakup['pre_tax_price'] = round(rnd_factor * night_breakup['pre_tax_price'], 2)
            night_breakup['post_tax_price'] = round(rnd_factor * night_breakup['post_tax_price'], 2)

        return corp_price_details

    @classmethod
    def _get_discounted_factor(cls):
        ta_pricing_discount = cls._corp.get_ta_discount()
        factor = (float(100 - ta_pricing_discount)) / 100
        return factor

    @classmethod
    def _convert_website_itinerary_data(cls, website_itinerary, factor):
        # keys are different in website itineray data, convert it into corporate data key
        if not website_itinerary:
            return None

        pretax_amount, tax_amount, post_tax_amount = cls._get_price_details_from_website_itinerary(website_itinerary)

        data = dict(
            pretax_price=pretax_amount*factor,
            total_tax=tax_amount*factor,
            post_tax_price=post_tax_amount*factor,
            nights_breakup=[]
        )

        for night_breakup in website_itinerary['date_wise_price_breakup']:
            data['nights_breakup'].append(dict(
                tax=round(night_breakup['tax']['total_value']*factor, 2),
                date=night_breakup['date'],
                pre_tax_price=round(night_breakup['pre_tax']['sell_price']*factor, 2),
                post_tax_price=round(night_breakup['post_tax']*factor, 2)
            ))
        return data

    @classmethod
    def get_website_itinerary(cls, rpa_req_data):
        """
        Get prices from website itinerary
        :param rpa_req_data:
        :return:
        """
        itinerary_backend = get_itinerary_backend(settings.SERVICE_CONFIG['itineray_nackend'])
        try:

            itinerary_dto = ItineraryDTO(data=dict(
                legal_entity_id=rpa_req_data['legal_entity_id'],
                hotel_id=rpa_req_data['hotel_id'],
                room_type=rpa_req_data['roomtype'],
                room_config=rpa_req_data['roomconfig'],
                check_in=rpa_req_data['checkin'],
                check_out=rpa_req_data['checkout'],
                utm_campaign=settings.UTM['campaign'],
                utm_medium=settings.UTM['medium'],
                utm_source=settings.UTM['source']
            ))
            itinerary_data = itinerary_backend.get_itinerary_data(itinerary_dto)

        except Exception as e:
            logger.info("Error occurred while getting itinerary data. Error {err}".format(err=str(e)))
            itinerary_data = {}

        return itinerary_data

    @classmethod
    def _convert_prices_to_float(cls, tax_data):
        """
        Convert prices to float values for response. Decimal field gets converted to string values for dto.data
        Args:
            tax_data:

        Returns:

        """
        for nb in tax_data['nights_breakup']:
            nb['pre_tax_price'] = round(float(nb['pre_tax_price']), 2)
            nb['post_tax_price'] = round(float(nb['post_tax_price']), 2)
            nb['tax'] = round(float(nb['tax']), 2)
        tax_data['total_tax'] = round(float(tax_data.pop('tax')), 2)
        tax_data['pretax_price'] = round(float(tax_data['pretax_price']), 2)
        tax_data['post_tax_price'] = round(float(tax_data['post_tax_price']), 2)
        return tax_data

    @classmethod
    def _get_room_details(cls, room_config, room_type):
        room_configs = room_config.split(',')
        config_details = []
        for config in room_configs:
            counts = config.split('-')
            config_details.append({
                'adults': counts[0],
                'children': counts[1]
            })
        return {
            'count': len(room_configs),
            'type': room_type.title(),
            'config': config_details
        }

    @classmethod
    def _get_price_details_from_website_itinerary(cls, website_itinerary):

        sell_price, tax_value, post_tax_price = 0, 0, 0

        try:
            if website_itinerary['total_price_breakup']:
                total_price_breakup = website_itinerary['total_price_breakup']
                if total_price_breakup['pre_tax']:
                    pre_price_breakup = total_price_breakup['pre_tax']
                    if pre_price_breakup['sell_price']:
                        sell_price = round(pre_price_breakup['sell_price'], 2)
                if total_price_breakup['tax']:
                    tax_breakup = total_price_breakup['tax']
                    if tax_breakup['total_value']:
                        tax_value = round(tax_breakup['total_value'], 2)
                if total_price_breakup['post_tax']:
                    post_tax_price = round((total_price_breakup['post_tax']), 2)

        except Exception as e:
            logger.info("Getting error {err} while retriving price details from po response".format(err=str(e)))

        return sell_price, tax_value, post_tax_price
