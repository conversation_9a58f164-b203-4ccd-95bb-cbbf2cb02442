import logging
import time

from b2b.domain.services import HotelService, CorporateService
from b2b.domain.services.availability import AvailabiltyService
from b2b.domain.services.ta_corporate import TAPricingService
from b2b.domain.utils.converters import rpa_to_ca
from b2b.models import LegalEntity
from b2b.models import Hotel

logger = logging.getLogger(__name__)


class CheapestRoomPricingServices(object):

    def __init__(self, legal_entity_id, room_config, check_in, check_out):
        try:
            self._legal_entity = LegalEntity.objects.get(legal_entity_id=legal_entity_id)
        except LegalEntity.DoesNotExist:
            logger.info(
                "Legal Entity does not exist for corp id {legal_entity_id}".format(legal_entity_id=legal_entity_id))
            raise

        self._ta_pricing_svc = TAPricingService(legal_entity_id, room_config, check_in, check_out)
        self._website_prices = {}

    def get_cheapest_room_pricing(self, room_pricing_availability_dto):
        """
        Gets cheapest room pricing for given list of hotels, checkin, checkout and room config
        Calculating just for single room type at this layer and not the combination of different
        room types to calculate the cheapest room type
        Calculation is based on pretax charges
        Args:
            room_pricing_availability_dto:

        Returns: Dictionary of cheapest room type and its price for all the given hotels
        sample json
        {
            u '12792':
                cheapest_room: {
                'room_type': 'Oak',
                'availability': 20
            },
            sell: 70
        }
        """
        # Get pricing for room types supported by the hotel
        start_time = time.time()
        hotels_pricing = HotelService.get_room_pricing(room_pricing_availability_dto)
        logger.info("HotelService.get_room_pricing: {s}s".format(s=time.time() - start_time))

        rpa_data = room_pricing_availability_dto.data

        hotel_ids = rpa_data['hotel_ids'].split(',')
        check_availability_dto = rpa_to_ca(room_pricing_availability_dto)

        # Get hotels availability
        start_time = time.time()
        hotels_availability = AvailabiltyService.get_availability(check_availability_dto)
        logger.info("AvailabiltyService.get_availability: {s}s".format(s=time.time() - start_time))

        ta_pricing_applicable = self._legal_entity.is_web_pricing_for_ta_or_tmc_applicable()
        corporate_service = CorporateService()

        if self._legal_entity.is_web_prices_enabled() or ta_pricing_applicable:
            try:
                self._website_prices = HotelService.get_website_hotel_price(hotel_ids, rpa_data['checkin'],
                                                                            rpa_data['checkout'],
                                                                            rpa_data['roomconfig'],
                                                                            ta_pricing_applicable)
            except Exception as e:
                logger.info("Getting Error {e} while fetching website price from PO".format(e=str(e)))
                self._website_prices = {}

        # Get cheapest room price
        cheapest_room_pricing = {}
        for hotel, pricings in list(hotels_pricing.items()):
            room_prices = {}
            is_legal_entity_brand_blacklist, is_legal_entity_city_blacklist, is_legal_entity_hotel_blacklist = \
                corporate_service.is_legal_entity_blacklisted(self._legal_entity.legal_entity_id,
                                                              hotel)
            hotel_availability = None

            if not (
                    is_legal_entity_brand_blacklist or is_legal_entity_city_blacklist or is_legal_entity_hotel_blacklist):
                hotel_availability = hotels_availability['data'].get(hotel)

            tcr_multiplier = Hotel.objects.filter(hotel_id=hotel).values('tcr_multiplier')[0]['tcr_multiplier']
            if not tcr_multiplier:
                tcr_multiplier = None

            for room, room_pricing in list(pricings.items()):
                if hotel_availability and hotel_availability.get(room):
                    room_price = sum([room_pricing[room_config]['pre_tax_charges'] for
                                      room_config in rpa_data['roomconfig'].split(',')])

                    room_prices[room] = room_price
            if room_prices:
                cheapest_room_type = min(room_prices, key=room_prices.get)
                room_type, room_price = self._get_valid_corp_price(cheapest_room_type,
                                                                   room_prices[cheapest_room_type],
                                                                   self._website_prices.get(hotel))
                room_type = room_type.capitalize()
                cheapest_room_pricing[hotel] = {
                    'cheapest_room': {
                        'corp': {
                            'type': room_type,
                            'price': room_price
                        },
                        'website': self._ta_pricing_svc.get_website_cheapest_price(self._website_prices.get(hotel))
                    },
                    'availability': hotel_availability[cheapest_room_type],
                    'tcr_multiplier': tcr_multiplier
                }
            else:
                cheapest_room_pricing[hotel] = {
                    'cheapest_room': {
                        'corp': {},
                        'website': {}
                    },
                    'availability': False
                }

        return cheapest_room_pricing

    def _get_valid_corp_price(self, room_type, room_price, website_price):
        """
        It will return the cheapest corporate price
        In case of TA, it will return the cheapest website price
        """

        website_room_type, website_room_price = None, None

        if website_price:
            website_room_type, website_room_price = self._get_website_cheapest_price(website_price)

        if self._legal_entity.is_web_pricing_for_ta_or_tmc_applicable():
            website_room_price = self._ta_pricing_svc.get_discounted_price(website_room_price)
            room_price = self._ta_pricing_svc.apply_room_night_discount(website_room_price)
            room_type = website_room_type

        return room_type, room_price

    def _get_website_cheapest_price(self, website_price_details):
        try:
            website_room_price_details = website_price_details['room_wise_price']
            website_room_type = list(website_room_price_details.keys())[0]
            website_room_price_breakup = list(website_room_price_details.values())[0][0]
            website_room_total_price_breakup = website_room_price_breakup['total_price_breakup']
            website_room_pretax_price_breakup = website_room_total_price_breakup['pre_tax']
            website_room_sell_price = website_room_pretax_price_breakup['sell_price']
        except Exception as e:
            logger.info(
                "Getting error {err} while extracting price from cheapest availability response {price_details}".format(
                    price_details=website_price_details, err=str(e)))
            raise Exception

        return website_room_type, website_room_sell_price
