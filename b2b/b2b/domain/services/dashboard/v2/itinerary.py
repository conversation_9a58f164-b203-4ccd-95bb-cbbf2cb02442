# pylint: disable=invalid-name,too-many-locals,unused-variable

import logging

from django.conf import settings

from b2b.constants import HotelAttributes as HotelAttributesConstants
from b2b.constants import Tax
from b2b.consumer.itinerary import get_itinerary_backend
from b2b.domain.services.hotel import HotelService
from b2b.domain.services.ta_corporate import TAPricingService
from b2b.domain.services.tax import TaxService
from b2b.domain.utils import DTOExchange
from b2b.domain.utils.date_utils import count_room_nights
from b2b.dto import HotelDetailsDTO
from b2b.dto import RoomPricingAvailabilityDTO
from b2b.dto import TaxRequestDTO
from b2b.dto.itinerary import ItineraryDTO
from b2b.models import HotelAttribute, Hotel, LegalEntity

logger = logging.getLogger(__name__)


class ItineraryService(object):
    """
    Get and construct dict for itinerary api used for dashboard
    """

    def __init__(self, legal_entity_id, room_config, check_in, check_out):

        try:
            self._legal_entity = LegalEntity.objects.get(legal_entity_id=legal_entity_id)
        except LegalEntity.DoesNotExist:
            logger.info("Legal entity does not exist for corp id {legal_entity_id}".format(legal_entity_id=legal_entity_id))
            raise
        self._ta_pricing_svc = TAPricingService(legal_entity_id, room_config, check_in, check_out)
        self._website_price = {}

    def get_itinerary_data(self, itinerary_request_dto):
        if not itinerary_request_dto.is_valid():
            logger.info("Invalid Itinerary DTO")
            raise Exception('Invalid Itinerary DTO')

        rpa_req_data = itinerary_request_dto.data
        hotel_id = rpa_req_data['hotel_id']
        hd_dto = HotelDetailsDTO(data=dict(hotels=hotel_id))
        hotels_details_response = HotelService.get_hotel_details(hd_dto)
        hotel_details_response = hotels_details_response['data'][0]

        hotel = Hotel.objects.get(hotel_id=hotel_id)
        factor = self._ta_pricing_svc.get_ta_discount_factor()

        try:
            hotel_attribute = HotelAttribute.objects.get(key=HotelAttributesConstants.LEGAL_STATE_CODE,
                                                         hotel_id=hotel.id)
            legal_state_code = hotel_attribute.value
        except HotelAttribute.DoesNotExist as e:
            logger.info('HotelAttribute LEGAL_STATE_CODE does not exist for hotel {hotel}'.format(hotel=hotel_id))
            legal_state_code = None

        hotel_details = {
            'name': hotel_details_response['name'],
            'id': hotel_id,
            'image_url': hotel_details_response.get('image_url', ''),
            'legal_state_code': legal_state_code
        }
        hotel_details.update(hotel_details_response['address'])
        room_pricing_availability_dto = RoomPricingAvailabilityDTO(data=dict(
                checkin=rpa_req_data['checkin'],
                checkout=rpa_req_data['checkout'],
                roomconfig=rpa_req_data['roomconfig'],
                hotel_ids=rpa_req_data['hotel_id'],
                legal_entity_id=rpa_req_data['legal_entity_id']
            ))

        room_pricing = HotelService.get_room_pricing(room_pricing_availability_dto)
        room_pricing = room_pricing[rpa_req_data['hotel_id']][rpa_req_data['roomtype'].title()]
        btc_room_pricing = self.extract_btc_room_pricing(room_pricing)

        tax_req_dto = DTOExchange.convert(itinerary_request_dto, TaxRequestDTO,
                                          room_pricing=room_pricing,
                                          charge_type=Tax.RoomNight)
        btc_req_dto = DTOExchange.convert(itinerary_request_dto, TaxRequestDTO,
                                          room_pricing=btc_room_pricing,
                                          charge_type=Tax.RoomNight)
        tax_dto = TaxService.get_tax(tax_req_dto)
        btc_tax_dto = TaxService.get_tax(btc_req_dto)
        if not tax_dto.is_valid():
            raise RuntimeError('Invalid Tax DTO')

        if not btc_tax_dto.is_valid():
            raise RuntimeError('Invalid BTC Tax DTO')

        price_details = self._convert_prices_to_float(tax_dto.data, btc_tax_dto.data)
        nights = count_room_nights(rpa_req_data['checkin'], rpa_req_data['checkout'])

        legal_entity = LegalEntity.objects.get(legal_entity_id=rpa_req_data['legal_entity_id'])

        try:
            self._legal_entity = LegalEntity.objects.get(legal_entity_id=rpa_req_data['legal_entity_id'])
        except LegalEntity.DoesNotExist:
            logger.info(
                "Legal entity does not exist for legal entity id {legal_entity_id}".format(legal_entity_id=rpa_req_data['legal_entity_id']))
            raise

        # If corporate is TA then show min prices between website and B2B
        if self._legal_entity.is_web_pricing_for_ta_or_tmc_applicable():
            price_details = self._itinerary_price(rpa_req_data, price_details)

        itinerary_data = {
            'hotel': hotel_details,
            'price': {'corp': price_details, 'website': {}},
            'nights': nights,
            'date': {
                'checkin': rpa_req_data['checkin'],
                'checkout': rpa_req_data['checkout']
            },
            'room': self._get_room_details(rpa_req_data['roomconfig'], rpa_req_data['roomtype']),
            'btc_allowed': legal_entity.btc_enabled,
            'hotel_saleable': hotel.is_saleable(),
            'is_dual_price_enabled': legal_entity.is_dual_price_applicable()
        }

        if not self._legal_entity.is_web_pricing_for_ta_or_tmc_applicable() and self._legal_entity.is_web_prices_enabled():
            if not self._website_price:
                self._website_price = self.get_website_itinerary(itinerary_request_dto.data)
            itinerary_data['price']['website'] = self._convert_website_itinerary_data(self._website_price, factor)

        return itinerary_data

    def _itinerary_price(self, rpa_req_data, corp_price_details):
        self._website_price = self.get_website_itinerary(rpa_req_data)
        factor = self._ta_pricing_svc.get_ta_discount_factor()

        web_pretax_amount = self._get_price_details_from_website_itinerary(
            self._website_price)[0]

        if self._website_price and web_pretax_amount:
            corp_price_details = self._convert_website_itinerary_data(self._website_price, factor)

        corp_price_details = self._apply_room_night_discount(corp_price_details)

        return corp_price_details

    def _apply_room_night_discount(self, corp_price_details):
        if not corp_price_details:
            return None
        rnd_factor = self._ta_pricing_svc.room_night_discount_factor()
        corp_price_details['pretax_price'] = round(rnd_factor * corp_price_details['pretax_price'], 2)
        corp_price_details['post_tax_price'] = round(rnd_factor * corp_price_details['post_tax_price'], 2)
        corp_price_details['total_tax'] = round(rnd_factor * corp_price_details['total_tax'], 2)

        for night_breakup in corp_price_details['nights_breakup']:
            night_breakup['tax'] = round(rnd_factor * night_breakup['tax'], 2)
            night_breakup['pre_tax_price'] = round(rnd_factor * night_breakup['pre_tax_price'], 2)
            night_breakup['post_tax_price'] = round((night_breakup['pre_tax_price'] + night_breakup['tax']), 2)

        return corp_price_details

    def _convert_website_itinerary_data(self, website_itinerary, factor):
        # keys are different in website itineray data, convert it into corporate data key
        if not website_itinerary:
            return None

        pretax_amount, tax_amount, post_tax_amount = self._get_price_details_from_website_itinerary(website_itinerary)

        data = dict(
            pretax_price=pretax_amount * factor,
            total_tax=tax_amount * factor,
            post_tax_price=post_tax_amount * factor,
            nights_breakup=[]
        )

        for night_breakup in website_itinerary['date_wise_price_breakup']:
            data['nights_breakup'].append(dict(
                tax=round(night_breakup['tax']['total_value'] * factor, 2),
                date=night_breakup['date'],
                pre_tax_price=round(night_breakup['pre_tax']['sell_price'] * factor, 2),
                post_tax_price=round(night_breakup['post_tax'] * factor, 2)
            ))

        return data

    def get_website_itinerary(self, rpa_req_data):
        """
        Get prices from website itinerary
        :param rpa_req_data:
        :return:
        """
        itinerary_backend = get_itinerary_backend(settings.SERVICE_CONFIG['itineray_nackend'])
        try:

            itinerary_dto = ItineraryDTO(data=dict(
                legal_entity_id=rpa_req_data['legal_entity_id'],
                hotel_id=rpa_req_data['hotel_id'],
                room_type=rpa_req_data['roomtype'],
                room_config=rpa_req_data['roomconfig'],
                check_in=rpa_req_data['checkin'],
                check_out=rpa_req_data['checkout'],
                utm_campaign=settings.UTM['campaign'],
                utm_medium=settings.UTM['medium'],
                utm_source=settings.UTM['source'],
                gstin=rpa_req_data['gstin']
            ))
            itinerary_data = itinerary_backend.get_itinerary_data(itinerary_dto)

        except Exception as e:
            logger.info("Error occurred while getting itinerary data. Error {err}".format(err=str(e)))
            itinerary_data = {}

        return itinerary_data

    def _convert_prices_to_float(self, tax_data, btc_tax_data):
        """
        Convert prices to float values for response. Decimal field gets converted to string values for dto.data
        Args:
            tax_data:

        Returns:

        """
        direct_nights_breakup_price_data = {}
        for nb in tax_data['nights_breakup']:
            direct_nights_breakup_price_data.update({nb.get('date'): {
                'pre_tax_price': round(float(nb['pre_tax_price']), 2),
                'post_tax_price': round(float(nb['post_tax_price']), 2),
                'tax': round(float(nb['tax']), 2)}
            })
        composite_nights_breakup_price_data = []
        for nb in btc_tax_data['nights_breakup']:
            composite_nights_breakup_price_data.append({
                'date': nb['date'],
                'btc_pre_tax_price': round(float(nb['pre_tax_price']), 2),
                'btc_post_tax_price': round(float(nb['post_tax_price']), 2),
                'btc_tax': round(float(nb['tax']), 2),
                'pre_tax_price': direct_nights_breakup_price_data.get(nb['date']).get('pre_tax_price'),
                'post_tax_price': direct_nights_breakup_price_data.get(nb['date']).get('post_tax_price'),
                'tax': direct_nights_breakup_price_data.get(nb['date']).get('tax')
            })
        tax_data['nights_breakup'] = composite_nights_breakup_price_data
        tax_data['total_tax'] = round(float(tax_data.pop('tax')), 2)
        tax_data['pretax_price'] = round(float(tax_data['pretax_price']), 2)
        tax_data['post_tax_price'] = round(float(tax_data['post_tax_price']), 2)
        tax_data['btc_total_tax'] = round(float(btc_tax_data.pop('tax')), 2)
        tax_data['btc_pretax_price'] = round(float(btc_tax_data['pretax_price']), 2)
        tax_data['btc_post_tax_price'] = round(float(btc_tax_data['post_tax_price']), 2)
        return tax_data

    def _get_room_details(self, room_config, room_type):
        room_configs = room_config.split(',')
        config_details = []
        for config in room_configs:
            counts = config.split('-')
            config_details.append({
                'adults': counts[0],
                'children': counts[1]
            })
        return {
            'count': len(room_configs),
            'type': room_type.title(),
            'config': config_details
        }

    def _get_price_details_from_website_itinerary(self, website_itinerary):

        sell_price, tax_value, post_tax_price = 0, 0, 0
        try:
            if website_itinerary['total_price_breakup']:
                total_price_breakup = website_itinerary['total_price_breakup']
                if total_price_breakup['pre_tax']:
                    pre_price_breakup = total_price_breakup['pre_tax']
                    if pre_price_breakup['sell_price']:
                        sell_price = round(pre_price_breakup['sell_price'], 2)
                if total_price_breakup['tax']:
                    tax_breakup = total_price_breakup['tax']
                    if tax_breakup['total_value']:
                        tax_value = round(tax_breakup['total_value'], 2)
                if total_price_breakup['post_tax']:
                    post_tax_price = round((total_price_breakup['post_tax']), 2)

        except Exception as e:
            logger.info("Getting error {err} while retriving price details from po response".format(err=str(e)))

        return sell_price, tax_value, post_tax_price

    def extract_btc_room_pricing(self, room_pricing):
        return {room_config: {'pre_tax_charges': price_detail.get('btc_pre_tax_charges'),
                              'post_tax_charges': price_detail.get('btc_post_tax_charges')}
                for room_config, price_detail in list(room_pricing.items())}
