# -*- coding: utf-8 -*-
import requests
import json
import logging

from django.conf import settings

from b2b.constants import Timeouts

logger = logging.getLogger(__name__)

class EposService:

    @classmethod
    def get_voucher_code(cls, booking_id):
        try:
            logger.info("Calling EPOS for voucher generation of booking_id : {booking_id}".format(booking_id=booking_id))
            res = requests.post(settings.EPOS_URL + '/voucher_code/generate/',
                                data=json.dumps({'booking_id': str(booking_id)}),
                                headers={'Content-Type': 'application/json'},timeout=(Timeouts.CONNECTION_TIMEOUT,Timeouts.RESPONSE_TIMEOUT))

            if res.status_code != 200:
                logger.info("Epos Voucher creation failed for booking {booking_id}, with response {resp_text}".format(booking_id=booking_id, resp_text=res.text))
                return None
        except Exception as e:
            logger.info(e)
            return None

        return json.loads(res.content)['data']['voucher_code']
