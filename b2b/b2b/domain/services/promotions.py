from b2b.models import PromotedHotelAttribute
from b2b.constants import HotelPromotions
import time
import logging
from b2b.models import PromotedHotel


class PromotionService(object):

    @classmethod
    def get_promotion_attribute(cls, key):
        today = time.strftime("%Y-%m-%d")
        promotions = PromotedHotelAttribute.objects.filter(key=key, end_date__gte=today)
        if promotions:
            promotion = promotions[0]
            return {
                "key": promotion.key,
                "value": promotion.value
            }
        return None

    @classmethod
    def get_incentive_factor(cls, hotel_id):
        """

        Args:
            hotel_id: b2b hotel id

        Returns: Sales incentive factor

        """
        try:
            assert isinstance(hotel_id, str)
        except AssertionError as asr_exp:
            return None
        logger = logging.getLogger(cls.__name__)
        try:
            promoted_hotel = PromotedHotel.objects.get(hotel_id=hotel_id)
        except PromotedHotel.DoesNotExist as exp:
            logger.info("No entry found for promoted hotel id {hid} due "
                             "to exception {exp}".format(hid=hotel_id, exp=exp))
            return None
        return promoted_hotel.sales_poc_incentive_factor


    @classmethod
    def get_distance_factor(cls):
        promotion_attrib = cls.get_promotion_attribute(HotelPromotions.DistancePromotedHotels)
        if promotion_attrib:
            return promotion_attrib['value']

    @classmethod
    def get_incentive_factor_bulk(cls, hotels):
        try:
            assert isinstance(hotels, list)
        except AssertionError as asr_exp:
            return {}

        logger = logging.getLogger(cls.__name__)
        promoted_hotels = PromotedHotel.objects.filter(hotel_id__in=[str(h['hotel_id']) for h in hotels]).all()

        return {hotel.hotel_id: hotel.sales_poc_incentive_factor for hotel in promoted_hotels}
