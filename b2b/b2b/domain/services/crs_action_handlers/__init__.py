# -*- coding: utf-8 -*-

"""
CRS Action Handlers

Sync Handler:
    Handles everything synchronously
    Everything might take ages to complete, but is easier to debug

Async Handler:
    Used to handle slow running CRS actions like confirming booking, registering a corporate etc
    via an asynchronous route, instead of holding up a caller in synchronous call
"""

from .async_crs_action_handler import AsyncCRSActionHandler
from .sync_crs_action_handler import SyncCR<PERSON>ctionHandler

from b2b.constants import Booking



def get_handler(booking_async=False):
    """
    get appropriate handler for sync or asynchronous way of handling
    :param asynchronous: use asynchronous handler? True or False
    :return: sync or asynchronous handler (CRSActionHandler)
    """
    return AsyncCR<PERSON>ction<PERSON>and<PERSON> if booking_async else SyncCRSActionHandler

def get_handler_crs_type(crs_type, booking_async=False):
    return 'thsc_backend', SyncCRSActionHandler




