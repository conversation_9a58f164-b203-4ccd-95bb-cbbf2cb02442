# -*- coding: utf-8 -*-
import logging

from b2b.dto import MealPriceDTO
from b2b.dto import MealPriceRequestDTO
from .base import BaseMealPricingStrategy


class MPSv1(BaseMealPricingStrategy):
    def __init__(self):
        self._selectors = []

    def get_meal_plan(self, meal_price_request_dto):
        assert type(meal_price_request_dto) is MealPriceRequestDTO
        if not meal_price_request_dto.is_valid():
            raise RuntimeError('Invalid MealPriceRequestDTO received for determining meal-plan')

        logger = logging.getLogger(self.__class__.__name__)

        # todo: add meal price selectors later, and remove this
        logger.info('FIXED MEAL PRICES')

        mpr_data = meal_price_request_dto.data

        return MealPriceDTO(data=dict(
            legal_entity_id=mpr_data['legal_entity_id'],
            hotel=mpr_data['hotel'],

            from_date=mpr_data['from_date'],
            to_date=mpr_data['to_date'],

            meal_plan=mpr_data['meal_plan'],
            meal_type=mpr_data['meal_type'],

            pre_tax_price=0,
            post_tax_price=0,

            audit_log=['FIXED PRICES'],
        ))
