from datetime import datetime, timedelta
from django.conf import settings


def count_room_nights(from_date, to_date, date_format=None):
    """
    given a date-range, calculates number of room-nights

    :param from_date: date-string in the format set in setting
    :param to_date: date-string in the format set in setting
    :return: number of room nights (int)
    """

    if date_format is None:
        date_format = settings.BOOKING['date_format']

    f = datetime.strptime(to_date, date_format)
    t = datetime.strptime(from_date, date_format)

    if t < f:
        f, t = t, f

    delta = t - f

    return delta.days


def stay_room_nights(room_config, from_date, to_date, date_format=None):
    """
    given a date-range and total rooms booked, calculates number of room-nights

    :param rooms: number of rooms booked
    :param from_date: date-string in the format set in setting
    :param to_date: date-string in the format set in setting
    :return: number of room nights (int)
    """
    rooms_nights = count_room_nights(from_date, to_date, date_format)

    rooms = len(room_config.split(',')) if room_config else 0

    return rooms_nights * rooms


def get_intermediate_dates(date1, date2):
    """
    Get dates between two dates
    Args:
        date1: Start Date string in 'yyyy-mm-dd' format
        date2: End Date string in 'yyyy-mm-dd' format

    Returns: List of string representation of dates between date1 and date2

    """
    date1 = datetime.strptime(date1, settings.BOOKING['date_format'])
    date2 = datetime.strptime(date2, settings.BOOKING['date_format'])

    delta = date2 - date1

    return [date1 + timedelta(days=i) for i in range(delta.days)]
