# -*- coding: utf-8 -*-

from django.db import models
from djutil.models import TimeStampedModel

from b2b import constants
from b2b.models import LegalEntity
from b2b.models import Hotel
from b2b.models import User
from b2b.models.mixins import DefaultPermissions


class CustomRoomPricing(TimeStampedModel, DefaultPermissions):
    legal_entity = models.ForeignKey(LegalEntity, on_delete=models.CASCADE)
    hotel = models.ForeignKey(Hotel, on_delete=models.CASCADE)
    room_type = models.CharField(max_length=50, choices=constants.Rooms.TYPES)

    # stay dates
    from_date = models.DateField(null=True, blank=True)
    to_date = models.DateField(null=True, blank=True)

    # plan constraints
    min_length_of_stay = models.PositiveIntegerField(default=1)
    max_length_of_stay = models.PositiveIntegerField(null=True, blank=True)
    # todo: should city be a choice-field? or validated against a list of valid cities
    city = models.CharField(max_length=50, null=True, blank=True)

    # price
    # todo: validate custom_price is specified if slab is set to custom
    price_slab = models.CharField(choices=constants.Pricing.SLABS,
                                  default=constants.Pricing.CUSTOM,
                                  max_length=50)

    # custom price
    pre_tax_custom_price = models.DecimalField(max_digits=20, decimal_places=2)
    post_tax_custom_price = models.DecimalField(max_digits=20, decimal_places=2, default=0)

    created_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)

    ep_pre_tax_custom_price = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    # todo: we need to validate at least one of the custom-pricing constraints is specified
    # todo: maybe this validation can happen as part of admin form validation

    class Meta(DefaultPermissions.Meta):
        get_latest_by = 'modified_at'

    def __str__(self):
        return f"{self.hotel.hotel_id} {self.room_type}"
