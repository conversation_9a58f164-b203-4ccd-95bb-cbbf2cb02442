from django.db import models
from django.utils.translation import ugettext_lazy as _

from b2b.models import LegalEntity
from b2b.models import User
from .mixins import DefaultPermissions, CustomTimeStampMixin


class LegalEntityAdmin(CustomTimeStampMixin, DefaultPermissions):
    legal_entity = models.ForeignKey(LegalEntity, on_delete=models.CASCADE)
    auth_user = models.ForeignKey(User, on_delete=models.CASCADE)

    # these are comma separated list of b2b internal hotel-ids
    # todo: this mechanism is temporary, we need to think of some better way to do this
    default_recommended_hotels = models.CharField(max_length=200, default='')

    loyalty_enabled = models.BooleanField(_('loyalty enabled'), default=True, help_text=_(
        'Designates whether loyalty is enabled for this user or not. '
        'Unselect this instead of deleting accounts.'))

    class Meta(DefaultPermissions.Meta):
        unique_together = ('legal_entity', 'auth_user')

    def __str__(self):
        return "{legal_entity} | {user}".format(legal_entity=self.legal_entity.name, user=self.auth_user.email)
