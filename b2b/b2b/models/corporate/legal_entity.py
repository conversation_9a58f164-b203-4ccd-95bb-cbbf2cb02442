# -*- coding: utf-8 -*-
# pylint: disable=fixme
import re
import warnings

from b2b.domain.utils.misc import safe_validate_email
from django.conf import settings
from django.core.exceptions import MultipleObjectsReturned
from django.db import models
from cached_property import cached_property

from b2b import constants
from b2b.models.mixins import DefaultPermissions, CustomTimeStampMixin
from b2b.models import Address
from b2b.models.exceptions import CorporateOnOldPricingScheme
from b2b.models.user import User
from b2b.constants import CorporateAttributes, RecordChangeReason
from core.booking.data_classes.payment_type import CorpPaymentTypes, TaPaymentTypes
from b2b.models.corporate import AccountAdmin


class LegalEntity(CustomTimeStampMixin, DefaultPermissions):
    """
    Legal Entities
    """
    legal_entity_id = models.CharField(max_length=20, unique=True, db_index=True)
    name = models.CharField(max_length=200)
    # TODO: Reomve the blank and null after ingesting data for existing Legal Entities.
    address = models.ForeignKey(blank=True, to=Address, null=True, on_delete=models.SET_NULL)
    slab_type = models.CharField(max_length=200, null=True)
    btc_admin_approval = models.BooleanField(default=False, null=False,
                                             help_text='check this if btc approval is needed for corporate')
    show_web_prices = models.BooleanField(default=False,
                                          help_text='Use this to enable to show the website prices for this corp.')
    treebo_poc = models.ForeignKey(User, related_name='treebo_poc', null=True, on_delete=models.SET_NULL)
    inside_sales_poc = models.ForeignKey(User, related_name='inside_sales_poc', null=True, on_delete=models.SET_NULL)
    posttax_enabled = models.BooleanField(default=False, null=False)
    btc_discount_slab = models.CharField(max_length=200, null=True)
    active = models.BooleanField(default=True,
                                 help_text='Use this instead of deleting. Inactive legal entities '
                                           'will show up nowhere in the system.')
    is_test = models.BooleanField(default=False,
                                  help_text='mark this true, if legal_entity is created for testing purpose')

    class Meta(DefaultPermissions.Meta):
        verbose_name = 'Legal Entities'
        verbose_name_plural = 'Legal Entities'
        db_table = 'b2b_corporate_legal_entity'

    def __str__(self):
        return "{name} | {legal_entity_id}".format(name=self.name, legal_entity_id=self.legal_entity_id)

    def gstin(self):
        return self.gstin_set.first()

    def gstins(self):
        return [dict(gstin=gstin.gstin,
                     addresses=gstin.addresses(),
                     default=gstin.default,
                     uid=gstin.id,
                     number=gstin.gstin) for gstin in
                self.gstin_set.all()]

    @cached_property
    def gstin_address_dict(self):
        gstin = self.gstin()
        return gstin.addresses()[0] if gstin and gstin.addresses() else None

    def gstin_address(self):
        if self.gstin_address_dict:
            filtered_data = {key: value for key, value in self.gstin_address_dict.items() if key in
                             [field.name for field in Address._meta.fields]}
            return Address(**filtered_data)
        return None

    @cached_property
    def gstin_number(self):
        gstin = self.gstin()
        return gstin and gstin.gstin

    def get_attr(self, key):
        try:
            attrib = self.legalentityattributes_set.get(key=key)
            return attrib.value
        except MultipleObjectsReturned:
            attrib = self.legalentityattributes_set.filter(key=key).latest('id')
            return attrib.value
        except:
            return None

    def set_attr(self, key, value, via_admin=False):
        attrib, created = self.legalentityattributes_set.get_or_create(key=key)
        attrib.value = value
        if via_admin:
            if created:
                attrib._change_reason = RecordChangeReason.CREATED_VIA_ADMIN
            else:
                attrib._change_reason = RecordChangeReason.UPDATED_VIA_ADMIN
        attrib.save()

    def create_or_update_corporate_mapping(self, corp_id, change):
        if not change:
            corp_mapping = self.corporatelegalentity_set.create(legal_entity_id=self.id,
                                                                corporate_id=corp_id)
            corp_mapping.save()

    def get_discount_on_b2b_rack_rate(self):
        """
        checks if the slab type is one of b2brack or b2brack@xx%
        if it is, returns the discount percentage from xx%
        :return: b2b rack discount percentage
        """
        if self.slab_type:
            matches = re.match(r"([Bb]2[Bb][Rr]ack(?:@(?P<discount>\d+(?:\.\d+)?)%)?)", self.slab_type)
            if matches is None:
                raise CorporateOnOldPricingScheme(self.legal_entity_id)

            return round(float(matches.group('discount')), 2)
        return None

    @property
    def credit_limit(self):
        val = self.get_attr(constants.CorporateAttributes.CREDIT_LIMIT)
        if val is None:
            return 0
        return int(val)

    @property
    def amount_received(self):
        val = self.get_attr(constants.CorporateAttributes.AMOUNT_RECEIVED)
        if val is None:
            return 0
        return int(val)

    @property
    def stay_summary_aggregation_criteria(self):
        return self.get_attr(constants.CorporateAttributes.STAY_SUMMARY_AGGREGATION_CRITERIA)

    @property
    def btc_enabled(self):
        val = self.get_attr(constants.CorporateAttributes.BTC_ENABLED)
        if val is None:
            return False
        return True if int(val) else False

    def is_web_prices_enabled(self):
        return self.show_web_prices and \
            (self.is_sme() or self.is_corporate() or self.is_local_corporate() or self.is_b2b_bulk() or self.is_tmc)

    def is_web_pricing_for_ta_or_tmc_applicable(self):
        return self.is_bulk_ta() or self.is_local_ta() or self.is_b2b_ta()

    @cached_property
    def agency_type(self):
        return self.get_attr(constants.CorporateAttributes.AgencyTypes.KEY)

    def is_sme(self):
        return self.agency_type == constants.CorporateAttributes.AgencyTypes.SME

    def is_corporate(self):
        return self.agency_type == constants.CorporateAttributes.AgencyTypes.CORPORATE

    def is_local_corporate(self):
        return self.agency_type == constants.CorporateAttributes.AgencyTypes.LOCAL_CORPORATE

    def is_b2b_bulk(self):
        return self.agency_type == constants.CorporateAttributes.AgencyTypes.B2B_BULK

    @property
    def is_tmc(self):
        val = self.get_attr(constants.CorporateAttributes.AgencyTypes.KEY)
        return val in constants.CorporateAttributes.AgencyTypes.TMC_CATEGORIES

    @property
    def available_payment_types(self):
        if self.is_ta():
            pay_types = TaPaymentTypes
        else:
            pay_types = CorpPaymentTypes

        if not self.btc_enabled:
            pay_types = [item for item in pay_types if not item.is_credit_type]

        return pay_types

    def is_ta(self):
        val = self.get_attr(constants.CorporateAttributes.AgencyTypes.KEY)
        return val in constants.CorporateAttributes.AgencyTypes.TA_CATEGORIES

    def is_local_ta(self):
        val = self.get_attr(constants.CorporateAttributes.AgencyTypes.KEY)
        return val == constants.CorporateAttributes.AgencyTypes.LOCAL_TA

    def is_bulk_ta(self):
        val = self.get_attr(constants.CorporateAttributes.AgencyTypes.KEY)
        return val == constants.CorporateAttributes.AgencyTypes.TA_BULK

    def is_b2b_ta(self):
        val = self.get_attr(constants.CorporateAttributes.AgencyTypes.KEY)
        return val == constants.CorporateAttributes.AgencyTypes.B2B_TA

    def is_loyalty_enabled(self):
        val = self.get_attr(constants.CorporateAttributes.LOYALTY_ENABLED)
        if val == '0' or val is None:
            agency_type = self.get_attr('agency_type')
            if agency_type in settings.ALLOWED_AGENCY_TYPE_FOR_LOYALTY:
                return True
            return False

        return val == '2'

    @property
    def booking_request_attachment_reminder_enabled(self):
        val = self.get_attr(constants.CorporateAttributes.BOOKING_REQUEST_ATTACHMENT_REMINDER_ENABLED)
        if val is None:
            return False
        return True if int(val) else False

    @property
    def booking_request_attachment_reminder_period(self):
        val = self.get_attr(constants.CorporateAttributes.STAY_SUMMARY_BOOKING_REQUEST_ATTACHMENT_ENABLED)
        if val is None:
            return None
        return int(val)

    @property
    def credit_period(self):
        val = self.get_attr(constants.CorporateAttributes.CREDIT_PERIOD)
        if not val:
            return 0
        return int(val)

    @property
    def tac_commission(self):
        val = self.get_attr(constants.CorporateAttributes.TAC_COMMISSION)
        if not val:
            return 0
        return int(val)

    @property
    def post_commission_amount(self):
        val = self.get_attr(constants.CorporateAttributes.POST_COMMISSION_AMOUNT)
        return False if val == '0' else True

    @property
    def invoice_dispatch_option_on_checkout(self):
        return self.get_attr(constants.CorporateAttributes.INVOICE_DISPATCH_OPTION_ON_CHECKOUT)

    @property
    def invoice_dispatch_option_with_stay_summary(self):
        return self.get_attr(constants.CorporateAttributes.INVOICE_DISPATCH_OPTION_WITH_STAY_SUMMARY)

    @property
    def should_dispatch_booking_request_with_stay_summary(self):
        val = self.get_attr(constants.CorporateAttributes.SHOULD_DISPATCH_BOOKING_REQUEST_WITH_STAY_SUMMARY)
        return False if val == '0' else True

    @property
    def billing_period(self):
        return self.get_attr(constants.CorporateAttributes.BILLING_PERIOD)

    @property
    def primary_admin(self):
        return getattr(self.account, 'primary_admin', None)

    @property
    def finance_admin(self):
        return getattr(self.account, 'primary_finance_admin', None)

    @cached_property
    def corporate(self):
        return self.corporatelegalentity_set.first().corporate

    @property
    def stay_summary_booking_request_attachment_enabled(self):
        val = self.get_attr(constants.CorporateAttributes.STAY_SUMMARY_BOOKING_REQUEST_ATTACHMENT_ENABLED)
        if val is None:
            return False
        return True if int(val) else False

    @property
    def tmc_commission_percentage(self):
        warnings.warn('Deprecated tmc_commission_percentage in favour of tac_commission. Remove the usage if found')
        val = self.get_attr(constants.CorporateAttributes.TAC_COMMISSION)
        if not val:
            return 0
        return int(val)

    @property
    def uid(self):
        return self.legal_entity_id

    def is_corp_on_new_pricing_scheme(self):
        """
        new pricing scheme works by specifying discounts on the b2b rack rate

        :return: True if the corp slab-type is set in '<EMAIL>%' format; False otherwise
        """
        try:
            # if we can get b2b-rack-rate-discount, the corporate is on new pricing scheme
            self.get_discount_on_b2b_rack_rate()

        except CorporateOnOldPricingScheme:
            return False

        return True

    def get_btc_discount_on_b2b_rack_rate(self):
        """
        checks if the slab type is one of b2brack or b2brack@xx%
        if it is, returns the discount percentage from xx%
        :return: b2b rack discount percentage
        """
        if self.btc_enabled and self.btc_discount_slab:
            matches = re.match(r"([Bb]2[Bb][Rr]ack(?:@(?P<discount>\d+(?:\.\d+)?)%)?)", self.btc_discount_slab)
            if matches is None:
                raise CorporateOnOldPricingScheme(self.legal_entity_id)

            return round(float(matches.group('discount')), 2)
        return None

    def is_dual_price_applicable(self):
        is_ta = self.is_ta()
        direct_slab = self.get_discount_on_b2b_rack_rate()
        btc_slab = self.get_btc_discount_on_b2b_rack_rate()
        return not is_ta and direct_slab != btc_slab

    def get_voucher_emails(self):
        admin_list = list(AccountAdmin.objects.filter(account__accountlegalentity__legal_entity=self).values_list(
            'auth_user__email', flat=True).distinct())
        treebo_poc_emails = [self.account_treebo_poc.email] if self.account_treebo_poc else []
        return [email for email in admin_list + treebo_poc_emails if safe_validate_email(email)]

    def get_ta_discount(self):
        if self.is_web_pricing_for_ta_or_tmc_applicable():
            ta_pricing_discount = self.get_attr(CorporateAttributes.TA_PRICING_DISCOUNT)
            ta_pricing_discount = float(ta_pricing_discount) if ta_pricing_discount else 0

            return ta_pricing_discount

        return 0

    def get_primary_admin_name(self):
        user = self.primary_admin
        if user:
            return user.get_full_name()

    def latest_account_version(self):
        """
        return account version model object
        """
        account_legal_entity = getattr(self, 'accountlegalentity', None)
        if account_legal_entity and account_legal_entity.account.is_active:
            return account_legal_entity.account.accountversion_set.order_by("-version").first()
        return None

    @cached_property
    def account(self):
        return getattr(self.accountlegalentity_set.first(), 'account', None)

    @cached_property
    def account_treebo_poc(self):
        return getattr(self.account, 'treebo_poc', None)

    @cached_property
    def account_hotel_finance_poc(self):
        return getattr(self.account, 'hotel_finance_poc', None)

    @cached_property
    def account_inside_sales_poc(self):
        return getattr(self.account, 'inside_sales_poc', None)

    @cached_property
    def is_default_for_account(self):
        account_legal_entity = self.accountlegalentity_set.first()
        return account_legal_entity and account_legal_entity.is_default

    @cached_property
    def le_account_rel(self):
        return self.accountlegalentity_set.first()
