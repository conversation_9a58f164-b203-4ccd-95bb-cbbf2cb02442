# -*- coding: utf-8 -*-
# pylint: disable=ungrouped-imports,wrong-import-order,too-many-public-methods,invalid-name,unused-variable,bare-except,line-too-long
import re

from django.core.exceptions import MultipleObjectsReturned
from django.db import models

from b2b import constants
from b2b.constants import CorporateAttributes, RecordChangeReason
from b2b.models.mixins import DefaultPermissions, CustomTimeStampMixin
from b2b.models.user import User


class Corporate(CustomTimeStampMixin, DefaultPermissions):
    # identity (internal)
    corporate_id = models.CharField(max_length=20, unique=True, db_index=True)
    _legal_name = models.CharField(max_length=200,
                                   help_text="Legal Name for the corporate. For ex: Legal name can be AIRTEL for "
                                             "Airtel North east")

    trading_name = models.CharField(max_length=200, unique=True,
                                    help_text="Corporate will be registered in Hx with this name. "
                                              "Do not prefix 'B2B-' it will be automatically done.")

    # contact details
    email = models.EmailField(max_length=200, null=True, blank=True)
    phone = models.CharField(max_length=200, null=True, blank=True)

    active = models.BooleanField(default=True,
                                 help_text='Use this instead of deleting. Inactive corporates will show up nowhere in the system.')

    parent = models.CharField(max_length=200, default="", null=True, blank=True)
    sector = models.CharField(choices=constants.Corporates.SECTOR_CHOICES, max_length=200, blank=True, null=True)

    created_by = models.ForeignKey(User, null=True, blank=True, related_name='created_by', on_delete=models.CASCADE)

    is_test = models.BooleanField(default=False,
                                  help_text='mark this true, if corporate is created for testing purpose')

    def __init__(self, *args, **kwargs):
        super(Corporate, self).__init__(*args, **kwargs)
        self._original_trading_name = self.trading_name

    class Meta(DefaultPermissions.Meta):
        pass

    def __str__(self):
        return "{trading_name} | {corporate_id}".format(trading_name=self.trading_name,
                                                        corporate_id=self.corporate_id)

    def default_legal_entity(self):
        legal_entities = self.corporatelegalentity_set.filter(default=True)
        if not legal_entities:
            return None
        # If default legal entities for a corporate is more than one, pick the first one
        return legal_entities[0].legal_entity

    @property
    def default_legal_name(self):
        """
        Returns: default legal name for the corporate.

        """
        legal_entity = self.default_legal_entity()
        if legal_entity:
            return self.default_legal_entity().name
        return ''

    def default_gstin_entity(self):
        """
        This would be used in cases where booking does not get any entry for legal entity or gstin. Pick the default
        gstin for default legal entity.
        Returns: Default gstin for default legal entity

        """
        legal_entity = self.default_legal_entity()
        default_gstins = []
        if legal_entity:
            default_gstins = legal_entity.gstin_set.filter(default=True)
        if not default_gstins:
            return None
        # For multiple default gstins, pick the first gstin code
        return default_gstins[0]

    @property
    def default_billing_address(self):
        """
        This would be used when no legal entity or gstin is provided. Returns default address of default gstin
        of default legal entity for corporate.
        Returns: Default address for corporate in dict form.
        """
        gstin_entity = self.default_gstin_entity()
        if gstin_entity:
            billing_address_entity = gstin_entity.legalentitygstinaddress_set.filter(default=True)
            if not billing_address_entity:
                return self.billing_address
            return billing_address_entity[0].address
        return self.billing_address

    @property
    def default_gstin(self):
        """
        This would be used in cases where booking does not get any entry for legal entity or gstin. Pick the default
        gstin for default legal entity.
        Returns: default gstin

        """
        gstin_entity = self.default_gstin_entity()
        if gstin_entity:
            return gstin_entity.gstin
        return ''

    def get_attr(self, key):
        try:
            attrib = self.corporateattributes_set.get(key=key)
            return attrib.value
        except MultipleObjectsReturned:
            attrib = self.corporateattributes_set.filter(key=key).latest('id')
            return attrib.value
        except:
            return None

    def set_attr(self, key, value, via_admin=False):
        attrib, created = self.corporateattributes_set.get_or_create(key=key)
        attrib.value = value
        if via_admin:
            if created:
                attrib._change_reason = RecordChangeReason.CREATED_VIA_ADMIN
            else:
                attrib._change_reason = RecordChangeReason.UPDATED_VIA_ADMIN
        attrib.save()

    @property
    def contact_address(self):
        from b2b.domain.services import AddressService
        return AddressService.get_address_by_id(address_id=self.get_attr(constants.CorporateAttributes.CONTACT_ADDRESS))

    @property
    def billing_address(self):
        from b2b.domain.services import AddressService
        return AddressService.get_address_by_id(address_id=self.get_attr(constants.CorporateAttributes.BILLING_ADDRESS))

    def get_ta_discount(self):
        if self.is_web_pricing_for_ta_or_tmc_applicable():
            ta_pricing_discount = self.get_attr(CorporateAttributes.TA_PRICING_DISCOUNT)
            ta_pricing_discount = float(ta_pricing_discount) if ta_pricing_discount else 0

            return ta_pricing_discount

        return 0

    @property
    def address(self):
        addr = self.contact_address
        return '{building}{street}{locality}'.format(building=addr.building,
                                                     street=addr.street,
                                                     locality=addr.locality)

    @property
    def city(self):
        return self.contact_address.city

    @property
    def state(self):
        return self.contact_address.state

    @property
    def zipcode(self):
        return self.contact_address.pincode

    def get_crs_registration_name(self):
        return 'B2B-{name}'.format(name=self.trading_name)

    def get_crs_registration_email(self):
        return 'b2b{id}.{eml}'.format(id=self.id, eml=self.email)

    def legal_entities(self):
        legal_entities_details = [dict(name=corp_legal_entity.legal_entity.name,
                                       treebo_poc=(getattr(corp_legal_entity.legal_entity.account_treebo_poc,
                                                           'name', None)),
                                       address=corp_legal_entity.legal_entity.address.to_dict()
                                       if corp_legal_entity.legal_entity.address else self.billing_address.to_dict(),
                                       gstins=corp_legal_entity.legal_entity.gstins(),
                                       default=corp_legal_entity.default,
                                       uid=corp_legal_entity.id,
                                       legal_entity_id=corp_legal_entity.legal_entity.legal_entity_id,
                                       btc_admin_approval=corp_legal_entity.legal_entity.btc_admin_approval,
                                       btc_enabled=corp_legal_entity.legal_entity.btc_enabled,
                                       is_b2b_ta=corp_legal_entity.legal_entity.is_b2b_ta(),
                                       is_ta=corp_legal_entity.legal_entity.is_ta(),
                                       posttax_enabled=corp_legal_entity.legal_entity.posttax_enabled,
                                       slab_type=corp_legal_entity.legal_entity.slab_type,
                                       voucher_emails=corp_legal_entity.legal_entity.get_voucher_emails(),
                                       active=corp_legal_entity.legal_entity.active,
                                       is_test=corp_legal_entity.legal_entity.is_test,
                                       )
                                  for corp_legal_entity in self.corporatelegalentity_set.select_related('legal_entity').all()]

        return sorted(legal_entities_details, key=lambda i: i['name'])

    @property
    def uid(self):
        return self.corporate_id

    @property
    def name(self):
        return self.trading_name or self._legal_name
