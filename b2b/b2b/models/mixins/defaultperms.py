from django.db import models

from b2b.constants import CustomPermissions


class DefaultPermissions(models.Model):
    class Meta:
        abstract = True
        default_permissions = (CustomPermissions.ADD, CustomPermissions.CHANGE,
                               CustomPermissions.READ, CustomPermissions.DELETE)


class LowerCaseField(models.CharField):
    def __init__(self, *args, **kwargs):
        super(LowerCaseField, self).__init__(*args, **kwargs)

    def get_prep_value(self, value):
        return str(value).lower()


class LowerCaseEmailField(models.EmailField):
    def __init__(self, *args, **kwargs):
        super(LowerCaseEmailField, self).__init__(*args, **kwargs)

    def get_prep_value(self, value):
        return str(value).lower()
