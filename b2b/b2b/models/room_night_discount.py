from django.db import models
from djutil.models import TimeStampedModel
from b2b.models.mixins import DefaultPermissions


class RoomNightDiscount(TimeStampedModel, DefaultPermissions):
    """
    Configuration to set the discount (in %) on the length of room night
    EX: 1-10 = 3%
        11-20 = 5%
    """
    min_slab = models.IntegerField()
    max_slab = models.IntegerField()
    discount = models.DecimalField(max_digits=20, decimal_places=2)
    active = models.BooleanField(default=True)

    def __str__(self):
        return f"ID: {self.id}"
