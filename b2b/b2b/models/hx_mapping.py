# -*- coding: utf-8 -*-
from django.db import models

from b2b.models import Corporate
from b2b.models import Hotel
from b2b.models.mixins import DefaultPermissions


class HxMapping(DefaultPermissions):
    hotel = models.ForeignKey(Hotel, on_delete=models.CASCADE)
    corporate = models.ForeignKey(Corporate, on_delete=models.CASCADE)
    corp_hx_id = models.CharField(max_length=20, db_index=True)

    class Meta(DefaultPermissions.Meta):
        unique_together = ('hotel', 'corporate')

    def __str__(self):
        return "{hotel_id} | {corporate_id}".format(hotel_id=self.hotel.hotel_id,
                                                    corporate_id=self.corporate.corporate_id)
