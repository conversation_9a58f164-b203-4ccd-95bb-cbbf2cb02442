# -*- coding: utf-8 -*-
# pylint: disable=invalid-name,old-style-class,no-init

# Corporate constants

class Corporates(object):
    DEFAULTS = {
        'MOP': 'Direct',
        'Salutation': 'Mr',
        'MealPlan': 'MAP'
    }

    SECTORS_LIST = ['', 'AGRICULTURE', 'AUTO COMPONENTS', 'AUTOMOBILES', 'AVIATION', 'BANKING',
                    'BIOTECHNOLOGY','CEMENT', 'CHEMICALS', 'CONSULTANCY', 'CONSUMER MARKETS',
                    'EDUCATION AND TRAINING','ELECTRICAL & ELECTRONICS', 'ENERGY', 'ENGINEERING',
                    'FINANCI<PERSON> SERVICES', 'FOOD INDUSTRY', 'GEMS AND JEWELLERY', 'HEALTHCARE',
                    'INFRASTRUCTURE', 'INSURANCE', 'IT', 'MANUFACTURING',
                    'MEDIA AND ENTERTAINMENT', 'NON-PROFIT', 'OIL AND GAS', 'PHARMACEUTICALS',
                    'PSU', 'REAL ESTATE', 'RESEARCH AND DEVELOPMENT', 'RETAIL',
                    'SCIENCE AND TECHNOLOGY',  'SEMICONDUCTOR', 'SERVICES','STARTUP', 'STEEL',
                    'TELECOMMUNICATIONS', 'TEXTILES', 'TOURISM AND HOSPITALITY', 'URBAN MARKET',
                    'OTHERS']
    SECTOR_CHOICES = tuple((x, x) for x in SECTORS_LIST)

    QUOTE = 'quote'
    FLOOR = 'floor'
    DEFAULT = 'default'
    B2B_RACK = 'b2brack'
    SLAB_CHOICES = tuple([(x, x) for x in [QUOTE, FLOOR, DEFAULT, B2B_RACK]])
    LOYALTY_LEVEL = "loyalty_level"
    ACTIVE = "active"


class BookingPriceSource(object):
    CORPORATE = "corporate"
    WEBSITE = "website"

    CHOICES = [CORPORATE, WEBSITE]


class AccountAdmin(object):
    # permission group names as for the role
    MANAGER_GROUP = 'account_admin_managers'
    FINANCE_GROUP = 'account_admin_fin_users'


class CorporateAttributes(object):
    PRIMARY_ADMIN = 'primary_admin'
    PRIMARY_FINANCE_ADMIN = 'primary_finance_admin'
    CONTACT_ADDRESS = 'contact_address'
    BILLING_ADDRESS = 'billing_address'
    PAN_NUMBER = 'pan_number'
    TAN_NUMBER = 'tan_number'
    CIN_NUMBER = 'cin_number'
    GSTIN_NUMBER = 'gstin_number'
    VIRTUAL_ACCOUNT_NUMBER = 'virtual_account_number'
    AMOUNT_RECEIVED = 'amount_received'
    CREDIT_LIMIT = 'credit_limit'
    CREDIT_PERIOD = 'credit_period'
    BILLING_PERIOD = 'billing_period'
    STAY_SUMMARY_AGGREGATION_CRITERIA = 'stay_summary_aggregation_criteria'
    BTC_ENABLED = 'btc_enabled'
    TAC_COMMISSION = 'tac_commission'
    POST_COMMISSION_AMOUNT = 'post_commission_amount'
    POS_REQUIRED = 'pos_required'
    NAVISION_CODE = 'navision_code'
    GROUP_NAME = 'group_name'
    VYMO_ID = 'vymo_id'
    LOYALTY_ENABLED = 'loyalty_enabled'
    TA_PRICING_DISCOUNT = 'ta_pricing_discount'
    BOOKING_REQUEST_ATTACHMENT_REMINDER_ENABLED = 'booking_request_attachment_reminder_enabled'
    BOOKING_REQUEST_ATTACHMENT_REMINDER_PERIOD = 'booking_request_attachment_reminder_period'
    STAY_SUMMARY_BOOKING_REQUEST_ATTACHMENT_ENABLED = \
        'stay_summary_booking_request_attachment_enabled'
    INVOICE_DISPATCH_OPTION_ON_CHECKOUT = 'invoice_dispatch_option_on_checkout'
    INVOICE_DISPATCH_OPTION_WITH_STAY_SUMMARY = 'invoice_dispatch_option_with_stay_summary'
    SHOULD_DISPATCH_BOOKING_REQUEST_WITH_STAY_SUMMARY = \
        'should_dispatch_booking_request_with_stay_summary'

    class AgencyTypes:
        KEY = 'agency_type'
        CORPORATE = 'corporate'
        TMC = 'tmc'
        B2B_TA = 'b2b_ta'
        LOCAL_TA = 'local_ta'
        SME = 'sme'
        B2B_BULK = 'b2b_bulk'
        TA_BULK = 'ta_bulk'
        LOCAL_CORPORATE = 'local_corporate'
        INTERNAL_FOT_AUDIT = 'internal_fot_audit'
        INTERNAL_FOT_REDEMPTION = 'internal_fot_redemption'
        INTERNAL_TREEBO_OFFICIAL = 'internal_treebo_official'
        INTERNAL_PARTNER = 'internal_partner'
        INTERNAL_GDC = 'internal_gdc'
        HOTEL_PARTNER = 'hotel_partner'
        PROACTIVE_BULK = 'proactive_bulk'
        PROACTIVE_CORPORATE = 'proactive_corporate'
        PROACTIVE_TA = 'proactive_ta'
        B2B_COMMISIONABLE = 'b2b_commisionable'
        TA_COMMISIONABLE = 'ta_commisionable'
        VALUES_CHOICES = ((x, x) for x in (
            CORPORATE, TMC, B2B_TA, LOCAL_TA, SME, B2B_BULK, TA_BULK, LOCAL_CORPORATE,
            INTERNAL_FOT_AUDIT, INTERNAL_FOT_REDEMPTION, INTERNAL_TREEBO_OFFICIAL,
            INTERNAL_PARTNER, INTERNAL_GDC, HOTEL_PARTNER,PROACTIVE_BULK, PROACTIVE_CORPORATE,
            PROACTIVE_TA, B2B_COMMISIONABLE, TA_COMMISIONABLE))
        TA_CATEGORIES = [B2B_TA, LOCAL_TA, TA_BULK]
        TMC_CATEGORIES = [TMC]
        TA_AND_TMC_CATEGORIES = TA_CATEGORIES + TMC_CATEGORIES
        VALID_AGENCY_TYPES = [CORPORATE, TMC, B2B_TA, LOCAL_TA, SME, B2B_BULK, TA_BULK,
                              LOCAL_CORPORATE, INTERNAL_FOT_AUDIT, INTERNAL_FOT_REDEMPTION,
                              INTERNAL_TREEBO_OFFICIAL, INTERNAL_PARTNER, INTERNAL_GDC,
                              HOTEL_PARTNER, PROACTIVE_BULK, PROACTIVE_CORPORATE, PROACTIVE_TA,
                              B2B_COMMISIONABLE, TA_COMMISIONABLE]

    class PaymentSource:
        KEY = 'payment_source'
        PAID_BY_GUEST = 'paid_by_guest'
        PAID_BY_GROUP_OWNER = 'paid_by_group_owner'
        VALUE_CHOICES = ((x, x) for x in (PAID_BY_GUEST, PAID_BY_GROUP_OWNER))


class HotelAttributes(object):
    WEBSITE = 'website'
    LUXURY_TAX_CODE = 'luxury_tax_code'
    EMAIL = 'email'
    SERVICE_TAX_CODE = 'service_tax_code'
    STATE_ID = 'state_id'
    GSTIN_CODE = 'gstin_code'
    LEGAL_STATE_CODE = 'legal_state_code'
    PROMOTION_PRIORITY = 'promotion_priority'
    LATITUDE = 'latitude'
    LONGITUDE = 'longitude'
    CHECKIN_TIME = 'checkin_time'
    CHECKOUT_TIME = 'checkout_time'
    ROOM_COUNT = 'room_count'
    BRAND_TYPE = 'brand'

    class Saleability(object):
        KEY = 'saleable'
        SALEABLE = '1'
        NON_SALEABLE = '0'
        VALID_VALUES = (SALEABLE, NON_SALEABLE)


class Meal(object):
    TYPES = (
        ('veg', 'Veg'),
        ('non-veg', 'Non-Veg')
    )

    PLANS = tuple([(x, x) for x in 'MAP AP EP CP'.split()])

    DEFAULTS = {
        'type': 'veg',
        'plan': 'MAP'
    }


class Persons(object):
    SALUTATIONS = tuple([(x, x) for x in 'Mr Mrs Ms'.split()])
    GENDER = (
        ('M', 'Male'),
        ('F', 'Female')
    )

    DEFAULTS = {
        'salutation': 'Mr',
        'gender': 'M'
    }


class Rooms(object):
    TYPES = tuple([(x, x) for x in "Acacia Maple Oak Mahogany".split()])

    # NOTE: these are the prices the system will fall back to in case no
    #       specific prices are found configured in any given context
    DefaultRoomPrices = dict(
        Acacia=10000,
        Oak=10000,
        Maple=10000,
        Mahogany=10000
    )


class Pricing(object):
    QUOTE = "quote"
    DEFAULT = "default"
    FLOOR = "floor"
    CUSTOM = "custom"  # needed exclusively for corps on **OLD** pricing scheme
    B2BRACK = "b2brack"  # needed exclusively for corps on new pricing scheme

    SLABS = tuple([(x, x.capitalize()) for x in "quote default floor custom b2brack".split()])


class Inclusions(object):
    EPCharges = 'ExtraPersonCharges'
    VegMealCharges = 'VegMealCharges'
    NonVegMealCharges = 'NonVegMealCharges'
    CabCharges = 'CabCharges'
    LaundryCharges = 'LaundryCharges'
    ConferenceRoomCharges = 'ConferenceRoomCharges'
    ConferenceMealCharges = 'ConferenceMealCharges'

    CategoryMapping = {
        EPCharges: 'Room Inclusions',
        VegMealCharges: 'Meal Inclusions',
        NonVegMealCharges: 'Meal Inclusions',
        CabCharges: 'Travel Inclusions',
        LaundryCharges: 'Room Inclusions',
        ConferenceRoomCharges: 'Conference Inclusions',
        ConferenceMealCharges: 'Conference Inclusions'
    }

    INCLUSIONS = (
        (EPCharges, 'Extra-person Charges'),
        (VegMealCharges, 'Veg Meal Charges'),
        (NonVegMealCharges, 'Non-Veg Meal Charges'),
        (CabCharges, 'Cab Charges'),
        (LaundryCharges, 'Laundry Charges'),
        (ConferenceRoomCharges, 'Conference Room Charges'),
        (ConferenceMealCharges, 'Conference Meal Charges')
    )

    PRICING_MECHS = (
        ('PP', 'Per Person'),
        ('PA', 'Per Adult'),
        ('PC', 'Per Child'),
        ('PB', 'Per Booking'),
        ('PR', 'Per Room'),
    )

    DEFAULTS = dict(pricing_mechanism='PP')


class Booking(object):
    # when a new booking request is received
    RECEIVED = 'Received'

    # when the bookign is recorded at our end
    INITIATED = 'Initiated'

    # when payment failed
    PAYMENT_FAILED = 'Payment failed'

    # when we save the booking in the CRS (HX)
    SAVED = 'Saved'

    # when we confirm the booking in CRS (HX)
    CONFIRMED = 'Confirmed'

    # when booking cancellation is initiated in the system
    INIT_CANCEL = 'Cancellation Initiated'

    # when the booking is successfully cancelled (in our system as well as in HX/CRS)
    CANCELLED = 'Cancelled'

    # when an update-booking-request is successfully merged with parent
    PROCESSED = 'Processed'

    # when either the HX fails to confirm the booking, or some
    # other error occurs and we can't process the booking any further
    FAILED = 'Failed'

    # statuses used for edit/update booking
    UPDATE_IN_PROGRESS = "Update In Progress"
    UPDATED = "Updated"
    UPDATE_FAILED = "Update Failed"
    UPDATE_DISCARDED = "Update Discarded"  # when there is no diff between old and new

    STATUSES = tuple([(x, x) for x in [RECEIVED, INITIATED, SAVED, CONFIRMED,
                                       INIT_CANCEL, CANCELLED, PROCESSED, FAILED,
                                       UPDATE_IN_PROGRESS, UPDATED, UPDATE_FAILED,
                                       UPDATE_DISCARDED]])

    HX_STATUSES_MAPPING = {'RESERVE': CONFIRMED, 'CANCEL': CANCELLED}

    # sub channel types
    BTC = 'Corporate (BTC)'
    BTT = 'Corporate (BTT)'
    DIRECT = 'Corporate (Direct)'
    TA_PAID_BY_TA = 'TA - PAID BY TA'
    TA_PAID_BY_GUEST = 'TA - PAID BY GUEST'

    SUB_CHANNELS = tuple([(x, x) for x in [BTC, DIRECT, BTT, TA_PAID_BY_TA, TA_PAID_BY_GUEST]])

    HX = 'HX'
    THSC = 'THSC'
    CRS_TYPES = tuple([(x, x) for x in [HX, THSC]])

    BTCTC = 'BTCTC'
    CASH = 'cash'
    PAYMENT_TYPE = {
        'BTT': 'BTT',
        'BTCTC': 'BTCTC',
    }

    # Booking types
    class Type:
        DIRECT = 'DIRECT'  # Subchannel will be Direct
        PRE_PAID = 'PREPAID'  # Subchannel will be Direct
        BTC = 'BTC'  # Subchannel will be BTC
        BTT = 'BTT'  # Subchannel will be BTT
        TA_PAID_BY_TA = 'TA_PAID_BY_TA'
        TA_PAID_BY_GUEST = 'TA_PAID_BY_GUEST'

    class Channel(object):
        # To check booking channel
        ATHENA = 'Athena'
        PRIMUS = 'Corporate'

    STUB_EMAILS = ['<EMAIL>', '<EMAIL>', '']
    STUB_NAMES = ['empty', '']
    STUB_PHONE = ['9322800100', '']

    TA_CANCELLATION_TIME = '6 pm'
    CUSTOMER_CARE_NUMBER = "08066085662"
    BOOKER_COMPANY = "booker_company"
    BOOKER = "booker"


class AuditCategories(object):
    MEAL = 'MEAL'
    ROOM = 'ROOM'
    INCL = 'INCLUSIONS'
    HX = 'HX'
    CORP = 'CORPORATE'
    CANCELLATION = 'CANCELLATION'
    CRS_BACKSYNC = 'CRS_BACKSYNC'
    BOOKING_UPDATE = "BOOKING_UPDATE"
    LOYALTY_POINTS_EARN = 'LOYALTY_POINTS_EARN'
    LOYALTY_POINTS_BURN = 'LOYALTY_POINTS_BURN'
    CORP_REWARD_FACTOR = 'CORP_REWARD_FACTOR'
    CORP_WALLET_BALANCE = 'CORP_WALLET_BALANCE'
    DOWNSELL_APPROVAL_NAME = 'DOWNSELL_APPROVER_NAME'


class AuditEntities(object):
    CORPORATE = 'CORPORATE'
    LOYALTY = 'LOYALTY'


class BookingAttributes(object):
    """
    MOBILE_DEVICE :
        Set of possible keys -
            1. 'device'
    """
    SOFT_BOOKING_EXPIRY = 'soft_booking_expiry'
    SOFT_BOOKING_CONFIRMED = 'soft_booking_confirmed'
    TMC_COMMISSION_PERCENTAGE = 'tmc_commission_percentage'
    PAYMENT_REVERTED = 'payment_reverted'
    PAYMENT_PROCESSED = 'payment_processed'
    AGENT_EMAILS = 'agent_emails'
    PRICE_CHANGE_REASON = 'price_change_reason'
    UPDATE_BOOKING_CONFIRM_MAIL = 'update_success_email_tracking_id'
    TEMPORARY_UPDATE_BOOKING_CONFIRM_MAIL = 'temporary_update_success_email_tracking_id'
    UPDATE_BOOKING_FAILURE_MAIL = 'update_failure_email_tracking_id'
    BOOKING_CONFIRM_MAIL = 'confirmation_email_tracking_id'
    BOOKING_CANCELLED_EMAIL = 'cancellation_email_tracking_id'
    GST_BOOKING_CONFIRM_MAIL = 'gst_confirmation_email_tracking_id'
    TEMPORARY_BOOKING_CONFIRM_MAIL = 'temporary_confirmation_email_tracking_id'
    SOFT_BOOKING_CONFIRM_MAIL = 'soft_confirmation_email_tracking_id'
    BOOKING_REMINDER_MAIL = 'reminder_email_tracking_id'
    PARTPAY_PAYMENT_REMINDER_MAIL = 'partpay_reminder_notification_tracking_id'
    PARTPAY_PAYMENT_CANCELLATION_MAIL = 'partpay_cancel_notification_tracking_id'
    PARTPAY_PAYMENT_REMINDER_MAIL_COUNT = 'partpay_reminder_notification_count'
    BOOKING_FINAL_REMAINDER_MAIL = 'final_reminder_email_tracking_id'
    BOOKING_TEMPORARY_CANCEL_MAIL = 'temporary_cancel_email_tracking_id'
    FLEXI_BOOKING_CONFIRM_MAIL = 'flexi_booking_notification_tracking_id'
    BOOKING_GSTIN = 'gstin'
    BOOKING_GSTIN_ADDRESS = 'gstin_address'
    BOOKING_LEGAL_NAME = 'legal_name'
    MOBILE_DEVICE = 'device'
    PREVIOUS_BOOKING_ID = 'previous_booking_id'
    LOYALTY_ORDER_ID = 'loyalty_order_id'
    SALES_INCENTIVE_FACTOR = 'sales_incentive_factor'
    PRICE_SOURCE = 'price_source'
    CONTRACTED_PRICE = 'contracted_price'
    WEB_PRICE = 'web_price'
    ROOM_NIGHT_DISCOUNT_PERCENTAGE = 'room_night_discount_percentage'
    TCR_MULTIPLIER = 'tcr_multiplier'
    CANCELLATION_REASON = 'cancellation_reason'
    PAYMENT_UPDATE_FAILURE_MAIL = 'payment_update_failure_email_tracking_id'
    PAY_NOW_BOOKING = 'pay_now'
    BTC_ADMIN_APPROVAL_BOOKING = 'btc_admin_approval_booking'
    BOOKING_REQUEST_PROOF_EMAIL = 'booking_request_proof_tracking_id'
    BOOKING_REQUEST_PROOF_EMAIL_REMINDER = 'booking_request_attachment_reminder_tracking_id'


class UserGroup(object):
    APPLICATION_GROUP = 'application'


class Payments(object):
    PASSED = 'Passed'
    CAPTURED = 'captured'
    FAILED = 'Failed'
    REFUNDED = 'Refunded'

    VERIFICATION_STATUS = tuple([(x, x) for x in [PASSED, FAILED, REFUNDED, CAPTURED]])
    # when the payment is recorded at our end
    INITIATED_ORDER = 'InitiatedOrder'

    VERIFICATION_INITIATED = 'VerificationInitiated'
    VERIFIED = 'Verified'
    VERIFICATION_FAILED = 'VerificationFailed'
    STATUS_LIST = [INITIATED_ORDER, VERIFICATION_INITIATED, VERIFIED, VERIFICATION_FAILED, REFUNDED]
    STATUSES = tuple([(x, x) for x in STATUS_LIST])
    TA_AUTOMATION = 'TA Automation'


class EmailTypes(object):
    FINAL_REMINDER_EMAIL = 'final_reminder'
    REMINDER_EMAIL = 'reminder'
    TEMPORARY_CANCELLATION = 'temporary_booking_cancellation'
    UPDATE_CANCELLATION = 'update_cancellation'


class Countries(object):
    INDIA = 'India'
    COUNTRY_LIST = [INDIA]
    COUNTRY_CHOICES = tuple(zip(tuple(COUNTRY_LIST), tuple(COUNTRY_LIST)))


class CustomPermissions(object):
    ADD = 'add'
    READ = 'read'
    CHANGE = 'change'
    DELETE = 'delete'
    UPLOAD = 'upload'


class Tax(object):
    RoomNight = 'RoomNight'

    ChargeTypes = (
        (Inclusions.VegMealCharges, Inclusions.VegMealCharges),
        (Inclusions.NonVegMealCharges, Inclusions.NonVegMealCharges),
        (Inclusions.CabCharges, Inclusions.CabCharges),
        (Inclusions.LaundryCharges, Inclusions.LaundryCharges),
        (Inclusions.ConferenceRoomCharges, Inclusions.ConferenceRoomCharges),
        (Inclusions.ConferenceMealCharges, Inclusions.ConferenceMealCharges),
        (RoomNight, RoomNight),
    )


class Address(object):
    BUILDING = 'building'
    STREET = 'street'
    LOCALITY = 'locality'
    LANDMARK = 'landmark'
    CITY = 'city'
    STATE = 'state'
    COUNTRY = 'country'
    PINCODE = 'pincode'

    FIELDS = [BUILDING, STREET, LOCALITY, LANDMARK, CITY, STATE, COUNTRY, PINCODE]


class Slack(object):
    NEAREST_HOTEL_SEARCH = "*****************************************************************************"

    BOT_USER = {
        "url": "https://slack.com/api/chat.postMessage",
        "token": "xoxb-280991206034-hfFvvYTDP1YbE7ZSR1STnrHK"
    }

    POC_BOT_USER = {
        "url": "https://slack.com/api/chat.postMessage",
        "token": "*****************************************************"
    }
    PROMOTED_HOTELS_URL = "https://hooks.slack.com/services/T067891FY/B9Q9Y8PC4" \
                          "/CFqP6fZ9Ms8KZYB7E71qOkwx"
    B2B_APP_ALERTS = "https://hooks.slack.com/services/T067891FY/BL8FRA8A0" \
                     "/b6O4OVHPXH4MwRHX8BLT6UBG"
    B2B_PRICING_ALERTS = "https://hooks.slack.com/services/T067891FY/B7F96FN06" \
                         "/gAZRRlYfuFilE7NxpUrkfiA0"
    PROMOTED_HOTELS_URL = "*****************************************************************************"
    B2B_APP_ALERTS = "*****************************************************************************"
    B2B_PRICING_ALERTS = "*****************************************************************************"
    B2B_PAYNOW_BOOKING_ALERTS = "*******************************************************************************"


class Zones(object):
    NORTH = "NORTH"
    SOUTH = "SOUTH"
    EAST = "EAST"
    WEST = "WEST"
    B2B_CENTRAL = "B2B_CENTRAL"
    SOUTH_CENTRAL = "SOUTH_CENTRAL"

    CHOICE_OF_ZONES = (
        (NORTH, NORTH),
        (SOUTH, SOUTH),
        (EAST, EAST),
        (WEST, WEST),
        (B2B_CENTRAL, B2B_CENTRAL),
        (SOUTH_CENTRAL, SOUTH_CENTRAL)
    )


class UserType(object):
    OTHERS = 'others'
    SALES_POC = 'sales_poc'
    LOCAL_SALES_POC = 'local_sales_poc'
    PROACTIVE_SALES_POC = 'proactive_sales_poc'
    UNIT_SALES_POC = 'unit_sales_poc'

    USER_TYPES = (
        (OTHERS, 'others'),
        (SALES_POC, 'sales_poc'),
        (LOCAL_SALES_POC, 'local_sales_poc'),
        (PROACTIVE_SALES_POC, 'proactive_sales_poc'),
        (UNIT_SALES_POC, 'unit_sales_poc')
    )


class UserTypeZoneMapping(object):
    MAPPING = {
        UserType.LOCAL_SALES_POC: (
            Zones.NORTH,
            Zones.SOUTH,
            Zones.EAST,
            Zones.WEST
        ),
        UserType.SALES_POC: (
            Zones.NORTH,
            Zones.SOUTH,
            Zones.EAST,
            Zones.WEST,
            Zones.B2B_CENTRAL,
            Zones.SOUTH_CENTRAL
        ),
        UserType.PROACTIVE_SALES_POC: (
            Zones.NORTH,
            Zones.SOUTH,
            Zones.EAST,
            Zones.WEST
        ),
        UserType.UNIT_SALES_POC: (
            Zones.NORTH,
            Zones.SOUTH,
            Zones.EAST,
            Zones.WEST
        )

    }


class Quarter(object):
    FIRST = 1
    SECOND = 2
    THIRD = 3
    FORTH = 4


class HotelPromotions(object):
    DistancePromotedHotels = 'DistancePromotedHotels'
    HotelPromotionPriorityLimit = 4


class NotificationPriority(object):
    """
    priority = 1
    Normal = 2
    """
    BOOKING_CANCELLATION = 1
    BOOKING_CONFIRMATION = 1
    BOOKING_CONFIRMATION_TO_SALES_MANAGER = 1
    BOOKING_FINAL_REMINDER = 2
    BOOKING_REMINDER = 2
    BOOKING_UPDATED = 1
    CORPORATE_REGISTERED = 2
    HOTEL_ONBOARDING_FAILED = 2
    REPORT_FAILURE = 2
    SOFT_BLOCK_REMINDER = 2
    SOFT_BOOKING_CONFIRMED = 1
    TEMPORARY_BOOKING_CONFIRMATION = 1
    TEMPORARY_BOOKING_UPDATED = 1
    TEMPORARY_CANCELLATION = 1
    USER_REGISTERED = 1


class CatalogServiceEndPoints(object):
    PROPERTY_DETAILS = 'cataloging-service/api/properties'
    TENANT_CONFIGS = 'cataloging-service/api/v1/tenant-configs/'


class GroupAccess(object):
    ACCESS_ATHENA_PERMISSION = 'access_athena'
    CORPORATE_UPLOAD_ACCESS = 'corporate_upload_access'
    ATHENA_AUTH_GROUP = 'athena'
    SALES_POC_GROUP = 'sales_poc'
    VALUE_CHOICES = [ATHENA_AUTH_GROUP, SALES_POC_GROUP]


class Timeouts(object):
    CONNECTION_TIMEOUT = 6
    RESPONSE_TIMEOUT = 18


class UserAutoLoginParameters(object):
    SECRET_KEY = 'malgudidays'
    DEFAULT_TOKEN_EXPIRY_DAYS = 30
    URL = '/b2b/auto-login/'


SLACK_PRICING_POCS = '<!subteam^SCKV286RZ|b2bpricing>'   # https://api.slack.com/docs/message-formatting
GOONJ_CONF = 'config/goonj_conf.yaml'
PAYMENT_LINK_REQUEST_LIMIT = 10
TA_AUTOMATION_CANCELLATION_HOUR = 18
TA_AUTOMATION_REMINDER_HOUR = 8
TA_AUTOMATION_LAUNCH = '2018-11-01'
TA_AUTOMATION_AMOUNT_BUFFER = 100
# Value describe here the number of reminder to be sent before cancellation is happening
TA_AUTOMATION_CANCELLATION_THRESHOLD = 3
PSEUDO_PAYMENT = 'b2b_pseudo_'

S3_CORPORATE_RATESHEET_BUCKET_REGION = 'ap-south-1'
S3_CORPORATE_RATESHEET_BUCKET_NAME = 'corporate-ratesheet'
S3_CORPORATE_RATESHEET_SAMPLE_URL = 'https://corporate-ratesheet.s3.ap-south-1.amazonaws.com/'
BTC_DEFUALT_DISCOUNT_SLAB = 15
DEFAULT_RATE_PLAN = "TRB-DEFAULT"
TA_PAID_BY_TA_BULK = "TA-PAID-BY-TA-BULK"
TA_PAID_BY_GUEST_BULK = "TA-PAID-BY-GUEST-BULK"
TA_PAID_BY_TA_FIT = "TA-PAID-BY-TA-FIT"
TA_PAID_BY_GUEST_FIT = "TA-PAID-BY-GUEST-FIT"
TREEBO_SEARCH_URL = "/search/?checkin={0}&checkout={1}&city={2}&pid={3}&hotelids={4}" \
                    "&roomtypes={5}&roomconfig=1-0"
GENERATE_CSV_URL = "/athena/generate-rate-sheet/{0}/"
CSV_RATESHEET_FILE_NAME = "ratesheet.csv"
DEFAULT_ROOM_CONFIG = "1-0"
URL_SHORTENING_API = "https://api.tree.bo/api/shorten"
CAPTURED = 'captured'
S3_TCR_BUCKET_REGION = "ap-south-1"
S3_TCR_BUCKET_NAME = "mercury.treebo.com"


class InclusionOfferingType(object):
    ROOM = "per_room"
    GUEST = "per_guest"


class InclusionFrequencyType(object):
    DAILY = "daily"
    DURING_THE_FULL_STAY = "during_the_full_stay"


class B2BEventType(object):
    CORPORATE_CREATED = 'CorporateCreated'
    LEGAL_ENTITY_CREATED = 'LegalEntityCreated'
    CORPORATE_UPDATED = 'CorporateUpdated'
    LEGAL_ENTITY_UPDATED = 'LegalEntityUpdated'


class CompanyProfileEventType(object):
    PARENT_ENTITY_CREATED = 'ParentEntityCreated'
    PARENT_ENTITY_UPDATED = 'ParentEntityUpdated'
    SUB_ENTITY_CREATED = 'SubEntityCreated'
    SUB_ENTITY_UPDATED = 'SubEntityUpdated'


class CompanyProfileStatus(object):
    COMPLETE = 'complete'
    INACTIVE = 'inactive'


class POCDesignation(object):
    FINANCE_POC = "Finance POC"
    BOOKING_POC = "Booking POC"
    INSIDE_SALES_POC = "Inside Sales POC"
    SALES_POC = "Sales POC"


class LegalEntityFields(object):
    TREEBO_POC = "treebo_poc"
    INSIDE_SALES_POC = "inside_sales_poc"
    COMMUNICATION_ADDRESS = "communication_address"
    REGISTERED_ADDRESS = "registered_address"
    SLAB_TYPE = "slab_type"
    BTC_DISCOUNT_SLAB = "btc_discount_slab"


class SkuCategory(object):
    ROOM = "room"


class GstinFields(object):
    HAS_LUT = "has_lut"
    IS_SEZ = "is_sez"
    DEFAULT = "default"
    VERIFIED = "verified"


class IntegrationEventStatus(object):
    PUBLISHED = "published"
    UNPUBLISHED = "unpublished"
    FAILED = "failed"


class CompanyProfileEventFields(object):
    GST = "gst"
    TRN = "TRN"


class DefaultValues(object):
    DEFAULT_BUILDING = "dummy #100"
    DEFAULT_STREET = "22nd A Main Dummy Rd"
    DEFAULT_LOCALITY = "Sector 2, HSR Layout"
    DEFAULT_LANDMARK = "Dummy wave coffee"
    DEFAULT_CITY = "Bangalore"
    DEFAULT_STATE = "Karnataka"
    DEFAULT_PINCODE = "560087"
    DEFAULT_COUNTRY = "India"
    DEFAULT_SLAB_TYPE = "0"
    DEFAULT_POC_ID = 29174

    @staticmethod
    def get_default_address():
        return dict(
            building=DefaultValues.DEFAULT_BUILDING,
            street=DefaultValues.DEFAULT_STREET,
            locality=DefaultValues.DEFAULT_LOCALITY,
            landmark=DefaultValues.DEFAULT_LANDMARK,
            city=DefaultValues.DEFAULT_CITY,
            state=DefaultValues.DEFAULT_STATE,
            country=DefaultValues.DEFAULT_COUNTRY,
            pincode=DefaultValues.DEFAULT_PINCODE,
        )



ANONYMOUS_SLACK_USER_EMAIL = "<EMAIL>"
DEFAULT_CHECKOUT_TIME = "11 AM"
B2B_SYSTEM_USER_EMAIL = "<EMAIL>"
B2B = "b2b"


class TCREmailRelatedConfig(object):
    from_email = "<EMAIL>"
    to_emails_for_weekly_report = ["<EMAIL>", "<EMAIL>"]
    cc_list = ["<EMAIL>"]
    to_emails_for_monthly_report = ["<EMAIL>", "<EMAIL>"]


SHOW_SIBLINGS_FOR_AGENCY_TYPE = [CorporateAttributes.AgencyTypes.CORPORATE, CorporateAttributes.AgencyTypes.SME,
                                 CorporateAttributes.AgencyTypes.LOCAL_CORPORATE]


class RecordChangeReason(object):
    UPDATED_VIA_ADMIN = "Updated via admin"
    CREATED_VIA_ADMIN = "Created via admin"
    CREATED_VIA_PROFILE_SYNC = "Created via profile sync"
    UPDATED_VIA_PROFILE_SYNC = "Updated via profile sync"
    CREATED_VIA_CSV_UPLOAD = "Created via csv upload"
    UPDATED_VIA_CSV_UPLOAD = "Updated via csv upload"


class RequestTypes(object):
    JSON = 'json'
    ARGS = 'args'


class TenantConfigConstants(object):
    ACQ_CONFIGS = "acq_configs"
    ROOMS_COUNT_FOR_BULK_RATE_PLAN_APPLICABILITY = "rooms_count_for_bulk_rate_plan_applicability"


class InvoiceDispatchOption(object):
    DISABLED = "disabled"
    ONLY_CREDIT = "only-credit"
    ALL_CREDIT = "all-credit"
    ONLY_SPOT_CREDIT = "only-spot-credit"
    PAID_INVOICE = "paid"
    ALL = "all"


VALID_CORPORATE_ATTRIBUTES = vars(CorporateAttributes).values()
SIKKIM_STATE_CODE = '11'
