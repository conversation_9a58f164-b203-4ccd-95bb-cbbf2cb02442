# -*- coding: utf-8 -*-
from rest_framework import serializers

from b2b import constants
from b2b.dto.booking_request import CardDTO


class VerifyPaymentSOARequestDTO(serializers.Serializer):
    pg_order_id = serializers.CharField()
    pg_payment_id = serializers.CharField()
    pg_payment_signature = serializers.CharField()
    pg_customer_id = serializers.CharField(required=False, allow_null=True)
    channel = serializers.CharField(max_length=20)
    amount_paid = serializers.DecimalField(max_digits=20, decimal_places=2)
    card = CardDTO(required=False)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass
