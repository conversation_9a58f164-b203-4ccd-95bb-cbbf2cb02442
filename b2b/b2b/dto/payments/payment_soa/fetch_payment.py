# -*- coding: utf-8 -*-


class FetchPaymentDto:
    def __init__(self, order_id, payment_id, amount_paid):
        self.order_id = order_id
        self.payment_id = payment_id
        self.amount_paid = amount_paid

    def to_json(self):
        return {
            "order_id": self.order_id,
            "payment_id": self.payment_id,
            "payment_service_id": self.payment_id,
            "signature": "",
            "amount_paid": self.amount_paid,
            "channel": ""
        }
