#!/usr/bin/env python
# -*- coding: utf-8 -*-
from django.conf import settings
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from b2b.constants import Tax as TaxConstants
from common.rounded_decimal_field import RoundedDecimalField


class _RoomCharge(serializers.Serializer):
    hotel_id = serializers.CharField()
    room_type = serializers.CharField()
    occupancy = serializers.CharField()
    pretax_price = RoundedDecimalField(max_digits=20, decimal_places=2)
    charge_type = serializers.CharField()

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class TaxRequestDTO(serializers.Serializer):
    """
    DTO to get tax from tax service.
    Gets tax for combination of multiple rooms and multiple occupancies for a single hotel
    """
    stay_start = serializers.DateField(format=settings.BOOKING['date_format'])
    stay_end = serializers.DateField(format=settings.BOOKING['date_format'])
    room_charges = _RoomCharge(many=True)
    gstin = serializers.CharField(max_length=50, default='', allow_blank=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class AttributeDTO(serializers.Serializer):
    key = serializers.CharField(max_length=50)
    value = serializers.CharField(max_length=100)


class PriceDTO(serializers.Serializer):
    date = serializers.DateField(format=settings.BOOKING['date_format'], required=True)
    pretax_price = RoundedDecimalField(max_digits=20, decimal_places=2, required=False)
    posttax_price = RoundedDecimalField(max_digits=20, decimal_places=2, required=False)
    is_pre_discount_price = serializers.BooleanField(required=False)
    index = serializers.CharField(max_length=100)

    def validate(self, data):
        pretax_price = data.get('pretax_price')
        posttax_price = data.get('posttax_price')

        if pretax_price is None and posttax_price is None:
            raise ValidationError("Either 'pretax_price' or 'posttax_price' must be provided.")

        if pretax_price is not None and posttax_price is not None:
            raise ValidationError("Only one of 'pretax_price' or 'posttax_price' should be provided, not both.")

        return data


class TaxSkuDTO(serializers.Serializer):
    attributes = AttributeDTO(many=True)
    prices = PriceDTO(many=True)
    category_id = serializers.CharField(max_length=100)
    index = serializers.CharField(max_length=100)


class TaxRequestV6DTO(serializers.Serializer):
    skus = TaxSkuDTO(many=True, required=True)
