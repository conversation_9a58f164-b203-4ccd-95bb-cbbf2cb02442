# -*- coding: utf-8 -*-
from django.conf import settings
from rest_framework import serializers


class EPChargeRequestDTO(serializers.Serializer):
    legal_entity_id = serializers.CharField(max_length=20)
    hotel = serializers.CharField(max_length=20)

    room_type = serializers.CharField(max_length=20)
    room_config = serializers.CharField(max_length=5, default="1-0")

    from_date = serializers.DateField(format=settings.BOOKING['date_format'])
    to_date = serializers.DateField(format=settings.BOOKING['date_format'])

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass
