# -*- coding: utf-8 -*-
from django.conf import settings
from rest_framework import serializers


class _EmailAttachmentDTO(serializers.Serializer):
    url = serializers.URLField(max_length=2048)
    filename = serializers.CharField(max_length=200)


class EmailDTO(serializers.Serializer):
    sender = serializers.EmailField(max_length=256)
    sender_name = serializers.CharField(max_length=300, allow_blank=True, required=False)
    reply_to = serializers.EmailField(max_length=256, default=settings.NOTIFICATION_CONF['reply_to'])

    to_list = serializers.ListField(child=serializers.EmailField(max_length=256))

    cc_list = serializers.ListField(child=serializers.EmailField(max_length=256),
                                    required=False,
                                    default=[])

    bcc_list = serializers.ListField(child=serializers.EmailField(max_length=256),
                                     required=False,
                                     default=[])

    subject = serializers.Char<PERSON>ield(max_length=300)

    content_type = serializers.ChoiceField(choices=['text', 'html'], default='html')
    content = serializers.CharField()
    attachments = _EmailAttachmentDTO(many=True, required=False, default=[])

    is_urgent = serializers.BooleanField(required=False, default=False)

    def to_internal_value(self, data):
        data['to_list'] = [email for email in data['to_list'] if email] if data.get('to_list') else []
        data['cc_list'] = [email for email in data['cc_list'] if email] if data.get('cc_list') else []
        data['bcc_list'] = [email for email in data['bcc_list'] if email] if data.get('bcc_list') else []
        return super().to_internal_value(data)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass
