# -*- coding: utf-8 -*-
from rest_framework import serializers

from b2b import constants
from b2b.dto.booking_request import CardDTO
from common.rounded_decimal_field import RoundedDecimalField


class RefundRequestDTO(serializers.Serializer):
    order_id = serializers.CharField(max_length=100, required=False, allow_null=True, allow_blank=True, default='')
    pg_payment_id = serializers.CharField(max_length=100, allow_null=True, allow_blank=True, default='')
    amount = RoundedDecimalField(max_digits=20, decimal_places=2, required=True)
    notes = serializers.ListField(default=[])
    gateway = serializers.CharField(max_length=100, required=False, allow_null=True, allow_blank=True)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass


class RefundResponseDTO(serializers.Serializer):
    receipt = serializers.CharField(max_length=250)
    currency = serializers.Char<PERSON>ield(max_length=250)
    amount = RoundedDecimalField(max_digits=20, decimal_places=2, min_value=0)
    refund_order_id = serializers.CharField(max_length=250)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass
