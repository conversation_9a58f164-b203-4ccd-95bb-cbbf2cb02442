#!/usr/bin/env python
# -*- coding: utf-8 -*-
from rest_framework import serializers


class HotelSyncDTO(serializers.Serializer):
    id = serializers.CharField(required=False, allow_null=True)
    name = serializers.CharField()
    hotelogix_id = serializers.CharField(required=False, allow_null=True)
    description = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    locality = serializers.CharField()
    street = serializers.CharField()
    city = serializers.CharField()
    phone_number = serializers.CharField(required=False, allow_null=True)
    active = serializers.BooleanField()
    catalogue_id = serializers.CharField(required=False, allow_null=True)
    city_id = serializers.IntegerField(required=False, allow_null=True)
    attributes = serializers.DictField(default={})
    current_business_date = serializers.CharField(required=False, allow_null=True)


class _CityDto(serializers.Serializer):
    name = serializers.Char<PERSON>ield(required=False, allow_null=True)
    id = serializers.IntegerField(required=False, allow_null=True)


class _LocalityDto(serializers.Serializer):
    name = serializers.CharField(required=False, allow_null=True)


class _StateDto(serializers.Serializer):
    id = serializers.CharField(required=False, allow_null=True)
    name = serializers.CharField(required=False, allow_null=True)

class _BrandDto(serializers.Serializer):
    name = serializers.CharField(required=False, allow_null=False)

class _LocationDto(serializers.Serializer):
    postal_address = serializers.CharField(required=False, allow_null=True)
    longitude = serializers.FloatField(required=False, allow_null=True)
    latitude = serializers.FloatField(required=False, allow_null=True)
    city = _CityDto()
    locality = _LocalityDto()
    state = _StateDto()
    pincode = serializers.IntegerField(required=False, allow_null=True)


class _PropertyDetailsDto(serializers.Serializer):
    reception_landline = serializers.CharField(required=False, allow_null=True)
    gstin = serializers.CharField(required=False, allow_null=True)
    has_lut = serializers.BooleanField(required=False, default=False)


class _NameDto(serializers.Serializer):
    new_name = serializers.CharField(required=False, allow_null=True)
    legal_name = serializers.CharField(required=False, allow_null=True)
    old_name = serializers.CharField(required=False, allow_null=True)


class _SuitedToDto(serializers.Serializer):
    type = serializers.CharField(required=False, allow_null=True)
    id = serializers.CharField(required=False, allow_null=True)

class _DescriptionDto(serializers.Serializer):
    acacia_description = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    id = serializers.IntegerField(required=False, allow_null=True)
    mahogany_description = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    maple_description = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    oak_description = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    property_description = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    trilight_one = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    trilight_two = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    trilight_three = serializers.CharField(required=False, allow_null=True, allow_blank=True)

class _GuestFacingDetialdDto(serializers.Serializer):
    checkin_grace_time = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    checkin_time = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    checkout_grace_time = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    checkout_time = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    early_checkin_fee = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    free_early_checkin_time = serializers.CharField(required=False, allow_null=True)
    free_late_checkout_time = serializers.CharField(required=False, allow_null=True)
    id = serializers.IntegerField(required=False, allow_null=True)
    late_checkout_fee = serializers.CharField(required=False, allow_null=True, allow_blank=True)

class _RoomTypeDTO(serializers.Serializer):
    type = serializers.CharField(required=False, allow_null=True)

class _RoomTypeConfigsDTO(serializers.Serializer):
    max_total = serializers.IntegerField(required=False, allow_null=True)
    children = serializers.IntegerField(required=False, allow_null=True)
    adults = serializers.IntegerField(required=False, allow_null=True)
    room_type = _RoomTypeDTO()
    min_room_size = serializers.FloatField(required=False, allow_null=True)

class _PropertyImagesDTO(serializers.Serializer):
    path = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    property_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    room_type = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    sort_order = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    tag_description = serializers.CharField(required=False, allow_null=True, allow_blank=True)

class _AmenityDTO(serializers.Serializer):
    amenity_key = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    category = serializers.CharField(required=False, allow_null=True, allow_blank=True)

class _AmenitySummaryDTO(serializers.Serializer):
    property_amenities = serializers.ListField(child=_AmenityDTO())

class _LandMarkDTO(serializers.Serializer):
    hatchback_cab_fare = serializers.FloatField(required=False, allow_null=True)
    hotel_direction = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    hotel_distance = serializers.FloatField(required=False, allow_null=True)
    id = serializers.IntegerField(required=False, allow_null=True)
    latitude = serializers.FloatField(required=False, allow_null=True)
    longitude = serializers.FloatField(required=False, allow_null=True)
    name = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    sedan_cab_fare = serializers.FloatField(required=False, allow_null=True)
    type = serializers.CharField(required=False, allow_null=True, allow_blank=True)

class PropertyDTO(serializers.Serializer):
    id = serializers.CharField(required=True)
    status = serializers.CharField(required=True, allow_null=False)
    property_details = _PropertyDetailsDto(required=False, allow_null=True)
    location = _LocationDto(required=False, allow_null=True)
    name = _NameDto(required=False, allow_null=True)
    suited_to = _SuitedToDto(many=True)
    description = _DescriptionDto(allow_null=True)
    room_count = serializers.IntegerField(allow_null=True, required=False)
    guest_facing_details = _GuestFacingDetialdDto()
    room_type_configs = serializers.ListField(child=_RoomTypeConfigsDTO())
    property_images = serializers.ListField(child=_PropertyImagesDTO(), allow_null=True)
    amenity_summary = _AmenitySummaryDTO()
    landmarks = serializers.ListField(child=_LandMarkDTO())
    brands = serializers.ListField(child=_BrandDto(), allow_null=True)
    current_business_date = serializers.CharField(required=False, allow_null=True)


class CatalogServiceHotelSyncDTO(serializers.Serializer):
    """
        DTO to match the Cataloge Service pushed message structure
    """
    cs_property_id = serializers.CharField(required=True, allow_null=False)
    hx_id = serializers.CharField(required=True, allow_null=False)
    data = PropertyDTO(source='datum', required=True, allow_null=False)
