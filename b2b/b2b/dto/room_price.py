# -*- coding: utf-8 -*-
from django.conf import settings
from rest_framework import serializers

from b2b import constants
from common.rounded_decimal_field import RoundedDecimalField


class RoomPriceDTO(serializers.Serializer):
    legal_entity_id = serializers.CharField(max_length=20)
    hotel = serializers.CharField(max_length=20)

    from_date = serializers.DateField(format=settings.BOOKING['date_format'])
    to_date = serializers.DateField(format=settings.BOOKING['date_format'])

    room_type = serializers.ChoiceField(choices=[x[0] for x in constants.Rooms.TYPES])

    # these ones are per-room-night prices
    pre_tax_price = RoundedDecimalField(max_digits=20, decimal_places=2)
    post_tax_price = RoundedDecimalField(max_digits=20, decimal_places=2)
    slab_type = serializers.RegexField(regex=r"[Dd]efault|[Ff]loor|[Qq]uote|[Cc]ustom|[Bb]2[Bb][Rr]ack(?:@\d+(?:\.\d+)?%)?",
                                       max_length=20)

    # this is total calculated for all room nights
    total_pre_tax_price = RoundedDecimalField(max_digits=20, decimal_places=2)

    audit_log = serializers.ListField(child=serializers.CharField(max_length=100))

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass
