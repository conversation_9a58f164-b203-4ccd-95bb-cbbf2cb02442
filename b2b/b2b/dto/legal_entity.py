from rest_framework import serializers

from .address import AddressDTO


class LegalEntityRequestDTO(serializers.Serializer):
    corporate_id = serializers.CharField()


class _AddressDTO(AddressDTO):
    default = serializers.BooleanField(default=False)


class _GstinDTO(serializers.Serializer):
    gstin = serializers.CharField()
    addresses = serializers.ListField(child=_AddressDTO())
    default = serializers.BooleanField(default=False)


class LegalEntityDTO(serializers.Serializer):
    id = serializers.CharField(max_length=20)
    legal_entity_id = serializers.CharField(max_length=20)
    legal_name = serializers.CharField()
    treebo_poc = serializers.CharField(allow_null=True)
    btc_admin_approval = serializers.BooleanField()
    btc_enabled = serializers.BooleanField()
    is_b2b_ta = serializers.BooleanField()
    is_ta = serializers.BooleanField()
    posttax_enabled = serializers.<PERSON>oleanField()
    slab_type = serializers.CharField(allow_null=True)
    address = AddressDTO()
    admin_name = serializers.CharField(max_length=50, allow_null=True, allow_blank=True)
    gstins = serializers.ListField(child=_GstinDTO())
    default = serializers.BooleanField(default=False)
    voucher_emails = serializers.ListField(child=serializers.CharField())
    active = serializers.BooleanField()
    primary_admin_email = serializers.EmailField(allow_null=True)
    treebo_poc_email = serializers.EmailField(allow_null=True)
    agency_type = serializers.CharField(allow_null=True, allow_blank=True)


class LegalEntitiesDTO(serializers.Serializer):
    legal_entities = serializers.ListField(child=LegalEntityDTO())


# These DTOs are used to get gstin state wise
class _StateGstinDTO(serializers.Serializer):
    name = serializers.CharField()
    gstins = serializers.ListField(child=_GstinDTO())
    legal_state_code = serializers.CharField(max_length=50, allow_blank=True, allow_null=True)
    default = serializers.BooleanField(default=False)


class _LegalEntityStateDTO(serializers.Serializer):
    name = serializers.CharField()
    address = AddressDTO()
    states = serializers.ListField(child=_StateGstinDTO())
    default = serializers.BooleanField(default=False)


class LegalEntitiesStateDTO(serializers.Serializer):
    legal_entities = serializers.ListField(child=_LegalEntityStateDTO())
