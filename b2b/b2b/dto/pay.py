# -*- coding: utf-8 -*-
from rest_framework import serializers

from b2b import constants
from b2b.dto.booking_request import CardDTO
from common.rounded_decimal_field import RoundedDecimalField


class PayRequestDTO(serializers.Serializer):
    amount = RoundedDecimalField(max_digits=20, decimal_places=2, min_value=1)
    receipt = serializers.CharField(max_length=250)
    notes = serializers.ListField(default=[])
    gateway = serializers.CharField(max_length=250)
    currency = serializers.CharField(max_length=20, default='INR')
    pg_meta = serializers.DictField()

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass


class PayResponseDTO(serializers.Serializer):
    order_id = serializers.CharField(max_length=250)
    receipt = serializers.CharField(max_length=250)
    currency = serializers.CharField(max_length=250)
    amount = RoundedDecimalField(max_digits=20, decimal_places=2, min_value=1)
    gateway = serializers.Char<PERSON>ield(max_length=250)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass
