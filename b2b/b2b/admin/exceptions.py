class InvalidPaymentAmount(Exception):
    """
    Raised when via Django Admin an invalid amount is passed for creating a payment link
    """

    def __init__(self, booking_id, total_amount, requested_amount):
        message = "Please input amount less that {total_amount} for booking {booking_id}".format(
            total_amount=total_amount, booking_id=booking_id)
        super(InvalidPaymentAmount, self).__init__(message)


class TooManyBookingsRequested(Exception):
    """
    Raised when via Django Admin an too many bookings are selected for payment link generation
    """

    def __init__(self):
        message = "Cannot request payment links for more than 10 bookings at a time"
        super(TooManyBookingsRequested, self).__init__(message)