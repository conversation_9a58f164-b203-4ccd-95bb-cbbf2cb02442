import logging

import pytz
from dal import autocomplete
from django import forms
from django.contrib import admin
from django.contrib import messages
from django.contrib.admin.widgets import FilteredSelectMultiple
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from django.forms import ModelForm, ModelChoiceField, ChoiceField
from django.http import HttpResponseRedirect
from grappelli.forms import GrappelliSortableHiddenMixin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from import_export.tmp_storages import MediaStorage

from b2b import constants
from b2b.admin.exceptions import TooManyBookingsRequested
from b2b.admin.mixins import CustomPermissionsMixin
from b2b.admin.payment_link_generator_service import PaymentGeneratorService
from b2b.admin.utils.related_field_widget_wrapper import RelatedFieldWidgetCanAdd
from b2b.constants import Countries, RecordChangeReason
from b2b.constants import CustomPermissions
from b2b.domain.services import BookingService
from b2b.models import Address, HotelAttribute, LegalEntity, AccountAdmin, Account
from b2b.models import (
    Booking, BookingAttributes, BookingInclusions, Corporate, CustomMealPricing,
    CustomRoomPricing, Hotel, HxMapping, StandardInclusionsPricing, StandardMealPricing,
    StandardRoomPricing, SalesPocPromotedHotelsMapping, PromotedHotel
)
from b2b.models import CorporateAttributes
from b2b.models import Person
from b2b.models import PromotedHotelAttribute
from b2b.models import SalesPocHierarchy
from b2b.models import State
from b2b.models import User
from b2b.models.corporate import LegalEntityAttributes
from b2b.models.room_night_discount import RoomNightDiscount
from b2b.payments.models import Payment

logger = logging.getLogger(__name__)


class CustomRoomPricingForm(ModelForm):
    legal_entity = ModelChoiceField(
        queryset=LegalEntity.objects.all(),
        widget=autocomplete.ModelSelect2(url='legal-entity-autocomplete')
    )

    hotel = ModelChoiceField(
        queryset=Hotel.objects.all(),
        widget=autocomplete.ModelSelect2(url='hotels-autocomplete')
    )

    def __init__(self, *args, **kwargs):
        super(CustomRoomPricingForm, self).__init__(*args, **kwargs)
        self.fields['from_date'].required = True
        self.fields['to_date'].required = True
        self.fields['max_length_of_stay'].required = True

    class Meta:
        model = CustomRoomPricing
        fields = ('__all__')


class CustomMealPricingForm(ModelForm):
    corporate = ModelChoiceField(
        queryset=Corporate.objects.all(),
        widget=autocomplete.ModelSelect2(url='corporates-autocomplete', )
    )

    hotel = ModelChoiceField(
        queryset=Hotel.objects.all(),
        widget=autocomplete.ModelSelect2(url='hotels-autocomplete')
    )

    class Meta:
        model = CustomMealPricing
        fields = ('__all__')


class StandardRoomPricingForm(ModelForm):
    hotel = ModelChoiceField(
        queryset=Hotel.objects.all(),
        widget=autocomplete.ModelSelect2(url='hotels-autocomplete')
    )

    class Meta:
        model = StandardRoomPricing
        fields = ('__all__')


class StandardMealPricingForm(ModelForm):
    hotel = ModelChoiceField(
        queryset=Hotel.objects.all(),
        widget=autocomplete.ModelSelect2(url='hotels-autocomplete')
    )

    class Meta:
        model = StandardMealPricing
        fields = ('__all__')


class StandardInclusionsPricingForm(ModelForm):
    hotel = ModelChoiceField(
        queryset=Hotel.objects.all(),
        widget=autocomplete.ModelSelect2(url='hotels-autocomplete')
    )
    inclusion = ChoiceField(choices=((constants.Inclusions.EPCharges, 'Extra-person Charges'),))

    class Meta:
        model = StandardInclusionsPricing
        fields = ('__all__')


class BookingAttributesForm(ModelForm):
    booking = ModelChoiceField(
        queryset=Booking.objects.all(),
        widget=autocomplete.ModelSelect2(url="bookings-autocomplete")
    )

    class Meta:
        model = Booking
        fields = ('__all__')


class HotelAttributeForm(ModelForm):
    booking = ModelChoiceField(
        queryset=HotelAttribute.objects.all(),
        widget=autocomplete.ModelSelect2(url="hotels-autocomplete")
    )

    class Meta:
        model = HotelAttribute
        fields = ('__all__')


class BookingInclusionsForm(ModelForm):
    booking = ModelChoiceField(
        queryset=Booking.objects.all(),
        widget=autocomplete.ModelSelect2(url="bookings-autocomplete")
    )

    class Meta:
        model = Booking
        fields = ('__all__')


class HotelAdminResource(resources.ModelResource):
    class Meta:
        model = Hotel
        skip_unchanged = True
        report_skipped = True
        import_id_fields = ('hotel_id',)
        fields = ('hotel_id', 'tcr_multiplier')

    def before_import_row(self, row, **kwargs):
        if Hotel.objects.get(hotel_id=row['hotel_id']):
            row['hotel_id'] = Hotel.objects.get(hotel_id=row['hotel_id']).hotel_id
        if not float(row['tcr_multiplier']) >= Hotel.TCR_MULTIPLIER_MIN_VALUE:
            raise RuntimeError(
                'TCR multiplier for hotel {h_id} should be greater than or equal to {tcr_min_value}'.format(
                    h_id=row['hotel_id'], tcr_min_value=Hotel.TCR_MULTIPLIER_MIN_VALUE))
        if not float(row['tcr_multiplier']) <= Hotel.TCR_MULTIPLIER_MAX_VALUE:
            raise RuntimeError('TCR multiplier for hotel {h_id} should be less than or equal to {tcr_max_value}'.format(
                h_id=row['hotel_id'], tcr_max_value=Hotel.TCR_MULTIPLIER_MAX_VALUE))


class HotelAdmin(ImportExportModelAdmin, GrappelliSortableHiddenMixin, CustomPermissionsMixin):
    tmp_storage_class = MediaStorage
    resource_class = HotelAdminResource
    search_fields = ['hotel_id', 'name', 'locality', 'street', 'city', 'hotelogix_id', 'external_id', 'phone_number']
    list_display = ['hotel_id', 'name', 'locality', 'street', 'city', 'hotelogix_id', 'external_id', 'phone_number',
                    'tcr_multiplier']


class StandardRoomPricingAdmin(CustomPermissionsMixin):
    form = StandardRoomPricingForm
    search_fields = ['hotel__name', 'hotel__hotelogix_id', 'room_type', 'description']
    list_display = ['get_hotel_name', 'room_type', 'description', 'from_date', 'to_date', 'pre_tax_default_price',
                    'pre_tax_quote_price', 'pre_tax_floor_price', 'b2b_rack_rate']
    fieldsets = ((None, {'fields': ('hotel', 'room_type', 'description', 'from_date', 'to_date')}),
                 ('Pre-Tax Prices',
                  {'fields': ('pre_tax_default_price', 'pre_tax_quote_price', 'pre_tax_floor_price',
                              'b2b_rack_rate')}),
                 )

    def get_hotel_name(self, obj):
        return obj.hotel.name

    def save_model(self, request, obj, form, change):
        if not obj.id:
            obj.created_by = request.user

        super(StandardRoomPricingAdmin, self).save_model(request, obj, form, change)

    get_hotel_name.short_description = 'Hotel Name'


class CustomRoomPricingAdmin(CustomPermissionsMixin):
    form = CustomRoomPricingForm
    search_fields = ['hotel__name', 'legal_entity__name', 'room_type', 'city', 'hotel__hotelogix_id']
    list_display = ['id', 'legal_entity_name', 'hotel_name', 'room_type', 'from_date', 'to_date',
                    'city', 'pre_tax_custom_price', 'ep_pre_tax_custom_price']
    fieldsets = ((None, {'fields': ('legal_entity', 'hotel', 'city', 'room_type', 'from_date', 'to_date')}),
                 ('Stay lengths', {'fields': ('min_length_of_stay', 'max_length_of_stay')}),
                 ('Pre-Tax Price', {'fields': ('pre_tax_custom_price',)}),
                 ('Extra Person Pre-Tax Price', {'fields': ('ep_pre_tax_custom_price',)}))

    def legal_entity_name(self, obj):
        return obj.legal_entity.name

    def hotel_name(self, obj):
        return obj.hotel.name

    def save_model(self, request, obj, form, change):
        if not obj.id:
            obj.created_by = request.user

        super(CustomRoomPricingAdmin, self).save_model(request, obj, form, change)


class CustomMealPricingAdmin(CustomPermissionsMixin):
    form = CustomMealPricingForm
    list_display = ['get_corp_id', 'get_corporate_id', 'get_hotel_id', 'from_date', 'to_date', 'meal_plan', 'meal_type',
                    'pre_tax_price', 'post_tax_price', 'created_by']
    search_fields = ['corporate__legal_name', 'corporate__trading_name', 'hotel__id', 'hotel__name',
                     'hotel__hotelogix_id']
    exclude = ['created_by']

    def get_corp_id(self, obj):
        return obj.corporate.id

    def get_corporate_id(self, obj):
        return obj.corporate.corporate_id

    def get_hotel_id(self, obj):
        return obj.hotel.hotel_id

    def save_model(self, request, obj, form, change):
        if not obj.id:
            obj.created_by = request.user

        super(CustomMealPricingAdmin, self).save_model(request, obj, form, change)

    get_corporate_id.short_description = "Corporate Code"
    get_hotel_id.short_description = "Hotel ID"
    get_corp_id.short_description = "Corporate ID"


class StandardInclusionsPricingAdmin(CustomPermissionsMixin):
    form = StandardInclusionsPricingForm
    list_display = ['get_hotel_name', 'room_type', 'from_date', 'to_date', 'category', 'inclusion', 'pricing_mechanism',
                    'pre_tax_price', 'post_tax_price', 'created_by']
    search_fields = ['hotel__name', 'room_type']
    fieldsets = (
        (None, {'fields': (
            'hotel', 'room_type', 'from_date', 'to_date', 'inclusion', 'pre_tax_price')}),
    )

    def get_hotel_name(self, obj):
        return obj.hotel.name

    def save_model(self, request, obj, form, change):
        if not obj.id:
            obj.created_by = request.user
            obj.new_pricing_scheme = True
            obj.category = constants.Inclusions.CategoryMapping[obj.inclusion]

        super(StandardInclusionsPricingAdmin, self).save_model(request, obj, form, change)

    get_hotel_name.short_description = "Hotel Name"


class StandardMealPricingAdmin(CustomPermissionsMixin):
    form = StandardMealPricingForm
    list_display = ['get_hotel_name', 'from_date', 'to_date', 'meal_plan', 'meal_type', 'pre_tax_price',
                    'post_tax_price', 'created_by']
    search_fields = ['hotel__name']
    fieldsets = (
        (None,
         {'fields': ('hotel', 'from_date', 'to_date', 'meal_plan', 'meal_type', 'pre_tax_price', 'post_tax_price')}),
    )

    def get_hotel_name(self, obj):
        return obj.hotel.name

    def save_model(self, request, obj, form, change):
        if not obj.id:
            obj.created_by = request.user

        super(StandardMealPricingAdmin, self).save_model(request, obj, form, change)

    get_hotel_name.short_description = "Hotel Name"


class HxMappingAdmin(CustomPermissionsMixin):
    pass


class BookingAdmin(CustomPermissionsMixin):
    list_display = ['booking_id', 'booking_date', 'legal_entity_name', 'hotel_name', 'status', 'check_in', 'check_out',
                    'sub_channel', 'total_amount', 'total_no_of_guests', 'no_of_rooms', 'is_soft_booking']
    search_fields = ['hotel__name', 'legal_entity__name', 'booking_id', 'sub_channel']
    readonly_fields = ('status', 'created_by')
    list_filter = ['check_in', 'check_out', 'sub_channel', 'status', 'created_by', 'created_at']
    ordering = ('-created_at',)
    exclude = ['agent']
    actions = ["generate_payment_link", "send_reminder"]

    def get_actions(self, request):
        actions = super(CustomPermissionsMixin, self).get_actions(request)
        if not self.validate_user_permission(request, CustomPermissions.CHANGE):
            actions.pop("generate_payment_link", None)
            actions.pop("send_reminder", None)
        return actions

    def send_reminder(modeladmin, request, queryset):
        booking_ids = queryset.values_list('booking_id', flat=True)
        logger.info("Sending payment reminder via Django Admin for booking_ids: {}".format(booking_ids))
        try:
            PaymentGeneratorService.send_reminder(booking_ids)
            return HttpResponseRedirect(
                "{path}?booking_ids={b_ids}&success=True".format(path=reverse('generate-payment-link'),
                                                                 b_ids=','.join(booking_ids)))
        except TooManyBookingsRequested as tbr:
            logger.info(
                "Exception in send_reminder action for bookings: {b_ids}, error: {err}".format(b_ids=booking_ids,
                                                                                               err=tbr.message))
            messages.add_message(request, messages.WARNING, tbr.message)
        except Exception as e:
            msg = "Exception, Payment Reminder[Django Admin], booking_ids: {booking_ids}, errors: {errors}".format(
                booking_ids=booking_ids, errors=str(e))
            logger.info(msg)
            messages.add_message(request, messages.WARNING, msg)
            return

    def generate_payment_link(modeladmin, request, queryset):
        booking_ids = queryset.values_list('booking_id', flat=True)
        logger.info("Generate Payment Link via Django Admin for booking_ids: {}".format(booking_ids))
        if len(booking_ids) > constants.PAYMENT_LINK_REQUEST_LIMIT:
            messages.add_message(request, messages.WARNING,
                                 "Cannot request payment links for more than 10 bookings at a time")
            return
        booking_ids = ','.join(list(booking_ids))
        return HttpResponseRedirect(
            "{path}?booking_ids={b_ids}".format(path=reverse('generate-payment-link'), b_ids=booking_ids))

    generate_payment_link.short_description = "Generate Payment Links"

    def total_no_of_guests(self, obj):
        return obj.get_guest_count()

    def booking_date(self, obj):
        utc_time_aware = obj.created_at.replace(tzinfo=pytz.utc)
        return utc_time_aware.astimezone(pytz.timezone('Asia/Calcutta'))

    def legal_entity_name(self, obj):
        return obj.legal_entity.name

    def hotel_name(self, obj):
        return obj.hotel.name

    def total_amount(self, obj):
        return BookingService.total_booking_amount(obj)

    def no_of_rooms(self, obj):
        return obj.get_booked_rooms().count()


class BookingAttributesAdmin(CustomPermissionsMixin):
    form = BookingAttributesForm
    list_display = ['get_booking_id', 'key', 'value']
    search_fields = ['booking__booking_id']
    fieldsets = ((None, {'fields': ('booking', 'key', 'value')}),
                 )

    def save_model(self, request, obj, form, change):
        if not BookingService.update_attribute(obj, request.user):
            super(BookingAttributesAdmin, self).save_model(request, obj, form, change)

    def get_booking_id(self, obj):
        return obj.booking.booking_id

    get_booking_id.short_description = "Booking ID"


class HotelAttributeResource(resources.ModelResource):
    class Meta:
        model = HotelAttribute
        exclude = ('id',)
        skip_unchanged = True
        report_skipped = True
        import_id_fields = ('key', 'hotel')
        fields = ('key', 'value', 'hotel')

    def before_import_row(self, row, **kwargs):
        """
        Converting hotelogix id to hotel name|id.
        """
        row['hotel'] = Hotel.objects.get(hotelogix_id=row['hotel']).id


class HotelAttributesAdmin(ImportExportModelAdmin, CustomPermissionsMixin):
    tmp_storage_class = MediaStorage
    resource_class = HotelAttributeResource
    form = HotelAttributeForm
    list_display = ['hotel', 'key', 'value']
    search_fields = ['key']
    fieldsets = ((None, {'fields': ('hotel', 'key', 'value')}),
                 )

    def has_add_permission(self, request):
        return False

    def get_list_display_links(self, request, list_display):
        """
        Disabling links in django admin hotelAttributes view
        """
        if self.list_display_links or not list_display:
            return self.list_display_links
        else:
            return (None,)


class BookingInclusionsAdmin(CustomPermissionsMixin):
    form = BookingInclusionsForm
    list_display = ['get_booking_id', 'get_booked_rooms', 'category', 'inclusion', 'quantity', 'pre_tax_price',
                    'post_tax_price']

    search_fields = ['booking__booking_id']

    def get_booking_id(self, obj):
        return obj.booking.booking_id

    def get_booked_rooms(self, obj):
        return obj.booked_room.room_type

    get_booking_id.short_description = "Booking ID"
    get_booked_rooms.short_description = 'Rooms'


class CorporateAttributeAdmin(CustomPermissionsMixin):
    list_display = ['corporate_id', 'key', 'value']
    search_fields = ['corporate__trading_name', 'key', 'corporate__corporate_id']

    def save_model(self, request, obj, form, change):
        if change:
            obj._change_reason = RecordChangeReason.UPDATED_VIA_ADMIN
        super(CorporateAttributeAdmin, self).save_model(request, obj, form, change)


class LegalEntityAttributeAdmin(CustomPermissionsMixin):
    list_display = ['legal_entity_id', 'key', 'value']
    search_fields = ['legal_entity__name', 'key', 'legal_entity__legal_entity_id']

    def save_model(self, request, obj, form, change):
        if change:
            obj._change_reason = RecordChangeReason.UPDATED_VIA_ADMIN
        super(LegalEntityAttributeAdmin, self).save_model(request, obj, form, change)


class PaymentsAdmin(CustomPermissionsMixin):
    list_display = ['booking_id', 'order_id', 'payment_id', 'customer_id', 'amount', 'gateway', 'status']
    search_fields = ['booking__booking_id', 'order_id', 'payment_id', 'customer_id']
    list_filter = ['status']

    def booking_id(self, obj):
        return obj.booking.booking_id


class SalesPocHierarchyAdmin(CustomPermissionsMixin):
    list_display = ['poc_first_name', 'poc_last_name', 'poc_email', 'poc_number', 'manager_first_name',
                    'manager_last_name', 'manager_email', 'manager_number']

    search_fields = ['poc__first_name', 'poc__last_name', 'poc__email', 'poc__phone_number', 'manager__first_name',
                     'manager__last_name', 'manager__email', 'manager__phone_number']

    def poc_first_name(self, obj):
        return obj.poc.first_name

    def poc_last_name(self, obj):
        return obj.poc.last_name

    def poc_email(self, obj):
        return obj.poc.email

    def poc_number(self, obj):
        return obj.poc.phone_number

    def manager_first_name(self, obj):
        return obj.manager.first_name

    def manager_last_name(self, obj):
        return obj.manager.last_name

    def manager_email(self, obj):
        return obj.manager.email

    def manager_number(self, obj):
        return obj.manager.phone_number


class StateAdmin(CustomPermissionsMixin):
    def get_model_perms(self, request):
        """
        Return empty perms dict thus hiding the model from admin index.
        """
        return {}


class AddressAdminModelForm(forms.ModelForm):
    state = forms.ModelChoiceField(queryset=State.objects.all(),
                                   widget=RelatedFieldWidgetCanAdd(related_model=State,
                                                                   url='state-autocomplete'),
                                   label='State:')
    country = forms.ChoiceField(choices=Countries.COUNTRY_CHOICES, label='Country')

    class Meta:
        model = Address
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super(AddressAdminModelForm, self).__init__(*args, **kwargs)
        obj = kwargs.get('instance')
        self.fields['locality'].required = True
        if obj:
            self.fields['state'] = forms.CharField(widget=forms.TextInput(attrs={'readonly': 'readonly'}))
            self.fields['country'] = forms.CharField(widget=forms.TextInput(attrs={'readonly': 'readonly'}))
            self.fields['city'].widget.attrs['readonly'] = 'readonly'
            self.fields['pincode'].widget.attrs['readonly'] = 'readonly'

            self.fields['building'].initial = obj.building
            self.fields['street'].initial = obj.street
            self.fields['locality'].initial = obj.locality
            self.fields['landmark'].initial = obj.landmark
            self.fields['city'].initial = obj.city
            self.fields['state'].initial = obj.state
            self.fields['pincode'].initial = obj.pincode
            self.fields['country'].initial = obj.country

    def clean_state(self):
        data = self.cleaned_data.get(constants.Address.STATE, '')
        if isinstance(data, State):
            return data.name
        return data


class AddressAdmin(CustomPermissionsMixin):
    form = AddressAdminModelForm


class PromotedHotelAttributeAdmin(CustomPermissionsMixin):
    list_display = ['key', 'value', 'start_date', 'end_date']
    pass


class SalesPocPromotedHotelsMappingResource(resources.ModelResource):
    class Meta:
        model = SalesPocPromotedHotelsMapping
        exclude = ('id',)
        skip_unchanged = True
        report_skipped = True
        import_id_fields = ('sales_poc_email', 'hotel')
        fields = ('sales_poc_email', 'hotel', 'active')

    def before_import_row(self, row, **kwargs):
        """
        Converting hotelogix id to hotel name|id.
        """

        if row['active'] in [None, '']:
            row['active'] = True
        elif row['active'].strip().lower() == 'true':
            row['active'] = True
        elif row['active'].strip().lower() == 'false':
            row['active'] = False

        row['hotel'] = PromotedHotel.objects.get(hotel_id=row['hotel']).id
        row['sales_poc_email'] = User.objects.get(email=row['sales_poc_email'].strip()).email


class SalesPocPromotedHotelsMappingAdmin(ImportExportModelAdmin, CustomPermissionsMixin):
    tmp_storage_class = MediaStorage
    resource_class = SalesPocPromotedHotelsMappingResource
    list_display = ['sales_poc_email', 'hotel_name', 'active']
    search_fields = ['sales_poc_email', 'hotel__name']
    fieldsets = ((None, {'fields': ('sales_poc', 'hotel', 'active')}),
                 )

    def has_add_permission(self, request):
        return False

    def hotel_name(self, obj):
        try:
            return Hotel.objects.get(hotel_id=obj.hotel.hotel_id).name
        except Hotel.DoesNotExist as exp:
            return None

    def get_list_display_links(self, request, list_display):
        """
        Disabling links in django admin hotelAttributes view
        """
        if self.list_display_links or not list_display:
            return self.list_display_links
        else:
            return (None,)


class PromotedHotelResource(resources.ModelResource):
    class Meta:
        model = PromotedHotel
        exclude = ('id',)
        skip_unchanged = True
        report_skipped = True
        import_id_fields = ('hotel_id',)
        fields = ('sales_poc_incentive_factor', 'hotel_id', 'priority')


class PromotedHotelAdmin(ImportExportModelAdmin, CustomPermissionsMixin):
    tmp_storage_class = MediaStorage
    resource_class = PromotedHotelResource
    list_display = ['hotel_id', 'sales_poc_incentive_factor', 'priority']
    search_fields = ['hotel_id']
    fieldsets = ((None, {'fields': ('hotel_id', 'sales_poc_incentive_factor', 'priority')}),
                 )

    def has_add_permission(self, request):
        return False

    def hotel(self, obj):
        try:
            Hotel.objects.get(hotel_id=obj.hotel_id).name
        except Hotel.DoesNotExist:
            return None

    def get_list_display_links(self, request, list_display):
        """
        Disabling links in django admin hotelAttributes view
        """
        if self.list_display_links or not list_display:
            return self.list_display_links
        else:
            return (None,)


class RoomNightDiscountAdmin(CustomPermissionsMixin):
    list_display = ['min_slab', 'max_slab', 'discount', 'active']
    pass

    # class Meta:
    #     model = RoomNightDiscount
    #     fields = '__all__'


class AccountAdminModelForm(forms.ModelForm):
    account = forms.ModelChoiceField(queryset=Account.objects.all(),
                                     widget=autocomplete.ModelSelect2(
                                         url='account-autocomplete'))
    auth_user = forms.ModelChoiceField(queryset=User.objects.all(),
                                       widget=autocomplete.ModelSelect2(url='users-autocomplete'))

    default_recommended_hotels = forms.ModelMultipleChoiceField(queryset=Hotel.objects.filter(active=True),
                                                                widget=FilteredSelectMultiple(
                                                                    "Recommended hotels for "
                                                                    "this admin:",
                                                                    is_stacked=False))
    is_finance_manager = forms.BooleanField(required=False, initial=False)
    is_super_admin = forms.BooleanField(required=False, initial=False)

    def __init__(self, *args, **kwargs):
        super(AccountAdminModelForm, self).__init__(*args, **kwargs)
        obj = kwargs.get('instance')
        if obj:
            self.fields['is_finance_manager'].initial = obj.auth_user.groups.filter(
                name=constants.AccountAdmin.FINANCE_GROUP).exists()
            self.fields['is_super_admin'].initial = obj.auth_user.groups.filter(
                name=constants.AccountAdmin.MANAGER_GROUP).exists()
            self.fields['default_recommended_hotels'].initial = Hotel.objects.filter(
                hotel_id__in=obj.default_recommended_hotels.split(','))

    def clean(self):
        if len(self.cleaned_data.get('default_recommended_hotels', [])) != 3:
            self.add_error('default_recommended_hotels', 'Selected hotels must be equals to 3.')
        super(AccountAdminModelForm, self).clean()


class AccountAdminAdmin(CustomPermissionsMixin):
    form = AccountAdminModelForm
    search_fields = ['auth_user__email', 'account__account_id']
    list_display = ['get_account_id', 'get_account_corporate', 'get_account_treebo_poc', 'get_account_primary_admin',
                    'get_account_is_active', 'get_user', 'default_recommended_hotels']
    list_display_links = ['get_account_id', 'get_user']
    exclude = ['default_recommended_hotels',]
    ordering = ['-modified_at']

    def get_account_id(self, obj):
        return obj.account.account_id

    def get_account_corporate(self, obj):
        return obj.account.corporate

    def get_account_treebo_poc(self, obj):
        return obj.account.treebo_poc

    def get_account_primary_admin(self, obj):
        return obj.account.primary_admin

    def get_account_is_active(self, obj):
        return obj.account.is_active

    def get_user(self, obj):
        return obj.auth_user

    get_account_id.short_description = "Account ID"
    get_account_corporate.short_description = "Account Corporate"
    get_account_treebo_poc.short_description = "Account POC"
    get_account_primary_admin.short_description = "Account Primary Admin"
    get_account_is_active.short_description = "Account Active"
    get_user.short_description = "Primus Admin"

    def save_model(self, request, obj, form, change):
        user = form.cleaned_data['auth_user']
        if not form.cleaned_data['is_finance_manager']:
            user.groups.remove(Group.objects.get(name=constants.AccountAdmin.FINANCE_GROUP))
        else:
            user.groups.add(Group.objects.get(name=constants.AccountAdmin.FINANCE_GROUP))

        if not form.cleaned_data['is_super_admin']:
            user.groups.remove(Group.objects.get(name=constants.AccountAdmin.MANAGER_GROUP))
        else:
            user.groups.add(Group.objects.get(name=constants.AccountAdmin.MANAGER_GROUP))

        selected_hotels = form.cleaned_data['default_recommended_hotels']
        if selected_hotels:
            obj.default_recommended_hotels = ','.join(
                selected_hotels.values_list('hotel_id', flat=True))

        obj._change_reason = RecordChangeReason.UPDATED_VIA_ADMIN

        super(AccountAdminAdmin, self).save_model(request, obj, form, change)


admin.site.register(Hotel, HotelAdmin)
admin.site.register(StandardRoomPricing, StandardRoomPricingAdmin)
admin.site.register(CustomRoomPricing, CustomRoomPricingAdmin)
# admin.site.register(CustomMealPricing, CustomMealPricingAdmin)
admin.site.register(StandardInclusionsPricing, StandardInclusionsPricingAdmin)
# admin.site.register(StandardMealPricing, StandardMealPricingAdmin)
admin.site.register(HxMapping, HxMappingAdmin)

admin.site.register(Booking, BookingAdmin)
admin.site.register(BookingAttributes, BookingAttributesAdmin)
admin.site.register(HotelAttribute, HotelAttributesAdmin)
admin.site.register(BookingInclusions, BookingInclusionsAdmin)
admin.site.register(Payment, PaymentsAdmin)
admin.site.register(Person)
admin.site.register(Address, AddressAdmin)
admin.site.register(CorporateAttributes, CorporateAttributeAdmin)
admin.site.register(LegalEntityAttributes, LegalEntityAttributeAdmin)
admin.site.register(Permission)
admin.site.register(ContentType)
admin.site.register(SalesPocHierarchy, SalesPocHierarchyAdmin)
admin.site.register(State, StateAdmin)
admin.site.register(PromotedHotelAttribute, PromotedHotelAttributeAdmin)

admin.site.register(RoomNightDiscount, RoomNightDiscountAdmin)
admin.site.register(SalesPocPromotedHotelsMapping, SalesPocPromotedHotelsMappingAdmin)
admin.site.register(PromotedHotel, PromotedHotelAdmin)
admin.site.register(AccountAdmin, AccountAdminAdmin)
