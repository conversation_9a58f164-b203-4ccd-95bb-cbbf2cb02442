# -*- coding: utf-8 -*-

from django.db import models
from djutil.models import TimeStampedModel

from b2b import constants
from b2b.models.mixins import DefaultPermissions
# pylint: disable=invalid-name


class Payment(TimeStampedModel, DefaultPermissions):
    class Type(object):
        LOYALTY = 'Loyalty'
        Choices = (
            (LOYALTY, 'Loyalty'),
        )

    class PaymentSource(object):
        HX = 'HX'
        TA_AUTOMATION = 'AUTOMATION'
        TA_PORTAL = 'TA_PORTAL'
        CRS = 'CRS'
        Choices = (
            (HX, 'HX'),
            (CRS, 'CRS'),
            (TA_AUTOMATION, 'TA_AUTOMATION'),
            (TA_PORTAL, 'TA_PORTAL'),
        )

    booking_id = models.CharField(max_length=50, null=True)
    order_id = models.CharField(max_length=20)
    payment_id = models.CharField(max_length=500, blank=True, default='')

    # Total payment amount
    amount = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    customer_id = models.CharField(max_length=500, blank=True, default='', null=True)
    currency = models.CharField(max_length=20, blank=True, default='')
    gateway = models.CharField(max_length=200, blank=True, default='')
    entity_type = models.CharField(choices=Type.Choices, blank=True, max_length=250)
    entity_id = models.CharField(max_length=250, blank=True)
    payment_source = models.CharField(choices=PaymentSource.Choices, blank=True, null=True, max_length=20)
    payment_link = models.CharField(blank=True, null=True, max_length=2000)
    status = models.CharField(max_length=50,
                              choices=constants.Payments.STATUSES,
                              default=constants.Payments.INITIATED_ORDER)
    extra_id = models.CharField(max_length=20, blank=True, default=None, null=True)
    notes = models.TextField(blank=True, default='')

    # Would be used in cases of part payments
    due_amount = models.DecimalField(null=True, max_digits=12, decimal_places=2)
    due_date = models.DateField(null=True)
    paid_date = models.DateField(null=True)

    class Meta(DefaultPermissions.Meta):
        verbose_name = 'Payment'
        verbose_name_plural = 'Payments'
        get_latest_by = 'modified_at'

    def __str__(self):
        return "{order_id} | {booking_id} | {status}".format(order_id=self.order_id,
                                                             booking_id=self.booking_id,
                                                             status=self.status)
