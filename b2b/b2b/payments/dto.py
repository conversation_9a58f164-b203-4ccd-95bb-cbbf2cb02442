from rest_framework import serializers
from common.rounded_decimal_field import RoundedDecimalField


class CardDTO(serializers.Serializer):
    id = serializers.CharField()
    name = serializers.CharField(max_length=200)
    last4 = serializers.CharField(max_length=4, min_length=4)
    network = serializers.CharField(max_length=200)
    expiry_month = serializers.IntegerField(min_value=1, max_value=12)
    expiry_year = serializers.IntegerField(min_value=2017, max_value=2100)
    emi = serializers.CharField(max_length=200, required=False, allow_blank=True)
    issuer = serializers.CharField(max_length=200, required=False, allow_blank=True)
    timestamp = serializers.CharField(required=False, allow_blank=True)
    used_at = serializers.CharField(required=False, allow_blank=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class _UserDTO(serializers.Serializer):
    name = serializers.CharField(source='get_full_name', allow_blank=True, allow_null=True)
    email = serializers.EmailField(allow_blank=True, allow_null=True)
    phone = serializers.CharField(source='phone_number', allow_blank=True, allow_null=True)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass


class InitiatePaymentRequestDTO(serializers.Serializer):
    amount = RoundedDecimalField(max_digits=20, decimal_places=2)
    currency = serializers.CharField(max_length=3, min_length=3, default='INR')
    receipt = serializers.CharField(required=False)
    notes = serializers.ListField(child=serializers.CharField(), required=False, allow_null=True)
    current_user = _UserDTO()
    cards = CardDTO(required=False, many=True)
    # Gateway specific details
    pg_meta = serializers.DictField(required=False)
    booking_id = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    payment_source = serializers.CharField(required=False, allow_blank=True)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass


class _BookingPaymentDTO(serializers.Serializer):
    booking_id = serializers.CharField()
    amount = RoundedDecimalField(max_digits=20, decimal_places=2)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class InitiateBulkPaymentRequestDTO(serializers.Serializer):
    currency = serializers.CharField(max_length=3, min_length=3, default='INR')
    receipt = serializers.CharField(required=False)
    notes = serializers.ListField(child=serializers.CharField(), allow_null=True, required=False)
    cards = CardDTO(required=False, many=True)
    payments = _BookingPaymentDTO(many=True)
    current_user = _UserDTO()

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass
