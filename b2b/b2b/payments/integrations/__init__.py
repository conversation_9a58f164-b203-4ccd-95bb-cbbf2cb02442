# -*- coding: utf-8 -*-
from .payment_soa import PaymentSOA


_payment_backends = [PaymentSOA]


def get_payment_backend(name):
    """
    :param name: name of the chosen class (that implements NotificationBackend interface)
    :return: the requested notification backend
    """
    for cls in _payment_backends:
        if name == cls.__name__:
            return cls

    raise RuntimeError("The Payment backend requested ('{name}') is not available".format(name=name))
