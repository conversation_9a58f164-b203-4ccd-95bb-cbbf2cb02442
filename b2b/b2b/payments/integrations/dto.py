from rest_framework import serializers


class CardDTO(serializers.Serializer):
    id = serializers.CharField()
    name = serializers.Char<PERSON>ield(max_length=200)
    last4 = serializers.CharField(max_length=4, min_length=4)
    network = serializers.CharField(max_length=200)
    expiry_month = serializers.IntegerField(min_value=1, max_value=12)
    expiry_year = serializers.IntegerField(min_value=2017, max_value=2100)
    emi = serializers.CharField(max_length=200, required=False, allow_blank=True)
    issuer = serializers.CharField(max_length=200, required=False, allow_blank=True)
    timestamp = serializers.CharField(required=False, allow_blank=True)
    used_at = serializers.CharField(required=False, allow_blank=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class _UserDTO(serializers.Serializer):
    name = serializers.Char<PERSON>ield(allow_blank=True, allow_null=True)
    email = serializers.EmailField(allow_blank=True, allow_null=True)
    phone = serializers.CharField(allow_blank=True, allow_null=True)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass


class InitiatePaymentRequestDTO(serializers.Serializer):
    amount = serializers.DecimalField(max_digits=20, decimal_places=2)
    currency = serializers.CharField(max_length=3, min_length=3, default='INR')
    receipt = serializers.CharField(required=False)
    current_user = _UserDTO()
    cards = CardDTO(required=False, many=True)
    pg_meta = serializers.DictField(required=False, default={})
    notes = serializers.ListField(child=serializers.CharField(), required=False)
    channel_callback_url = serializers.CharField(required=False, allow_blank=True)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass
