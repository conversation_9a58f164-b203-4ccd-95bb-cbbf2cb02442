# -*- coding: utf-8 -*-
import abc


class PaymentBackend(object, metaclass=abc.ABCMeta):

    @classmethod
    @abc.abstractmethod
    def initiate_payment(cls, initiate_payment_request_dto):
        """
        Initiates the payment for checkout in PaymentSOA
        :param initiate_payment_request_dto: InitiatePaymentRequestDTO Object
        :return: InitiatePaymentResponseDTO
        """
        raise NotImplementedError("'{n}' needs to implement initiate_payment method".format(n=cls.__name__))

    @classmethod
    @abc.abstractmethod
    def verify_payment(cls, verify_payment_request_dto):
        """
        Verifies and Finalizes payment in PaymentSOA
        :param verify_payment_request_dto: VerifyPaymentRequestDTO object
        :return: VerifyPaymentResponseDTO
        """
        raise NotImplementedError("'{n}' needs to implement verify_payment method".format(n=cls.__name__))

    @classmethod
    @abc.abstractmethod
    def pay(cls, pay_request_dto):
        raise NotImplementedError("'{n}' needs to implement pay method".format(n=cls.__name__))

    @classmethod
    @abc.abstractmethod
    def refund(cls, refund_request_dto):
        raise NotImplementedError("'{n}' needs to implement refund method".format(n=cls.__name__))
