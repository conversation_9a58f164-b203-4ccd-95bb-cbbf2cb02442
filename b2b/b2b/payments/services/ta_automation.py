# pylint: disable=unsubscriptable-object
import logging
from datetime import timedelta, datetime

from django.db import connection
from django.utils.timezone import localtime
from treebo_commons.utils import dateutils

from b2b.constants import TA_AUTOMATION_CANCELLATION_HOUR, TA_AUTOMATION_LAUNCH
from b2b.domain.services.exceptions import InvalidDTO
from b2b.payments.models.automation_config import AutomationConfig
from b2b.payments.services.dtos import BookingsByConfigDTO

logger = logging.getLogger(__name__)


class AutomationConfigService(object):

    @classmethod
    def get_payment_dues_by_automation_config(cls, booking_id, total_booking_amount,
                                              total_amount_paid, is_ta_booking):
        """
        :param booking_id, total_booking_amount, total_amount_paid
        :return:{
        "minimum_due_amount",
        "due_date_for_minimum_amount_due"
        "is_secure"
        }
        """
        bookings_with_config_info = cls.apply_automation_config(booking_id=booking_id)
        due_date_for_minimum_amount_due = ""
        if not bookings_with_config_info:
            minimum_due_amount = total_booking_amount - total_amount_paid
            is_secure = round(minimum_due_amount) <= 0
        else:
            executable_config, is_secure = cls.get_earliest_executable_config(bookings_with_config_info,
                                                                              total_booking_amount, total_amount_paid)
            minimum_due_amount = 0
            due_date_for_minimum_amount_due = ''
            if executable_config:
                minimum_due_amount = round(max((float(total_booking_amount) * float(
                    executable_config['percentage_to_be_paid']) / 100) - float(total_amount_paid), 0), 2)

                cancellation_config = cls.get_cancellation_config(executable_config, bookings_with_config_info)
                due_date_for_minimum_amount_due = cls.calculate_due_date(cancellation_config, executable_config)

        if not is_ta_booking:
            due_date_for_minimum_amount_due = ''
            is_secure = True

        return dict(minimum_due_amount=minimum_due_amount,
                    due_date_for_minimum_amount_due=due_date_for_minimum_amount_due,
                    is_secure=is_secure)

    @classmethod
    def calculate_due_date(cls, cancellation_config, executable_config):
        if not cancellation_config:
            return ''
        check_in = executable_config['check_in']
        check_in = datetime(year=check_in.year, month=check_in.month, day=check_in.day)
        if cancellation_config['days_to_checkin'] > 0:
            due_date_for_minimum_amount_due = check_in - timedelta(
                days=cancellation_config['days_to_checkin']) + timedelta(hours=TA_AUTOMATION_CANCELLATION_HOUR)
        else:
            due_date_for_minimum_amount_due = dateutils.current_datetime() + timedelta(minutes=30)
        return due_date_for_minimum_amount_due.strftime('%Y-%m-%d %H:%M:%S')

    @classmethod
    def apply_automation_config(cls, booking_id):
        columns_list = ['created_at', 'id', 'booking_id', 'check_in', 'payment_type', 'config_type',
                        'percentage_to_be_paid',
                        'minutes_from_booking_time', 'days_to_checkin']
        with connection.cursor() as cursor:
            cursor.execute("""select booking.created_at, booking.id, booking.booking_id, booking.check_in,
            config.payment_type, config.config_type,
            config.percentage_to_be_paid, config.minutes_from_booking_time, config.days_to_checkin
            from b2b_booking AS booking INNER JOIN b2b_automationconfig AS config
            ON booking.id=%s AND config.is_active='t' AND
            EXTRACT('days' FROM check_in - date_trunc('day',booking.created_at)) BETWEEN config.advanced_booking_window_start
            AND config.advanced_booking_window_end AND config.payment_type = booking.sub_channel AND booking.room_night_count
            BETWEEN config.room_nights_start_range AND config.room_nights_end_range
            AND booking_id NOT ILIKE '%%_v%%'
            AND
            booking.check_out >= date(now())
            AND
            booking.created_at >= %s
            AND
            (
              booking.check_in - CURRENT_DATE >= config.days_to_checkin
             )
            """, [booking_id, TA_AUTOMATION_LAUNCH])
            logger.info("Executing Query: {q}".format(q=cursor.query))
            rows = cursor.fetchall()
        bookings_with_config = cls.convert_tuple_to_dict(rows, columns_list)
        bookings_with_config = cls.change_booking_creation_time_to_localtime(bookings_with_config)
        return bookings_with_config

    @classmethod
    def get_earliest_executable_config(cls, bookings_with_config, total_booking_amount, total_amount_paid):
        """
        if there are multiple automation config, then we select the one with the highest 'days_to_checkin',
        as that will be at the top in the execution flow
        :param bookings_with_config:
        :return:
        """
        row = None
        is_secure = False
        for booking in bookings_with_config:
            if not row:
                row = booking
            elif row['days_to_checkin']:
                if row['days_to_checkin'] < booking['days_to_checkin']:
                    row = booking
        if row:
            config_amount = (float(row['percentage_to_be_paid'])) * float(total_booking_amount) / 100
            if round(config_amount, 2) <= round(total_amount_paid, 2):
                is_secure = True
        logger.info(f"Row: {row}, is_secure: {is_secure}")
        return row, is_secure

    @classmethod
    def get_cancellation_config(cls, executable_config, bookings_with_config):
        target_percentage = executable_config['percentage_to_be_paid']
        bookings_with_config = sorted(bookings_with_config, key=lambda x: x['days_to_checkin'], reverse=True)
        for config in bookings_with_config:
            if (config['percentage_to_be_paid'] == target_percentage
                    and config['config_type'] == AutomationConfig.ConfigType.CANCELLATION):
                logger.info(f"Config: {config}")
                return config
        logger.info("No Cancellation Config found")
        return []

    @classmethod
    def get_bookings_by_config(cls, config_type, target_date=None):
        """
        :param config_type:
        :param target_date:
        :return: A list of bookings along with the configs that match the above payment_type and config_type
        """
        if not target_date:
            target_date = datetime.now().strftime('%Y-%m-%d')
        bookings = cls.filter_by_config(config_type=config_type, target_date=target_date)
        booking_by_config_dto = BookingsByConfigDTO(data={"data": bookings})
        if not booking_by_config_dto.is_valid():
            logger.info(
                "Invalid input to booking config dto, error: {e}".format(e=booking_by_config_dto.errors))
            raise InvalidDTO(booking_by_config_dto)
        return booking_by_config_dto.data

    @classmethod
    def filter_by_config(cls, config_type, target_date):
        """
        This function gets all the booking against b2b_automationconfig, with the following clauses
        * Payment Type ( Paid by TA/ Guest )
        * Config Type ( Reminder / Cancellation )
        * Advanced Booking Window
        * Days before checkin
        * Room night range
        :return: A dictionary representation of a join between b2b_booking and b2b_automationconfig
        """
        column_list = ['id', 'booking_id', 'created_at', 'check_in', 'check_out', 'channel', 'sub_channel',
                       'legal_entity_id', 'travel_agent_entity_id', 'status', 'tax', 'pre_tax_price',
                       'room_night_count', 'days_to_checkin',
                       'minutes_from_booking_time', 'room_nights_start_range', 'room_nights_end_range',
                       'advanced_booking_window_start', 'advanced_booking_window_end', 'payment_type', 'config_type',
                       'percentage_to_be_paid']
        with connection.cursor() as cursor:
            cursor.execute("""
                    SELECT booking.id, booking.booking_id, booking.created_at , booking.check_in, booking.check_out,
                    booking.channel, booking.sub_channel, booking.legal_entity_id, booking.travel_agent_entity_id, booking.status, booking.tax, booking.pre_tax_price,
                    booking.room_night_count, config.days_to_checkin, config.minutes_from_booking_time, config.room_nights_start_range, config.room_nights_end_range, 
                    config.advanced_booking_window_start, config.advanced_booking_window_end, config.payment_type, config.config_type, 
                    config.percentage_to_be_paid FROM b2b_booking AS booking INNER JOIN b2b_automationconfig AS config
                    ON booking.status IN ('Confirmed') and booking.check_out>=date(now()) AND config.is_active='t' AND config.payment_type = booking.sub_channel
                    AND extract('days' from check_in - date_trunc('day',booking.created_at)) BETWEEN 
                    config.advanced_booking_window_start AND config.advanced_booking_window_end AND config.config_type=%s 
                    AND booking.room_night_count  
                    BETWEEN config.room_nights_start_range AND config.room_nights_end_range
                    AND
                    booking.created_at >= %s
                    AND
                    (
                      booking.check_in - %s = config.days_to_checkin  
                     )
                    """, [config_type, TA_AUTOMATION_LAUNCH, target_date])
            logger.info("Executing query: {q}".format(q=cursor.query))
            rows = cursor.fetchall()
        # converting to a dict representation
        res = cls.convert_tuple_to_dict(tuples=rows, column_list=column_list)
        res = cls.keep_valid_bookings(res)
        res = cls.remove_soft_bookings(res)
        return res

    @classmethod
    def keep_valid_bookings(cls, bookings):
        return [booking for booking in bookings if booking['legal_entity_id']]

    @classmethod
    def remove_soft_bookings(cls, bookings):
        from b2b.domain.services.booking import BookingService
        booking_ids = [b['id'] for b in bookings]
        non_soft_booking_ids = BookingService.filter_softbookings(booking_ids)
        return [booking for booking in bookings if booking['id'] in non_soft_booking_ids]

    @classmethod
    def convert_tuple_to_dict(cls, tuples, column_list):
        bookings = []
        for row in tuples:
            temp = {}
            for index, data in enumerate(row):
                temp[column_list[index]] = data
            bookings.append(temp)
        return bookings

    @classmethod
    def change_booking_creation_time_to_localtime(cls, bookings_with_config):
        for booking_data in bookings_with_config:
            if 'created_at' in booking_data:
                booking_data['created_at'] = localtime(booking_data['created_at'])
        return bookings_with_config
