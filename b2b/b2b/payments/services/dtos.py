from rest_framework import serializers

class _RowDTO(serializers.Serializer):
    id = serializers.CharField()
    booking_id = serializers.CharField()
    check_in = serializers.DateField()
    check_out = serializers.DateField()
    legal_entity_id = serializers.IntegerField()
    travel_agent_entity_id = serializers.IntegerField(allow_null=True, required=False)
    created_at = serializers.DateTimeField(format='%Y-%m-%d')
    channel = serializers.CharField()
    sub_channel = serializers.CharField()
    status = serializers.CharField()
    tax = serializers.FloatField()
    pre_tax_price = serializers.FloatField()
    days_to_checkin = serializers.IntegerField(allow_null=True)
    minutes_from_booking_time = serializers.IntegerField(allow_null=True)
    room_nights_start_range = serializers.IntegerField()
    room_nights_end_range = serializers.IntegerField()
    advanced_booking_window_start = serializers.IntegerField()
    advanced_booking_window_end = serializers.IntegerField()
    payment_type = serializers.CharField()
    config_type = serializers.CharField()
    room_night_count = serializers.IntegerField()
    percentage_to_be_paid = serializers.IntegerField()


class BookingsByConfigDTO(serializers.Serializer):
    data = serializers.ListField(child=_RowDTO())
