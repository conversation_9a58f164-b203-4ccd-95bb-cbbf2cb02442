# -*- coding: utf-8 -*-
import logging

from django.conf import settings
from django.db.models import Sum, Max
from rest_framework import reverse

from b2b import constants
from b2b.domain.services.exceptions import InvalidDTO
from b2b.domain.services.exceptions import PaymentVerificationFailed, PaymentInitiationFailed, PaymentFailed, \
    RefundFailed
from b2b.domain.utils.misc import get_random_alphanumeric_string
from b2b.dto.pay import PayRequestDTO
from b2b.dto.payment import PaymentDTO
from b2b.dto.refund import RefundRequestDTO
from b2b.payments.dto import InitiatePaymentRequestDTO, InitiateBulkPaymentRequestDTO
from b2b.payments.integrations import get_payment_backend
from b2b.payments.integrations.dto import InitiatePaymentRequestDTO as BackendPaymentInitDTO
from b2b.payments.models.payment import Payment
from common.utils import misc
from loyalty.exceptions import ServiceDown

logger = logging.getLogger(__name__)


class PaymentService(object):

    @staticmethod
    def get_payment(booking_id, order_id):
        return Payment.objects.filter(booking_id=booking_id, order_id=order_id).first()

    @staticmethod
    def payment_exists(booking_id, amount, status, payment_source):
        logger.info(
            'Looking for payment with booking_id: {b_id}, amount: {amt}, status: {status}, '
            'payment_source: {pmt_src}'.format(b_id=booking_id, amt=amount, status=status, pmt_src=payment_source))
        payments = Payment.objects.filter(booking_id=str(booking_id), amount=str(round(amount, 2)), status=status,
                                          payment_source=payment_source).exclude(payment_link=None)
        if payments:
            return payments[0]
        logger.info("Payment not found for booking id {bid}".format(bid=booking_id))
        return None

    @staticmethod
    def get_total_paid_multiple(booking_ids):
        booking_id_by_total_paid = Payment.objects.filter(booking_id__in=booking_ids,
                                                          status=constants.Payments.VERIFIED).values_list(
            'booking_id').annotate(Sum('amount'))
        return {row[0]: row[1] for row in booking_id_by_total_paid}

    @staticmethod
    def add_payment(payment_data):
        payment_dto = PaymentDTO(data=payment_data)
        logger.info("Saving payment, {}".format(payment_data))
        if payment_dto.is_valid():
            payment_dto.save()
        else:
            logger.info("Invalid payment info. Error: {}".format(payment_dto.errors))
            raise InvalidDTO(payment_dto)

    @staticmethod
    def get_latest_payment(booking_id):
        latest_payment = Payment.objects.filter(booking_id=booking_id, status=constants.Payments.VERIFIED).order_by(
            '-created_at')
        return latest_payment.first()

    @staticmethod
    def get_latest_payments_bulk(booking_ids):
        """
        Fetches the latest payment for each booking in the given list of booking_ids.
        Returns a dictionary mapping booking_id to the latest payment object.
        """
        latest_payments = (
            Payment.objects.filter(booking_id__in=booking_ids, status=constants.Payments.VERIFIED)
            .values('booking_id')
            .annotate(latest_created_at=Max('created_at'))
        )

        latest_payments_dict = {
            payment.booking_id: payment
            for payment in Payment.objects.filter(
                booking_id__in=booking_ids,
                status=constants.Payments.VERIFIED,
                created_at__in=[p['latest_created_at'] for p in latest_payments]
            )
        }

        return latest_payments_dict

    @staticmethod
    def get_total_paid(booking_id):
        payment_sum = Payment.objects.filter(booking_id=booking_id, status=constants.Payments.VERIFIED).aggregate(
            Sum('amount'))
        return float(payment_sum['amount__sum']) if payment_sum['amount__sum'] else 0

    @staticmethod
    def get_total_paid_bulk(booking_ids):
        """
        Fetches the total paid amount for each booking in the given list of booking_ids.
        Returns a dictionary mapping booking_id to the total paid amount.
        """
        payments = (
            Payment.objects.filter(booking_id__in=booking_ids, status=constants.Payments.VERIFIED)
            .values('booking_id')
            .annotate(total_paid=Sum('amount'))
        )

        total_paid_dict = {payment['booking_id']: float(payment['total_paid']) for payment in payments}

        return total_paid_dict

    @staticmethod
    def generate_unique_order_id():
        all_order_ids = Payment.objects.values_list('order_id', flat=True)
        all_order_ids = {hotel_id_tuple for hotel_id_tuple in all_order_ids}
        while True:
            random_string = get_random_alphanumeric_string(length=10)
            order_id = "gen_ord_{random_string}".format(random_string=random_string)
            if order_id not in all_order_ids:
                return order_id

    @classmethod
    def initiate_payment(cls, initiate_payment_request_dto):
        if not initiate_payment_request_dto.is_valid():
            raise PaymentInitiationFailed('Invalid request to payment service due to {errors}'.format(
                errors=initiate_payment_request_dto.errors))

        try:
            payment_backend = get_payment_backend(settings.PAYMENT_CONF['backend'])
            payment_data = initiate_payment_request_dto.data
            backend_req_data = dict(
                amount=payment_data['amount'],
                currency=payment_data['currency'],
                current_user=payment_data['current_user'],
                channel_callback_url='/'.join([x.strip('/') for x in
                                               [settings.B2B_HOST, reverse.reverse("payment-invoice-callback"), '']])
            )
            if payment_data.get('notes'):
                backend_req_data['notes'] = payment_data['notes']
            backend_req_data['pg_meta'] = payment_data.get('pg_meta') if payment_data.get('pg_meta') else {}
            if payment_data.get('cards'):
                backend_req_data['pg_meta']['cards'] = payment_data['cards']
            if payment_data.get('receipt'):
                backend_req_data['receipt'] = payment_data['receipt']

            initiate_payment_response_dto = payment_backend.initiate_payment(
                BackendPaymentInitDTO(data=backend_req_data))

            if not initiate_payment_response_dto.is_valid():
                raise PaymentInitiationFailed('Invalid response from payment backend {name} due to {errors}'.format(
                    errors=initiate_payment_response_dto.errors,
                    name=payment_backend.__name__))

            payment_resp_data = initiate_payment_response_dto.data
            payment_resp_data['booking_id'] = payment_data.get('booking_id')
            payment_resp_data['payment_source'] = payment_data.get('payment_source')
            payment_resp_data['payment_link'] = payment_resp_data.get('payment_link')
            payment_dto = PaymentDTO(data=payment_resp_data)
            if not payment_dto.is_valid():
                raise PaymentInitiationFailed(
                    'Invalid data to create payment from: backend {name} due to {errors}'.format(
                        errors=payment_dto.errors,
                        name=payment_backend.__name__))
            payment_dto.save()
            return initiate_payment_response_dto

        except Exception as e:
            logger.info("Failed to initiate payment for error as {e}".format(e=str(e)))

    @classmethod
    def verify_payment(cls, verify_payment_request_dto):
        try:
            logger.debug('Inside verify payment')
            if not verify_payment_request_dto.is_valid():
                raise PaymentVerificationFailed('Invalid data to verify payments service due to {errors}'.format(
                    errors=verify_payment_request_dto.errors))
            data = verify_payment_request_dto.data
            payments = Payment.objects.filter(order_id=data['order_id'])
            payments.update(status=constants.Payments.VERIFICATION_INITIATED)

            logger.debug('Verify payment Started')
            payment_backend = get_payment_backend(settings.PAYMENT_CONF['backend'])
            verify_payment_response_dto = payment_backend.verify_payment(verify_payment_request_dto)

            logger.debug('Verify payment Ended')
            if not verify_payment_response_dto.is_valid():
                payments.update(status=constants.Payments.VERIFICATION_FAILED)
                raise PaymentVerificationFailed("Invalid data from backend: {backend} due to {errors}".format(
                    backend=payment_backend.__name__,
                    errors=verify_payment_response_dto.errors))

            if not verify_payment_response_dto.data.get('status') in [constants.Payments.PASSED, constants.Payments.CAPTURED]:
                payments.update(status=constants.Payments.VERIFICATION_FAILED)
                raise PaymentVerificationFailed("payment not valid.")

            # skip the payments for the bookings and o which are already processed
            payments_being_processed = payments.exclude(status=constants.Payments.VERIFIED)
            payment_ids_to_be_processed = [payment.id for payment in payments_being_processed]
            payments_being_processed.update(status=constants.Payments.VERIFIED,
                                            payment_id=data.get('payment_service_id'))
            logger.info("processing payments for payment ids {pids}".format(pids=payment_ids_to_be_processed))
            payments_being_processed = Payment.objects.filter(id__in=payment_ids_to_be_processed)
            return payments_being_processed

        except Payment.DoesNotExist:
            raise PaymentVerificationFailed("The payment order id does not exist.")

    @classmethod
    def pay(cls, pay_request_dto):
        assert isinstance(pay_request_dto, PayRequestDTO)
        if not pay_request_dto.is_valid():
            logger.info("PayRequestDTO invalid DTO. Error {err}".format(err=pay_request_dto.errors))
            raise PaymentFailed(pay_request_dto.data['receipt'], pay_request_dto.errors)
        payment_backend = get_payment_backend(settings.PAYMENT_CONF['backend'])
        try:
            pay_response_dto = payment_backend.pay(pay_request_dto)
        except (PaymentFailed, ServiceDown):
            raise
        except Exception as e:
            logger.info("Pay failed for order {order_id} due to {err}".format(err=str(e),
                                                                                   order_id=pay_request_dto.data[
                                                                                       'receipt']))
            raise

        if not pay_response_dto.is_valid():
            logger.info("Invalid data from backend: {backend} for order_id {order_id}. Error {err}".format(
                backend=payment_backend.__name__, err=pay_response_dto.errors,
                order_id=pay_request_dto.data['receipt']))
            raise PaymentFailed(pay_request_dto.data['receipt'], pay_response_dto.errors)
        data = pay_response_dto.data
        data['status'] = constants.Payments.VERIFIED
        data['entity_type'] = Payment.Type.LOYALTY
        data['entity_id'] = data['receipt']
        payment_dto = PaymentDTO(data=data)
        if not payment_dto.is_valid():
            logger.info("Invalid data to create from backend: {backend} for order_id {order_id}. Error {err}".format(
                backend=payment_backend.__name__, err=payment_dto.errors,
                order_id=pay_request_dto.data['receipt']))
            raise PaymentFailed(pay_request_dto.data['receipt'], payment_dto.errors)

        payment_dto.save()
        return payment_dto

    @classmethod
    def refund(cls, refund_request_dto, order_id):
        assert isinstance(refund_request_dto, RefundRequestDTO)
        if not refund_request_dto.is_valid():
            logger.info("Invalid RefundRequestDTO for order {order_id}. Error {err}".format(
                order_id=order_id, err=refund_request_dto.errors))
            raise RefundFailed(order_id, refund_request_dto.errors)
        payment_backend = get_payment_backend(settings.PAYMENT_CONF['backend'])
        try:
            refund_response_dto = payment_backend.refund(refund_request_dto)
        except (RefundFailed, ServiceDown):
            raise
        except Exception as e:
            logger.info("Refund failed for order {order_id} due to {err}".format(err=str(e),
                                                                                      order_id=order_id))
            raise

        if not refund_response_dto.is_valid():
            msg = "Invalid data for refund from backend: {backend} due to {errors}".format(
                backend=payment_backend.__name__, errors=refund_response_dto.errors)
            logger.info(msg)
            raise RefundFailed(refund_request_dto.data['order_id'], msg)
        data = refund_response_dto.data
        data['status'] = constants.Payments.VERIFIED
        data['extra_id'] = data['receipt']
        data['order_id'] = data['refund_order_id']
        payment_dto = PaymentDTO(data=data)
        if not payment_dto.is_valid():
            msg = "Invalid data for refund from backend: {backend} due to {errors}".format(
                backend=payment_backend.__name__, errors=payment_dto.errors)
            logger.info(msg)
            raise RefundFailed(order_id, msg)

        payment_dto.save()
        return payment_dto

    @classmethod
    def initiate_payment_with_link(cls, booking_id, name, email, phone, amount):
        logger.info(
            "Generating payment with link for booking_id: {b_id}, name: {name}, email: {email}, phone: {phone}, "
            "amount: {amt}".format(
                b_id=booking_id, name=name, email=email, phone=phone, amt=amount))
        payment_req_dto = InitiatePaymentRequestDTO(data=dict(amount=amount,
                                                              currency='INR',
                                                              booking_id=booking_id,
                                                              receipt=booking_id,
                                                              current_user=dict(name=name,
                                                                                email=email,
                                                                                phone=phone
                                                                                ),
                                                              pg_meta=dict(
                                                                  customer=dict(name=misc.get_alphanum_string(name),
                                                                                email=email,
                                                                                contact=phone),
                                                                  generate_link=True),
                                                              payment_source=Payment.PaymentSource.TA_AUTOMATION
                                                              )
                                                    )
        initiate_payment_response = cls.initiate_payment(payment_req_dto)
        return Payment.objects.get(order_id=initiate_payment_response.data['order_id'])

    @classmethod
    def initiate_bulk_payment(cls, initiate_bulk_payment_data):
        init_bulk_payment_request_dto = InitiateBulkPaymentRequestDTO(data=initiate_bulk_payment_data)

        if not init_bulk_payment_request_dto.is_valid():
            raise PaymentInitiationFailed('Invalid request to payment service due to {errors}'.format(
                errors=init_bulk_payment_request_dto.errors))

        payment_backend = get_payment_backend(settings.PAYMENT_CONF['backend'])
        bp_data = init_bulk_payment_request_dto.data
        total_amount = sum(float(bp['amount']) for bp in bp_data['payments'])

        backend_req_data = dict(
            amount=total_amount,
            currency=bp_data['currency'],
            current_user=bp_data['current_user'],
            channel_callback_url='/'.join([x.strip('/') for x in
                                           [settings.B2B_HOST, reverse.reverse("payment-invoice-callback"), '']])
        )
        if bp_data.get('cards'):
            backend_req_data['cards'] = bp_data['cards']
        if bp_data.get('notes'):
            backend_req_data['notes'] = bp_data['notes']
        if bp_data.get('receipt'):
            backend_req_data['receipt'] = bp_data['receipt']

        payment_req_dto = BackendPaymentInitDTO(data=backend_req_data)
        initiate_payment_response_dto = payment_backend.initiate_payment(payment_req_dto)

        if not initiate_payment_response_dto.is_valid():
            raise PaymentInitiationFailed('Invalid response from payment backend {name} due to {errors}'.format(
                errors=initiate_payment_response_dto.errors,
                name=payment_backend.__name__))

        cls._save_payment_to_db(initiate_payment_response_dto.data, bp_data)
        return initiate_payment_response_dto

    @classmethod
    def _save_payment_to_db(cls, payment_response_data, bulkpayment_req_data):
        booking_payments = []

        for booking_payment in bulkpayment_req_data['payments']:
            p_response_data = dict(booking_id=booking_payment['booking_id'],
                                   amount=booking_payment['amount'],
                                   order_id=payment_response_data['order_id'],
                                   customer_id=payment_response_data['customer_id'],
                                   currency=payment_response_data['currency']
                                   )
            booking_payments.append(Payment(**p_response_data))

        Payment.objects.bulk_create(booking_payments)

    @staticmethod
    def update_booking_id_in_payment(booking_id, order_id):
        payment = Payment.objects.filter(order_id=order_id)
        if payment:
            payment.update(booking_id=booking_id)

    @staticmethod
    def get_all_payments(booking_id):
        payments = Payment.objects.filter(booking_id=booking_id)
        return payments
