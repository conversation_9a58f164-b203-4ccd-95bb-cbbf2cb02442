import json

import requests
from django.conf import settings
import logging

from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from b2b.constants import Timeouts

logger = logging.getLogger(__name__)


class PaymentLinkGenerationService(object):

    @staticmethod
    def get_headers():
        headers = {
            "X-Application": "B2B Backend",
            "Content-Type": "application/json"
        }
        enrich_outgoing_request(headers)
        return headers

    @classmethod
    def generate_pending_payment_link(cls, crs_booking_id, hotel_cs_id):
        try:
            payment_link_generation_url = settings.GROWTH_HOST + "payment/generate-pending-amount-payment-link/"
            logger.info(
                "Calling growth for payment link generation for booking_id {crs_booking_id}, hotel_id {hotel_cs_id}".format(
                    crs_booking_id=crs_booking_id, hotel_cs_id=hotel_cs_id))
            res = requests.post(payment_link_generation_url,
                                data=json.dumps({
                                    "group_code": crs_booking_id,
                                    "hotel_code": hotel_cs_id,
                                    "full_payment": True
                                }),
                                headers=cls.get_headers(),
                                timeout=(Timeouts.CONNECTION_TIMEOUT, Timeouts.RESPONSE_TIMEOUT))

            if res.status_code != 200:
                logger.info("Getting error while payment link generation for booking_id {crs_booking_id}".format(
                    crs_booking_id=crs_booking_id))
                return None
        except Exception as e:
            logger.info(e)
            return None

        return json.loads(res.content)['data']['payment_url']
