# -*- coding: utf-8 -*-

import logging
import traceback

from rest_framework import status
from rest_framework.response import Response
from b2b.api.api import TreeboAPI
from third_party_intg.axisrooms.dto.booking import AxisBookingDTO
from third_party_intg.axisrooms.domain.utils.converters import convert_axis_to_br_dto
from b2b.domain.services import BookingService
from b2b.dto.booking_status import BookingStatusDTO
from b2b.domain.services.exceptions import RoomsNotAvailable, LegalEntityHotelBookingRestricted
from b2b.api.api_error_response import APIErrorResponse


class BookingAPI(TreeboAPI):
    def post(self, request):
        """
        creates a new booking
        :param request: booking-request DTO
        :return:
        """
        logger = logging.getLogger(self.__class__.__name__)

        axis_dto = AxisBookingDTO(data=request.data)
        if not axis_dto.is_valid():
            return Response(axis_dto.errors, status.HTTP_400_BAD_REQUEST)

        try:
            br_dto = convert_axis_to_br_dto(axis_dto)

            if br_dto.is_valid():
                booking_status_dto = BookingService.new_booking(br_dto)

                assert type(booking_status_dto) is BookingStatusDTO

                if not booking_status_dto.is_valid():
                    raise RuntimeError('Invalid booking status DTO: {err}'.format(err=booking_status_dto.errors))

        except RoomsNotAvailable as e:  # NOQA
            logger.info("error occurred while creating a new booking: %s", str(e))
            return APIErrorResponse.rooms_not_available()
        except LegalEntityHotelBookingRestricted as e:
            logger.info(str(e))
            return APIErrorResponse.hotel_restricted_for_corporate(str(e))
        except Exception as e:  # NOQA
            # todo: we need to catch and treat specific exceptions
            traceback.print_exc()
            logger.info("error occurred while creating a new booking: %s", str(e))
            return APIErrorResponse.booking_request_failed(booking_dto=br_dto,
                                                           reason=str(e))

        response = Response(data=booking_status_dto.data,
                            status=status.HTTP_200_OK)
        return response
