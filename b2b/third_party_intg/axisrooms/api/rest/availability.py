import traceback
import logging
import requests
import json
from datetime import datetime, timedelta as td

from django.conf import settings

from b2b.constants import Timeouts
from third_party_intg.axisrooms.dto.availability import AxisCheckAvailabilityDTO
from rest_framework.response import Response
from b2b.api.api import Tree<PERSON><PERSON><PERSON>
from third_party_intg.axisrooms.domain.services.availability import AxisAvailabiltyService
from rest_framework import status


class AvailabilityAPI(TreeboAPI):
    def get(self, request):
        response_data = None
        logger = logging.getLogger(self.__class__.__name__)
        request_data = request.query_params.copy()


        if request_data.get('date'):
            request_data['checkin'] = request_data['date']
            request_data.pop('date')

        try:
            availability_dto = AxisCheckAvailabilityDTO(data=request_data)

            if not availability_dto.is_valid():
                return Response(availability_dto.errors, status.HTTP_400_BAD_REQUEST)

            #If request is coming from hx
            if request_data.get('hx'):
                if request_data['hx'] == 'true':
                    response_data = AxisAvailabiltyService.get_availability(availability_dto,hx=True)
                else:
                    response_data = AxisAvailabiltyService.get_availability(availability_dto)

            else:
                response_data = AxisAvailabiltyService.get_availability(availability_dto)
        except Exception as e:
            traceback.print_exc()
            logger.info("error occurred while getting details for "
                         "hotel {hotel_id} checkin {checkin} checkout {checkout}"
                         "room_config {room_config} : {err}".format(err=str(e),
                                                                    hotel_id=request_data['hotel'],
                                                                    checkin=request_data['checkin'],
                                                                    checkout=request_data['checkout'],
                                                                    room_config=request_data['roomconfig']))
            raise e
        if response_data:
            payload = json.dumps(response_data)
            response = requests.post(
                settings.AXIS_ROOMS['availability_url'], payload,timeout=(Timeouts.CONNECTION_TIMEOUT,Timeouts.RESPONSE_TIMEOUT))
        response = Response(data=response.content, status=200)
        return response
