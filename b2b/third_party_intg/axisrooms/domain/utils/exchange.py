# -*- coding: utf-8 -*-
from collections import defaultdict

from b2b.domain.utils.exceptions import UnsupportedDTOConversion


class DTOExchange(object):
    """
    faciliates exchange between different DTOs
    """

    def __init__(self):
        self._exchange = defaultdict(dict)

    def register(self, from_dto_cls, to_dto_cls, converter_function):
        if from_dto_cls not in self._exchange:
            self._exchange[from_dto_cls] = {}

        if to_dto_cls in self._exchange[from_dto_cls]:
            # FIXME: Better error message here
            raise Exception("Error raised during registration")
        self._exchange[from_dto_cls.__name__][to_dto_cls.__name__] = converter_function

    def convert(self, from_dto, to_dto_class, *args, **kwargs):
        """
        convert from one DTO to another based on registered converters
        :param from_dto: DTO *object* to convert from
        :param to_dto_class: DTO class to convert to
        :return: object of to_dto_class class after conversion
        """
        from_cls = from_dto.__class__.__name__

        if from_cls not in self._exchange:
            raise UnsupportedDTOConversion(from_cls, to_dto_class)

        if to_dto_class not in self._exchange[from_cls]:
            raise UnsupportedDTOConversion(from_cls, to_dto_class)

        converted_dto = self._exchange[from_cls][to_dto_class](from_dto, *args, **kwargs)
        assert type(converted_dto) is to_dto_class

        return converted_dto


dto_exchange = DTOExchange()


def dto_converter(from_dto_cls, to_dto_cls):
    """
    decorator for annotating DTO converters
    :param from_dto_cls: DTO class to convert from
    :param to_dto_cls: DTO class to convert to
    :return: decorator
    """

    def register(cls):
        dto_exchange.register(from_dto_cls, to_dto_cls, cls)
        return cls

    return register
