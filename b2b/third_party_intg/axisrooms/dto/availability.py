# -*- coding: utf-8 -*-

from rest_framework import serializers
from django.conf import settings


class DataAvailabilityDTO(serializers.Serializer):
    date = serializers.DateField(format=settings.BOOKING['date_format'])
    free = serializers.IntegerField()


class RoomAvailabilityDTO(serializers.Serializer):
    roomId = serializers.CharField(allow_null=False, allow_blank=False)
    availability = DataAvailabilityDTO(many=True)


class HotelDTO(serializers.Serializer):
    hotelId = serializers.CharField(allow_null=False, allow_blank=False)
    rooms = RoomAvailabilityDTO(many=True)


class AxisRoomAvailability(serializers.Serializer):
    accessKey = serializers.CharField(max_length=100, allow_null=False)
    channelId = serializers.CharField(max_length=100, allow_null=False)
    hotels = HotelDTO(many=True)


class AxisCheckAvailabilityDTO(serializers.Serializer):
    # TODO: Change hotel_ids and room_type to list rather than comma separated string
    checkin = serializers.DateField(format=settings.BOOKING['date_format'])
    checkout = serializers.DateField(format=settings.BOOKING['date_format'], required=False)
    hotel_ids = serializers.CharField(required=False)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass
