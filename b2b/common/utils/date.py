# -*- coding: utf-8 -*-
# pylint: disable=invalid-name
import datetime
import logging

import pytz
from dateutil import parser
from django.conf import settings

logger = logging.getLogger(__name__)

LocalTimeZoneObject = pytz.timezone(settings.TIME_ZONE)


def daterange(start_date, end_date):
    """
    Returns a list of dates between start date and end date
    Type casting to date is done because start date can be 24th july 12 PM
    and end date can be 25th july 11AM. days diff becomes 0.

    :param start_date: date/datetime
    :param end_date: date/datetime
    :return: list of datetime in between including start date but not end date
    """
    _start_date = start_date
    if isinstance(start_date, datetime.datetime):
        _start_date = start_date.date()

    _end_date = end_date
    if isinstance(end_date, datetime.datetime):
        _end_date = end_date.date()

    first_date = min(start_date, end_date)  # helps if the end date is less than start date to not mess up the order
    for delta in range(int((_end_date - _start_date).days)):
        yield first_date + datetime.timedelta(delta)


def maybe_convert_string_to_datetime(date, ignoretz=False):
    if isinstance(date, str):
        return parser.parse(date, ignoretz=ignoretz)
    return date


def localize(timestamp, time_zone='utc'):
    """
     localizes given naive datetime to current timezone.
     Use this instead of timestamp.replace(tzinfo=pytz.timezone(settings.TIME_ZONE))
     :param: timestamp - A naive python datetime object
     :param: timezone - time zone string: Ex: 'Asia/Kolkata'
     :return: A timezone aware python datetime localized to timezone in settings
    """
    tz = pytz.timezone(time_zone)
    return tz.localize(timestamp)


def utc_now():
    return localize(datetime.datetime.utcnow())


def application_tz_now():
    utc_with_timezone = utc_now()
    return utc_with_timezone.astimezone(LocalTimeZoneObject)


def blindly_replace_with_application_timezone(naive_timestamp):
    logger.warning("Replacing naive timestamp {d}'s tzinfo with {tz}".format(d=naive_timestamp, tz=settings.TIME_ZONE))
    return localize(naive_timestamp, time_zone=settings.TIME_ZONE)


def convert_datetime_format(from_datetime, to_format):
    from_datetime = maybe_convert_string_to_datetime(from_datetime)
    return from_datetime.strftime(to_format)


def is_naive_datetime(d):
    return d.tzinfo is None or d.tzinfo.utcoffset(d) is None
