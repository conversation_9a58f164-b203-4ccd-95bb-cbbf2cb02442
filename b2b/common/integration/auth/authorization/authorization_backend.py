import json
import logging
from django.conf import settings
import requests

from b2b.constants import Timeouts
from common.integration.auth.interface import BaseAuthBackend

logger = logging.getLogger(__name__)


class AuthorizationBackend(BaseAuthBackend):
    def get_auth_id(self, email_id):

        logger.info("Getting auth_id for emailId {email}".format(email=email_id))

        user_data = {
            "first_name": email_id,
            "password": email_id,
            "email": email_id,
            "phone_number": None,
            "last_name": "",
            "app_type": "PrimusAuthBackend"
        }

        auth_service_url = self._get_auth_url()
        request_headers = {"Content-type": 'application/json'}
        try:
            treebo_auth_res = requests.post(auth_service_url, data=json.dumps(user_data), headers=request_headers,
                                            timeout=(Timeouts.CONNECTION_TIMEOUT,Timeouts.RESPONSE_TIMEOUT))

            treebo_auth_json_res = treebo_auth_res.json()
        except Exception as e:
            logger.info(
                "Exception occurred while geeting auth id from auth service. Error {err}".format(err=str(e)))
            raise

        if treebo_auth_res.status_code == 200 or treebo_auth_res.status_code == 201:
            return treebo_auth_json_res['data']['id']

        raise RuntimeError("Error occurred while getting auth_id from Authorization service")

    @classmethod
    def _get_auth_url(cls):
        return settings.AUTH_SERVER_HOST + settings.AUTH_URL_ENDPOINTS['user_detail']
