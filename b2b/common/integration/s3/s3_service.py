# -*- coding: utf-8 -*-
import logging
import os

import boto3
from django.conf import settings

logger = logging.getLogger(__name__)


class S3Service():
    s3_settings = settings.S3_INTEGRATION
    b2b_bucket = s3_settings["bulk_onboarding"]["bucket"]
    host_region = s3_settings["bulk_onboarding"]["bucket_region"]

    """
    Upload files to AWS S3
    """

    @classmethod
    def upload_file(cls, file_name, path):
        """
        Uploads the file to the specified path in s3
        :returns file_path: relative url of the file uploaded to datastore
        """
        try:
            logger.info('Uploading a file  {file_name}'.format(file_name=file_name))

            s3_connection = boto3.resource('s3', region_name=cls.host_region)

            file_path = os.path.join(path, os.path.basename(file_name))

            with open(file_name, 'r+') as fp:
                s3_connection.Object(cls.b2b_bucket, fp).put(
                    Body=open('{filename}'.format(filename=fp), 'rb'))
        except Exception as e:
            logger.info("Failed to upload file to s3 :: %s" % (str(e)))
            raise e

        return file_path

    @classmethod
    def download_file(cls, file_path, *args, **kwargs):
        """

        :param file_path:
        :return: file_name: name of the downloaded file
        """
        file_name = os.path.basename(file_path)
        try:
            s3_connection = boto3.resource('s3', region_name=cls.host_region)

            with open(file_name, 'wb') as f:
                s3_connection.Bucket(cls.b2b_bucket).download_file(f, f)
        except Exception as e:
            logger.info(
                "Failed to download file {file_name}to s3 :: {msg}".format(file_name=file_name, msg=str(e)))
            raise

        return file_name
