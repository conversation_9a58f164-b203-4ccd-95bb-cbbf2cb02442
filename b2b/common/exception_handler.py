import logging
from collections import defaultdict

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import exception_handler

from common.utils.misc import fully_qualified_name

logger = logging.getLogger(__name__)


class APIExceptionHandler(object):
    registry = defaultdict()

    @classmethod
    def register(cls, exc):

        def wrapper(func):
            try:
                _exc = iter(exc)
            except TypeError:
                _exc = [exc]

            for ex in _exc:
                cls.registry[fully_qualified_name(ex)] = func
            return func

        return wrapper

    @classmethod
    def handle(cls, exc, context):

        handler_name = fully_qualified_name(exc)
        if handler_name in cls.registry:
            handler = cls.registry[handler_name]
        else:
            handler = cls.generic_handler

        return handler(exc, context)

    @classmethod
    def generic_handler(cls, exc, context):
        logger.info("{c} failed due to {e}".format(c=context, e=exc))
        response = exception_handler(exc, context)
        if not response:
            response = cls.response_for_exception(exc, status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return response

    @classmethod
    def extract_message_and_errors(cls, exc):
        errors = exc.errors if hasattr(exc, 'errors') else []
        message = getattr(exc, 'message', '')

        if not message:
            message = str(exc)

        if not errors:
            errors = [message]
        return message, errors

    @classmethod
    def response_for_exception(cls, exc, status_code):
        message, errors = cls.extract_message_and_errors(exc)
        return cls.standard_error_response(message, errors, status_code)

    @classmethod
    def standard_error_response(cls, message, errors, status_code):
        return Response(data={'errors': errors, 'message': message, }, status=status_code, )
