from ths_common.constants.base_enum import BaseEnum
from treebo_commons.utils.config_value_parser import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>anPars<PERSON>, \
    <PERSON>sonParser, StringParser, IntegerParser


class ConfigValueType(BaseEnum):
    INTEGER = 'integer'
    STRING = 'string'
    JSON = 'json'
    BOOLEAN = 'boolean'
    ARRAY = 'array'
    DATE = 'date'


class ConfigDto(object):
    PARSER = {
        ConfigValueType.INTEGER: IntegerParser(),
        ConfigValueType.STRING: StringParser(),
        ConfigValueType.JSON: JsonParser(),
        ConfigValueType.BOOLEAN: BooleanParser(),
        ConfigValueType.ARRAY: ListParser(),
        ConfigValueType.DATE: DateParser()
    }

    def __init__(self, config_name, config_value, value_type):
        self.config_name = config_name
        self.config_value = config_value
        self.value_type = ConfigValueType(value_type)

    def get_config_value(self):
        return self.PARSER.get(self.value_type).parse(self.config_value)
