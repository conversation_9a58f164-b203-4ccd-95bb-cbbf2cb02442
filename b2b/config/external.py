# pylint: disable=invalid-name,no-init
import os

from configurations import values

from config import log_conf
from config.common import Common

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


class External(Common):
    ENV_NAME = "prod"

    DEBUG = values.BooleanValue(False)
    # Honor the 'X-Forwarded-Proto' header for request.is_secure()
    # https://devcenter.heroku.com/articles/getting-started-with-django
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

    INSTALLED_APPS = Common.INSTALLED_APPS
    INSTALLED_APPS += ('django.contrib.staticfiles',)

    RABBITMQ_CONFIG = Common.RABBITMQ_CONFIG.copy()
    RABBITMQ_CONFIG['hostname'] = 'shared-rmq.treebo.pr'
    RABBITMQ_CONFIG['userid'] = 'b2badmin'
    RABBITMQ_CONFIG['password'] = 'M3800'
    RABBITMQ_CONFIG['virtual_host'] = 'b2b-external'

    WEBSITE_HOST = 'https://www.treebo.com'

    TAX = dict(
        HOST='http://tax.treebo.com',
        API='tax/v2/calculate_tax?breakup=1'
    )

    STATIC_ROOT = os.path.join(BASE_DIR, 'static')
    STATIC_URL = '/b2b/static/'
    SERVER_EMAIL = 'Treebo Hotels <<EMAIL>>'
    PROWL_HOST = 'http://growth.treebohotels.com'

    # Axis rooms settings
    AXIS_ROOMS = dict(
        checkin="2017-03-24",
        checkout="2017-10-30",
        availability_url='http://app.axisrooms.com/api/daywiseInventory/',
        inventory_url='https://corp-api.treebo.com/ext/api/daywiseInventory/',
        hotel_limit=1
    )
    # INFLUX = {
    #     'default': {
    #         'NAME': 'b2b',
    #         'USER': 'root',
    #         'PASSWORD': 'root',
    #         'HOST': '************',
    #         'PORT': '8086',
    #     }
    # }
    # INFLUX_RETENTION_POLICY = "INF"  # could be for a lesser duration ex 1, 2, 5, etc.
    # INFLUX_UDP = False
    # INFLUX_UDP_PORT = 4444
    # INFLUX_RETENTION_REPLICATION = 1

    # # https://docs.djangoproject.com/en/1.8/topics/http/middleware/
    # # MIDDLEWARE_CLASSES = Common.MIDDLEWARE_CLASSES
    # # MIDDLEWARE_CLASSES += (
    # #     'treebo-influx.middleware.InfluxMiddleWare',
    # # )

    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': 'b2bapi',
            'USER': 'treeboadmin',
            'PASSWORD': 'caTwimEx3',
            'HOST': 'b2b-p-corp-pg.treebo.pr',
            'PORT': '6432',
        }
    }

    LOGGING = log_conf.get(log_root='/ebs1/logs/')

    # Notification SOA Settings
    NOTIFICATION_CONF = dict(
        # which notification backend to use
        backend='NotificationSOA',
        reply_to='<EMAIL>',

        # configurations needed by different notification backends
        backend_configs=dict(
            soa=dict(
                email=dict(
                    url='http://notification.treebo.pr/v1/notification/email/'
                ),
                sms=dict(
                    url='http://notification.treebo.pr/v1/notification/sms/'
                )
            )
        ),

        # email template settings
        email_templates=dict(
            booking=dict(
                confirmation=dict(
                    template='booking_confirmation.html',
                    sender='<EMAIL>',
                    sender_name='',
                    to=[],
                    cc=[],
                    bcc=[]
                ),
                failure=dict(
                    template='booking_failure.html',
                    sender='<EMAIL>',
                    sender_name='',
                    to=[],

                    cc=[],
                    bcc=[]
                ),
            ),
            cancellation=dict(
                success=dict(
                    template='booking_cancellation.html',
                    sender='<EMAIL>',
                    sender_name='',
                    to=[],
                    cc=[],
                    bcc=[]
                ),
                failure=dict(
                    template='cancellation_failure.html',
                    sender='<EMAIL>',
                    sender_name='',
                    to=[],

                    cc=[],
                    bcc=[]
                )
            )
        ),
        env=ENV_NAME
    )
    PAYMENT_CONF = dict(
        # which payment backend to use
        backend='PaymentSOA',

        # configurations needed by different payments backends
        backend_configs=dict(
            soa=dict(
                initiate=dict(
                    url='https://payments.treebo.com/api/paynow/order/'
                ),
                verify=dict(
                    url='https://payments.treebo.com/api/paynow/payment/'
                ),
                create_user=dict(
                    url='https://payments.treebo.com/api/paynow/customer/'
                )
            )
        )
    )
    HEALTH_CHECK_CONF = dict(
        rmq_host=RABBITMQ_URL_EXTERNAL,
        use_basic_db_check=True,
        soft_dependencies=[]
    )
    TRAVEL_AGENT_MANUAL_PAYMENT_LINK = "http://growth.treebohotels.com/growth/admin/travel_agent/tapaymentview/"

    ENVIRONMENT = 'production'

    @classmethod
    def post_setup(cls):
        os.environ['THSC_ENVIRONMENT'] = cls.ENVIRONMENT
        super(External, cls).post_setup()
