from rest_framework import serializers
from django.conf import settings
from data_sync.models import MigrationMapping
from django.db import transaction
from b2b.models import User, StandardRoomPricing
from data_sync.constants import User as UserConstant


class StandardRoomPricingDTO(serializers.Serializer):

    room_type = serializers.CharField(max_length=50)
    id = serializers.CharField(max_length=20)
    from_date = serializers.DateField(format=settings.BOOKING['date_format'])
    to_date = serializers.DateField(format=settings.BOOKING['date_format'])
    pre_tax_default_price = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    pre_tax_quote_price = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    pre_tax_floor_price = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    b2b_rack_rate = serializers.DecimalField(max_digits=20, decimal_places=2, default=0)
    hotel_id = serializers.CharField(max_length=50)
    source = serializers.CharField(max_length=50, default='b2butil')

    def update(self, instance, validated_data):
        id = validated_data.pop('id')
        source = validated_data.pop('source')
        hotel_id = validated_data.pop('hotel_id')
        for (key, value) in list(validated_data.items()):
            setattr(instance, key, value)
        instance.save()
        instance.id = id
        instance.source = source
        instance.hotel_id = hotel_id
        return instance

    def create(self, validate_data):
        hotel_map = MigrationMapping.objects.get(source=validate_data['source'],
                                                     instance='hotel',
                                                     external_id=validate_data['hotel_id'])

        with transaction.atomic():
            try:
                migration_user = User.objects.get(email=UserConstant.MIGRATION_USER)
            except User.DoesNotExist as e:
                migration_user = User.objects.create_user(email=UserConstant.MIGRATION_USER,
                                                password=UserConstant.DEFAULTS['password'])
            post_data = validate_data.copy()
            source = post_data.pop('source')
            ext_id = post_data.pop('id')
            post_data['created_by'] = migration_user  # This would be the migration user
            post_data['hotel_id'] = hotel_map.b2b_id
            srp = StandardRoomPricing.objects.create(**post_data)
            MigrationMapping.objects.create(b2b_id=srp.id, external_id=ext_id,
                                            source=source, instance="standard_room_pricing")
        srp.source = source
        return srp