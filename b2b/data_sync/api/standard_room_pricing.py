import traceback
import logging

from rest_framework.response import Response
from b2b.api.api import Treebo<PERSON><PERSON>
from data_sync.dto import StandardRoomPricingDTO
from data_sync.api.api_error_response import APIErrorResponse
from rest_framework import status
from django.db import IntegrityError
from data_sync.models import MigrationMapping
from b2b.models import StandardRoomPricing


class StandardRoomPricingAPI(TreeboAPI):

    def post(self, request):
        logger = logging.getLogger(self.__class__.__name__)
        try:
            srp_dto = StandardRoomPricingDTO(data=request.data)
            if not srp_dto.is_valid():
                logger.info("Invalid dto for Standard Room pricing "
                             "id {srp_id}".format(srp_id=request.data.get('id')))
                return Response(srp_dto.errors, status.HTTP_400_BAD_REQUEST)
            srp_data = srp_dto.data
            srp_dto.save()

        except MigrationMapping.DoesNotExist as e:
            logger.info("Standard room pricing migration failed for external "
                             "hotel id: {hotel_id}".format(hotel_id=srp_dto.data['hotel_id']))
            return APIErrorResponse.migration_map_does_not_exist(instance='standard_room_pricing',
                                                                 ext_id = srp_data['id'],
                                                                 source = srp_data['source'])

        except IntegrityError as ie:
            err_msg = "Integrity Error while saving StandardRoomPricing for hotel {hotel_id} : {err}"
            logger.info(err_msg.format(err=str(ie), hotel_id=request.data['id']))
            return APIErrorResponse.integrity_error('StandardRoomPricing', request.data.get('hotel_id'))

        except Exception as e:
            traceback.print_exc()
            err_msg = "error occurred while inserting standard room pricing record for hotel {hotel_id} : {err}"
            logger.info(err_msg.format(err=str(e), hotel_id=request.data['id']))
            return Response({'error': err_msg.format(err=str(e), hotel_id=request.data['hotel_id'])},
                            status.HTTP_500_INTERNAL_SERVER_ERROR)

        response = Response(data=srp_dto.data,
                            status=status.HTTP_201_CREATED)
        return response

    def put(self, request, pk):
        logger = logging.getLogger(self.__class__.__name__)

        # Get b2b ids using MigrationMapping
        try:
            srp_dto = StandardRoomPricingDTO(data=request.data)
            if not srp_dto.is_valid():
                logger.info("Invalid dto for Standard Room pricing "
                             "hotel id {hotel_id}".format(request.data.get('hotel_id')))
                return Response(srp_dto.errors, status.HTTP_400_BAD_REQUEST)

            srp_data = srp_dto.data
            try:
                migration_map = MigrationMapping.objects.get(instance='standard_room_pricing',
                                                             external_id=pk, source=srp_data['source'])
            except MigrationMapping.DoesNotExist as e:
                logger.info("Migration Mapping not found for standard_room_pricing "
                             "with ext id {ext_id}".format(ext_id=pk))
                return APIErrorResponse.migration_map_does_not_exist(srp_data['instance'],
                                                                     srp_data['b2b_id'],
                                                                     srp_data['ext_id'],
                                                                     srp_data['source'])

            # Get Standard room pricing instance
            try:
                srp = StandardRoomPricing.objects.get(id=migration_map.b2b_id)
            except StandardRoomPricing.DoesNotExist as e:
                logger.info("Invalid StandardRoomPricing id {srp_id}".format(srp_id=migration_map.b2b_id))
                return APIErrorResponse.standard_room_pricing_does_not_exist(migration_map.b2b_id)

            # Update StandardRoomPricing
            srp_dto = StandardRoomPricingDTO(srp, data=request.data)
            if not srp_dto.is_valid():
                logger.info("Invalid dto for StandardRoomPricing put request "
                             "for id {srp_id}".format(srp_id=migration_map.b2b_id))
                return Response(srp_dto.errors, status.HTTP_400_BAD_REQUEST)
            srp_dto.save()

        except Exception as e:
            traceback.print_exc()
            err_msg = "error occurred while inserting standard room pricing record for id {srp_id} : {err}"
            logger.info(err_msg.format(err=str(e), srp_id=request.data['id']))
            return Response({'error': err_msg}, status.HTTP_500_INTERNAL_SERVER_ERROR)

        response = Response(data=srp_dto.data,
                            status=status.HTTP_201_CREATED)
        return response
