from rest_framework import serializers


class CorporatePriceRequestDTO(serializers.Serializer):
    hotel_ids = serializers.CharField()
    applicable_from_date = serializers.DateField()
    applicable_to_date = serializers.DateField()
    room_types = serializers.CharField()
    payload_id = serializers.CharField()
    roomconfig = serializers.CharField(required=False)

    def validate(self, attrs):
        if attrs['applicable_to_date'] < attrs['applicable_from_date']:
            raise serializers.ValidationError("To date can not be smaller then from date")
        if attrs['payload_id'] in ['NULL', 'null', 'None', '', 'NONE']:
            raise serializers.ValidationError("Payload Id cannot be null")
        return attrs
