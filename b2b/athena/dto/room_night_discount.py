from rest_framework import serializers
from b2b.models.room_night_discount import RoomNightDiscount


class RoomNightDiscountSerializer(serializers.ModelSerializer):
    discount = serializers.SerializerMethodField()

    class Meta:
        model = RoomNightDiscount
        fields = ['min_slab', 'max_slab', 'discount', 'active']

    def get_discount(self, object):
        return round(float(object.discount), 2)
