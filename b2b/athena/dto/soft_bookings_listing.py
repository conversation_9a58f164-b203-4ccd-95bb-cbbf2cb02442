from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from b2b.models import Booking
from b2b.models import LegalEntity
from b2b.models import Hotel


class SoftBookingsListingRequestDTO(serializers.Serializer):
    hotel = serializers.CharField()
    legal_entity = serializers.CharField(required=False)

    def validate(self, validated_data):
        if not Hotel.objects.filter(hotel_id=validated_data['hotel']).exists():
            raise ValidationError({'hotel': 'No valid hotel exists for given id'})
        if 'legal_entity' in validated_data and validated_data['legal_entity'] and not LegalEntity.objects.filter(legal_entity_id=validated_data['legal_entity']).exists():
            raise ValidationError({'legal_entity': 'No valid legal_entity exists for given id'})
        return validated_data


class SoftBookingsListingHotelDTO(serializers.ModelSerializer):
    id = serializers.CharField(source='hotel_id')

    class Meta:
        model = Hotel
        fields = ['id', 'name']


class SoftBookingsListingLegalEntityDTO(serializers.ModelSerializer):
    id = serializers.CharField(source='legal_entity_id')

    class Meta:
        model = LegalEntity
        fields = ['id', 'name']


class SoftBookingsListingResponseDTO(serializers.ModelSerializer):
    hotel = SoftBookingsListingHotelDTO()
    legal_entity = serializers.SerializerMethodField(method_name='get_booker_company')
    subchannel = serializers.CharField(source='sub_channel')
    total_guests = serializers.IntegerField(source='get_guest_count')
    total_rooms = serializers.SerializerMethodField(method_name='get_rooms_count')
    room_type_count = serializers.SerializerMethodField(method_name='get_rooms_type_count')

    def get_booker_company(self, obj):
        booker_company = obj.travel_agent_entity or obj.legal_entity
        return SoftBookingsListingLegalEntityDTO(booker_company).data

    def get_rooms_count(self, obj):
        return obj.bookedroom_set.all().count()

    def get_rooms_type_count(self, obj):
        bookedrooms = obj.bookedroom_set.all()
        room_type_count = {}
        for room in bookedrooms:
            if room.room_type not in room_type_count:
                room_type_count[room.room_type] = 0

            room_type_count[room.room_type] = room_type_count[room.room_type] + 1
        return room_type_count

    class Meta:
        model = Booking
        fields = ['booking_id', 'hotel', 'legal_entity', 'check_in', 'check_out', 'subchannel', 'total_guests',
                  'total_rooms','room_type_count']
