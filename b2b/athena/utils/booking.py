from core.corporate.data_classes import Gstin, LegalEntity


# pylint: disable=invalid-name
def expand_booking_gst_details_as_corporate_legal_entity(booking):
    """ Converts Booking.GstDetails -> Corp.LegalEntity.Gstin.Address structure"""
    address = booking.gst_details.address
    address.default = True

    gstin_number = booking.gst_details.gstin_number
    gstin = Gstin(gstin_number, addresses=[address], has_provided_lut=booking.gst_details.has_provided_lut,
                  is_sez=booking.gst_details.is_sez)

    legal_entity = LegalEntity(booking.gst_details.legal_name, gstins=[gstin])
    return legal_entity


# pylint: disable=invalid-name,fixme
def add_gst_details_to_legal_entity_and_mark_as_default(legal_entities, booking_legal_entity):
    """
     morphs gst details on booking level to corp legal entity, sets
     them as default and corporate level legal entities as non default

     TODO: Make it more pure by not modifying the defaults
     """

    for legal_entity in legal_entities:
        legal_entity.default = False

    booking_legal_entity.default = True
    for gstin in booking_legal_entity.gstins:
        gstin.default = True

    return [booking_legal_entity] + list(legal_entities)
