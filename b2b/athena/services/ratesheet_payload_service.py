import geopy.distance
import json
from collections import defaultdict

from athena.api.exceptions import HotelsNotAvailableForRatesheet
from athena.models.ratesheet_request_payload import RateSheetRequestPayload
from b2b.constants import HotelAttributes
from b2b.models import Hotel, HotelAttribute


class RateSheetPayloadService(object):
    @classmethod
    def get_payload_from_id(cls, payload_id):
        payload_query = RateSheetRequestPayload.objects.filter(payload_id=payload_id).values(
            'payload')
        return payload_query[0]['payload']

    @classmethod
    def _filter_hotels_for_cities(cls, hotel_ids, cities):
        for city in cities:
            city_name = list(city.keys())[0]
            localities = list(city.values())[0]
            hotel_info = Hotel.objects.filter(city=city_name, locality__in=localities,
                                              active=True)
            for hotel in hotel_info:
                hotel_ids.append(hotel.id)

    @classmethod
    def _filter_hotels_for_offices(cls, hotel_ids, offices, radius):
        hotel_location_info = HotelAttribute.objects.filter(key__in=['latitude',
                                                                     'longitude']). \
            values('hotel_id', 'key', 'value')
        hotel_locations = defaultdict(dict)
        for hotel in hotel_location_info:
            if hotel['key'] == 'latitude':
                hotel_locations[hotel['hotel_id']]['latitude'] = hotel['value']
            if hotel['key'] == 'longitude':
                hotel_locations[hotel['hotel_id']]['longitude'] = hotel['value']

        for office in offices:
            for coordinates in office.values():
                office_location = (coordinates["latitude"], coordinates["longitude"])
                for hotel_id, location in hotel_locations.items():
                    hotel_location = (location['latitude'], location['longitude'])
                    distance = round(geopy.distance.geodesic(office_location,
                                                             hotel_location).km, 2)
                    if distance <= radius:
                        if hotel_id in hotel_ids:
                            continue
                        hotel_ids.append(hotel_id)

    @classmethod
    def _filter_hotels_for_corporate_rating_and_saleability(cls, eligible_hotel_ids,
                                                            corporate_preferred_ratings):
        hotel_info = HotelAttribute.objects.filter(
            key__in=['corporate_rating', 'saleable'], hotel_id__in=eligible_hotel_ids).\
            values('hotel_id', 'key', 'value')

        for hotel in hotel_info:
            hotel_id = hotel['hotel_id']
            if corporate_preferred_ratings:
                if (hotel['key'] == 'corporate_rating') and (hotel['value'] not in
                                                             corporate_preferred_ratings):
                    if hotel_id in eligible_hotel_ids:
                        eligible_hotel_ids.remove(hotel_id)

            if (hotel['key'] == 'saleable') and (hotel['value'] == '0'):
                if hotel_id in eligible_hotel_ids:
                    eligible_hotel_ids.remove(hotel_id)

    @classmethod
    def get_eligible_hotel_ids(cls, ratesheet_request):
        cities = ratesheet_request.get('cities')
        hotels = ratesheet_request.get('hotel_names')
        hotel_brands = ratesheet_request.get('hotel_brands')
        corporate_preferred_ratings = ratesheet_request.get('corporate_preferred_ratings')
        radius = ratesheet_request.get('radius')
        offices = ratesheet_request.get('offices')

        hotel_ids = []
        is_hotel_level_rate_sheet = True
        is_city_level_rate_sheet = False

        if hotels:
            hotel_info = Hotel.objects.filter(name__in=hotels, active=True)
            for hotel in hotel_info:
                hotel_ids.append(hotel.id)
        else:
            if cities:
                cls._filter_hotels_for_cities(hotel_ids, cities)
                is_city_level_rate_sheet = True
            else:
                cls._filter_hotels_for_offices(hotel_ids, offices, radius)
            is_hotel_level_rate_sheet = False

        eligible_hotel_ids = hotel_ids
        cls._filter_hotels_for_corporate_rating_and_saleability(eligible_hotel_ids,
                                                                corporate_preferred_ratings)

        if not eligible_hotel_ids:
            raise HotelsNotAvailableForRatesheet("Hotels are unavailable for given criteria")

        return eligible_hotel_ids, is_hotel_level_rate_sheet, is_city_level_rate_sheet
