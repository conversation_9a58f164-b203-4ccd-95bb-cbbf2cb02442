from rest_framework import permissions

from b2b.models import LegalEntity
from b2b.models import CorporateAttributes
from b2b.models import Booking
from django.db.models import Q


class AthenaPOCAuthorization(permissions.BasePermission):
    def has_permission(self, request, view):
        # Adding a check based on access_token cookie since it is only present in Mercury
        if request.COOKIES.get('access_token'):
            data = request.data
            legal_entity_id = data.get('travel_agent_entity_id') or data.get('legal_entity_id')
            booking_id = data.get('booking_id')

            if booking_id:
                legal_entity_id = Booking.objects.get(booking_id=booking_id).booker_company.legal_entity_id

            legal_entities = LegalEntity.objects.filter(legal_entity_id__iexact=legal_entity_id)
            treebo_poc_ids = []
            inside_sales_poc_ids = []
            for legal_entity in legal_entities:
                if legal_entity.account_treebo_poc:
                    treebo_poc_ids.append(legal_entity.account_treebo_poc.id)
                if legal_entity.account_inside_sales_poc:
                    inside_sales_poc_ids.append(
                        legal_entity.account_inside_sales_poc.id)
            return request.user.id in (inside_sales_poc_ids + treebo_poc_ids)

        else:
            return True
 
