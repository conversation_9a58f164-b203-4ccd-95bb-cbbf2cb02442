from django.db.models import Q

from athena.dto.corporate_search import CorporateSearchRequestDTO
from b2b.domain.services.exceptions import InvalidDTO
from b2b.models import Corporate


class CorporateService(object):
    @classmethod
    def search_corporates(cls, corp_search_req_dto, user):
        """use filter_types instead of filter_type"""
        assert type(corp_search_req_dto) is CorporateSearchRequestDTO
        if not corp_search_req_dto.is_valid():
            raise InvalidDTO(corp_search_req_dto)
        corporates = Corporate.objects.filter(active=True).order_by('trading_name')
        query = corp_search_req_dto.data['query']
        filter_types = corp_search_req_dto.data.get('filter_types')

        corporates = corporates.filter((Q(trading_name__icontains=query) | Q(corporate_id__iexact=query)
                                        | Q(corporatelegalentity__legal_entity__name__icontains=query)
                                        )).distinct()

        return corporates[corp_search_req_dto.data['offset']:corp_search_req_dto.data['limit']]
