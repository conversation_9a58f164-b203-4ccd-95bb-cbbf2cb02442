from athena.dto.room_night_discount import RoomNightDiscountSerializer
from b2b.models import LegalEntity
from b2b.models.room_night_discount import RoomNightDiscount


class Discount(object):
    def __init__(self, legal_entity_id):
        try:
            self.legal_entity = LegalEntity.objects.get(legal_entity_id=legal_entity_id)
        except LegalEntity.DoesNotExist:
            raise

    def room_night_discount(self):
        if self.legal_entity.is_web_pricing_for_ta_or_tmc_applicable():
            room_night_discount = RoomNightDiscount.objects.filter(active=True)
            dto = RoomNightDiscountSerializer(room_night_discount, many=True)
            return dto.data
        return []
