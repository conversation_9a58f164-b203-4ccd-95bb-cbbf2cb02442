from django.conf.urls import url

from athena.v2.views.availability import AvailabilityAPI
from athena.v2.views.booking import BookingAPI
from athena.v2.views.booking_reminders import BookingReminderAPI
from athena.v2.views.corporate_ratesheet import CorporateRatesheetEmailer
from athena.v2.views.landing import AthenaLanding
from athena.v2.views.pricing import BookingPricingAPI

urlpatterns = [
    url(r'^booking/(?P<booking_id>[a-zA-Z0-9-_]+)/$', BookingAPI.as_view(), name="booking"),
    url(r'^booking/(?P<booking_id>[a-zA-Z0-9-_]+)/pricing/', BookingPricingAPI.as_view(), name="booking-pricing"),
    url(r'^$', AthenaLanding.as_view(), name="landing"),
    url(r'^availability/', AvailabilityAPI.as_view(), name="availability"),
    url(r'^bookings/send-reminders/', BookingReminderAPI.as_view(), name="booking-send-reminders"),
    url(r'^corporate-ratesheet-emailer/', CorporateRatesheetEmailer.as_view(), name="corporate-ratesheet-emailer")
]
