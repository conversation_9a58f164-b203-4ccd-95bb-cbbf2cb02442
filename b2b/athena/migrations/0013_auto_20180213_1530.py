from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('athena', '0012_auto_20180125_1751'),
    ]

    operations = [
        migrations.AlterField(
            model_name='configrules',
            name='key',
            field=models.CharField(max_length=100, choices=[(b'softblock_booking_window', b'softblock_booking_window'), (b'softblock_hold_duration', b'softblock_hold_duration'), (b'softblock_inventory_check', b'softblock_inventory_check'), (b'softblock_inventory_threshold', b'softblock_inventory_threshold')]),
        ),
    ]
