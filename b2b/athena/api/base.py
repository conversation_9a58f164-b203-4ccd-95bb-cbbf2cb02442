import logging

from rest_framework.authentication import BasicAuthentication
from rest_framework.exceptions import AuthenticationFailed, NotAuthenticated
from rest_framework.renderers import Temp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.views import APIView

from athena.api.exception_handlers import authentication_failure_handler
from athena.api.exception_handlers.authentication_failed_handler import not_authenticated_failure_handler
from athena.api.exception_handlers.exception_handler import AthenaAPIExcpetionHandler
from athena.services.authentication import AthenaAuthorization
from b2b.api import CsrfExemptSessionAuthentication
from b2b.api.api import TreeboAPI
from b2b.api.renderer import TreeboJSONRenderer

logger = logging.getLogger(__name__)


class AthenaAPI(TreeboAPI):
    template_name = None
    renderer_classes = (<PERSON>boJ<PERSON><PERSON><PERSON>er, BrowsableAPIRenderer)
    permission_classes = (AthenaAuthorization,)
    authentication_classes = (BasicAuthentication, CsrfExemptSessionAuthentication)

    def dispatch(self, request, *args, **kwargs):
        if self.template_name:
            self.renderer_classes = (TemplateHTML<PERSON>enderer,)
        return super(AthenaAPI, self).dispatch(request, *args, **kwargs)

    def handle_exception(self, exc):
        # explicitly bypassing TreeboAPI handle exception as it does not handle correctly.
        # It also throws out a 5002 code and Unknown exception if any exception boils down to this method
        # APIView handles better.
        if isinstance(exc, AuthenticationFailed):
            return authentication_failure_handler(exc, context=None)
        if isinstance(exc, NotAuthenticated):
            return not_authenticated_failure_handler(exc, context=None)

        return APIView.handle_exception(self, exc)


class AthenaV2API(APIView):
    template_name = None
    renderer_classes = [JSONRenderer, BrowsableAPIRenderer, ]
    permission_classes = (AthenaAuthorization,)
    authentication_classes = (BasicAuthentication, CsrfExemptSessionAuthentication)

    def initial(self, request, *args, **kwargs):
        logger.info("API request [{m}]: {p} by {u} \n data:{d}".format(p=request.get_full_path(),
                                                                       u=request.user,
                                                                       m=request.method,
                                                                       d=request.data,
                                                                       )
                    )
        super(AthenaV2API, self).initial(request, *args, **kwargs)

    def dispatch(self, request, *args, **kwargs):
        if self.template_name:
            self.renderer_classes = [TemplateHTMLRenderer, ] + self.renderer_classes
        return super(AthenaV2API, self).dispatch(request, *args, **kwargs)

    def handle_exception(self, exc):
        self.settings.EXCEPTION_HANDLER = AthenaAPIExcpetionHandler.handle
        return super(AthenaV2API, self).handle_exception(exc)
