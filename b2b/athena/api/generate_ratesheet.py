import json
import logging


from django.http import HttpResponse
from rest_framework import status

from athena.models.ratesheet_request_payload import RateSheetRequestPayload
from athena.services.legal_entity_ratesheet_emailer_service import LegalEntityRatesheetEmailerService
from athena.services.ratesheet_payload_service import RateSheetPayloadService
from b2b.api.api import TreeboAPI
from b2b.constants import CSV_RATESHEET_FILE_NAME

logger = logging.getLogger(__name__)


class GenerateRatesheetAPI(TreeboAPI):

    def get(self, request, payload_id):
        payload = RateSheetPayloadService.get_payload_from_id(payload_id=payload_id)

        eligible_hotel_ids, is_hotel_level_rate_sheet, is_city_level_rate_sheet = \
            RateSheetPayloadService.get_eligible_hotel_ids(ratesheet_request=payload)

        corporate_ratesheet_service = LegalEntityRatesheetEmailerService(
            payload['applicable_from_date'], payload['applicable_to_date'], payload['slab'],
            payload['room_types'], eligible_hotel_ids, payload.get('cities'),
            is_hotel_level_rate_sheet, is_city_level_rate_sheet, payload.get('offices'))
        try:
            filename = CSV_RATESHEET_FILE_NAME
            file_response = HttpResponse()
            file_response['Content-Disposition'] = f"attachment; filename={filename}"
            response = corporate_ratesheet_service.create_v2_ratesheet(file_response)
            return response
        except Exception as e:
            logger.info(str(e))
            return HttpResponse(f"Fail to create rate sheet, error: {str(e)}",
                                status=status.HTTP_500_INTERNAL_SERVER_ERROR)
