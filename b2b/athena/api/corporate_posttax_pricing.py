# -*- coding: utf-8 -*-
import logging
from datetime import datetime

from rest_framework import status
from rest_framework.authentication import BasicAuthentication
from rest_framework.response import Response

from athena.api.base import AthenaAPI
from athena.dto.availibility_and_pricing_request import AvailibilityAndPricingRequestDTO
from b2b.api import CsrfExemptSessionAuthentication
from b2b.domain.services.dashboard.v2.itinerary import ItineraryService
from b2b.dto import ItineraryRequestDTO
from b2b.models import Hotel
from django.core.exceptions import ValidationError
from django.conf import settings

logger = logging.getLogger(__name__)


class CorporatePosttaxPricingAPI(AthenaAPI):
    authentication_classes = (BasicAuthentication, CsrfExemptSessionAuthentication)

    # pylint: disable=too-many-locals
    def get(self, request):

        pricing_request_dto = AvailibilityAndPricingRequestDTO(data=request.query_params)
        if not pricing_request_dto.is_valid():
            return Response(pricing_request_dto.errors, status.HTTP_400_BAD_REQUEST)
        try:
            request_data = pricing_request_dto.data
            legal_entity_id = request_data['legal_entity_id']
            room_conf = request_data['room_config']
            checkin = request_data['check_in']
            checkout = request_data['check_out']
            hotel_id = request_data['hotel']
            room_type = request_data['room_type']

            itinerary_svc = ItineraryService(legal_entity_id, room_conf, checkin, checkout)
            itinerary_request_dto = ItineraryRequestDTO(data=dict(
                checkin=checkin,
                checkout=checkout,
                roomconfig=room_conf,
                hotel_id=str(hotel_id),
                legal_entity_id=legal_entity_id,
                roomtype=room_type
            ))

            hotel = Hotel.objects.get(hotel_id=hotel_id)
            checkin_date = datetime.strptime(checkin, settings.BOOKING['date_format']).date()
            if hotel.current_business_date and checkin_date < hotel.current_business_date:
                raise ValidationError("check-in date should be greater than equal to current-business-date")

            itinerary_data = itinerary_svc.get_itinerary_data(itinerary_request_dto)

            number_of_days = self.get_number_of_days(checkin=checkin, checkout=checkout)

            corp_price = itinerary_data['price']['corp']

            response = Response(data={
                'price': round((corp_price['pretax_price'] / number_of_days),2),
                'posttax_price': round((corp_price['post_tax_price'] / number_of_days),2),
                'btc_price': round((corp_price['btc_pretax_price'] / number_of_days),2) if corp_price.get(
                    'btc_pretax_price') else round((corp_price['pretax_price'] / number_of_days),2),
                'btc_posttax_price': round((corp_price['btc_post_tax_price'] / number_of_days),2) if corp_price.get(
                    'btc_post_tax_price') else round((corp_price['post_tax_price'] / number_of_days),2),
                'is_dual_price_enabled': itinerary_data.get('is_dual_price_enabled', False)
            },
                status=status.HTTP_200_OK)
            return response

        except Exception as e:
            logger.info("Error occurred while getting price. Error {err}".format(err=str(e)))
            return Response(data={'errors': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_number_of_days(self, checkin, checkout):
        """
        pass the checkin and checkout in string format ('%Y-%m-%d')
        """
        from_date = datetime.strptime(checkin, '%Y-%m-%d').date()
        to_date = datetime.strptime(checkout, '%Y-%m-%d').date()
        return (to_date - from_date).days
