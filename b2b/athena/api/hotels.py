# -*- coding: utf-8 -*-
from rest_framework import status
from rest_framework.response import Response

from athena.api.base import AthenaAPI
from athena.services import HotelService


class Hotels(AthenaAPI):
    def get(self, request):
        """
        lists only saleable hotels
        """
        hotels = HotelService.get_hotels()
        return Response(data=hotels, status=status.HTTP_200_OK)
