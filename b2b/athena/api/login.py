from django.contrib.auth import login
from django.contrib.auth.forms import AuthenticationForm as LoginForm
from django.urls import reverse
from django.http.response import HttpResponseRedirect
from rest_framework.authentication import BasicAuthentication
from rest_framework.response import Response

from athena.api.base import AthenaAPI


class Login(AthenaAPI):
    template_name = 'athena/login.html'
    permission_classes = ()
    authentication_classes = (BasicAuthentication, )

    def dispatch(self, request, **kwargs):
        if not request.user.is_anonymous:
            return HttpResponseRedirect('/athena')
        return super(Login, self).dispatch(request, **kwargs)

    def get(self, request, *args, **kwargs):
        form = LoginForm
        return Response(data={'request': request, 'form': form}, template_name=self.template_name)

    def post(self, request):
        form = LoginForm(data=request.POST)
        if form.is_valid():
            login(request, form.get_user())
            return HttpResponseRedirect(reverse('athena:landing'))
        return Response(data={'request': request, 'form': form}, template_name=self.template_name)
