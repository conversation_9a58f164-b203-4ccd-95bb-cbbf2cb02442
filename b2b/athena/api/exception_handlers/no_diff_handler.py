from rest_framework import status

from athena.api.exception_handlers.exception_handler import Athena<PERSON>IExcpetionHandler
from common.diff_and_patch.exceptions import InvalidDelta, NoDelta


@AthenaAPIExcpetionHandler.register(exc=(NoDelta, InvalidDelta))
def no_diff_handler(exc, context):  # pylint: disable=unused-argument
    return AthenaAPIExcpetionHandler.response_for_exception(exc, status.HTTP_406_NOT_ACCEPTABLE)
