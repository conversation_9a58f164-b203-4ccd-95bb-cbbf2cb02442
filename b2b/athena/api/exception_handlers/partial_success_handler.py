from rest_framework.response import Response

from athena.api.exception_handlers.exception_handler import AthenaAPIExcpetionHandler
from common.exceptions import PartialSuccess


@AthenaAPIExcpetionHandler.register(exc=PartialSuccess)
def partial_success_handler(exc, context):  # pylint: disable=unused-argument
    return Response(data={'errors': list(map(str, exc)),
                          'message': str(exc),
                          'successes': list(map(str, exc)),
                          },
                    status=207)
