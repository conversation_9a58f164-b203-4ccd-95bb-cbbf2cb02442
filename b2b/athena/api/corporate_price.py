from rest_framework import status
from rest_framework.response import Response

from athena.api.exceptions import PriceDataNotAvailableForRatesheet, HotelsDoesNotExists
from athena.services import HotelService
from athena.services.ratesheet_payload_service import RateSheetPayloadService
from b2b.api.api import TreeboAPI
from athena.dto.corporate_price import CorporatePriceRequestDTO
from athena.services.legal_entity_ratesheet_emailer_service import LegalEntityRatesheetEmailerService
from b2b.constants import DEFAULT_ROOM_CONFIG


class CorporatePriceAPI(TreeboAPI):

    def post(self, request):
        request_data = CorporatePriceRequestDTO(data=request.GET)
        if not request_data.is_valid():
            return Response(request_data.errors, status.HTTP_400_BAD_REQUEST)
        external_hotel_ids = request_data.data['hotel_ids'].split(',')
        room_types = request_data.data['room_types'].split(',')
        payload_id = request_data.data['payload_id']
        applicable_from_date = request_data.data['applicable_from_date']
        applicable_to_date = request_data.data['applicable_to_date']
        roomconfig = request_data.data.get('roomconfig')
        try:
            payload = RateSheetPayloadService.get_payload_from_id(payload_id=payload_id)
            slab = payload['slab']
            corporate_ratesheet_service = LegalEntityRatesheetEmailerService(
                applicable_from_date, applicable_to_date, slab, room_types, roomconfig=roomconfig)

            corporate_prices = corporate_ratesheet_service.get_corporate_prices(
                external_hotel_ids)
            corporate_prices['recipient_name'] = payload.get('recipient_name')
            return Response(data=corporate_prices, status=status.HTTP_200_OK)
        except (PriceDataNotAvailableForRatesheet, HotelsDoesNotExists) as e:
            return Response(str(e), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(str(e), status=status.HTTP_500_INTERNAL_SERVER_ERROR)

