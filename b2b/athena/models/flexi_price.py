from django.db import models
from djutil.models import TimeStampedModel

from b2b import constants
from b2b.models.hotel import Hotel
from b2b.models.mixins import DefaultPermissions


class FlexiPrice(TimeStampedModel, DefaultPermissions):
    hotel = models.ForeignKey(Hotel, on_delete=models.CASCADE)
    room_type = models.CharField(choices=constants.Rooms.TYPES,
                                 max_length=50)
    start_date = models.DateField()
    end_date = models.DateField()
    room_price = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    extra_person_charges = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    reason = models.CharField(default="", max_length=200, null=True)

    def __str__(self):
        return f"ID: {self.id}"
