import logging
from math import floor

from athena.api.exceptions import SoftblockAvailabilityValidationError
from athena.models.config_rules import ConfigRules
from b2b.domain.services import AvailabiltyService
from b2b.dto import CheckAvailabilityDTO

logger = logging.getLogger(__name__)

class AvailabilityValidator(object):

    @classmethod
    def get_total_availability(cls, checkin, checkout, hotel_id):
        dto = CheckAvailabilityDTO(data=dict(checkin=checkin, checkout=checkout, roomconfig='1-0', hotel_ids=hotel_id))
        try:
            return sum(list(AvailabiltyService.get_availability(dto).get('data').values())[0].values())
        except Exception as e:
            logger.info('error checking availability for softblock %s : %s', str(e), hotel_id)
            raise SoftblockAvailabilityValidationError('unable to check availability')

    @classmethod
    def validate(cls, requested_rooms, checkin, checkout, hotel_id):
        threshold = ConfigRules.objects.get(key=ConfigRules.KEYS['softblock_inventory_threshold'])
        threshold = int(round(float(threshold.value)))
        total = cls.get_total_availability(checkin, checkout, hotel_id)
        if 100 * requested_rooms / total > threshold:
            raise SoftblockAvailabilityValidationError(
                'Soft block cannot be created since only {} rooms can be softblocked'.format(
                    floor(total * threshold / 100)))
