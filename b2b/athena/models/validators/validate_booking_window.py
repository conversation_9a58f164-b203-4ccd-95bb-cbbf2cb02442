import json
import logging
from datetime import datetime, timedelta

import pytz
from django.conf import settings

from athena.api.exceptions import SoftblockValidationError, SoftblockTimeLimitValidationError
from athena.constants import SoftBookingConfig
from athena.models.config_rules import ConfigRules
from b2b.consumer.crs.utils import convert_to_utc_timezone

logger = logging.getLogger(__name__)


class BookingWindowValidation(object):

    @classmethod
    def validate_booking_window(cls, checkin_date, key):

        checkin_time = datetime.strptime(checkin_date, '%Y-%m-%d')
        booking_window = (checkin_time.date() - datetime.today().date()).days
        json_data = ConfigRules.get_configuration_for_key(namespace='softblock_validation', key=key).value
        booking_window_config = json.loads(json_data)
        booking_window_config = {int(key): value for key, value in list(booking_window_config.items())}
        booking_windows = list(booking_window_config.keys())
        booking_windows.sort()

        applicable_window = None
        for window in booking_windows:
            if int(window) <= booking_window:
                applicable_window = window

        if applicable_window is None:
            raise SoftblockTimeLimitValidationError('Booking window exceeds config.')

        try:
            return cls.validate_time_limit(checkin_time, booking_window_config.get(applicable_window), key)
        except SoftblockTimeLimitValidationError as e:
            raise e
        except Exception as e:
            logger.info(e)
            raise SoftblockValidationError('Booking window validation failed for checkin: {}'.format(checkin_date))

    @classmethod
    def validate_time_limit(cls, checkin_time, applicable_window, key):
        """
        hard limit to release d-1 softblock
        :param checkin_time:
        :param applicable_window:
        :param current_time:
        :return:
        """
        today = datetime.now(pytz.utc)
        tomorrow = today + timedelta(days=1)

        if key == SoftBookingConfig.BTC_CONFIRM_SOFT_BOOKING_WINDOW_KEY:
            SOFT_BLOCK_RELEASE_TIME_MAX_HOUR = settings.BTC_CONFIRM_SOFTBLOCK_RELEASE_TIME['hour']
            SOFT_BLOCK_RELEASE_TIME_MAX_MINUTE = settings.BTC_CONFIRM_SOFTBLOCK_RELEASE_TIME['minute']
        else:
            SOFT_BLOCK_RELEASE_TIME_MAX_HOUR = settings.SOFTBLOCK_RELEASE_TIME['hour']
            SOFT_BLOCK_RELEASE_TIME_MAX_MINUTE = settings.SOFTBLOCK_RELEASE_TIME['minute']


        local_max_time = checkin_time.replace(hour=SOFT_BLOCK_RELEASE_TIME_MAX_HOUR,
                                              minute=SOFT_BLOCK_RELEASE_TIME_MAX_MINUTE)

        _utc_max_time = convert_to_utc_timezone(local_max_time.strftime("%Y-%m-%d %H:%M:%S"))
        _utc_max_time = datetime.strptime(_utc_max_time, "%Y-%m-%d %H:%M:%S").replace(tzinfo=pytz.utc)

        forecasted_expiry = today + timedelta(hours=int(applicable_window))

        if checkin_time.date() in [today.date(), tomorrow.date()]:

            if today.hour >= _utc_max_time.hour and not key == SoftBookingConfig.BTC_CONFIRM_SOFT_BOOKING_WINDOW_KEY:
                raise SoftblockTimeLimitValidationError('Softblock can\'t be created after: {}'.format(local_max_time))

            if checkin_time.date() == tomorrow.date() and \
                    not key == SoftBookingConfig.BTC_CONFIRM_SOFT_BOOKING_WINDOW_KEY:
                _new_utc_max_time = _utc_max_time - timedelta(days=1)
                if forecasted_expiry > _new_utc_max_time:
                    _new_local_max_time = local_max_time - timedelta(days=1)
                    raise SoftblockTimeLimitValidationError(
                        'Softblock can\'t be created after: {} (checkin -1)'.format(_new_local_max_time))

            return min(_utc_max_time, forecasted_expiry)

        return forecasted_expiry
