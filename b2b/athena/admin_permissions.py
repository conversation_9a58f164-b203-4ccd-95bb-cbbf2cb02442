#!/usr/bin/env python
# -*- coding: utf-8 -*-


from django.contrib import admin
from django.contrib.admin.utils import flatten_fieldsets
from django.contrib.contenttypes.models import ContentType


class AdminPermissionsMixin(admin.ModelAdmin):
    ACTIONS = dict(ADD='add',
                   READ = 'read',
                   CHANGE = 'change',
                   DELETE = 'delete')

    def has_change_permission(self, request, obj=None):
        return self.validate_user_permission(request, self.ACTIONS['CHANGE'])

    def has_add_permission(self, request):
        return self.validate_user_permission(request, self.ACTIONS['ADD'])

    def has_delete_permission(self, request, obj=None):
        return self.validate_user_permission(request, self.ACTIONS['DELETE'])

    def get_actions(self, request):
        # queryset.delete() still can delete - hence disabled by overriding actions
        actions = super(AdminPermissionsMixin, self).get_actions(request)
        if not self.validate_user_permission(request, self.ACTIONS['DELETE']):
            actions.pop("delete_selected", None)
        return actions

    def get_readonly_fields(self, request, obj=None):
        if obj:
            if not self.validate_user_permission(request, self.ACTIONS['CHANGE']):
                if self.declared_fieldsets:
                    return flatten_fieldsets(self.declared_fieldsets)
                else:
                    return list(set(
                        [field.name for field in self.opts.local_fields] +
                        [field.name for field in self.opts.local_many_to_many]
                    ))
            return super(AdminPermissionsMixin, self).get_readonly_fields(request, obj)
        return list()

    # we set the context here and use it in submit_row method above since we cannot get user permissions there
    def change_view(self, request, object_id, form_url='', extra_context=None):
        if not self.validate_user_permission(request, self.ACTIONS['CHANGE']):
            extra_context = extra_context or {}
            extra_context['show_save_and_add_another'] = False
            extra_context['show_save_and_continue'] = False
            extra_context['show_save'] = False

        return super(AdminPermissionsMixin, self).change_view(request, object_id,
                                                               extra_context=extra_context)

    def validate_user_permission(self, request, validation_type):
        if request.user.is_superuser:
            return True
        content_type = ContentType.objects.get_for_model(self.model)
        perm_name = '{app_label}.{perm_type}_{model}'.format(app_label=content_type.app_label,
                                                             perm_type=validation_type,
                                                             model=content_type.model)
        if request.user.has_perm(perm_name):
            return True
