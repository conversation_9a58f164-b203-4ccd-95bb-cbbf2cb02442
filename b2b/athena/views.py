from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from rest_framework.response import Response

from athena.api.base import AthenaAPI


class AthenaLanding(AthenaAPI):
    template_name = "athena/corporate_booking.html"

    # pylint: disable=arguments-differ
    @method_decorator(login_required(login_url='/athena/login/'))
    def dispatch(self, *args, **kwargs):
        return super(AthenaLanding, self).dispatch(*args, **kwargs)

    def get(self, request, *args, **kwargs):
        return Response(
            data={'request': request, 'gstin_enabled': True, 'max_booking_period': settings.MAX_BOOKING_PERIOD},
            template_name=self.template_name)
