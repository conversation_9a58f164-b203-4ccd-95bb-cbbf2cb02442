# pylint: disable=invalid-name

from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

from django.conf.urls import url, include

from athena.api.availibility_and_pricing import AvailibilityAndPricingAPI
from athena.api.booking import BookingAPI
from athena.api.booking_search import BookingSearch
from athena.api.bookings_detail import EditBookingView
from athena.api.bookings_list import BookingsListing
from athena.api.cheapest_availability import CheapestAvailabilityAPI
from athena.api.cities import CitiesAPI, CityLevelLocalityAPI
from athena.api.corporate_posttax_pricing import CorporatePosttaxPricingAPI
from athena.api.expense_item import ExpenseItemPrice
from athena.api.legal_entity_rateheet_emailer import LegalEntityRatesheetEmailer
from athena.api.corporates import Corporates
from athena.api.corporate_price import CorporatePriceAPI
from athena.api.generate_ratesheet import GenerateRatesheetAPI
from athena.api.hotels import Hotels
from athena.api.legal_entities import LegalEntities
from athena.api.localities import LocalitiesAPI
from athena.api.login import Login
from athena.api.logout import Logout
from athena.api.soft_bookings import SoftBookingsListing
from athena.api.soft_bookings_reminders import SoftBookingsReminder
from athena.api.state_wise_legal_entity import StateWiseLegalEntityAPI
from athena.api.vip_booking import VIPBooking
from athena.api.web_pricing import WebPricingAPI
from athena.api.search_billing_entities import SearchBillingEntitiesAPI
from athena.views import AthenaLanding

urlpatterns = [
    url(r'^$', AthenaLanding.as_view(), name='landing'),
    url(r'^v2/', include(('athena.v2.urls', 'v2'), namespace='v2')),
    url(r'^hotels/$', Hotels.as_view(), name='hotels'),
    url(r'^corporates/$', Corporates.as_view(), name='corporates'),
    url(r'^legal-entities/$', LegalEntities.as_view(), name='legal-entities'),
    url(r'^state-wise-legal-entity/$', StateWiseLegalEntityAPI.as_view(), name='state-wise-legal-entity'),
    url(r'^login/$', Login.as_view(), name='login'),
    url(r'^logout/$', Logout.as_view(), name='logout'),

    url(r'^booking/$', BookingAPI.as_view(), name='booking'),
    url(r'^softbookings/$', SoftBookingsListing.as_view(), name='softbookings-list'),

    url(r'^flexi-pricing/', BookingAPI.as_view(), name='flexi-pricing'),
    url(r'^fetch-availibility-pricing/', AvailibilityAndPricingAPI.as_view(), name='fetch-availibility-pricing'),

    url(r'bookings/', BookingsListing.as_view(), name='bookings-list'),
    url(r'bookings-search/', BookingSearch.as_view(), name='bookings-search'),
    url(r'^booking/(?P<booking_id>[a-zA-Z0-9-_]+)/$', EditBookingView.as_view(), name="booking-edit"),

    # send vip booking data to prowl
    url(r'^vip-booking/', VIPBooking.as_view(), name='vip-booking'),

    url(r'^soft-block-reminder/', SoftBookingsReminder.as_view(), name='soft-block-reminder'),

    url(r'^web-pricing/', WebPricingAPI.as_view(), name='pricing'),
    url(r'^corporate-posttax-pricing/', CorporatePosttaxPricingAPI.as_view(), name='corporate-posttax-pricing'),

    url(r'^cheapest-availability/', CheapestAvailabilityAPI.as_view(), name='cheapest-availability-pricing'),
    url(r'^localities/', LocalitiesAPI.as_view(), name='localities'),
    url(r'^cities/', CitiesAPI.as_view(), name='cities'),
    url(r'^corporate-ratesheet-emailer/', LegalEntityRatesheetEmailer.as_view(), name='corporate-ratesheet-emailer'),
    url(r'^city-level-localities/', CityLevelLocalityAPI.as_view(), name='city-level-localities'),
    url(r'^generate-rate-sheet/(?P<payload_id>[\w-]+)/', GenerateRatesheetAPI.as_view(),
        name='generate-rate-sheet'),
    url(r'^corporate-price/', CorporatePriceAPI.as_view(), name='get-corporate-price'),
    url(r'^legal-entities/(?P<legal_entity_id>[a-zA-Z0-9]+)/billing-entities/$',
        SearchBillingEntitiesAPI.as_view(), name='biling-entity-search'),
    url(r'^expense-item-prices/', ExpenseItemPrice.as_view(), name='expense-item-prices'),
]
