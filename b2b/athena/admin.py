from django import forms
from django.contrib import admin
from django.core.exceptions import ValidationError

from athena.admin_permissions import AdminPermissionsMixin
from athena.models import BlackoutBookingDates, ExpenseItem
from athena.models.config_rules import ConfigRules
from athena.models.default_flexi_price import DefaultFlexiPrice
from athena.models.flexi_price import FlexiPrice
from athena.models.flexible_price_booking_status import FlexiPriceBookingStatus


class FlexiPriceAdmin(admin.ModelAdmin):
    list_display = ['hotel_name', 'room_type', 'start_date', 'end_date', 'room_price', 'extra_person_charges', 'reason']
    search_fields = ['room_type', 'reason', 'hotel__name']

    def hotel_name(self, obj):
        return obj.hotel.name


class DefaultFlexiPriceAdmin(admin.ModelAdmin):
    list_display = ['room_type', 'room_price', 'extra_person_charges', 'reason']
    search_fields = ['room_type', 'reason']


class FlexiPriceBookingStatusAdmin(admin.ModelAdmin):
    list_display = ['booking_id', 'reason', 'actual_price', 'flexible_price', 'discount']
    search_fields = ['booking_id', 'reason']


class ConfigRulesCreationForm(forms.ModelForm):

    def clean(self):
        if not self.permitted:
            raise ValidationError('You are not allowed to perform this action')


class ConfigRulesAdmin(AdminPermissionsMixin):
    fields = ('key', 'value', 'namespace')
    list_display = ['key', 'value', 'namespace']

    form = ConfigRulesCreationForm

    def get_form(self, request, *args, **kwargs):
        form = super(ConfigRulesAdmin, self).get_form(request, *args, **kwargs)
        form.permitted = self.validate_user_permission(request,
                                                       "{}_{}".format(request.POST.get('namespace'),
                                                                      request.POST.get('key')))
        return form


class ExpenseItemAdmin(AdminPermissionsMixin):
    fields = ('name', 'description', 'short_name', 'sku_category_id', 'sku_id', 'is_active')
    list_display = ['name', 'description', 'short_name', 'sku_category_id', 'sku_id', 'is_active']


class BlackoutBookingDatesAdmin(admin.ModelAdmin):
    list = ['blackout_start_date', 'blackout_end_date', 'hotel', 'active']
    search_fields = ['hotel__name']
    list_display = ['created_at', 'modified_at', 'blackout_start_date', 'blackout_end_date', 'hotel', 'active']


admin.site.register(DefaultFlexiPrice, DefaultFlexiPriceAdmin)
admin.site.register(FlexiPrice, FlexiPriceAdmin)
admin.site.register(FlexiPriceBookingStatus, FlexiPriceBookingStatusAdmin)
admin.site.register(ConfigRules, ConfigRulesAdmin)
admin.site.register(BlackoutBookingDates, BlackoutBookingDatesAdmin)
admin.site.register(ExpenseItem, ExpenseItemAdmin)
