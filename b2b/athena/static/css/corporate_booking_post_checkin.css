[v-cloak] .v-cloak--hidden {
    display: none;
}

.treebo-logo {
    width: 125px;
    margin-top: 6px;
    margin-left: 2px;
}

.corporate .multiselect__tags {
    border: none;
    padding-left: 0;
    padding-right: 0;
}

.corporate .multiselect__select {
    padding: 0;
    width: auto;
    top: 9px;
}

.corporate .multiselect__select:before {
    border-width: 8px 4px 0;
    border-color: #000 transparent transparent;
}

.confirm-booking {
    width: 320px;
}

.room-occupancy {
    width: 90%;
}

.room-remove {
    transform: rotateZ(45deg);
}

.room-item {
    margin: 25px;
    padding: 10px;
}

.btn-small {
    width: 15px;
    height: 15px;
    font-size: 13px;
    line-height: 14px;
}

i.btn-small {
    background-color: #fafafa !important;
    border: 1px solid #9e9e9e;
    border-radius: 50%;
}

.count {
    padding: 5px !important;
    float: none !important;
}

.count-big {
    padding: 5px !important;
    float: none !important;
}

.addon.collection {
    overflow: visible;
}

.add-on-price {
    margin-top: -11px !important;
}

.total-price {
    margin: 31px 0px;
    padding: 24px 0;
}

.collection-item {
    background-color: #fafafa !important;
}

.grey-input input[type=text] {
    /*background-color: #f5f5f5 !important;*/
}

.guest-details {
    /*padding-left: 27px;*/
    padding-bottom: 40px;
}

.meal-value {
    margin-left: -9px !important;
    padding-left: 0 !important;
    margin-right: 9px !important;
}

.grey-input .multiselect {
    background-color: #fafafa !important;
}

.light-grey {
    color: #bdbdbd;
}

.btn-room-remove {
    padding-top: 15px;
}

.small-font {
    font-size: x-small !important;
}

.btn-cancel {
    margin-right: 30px !important;
    background-color: #bdbdbd;
}

i.disabled {
    pointer-events: none;
    background-color: #DFDFDF !important;
    box-shadow: none;
    color: #9F9F9F !important;
    cursor: default;
}

.min-price-room-type {
    padding-top: 12px !important;
}

.add-details {
    padding-bottom: 30px;
}

#comments {
    height: 10px;
    min-height: 5px;
}

.teal.lighten-2 {
    background-color: #074a44 !important;
}

.athena-title {
    padding-left: 15px;
    font-size: 30px;
}

.card-height {
    min-height: 250px;
}

.validBooking {
    border: 1px solid green;
    padding: 14px;
    color: green;
}

.invalidBooking {
    color: red;
    font-weight: 300;
}

#link-oldbooking-modal {
    top: 25% !important;
    height: 250px;
}

.highlighted-properties-section {
    background: #fff0d6;
    margin-left: 0%;
    width: 100%;
    padding: 1px 15px 10px 15px;
}

.promoted-rooms {
    padding-left: 20px;
    color: grey;
}

.rectangle-4 {
    width: 1048px;
    height: 55px;
    background-color: #fff0d6;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.43);
    padding-left: 6px;
    padding-top: 15px;
}

.change-stay-dates{
    height: 3rem;
    margin-top: 45px;
}
.room-type {
    display: inline-block;
    text-transform: capitalize;

}
.max-guests-allowed{
    margin-left: 14px;
}
/* Absolute Center Spinner */
.loading {
    visibility: visible !important;
    position: fixed;
    z-index: 2000;
    height: 2em;
    width: 2em;
    overflow: show;
    margin: auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

/* Transparent Overlay */
.loading:before {
    content: '';
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(rgba(20, 20, 20, .8), rgba(0, 0, 0, .8));
    background: -webkit-radial-gradient(rgba(20, 20, 20, .8), rgba(0, 0, 0, .8));
}

/* Animation */

@-webkit-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-moz-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-o-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.price_block{
    min-height: 45px !important;
    border: 1px solid #e0e0e0;
    padding: 27px !important;
    line-height: 2.5;
}

.room-header{
    margin-bottom: 15px;
}

a.disabled{
    pointer-events: none;
    cursor: default;
    text-decoration: none;
}

.addons_select{
    display: block;
}

.capitalize {
  text-transform: capitalize;
  font-size: 20px;
}

label[for=checkin],
label[for=checkout] {
    width: 100%;
    height: 100%;
}

label[for=checkin].active,
label[for=checkout].active {
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }

.displayRoomDetails {
    display: none;
}

.update-price-heading{
    font-weight: bold;
}

.update-price-label{
    margin-top: 40px;
    margin-bottom: 0px;
}

.update-price-input{
    margin-top: 0px;
}

.popup-bg{
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    display: block;
    z-index: 3;
    background-color: rgba(22,22,22,0.5);
}

.popup {
    display: block;
    font-size: 16px;
    position: relative;
    z-index: 1;
    margin: 0 auto;
    top: 35%;
    padding: 20px;
    width: 25%;
    height: fit-content;
    background-color: white;
}

.popup-title{
    font-size: 20px;
    margin: 0 0 16px 0;
    font-weight: bold;
}

.popup-buttons{
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.popup-button {
    border: none;
    font-weight: bold;
    text-decoration: none;
    cursor: pointer;
    background: transparent;
    color: teal;
}

.popup-button:focus {
    outline: none;
    background: transparent;
}

.popup-value{
    font-weight: bold;
}

.popup-summary{
    display: flex;
    justify-content: space-between;
}