<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <!--<![endif]-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>

    <style type="text/css">

        .container {
            width: 80%;
            margin: 100px auto;
        }

        .progressbar {
            counter-reset: step;
        }

        .progress-bar {
            padding-bottom: 80px;
        }

        .progressbar li {
            list-style-type: none;
            width: 25%;
            float: left;
            font-size: 12px;
            position: relative;
            text-align: center;
            text-transform: uppercase;
            color: red;
        }

        .progressbar li:before {
            width: 30px;
            height: 30px;
            content: counter(step);
            counter-increment: step;
            line-height: 30px;
            border: 2px solid red;
            display: block;
            text-align: center;
            margin: 0 auto 10px auto;
            border-radius: 50%;
            background-color: white;
        }

        .progressbar li:after {
            width: 100%;
            height: 2px;
            content: '';
            position: absolute;
            background-color: #7d7d7d;
            top: 15px;
            left: -50%;
            z-index: -1;
        }

        .progressbar li:first-child:after {
            content: none;
        }

        .progressbar li.active {
            color: green;
        }

        .progressbar li.active:before {
            border-color: #55b776;
        }

        .progressbar li.active + li:after {
            background-color: #55b776;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        tr:hover {
            background-color: #f5f5f5
        }

    </style>


</head>
<body>
<div class="container">
    <div class="progress-bar">
        <!--<ul class="progressbar">-->
        <!--<li {% if progress_indicator >= 1 %} class="active" {% endif %}>Saved Booking(Temporary)</li>-->
        <!--<li {% if progress_indicator >= 2 %} class="active" {% endif %}>Confirm Booking</li>-->
        <!--<li {% if progress_indicator >= 3 %} class="active" {% endif %}>Payments</li>-->
        {#          <!--<li {% if progress_indicator >= 4 %} class="active" {% endif %}>Guest Details</li>-->#}
        <!--</ul>-->
        <table style="border:0;" cellpadding="0" cellspacing="0" width="250">
            <tr>
                <th>Saved Booking(Temporary)</th>
                <th>Confirm Booking</th>
                <th>Payments</th>
                {#            <th>Guest Details</th>#}
            </tr>
            <tr>
                <td {% if progress_indicator >= 1 %} bgcolor="#55b776" {% else %} bgcolor="#cccccc" {% endif %} ></td>
                <td {% if progress_indicator >= 2 %} bgcolor="#55b776" {% else %} bgcolor="#cccccc" {% endif %} ></td>
                <td {% if progress_indicator >= 3 %} bgcolor="#55b776" {% else %} bgcolor="#cccccc" {% endif %} ></td>
                {#            <td {% if 4 in progress_indicator %} bgcolor="#55b776" {% else %} bgcolor="#cccccc" {% endif %} ></td>#}
            </tr>
        </table>
    </div>
    <br>
    <hr>
    Steps:
    <ol>
        <li>If failed in confirm booking please port booking and add payments</li>
        <li>If failed in add payments please port payments</li>
        <li>If booking is TA (Travel Agent) - to be paid by TA(check pay_type)</li>
        <ul>
            <li>create booking in HX with source corporate - Direct</li>
            <li>Please enter guest email in owner email</li>
        </ul>
        <li>If booking is TA (Travel Agent) - to be paid by guest(check pay_type)</li>
        <ul>
            <li>create booking in HX with source as corporate - Direct</li>
            <li>Please enter TA email in owner email</li>
        </ul>

        {#        <li>If failed in guest details please port guest details</li>#}
    </ol>

    <br>
    <hr>
    <div class="booking-details">
        <table>
            <tr>
                <th>ORDER ID:</th>
                <th>{{ order_id }}</th>
            </tr>
            <tr>
                <td>Hotel Name:</td>
                <td>{{ hotel_name }}</td>
            </tr>
            <tr>
                <td>Corporate Trading Name:</td>
                <td>{{ corporate_trading_name }}</td>
            </tr>
            <tr>
                <td>Corporate Name:</td>
                <td>{{ corporate_name }}</td>
            </tr>
            <tr>
                <td>Corporate ID:</td>
                <td>{{ corporate_id }}</td>
            </tr>
            <tr>
                <td>Check in:</td>
                <td>{{ check_in_date }}</td>
            </tr>
            <tr>
                <td>Check out:</td>
                <td>{{ check_out_date }}</td>
            </tr>
            <tr>
                <td>Meal code:</td>
                <td>{{ meal_code }}</td>
            </tr>
            <tr>
                <td>Meal type:</td>
                <td>{{ meal_type }}</td>
            </tr>
            <tr>
                <td>Pay type:</td>
                <td>{{ pay_type }}</td>
            </tr>
            <tr>
                <td>Booking Status:</td>
                <td>{{ booking_status }}</td>
            </tr>
            <tr>
                <td>Messages:</td>
                <td>{{ messages }}</td>
            </tr>
            <tr>
                <td>Rooms:</td>
                <td>
                    <table>
                        <tr>
                            <td>Room_type</td>
                            <td>No of guests</td>
                            <td>Price per room</td>
                            <td>Guest detail</td>
                        </tr>
                        {% for room in rooms %}
                            <tr>
                                <td>{{ room.room_type }}</td>
                                <td>
                                    {{ room.guest_count }}
                                </td>
                                <td>
                                    {{ room.total_pre_tax_room_charges }}
                                </td>
                                <td>
                                    {% for guest in room.all_guests_details %}
                                        <table>
                                            <tr>
                                                <td>
                                                    {{ guest.name }}
                                                </td>
                                                <td>
                                                    {{ guest.email }}
                                                </td>
                                                <td>
                                                    {{ guest.phone }}
                                                </td>
                                            </tr>
                                        </table>
                                    {% endfor %}
                                </td>
                            </tr>
                        {% endfor %}
                    </table>
                </td>
            </tr>
        </table>
    </div>

</div>
</body>
</html>
