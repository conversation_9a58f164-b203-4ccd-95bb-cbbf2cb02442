<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
    <meta name="viewport" content="width=device-width">
    <title>User Registration</title>
</head>
<style>
    body {
        font-family: Roboto;
        background-color: #f1f1f1;
        margin: 0px;
    }

    .banner-container {
        background-color: white;
        height: 59px;
        display: flex;
    }

    .logo {
        flex-grow: 1;
        text-align: left;
        padding-left: 140px;
        display: flex
    }

    .helpline {
        flex-grow: 1;
        text-align: right;
        padding-right: 145px;
    }

    .logo-img {
        flex-grow: 1;
        text-align: right;
        padding-right: 16px;
        max-width: 100px;
    }

    .logo-name {
        flex-grow: 1;
        padding-top: 21px;
    }

    .brand-name {
        font-weight: bold;
        font-size: 1.2em;
    }

    .additional-brand-name {
        color: #aeaeae
    }

    .helpline {
        display: flex;
        padding-top: 21px;
    }

    .help-text {
        flex-grow: 1;
        color: #727171;
    }

    .help-number {
        flex-grow: 1;
        max-width: 7em;
        color: #4a4a4a;
    }

    .set-password-container {
        background-color: #f1f1f1;
    }

    .password-container {
        width: 480px;
    }

    .form-container {
        background-color: white;
        margin: 4em 18em;
        height: 480px;
        width: 864px;
        display: flex;
    }

    .why-treebo {}

    .set-password-msg {
        color: #aeaeae;
        font-size: 0.8em;
    }

    .password-success-msg {
        color: #4a4a4a;
        font-size: 0.8em;
        margin-top: 32px;
    }

    .password-form {
        padding: 48px 96px;
    }

    .password-input-form {
        margin-top: 32px;
    }

    .password-input-field {
        border: 1px solid #aeaeae;
        height: 42px;
        width: 288px;
        margin: 8px 0px;
        color: #4a4a4a;
        border-radius: 2px;
    }

    .submit-button {
        height: 42px;
        width: 292px;
        background: #0eb550;
        color: white;
        font-size: 1em;
        margin-top: 30px;
        border: none;
    }

    .password-instruction {
        color: #aeaeae;
        font-size: 0.7em;
    }

    .error-msg {
        height: 28px;
        padding-left: 32px;
        padding-top: 10px;
        padding-bottom: 0px;
        background: #ffeff1;
        color: #4a4a4a;
        font-size: 0.9em;
    }

    .redirect-homepage-btn {
        width: 288px;
        height: 42px;
        border-radius: 2px;
        color: white;
        background: #0eb550;
        margin-top: 48px;
    }
</style>

<body>
    <!-- BANNER -->

    <div class="banner-container">
        <div class="logo">
            <div class="logo-name">
                <img src="https://s3-ap-southeast-1.amazonaws.com/treebo/static/images/B2B/treebo-logo-text.svg" , alt="treebo-logo">
            </div>
        </div>

        <div class="helpline">
            <div class="help-text">
                Need help? Call
            </div>
            <div class="help-number">
                093228 00100
            </div>
        </div>
    </div>

    <!-- FORM CONTAINER -->
    <div class="set-password-container">
        <div class="form-container">
            <div class="why-treebo">
                <img src="https://s3-ap-southeast-1.amazonaws.com/treebo/static/images/B2B/why-treebo.svg" alt="Why Treebo" />
            </div>
            <div class="password-container">

                {% if password_reset_success == True %}
                <div class="password-form">
                    <h3 style="margin-bottom: 8px; color: #4a4a4a"> Password Set Successfully</h3>
                    <div class="password-success-msg">Hi {{ username }}, your password is set successfully. Your account is now ready!</div>
                    <form method="get" action="/">
                        <input class="redirect-homepage-btn" type="submit" value="LOGIN TO START BOOKING">
                    </form>
                </div>

                {% else %}
                <div class="password-form">
                    <h3 style="margin-bottom: 8px; color: #4a4a4a;"> Set Your Password</h3>
                    <div class="set-password-msg">You are just one step away from logging in.</div>
                    <form class="password-input-form" method="post">
                        <div>
                            <div class="password-instruction">Enter Password</div>
                            <input class="password-input-field" type="password" name="password1" value="">
                        </div>

                        <div style="margin-top: 28px;">
                            <div class="password-instruction">Confirm Password</div>
                            <input class="password-input-field" type="password" name="password2" value="">
                        </div>

                        <input class="submit-button" type="submit" value="SET PASSWORD">
                    </form>
                </div>
                {% endif %} {% if error == True %}
                <div class="error-msg">
                    {{ message }}
                </div>
                {% endif %}
            </div>

        </div>

    </div>
</body>

</html>