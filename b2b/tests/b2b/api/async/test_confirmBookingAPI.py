# -*- coding: utf-8 -*-
import json

from django.test import Simple<PERSON><PERSON><PERSON><PERSON>, Client
from mock import patch

from b2b.domain.services.exceptions import UnableToConfirmBooking, ResourceUnavailable, FailedToCancelBooking


class TestConfirmBookingAPI(SimpleTestCase):
    def setUp(self):
        super(TestConfirmBookingAPI, self).setUp()

        self.client = Client()

    @patch('b2b.api.asynchronous.confirm_booking.BookingService')
    def test_post_with_bad_request_data(self, bs_mock):
        response = self.client.post('/b2b/crs/confirm_booking/',
                                    {'username': 'user', 'password': 'password'})

        bs_mock.confirm_booking_in_crs.assert_not_called()
        self.assertEqual(2201, json.loads(response.content)['errors'][0]['code'])
        self.assertEqual(400, response.status_code)

    @patch('b2b.api.asynchronous.confirm_booking.BookingService')
    def test_post_with_existing_booking_id_in_query_param(self, bs_mock):
        booking_id = "B2B-1234567890"
        response = self.client.post('/b2b/crs/confirm_booking/?booking_id={bid}'.format(bid=booking_id))
        bs_mock.confirm_booking_in_crs.assert_called_with(booking_id)

        self.assertEqual(200, response.status_code)

    @patch('b2b.api.asynchronous.confirm_booking.BookingService')
    def test_post_with_existing_booking_id_in_request_data(self, bs_mock):
        booking_id = "B2B-1234567890"
        response = self.client.post('/b2b/crs/confirm_booking/',
                                    {'booking_id': booking_id})
        bs_mock.confirm_booking_in_crs.assert_called_with(booking_id)

        self.assertEqual(200, response.status_code)

    @patch('b2b.api.asynchronous.confirm_booking.BookingService')
    def test_post_with_unknown_booking_id(self, bs_mock):
        """
        when booking-id is unknown, underlying booking-svc catches Booking.DoesNotExist
        exception and chains it to UnableToConfirmBooking
        :param bs_mock:
        :return:
        """
        bs_mock.confirm_booking_in_crs.side_effect = UnableToConfirmBooking(booking_id='B2B-UNKN0WN',
                                                                            error='booking-does-not-exist')
        response = self.client.post('/b2b/crs/confirm_booking/',
                                    {'booking_id': 'B2B-UNKN0WN'})
        bs_mock.confirm_booking_in_crs.assert_called_with('B2B-UNKN0WN')

        self.assertEqual(2201, json.loads(response.content)['errors'][0]['code'])
        self.assertEqual(400, response.status_code)

    @patch('b2b.api.asynchronous.confirm_booking.BookingService')
    def test_post_where_bookingsvc_raises_unknown_exception(self, bs_mock):
        bs_mock.confirm_booking_in_crs.side_effect = Exception('test-exception')
        response = self.client.post('/b2b/crs/confirm_booking/',
                                    {'booking_id': 'B2B-1234567890'})

        bs_mock.confirm_booking_in_crs.assert_called_with('B2B-1234567890')

        resp = json.loads(response.content)
        self.assertEqual(2202, resp['errors'][0]['code'])
        self.assertEqual(500, response.status_code)

    @patch('b2b.api.asynchronous.confirm_booking.BookingService')
    def test_post_when_hx_is_down(self, bs_mock):
        """
        when hx or any other external dep is down, ResourceUnavailable exception is raised
        """
        bs_mock.confirm_booking_in_crs.side_effect = ResourceUnavailable('test-svc',
                                                                         'test-resource',
                                                                         'MySQL went away')
        response = self.client.post('/b2b/crs/confirm_booking/',
                                    {'booking_id': 'B2B-1234567890'})

        bs_mock.confirm_booking_in_crs.assert_called_with('B2B-1234567890')

        resp = json.loads(response.content)
        self.assertEqual(2200, resp['errors'][0]['code'])
        self.assertEqual(503, response.status_code)

    @patch('b2b.api.asynchronous.confirm_booking.BookingService')
    def test_post_when_crs_api_fails(self, bs_mock):
        """
        when a crs api is fails, the exception eventually gets chained into FailedToCancelBooking
        """
        bs_mock.confirm_booking_in_crs.side_effect = FailedToCancelBooking(booking_id='B2B-1234567890',
                                                                           error='test-error')
        response = self.client.post('/b2b/crs/confirm_booking/',
                                    {'booking_id': 'B2B-1234567890'})

        bs_mock.confirm_booking_in_crs.assert_called_with('B2B-1234567890')

        resp = json.loads(response.content)
        self.assertEqual(2202, resp['errors'][0]['code'])
        self.assertEqual(500, response.status_code)
