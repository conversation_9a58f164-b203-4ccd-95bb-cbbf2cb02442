import json
from unittest import skip

from django.test import Client
from django.test import TestCase
from rest_framework.reverse import reverse

from b2b.dto.hotel_sync import CatalogServiceHotelSyncDTO
from b2b.domain.utils.converters import cs_hotel_sync_to_b2b_hotel_sync

from tests.b2b.api.csintegration import test_constants

#pylint: disable=invalid-name
@skip("These are integration tests, not unit tests")
class TestHotelSyncApi(TestCase):
    def setUp(self):
        """
        this method runs for every test case, test methods are atomic and not related
        :return:
        """
        super(TestHotelSyncApi, self).setUp()
        self.sample_no_saleable = test_constants.sample_no_saleable_attribute
        self.sample_saleable = test_constants.sample_saleable_attribute
        self.sample_no_cs_id = test_constants.sample_no_cs_id_attribute
        self.client = Client()

    def test_post_with_new_cs_id_and_existing_cs_id(self):
        """
        Tests both creation and update of hotels
        :return:
        """
        new_hotel = self.sample_saleable
        response = self.client.post(reverse('hotel-sync-v3'), data=json.dumps(new_hotel),
                                    content_type='application/json')

        self.assertEqual(201, response.status_code)

        existing_hotel = self.sample_saleable
        response = self.client.post(reverse('hotel-sync-v3'), data=json.dumps(existing_hotel),
                                    content_type='application/json')

        self.assertEqual(202, response.status_code)

    def test_post_with_new_cs_id(self):
        """
        Just tests the creation of hotel
        :return:
        """
        existing_hotel = self.sample_saleable
        response = self.client.post(reverse('hotel-sync-v3'), data=json.dumps(existing_hotel),
                                    content_type='application/json')

        self.assertEqual(201, response.status_code)

    def test_saleable_attribute_creation_Absent(self):
        """
        Tests the saleable attribute absence
        :return:
        """
        hotel_sync_dto_v3 = CatalogServiceHotelSyncDTO(data=self.sample_no_saleable)
        hotel_sync_dto_v3.is_valid()
        hotel_sync_dto = cs_hotel_sync_to_b2b_hotel_sync(hotel_sync_dto_v3)
        hotel_sync_dto.is_valid()
        self.assertIs(hotel_sync_dto.data['attributes']['saleable'], False)

    def test_saleable_attribute_creation_Present(self):
        """
        Tests the saleable attribute presence
        :return:
        """
        hotel_sync_dto_v3 = CatalogServiceHotelSyncDTO(data=self.sample_saleable)
        hotel_sync_dto_v3.is_valid()
        hotel_sync_dto = cs_hotel_sync_to_b2b_hotel_sync(hotel_sync_dto_v3)
        hotel_sync_dto.is_valid()
        self.assertIs(hotel_sync_dto.data['attributes']['saleable'], True)

    def test_post_with_no_cs_id(self):
        """
        Tests invalid message from CS service, with no cs_id
        :return:
        """
        faulty_hotel = self.sample_no_cs_id
        response = self.client.post(reverse('hotel-sync-v3'), data=json.dumps(faulty_hotel),
                                    content_type='application/json')

        self.assertEqual(500, response.status_code)
