# -*- coding: utf-8 -*-
from mock import patch

from django.test import SimpleTestCase

from b2b.consumer.crs import HX<PERSON>rapper
from b2b.consumer.crs.exceptions import CrsApiFailed, CrsInMaintenanceWindow, CrsAuthenticationFailure, CrsUnknownError, \
    MaxRetriesExceeded
from b2b.consumer.crs.hx import AccessCreds
from b2b.consumer.crs.hx.exceptions import HxApiFailed, HxSystemUnderMaintenance, HxAuthenticationFailure, \
    HxResponseParsingError, UnknownHxAPIGroup, HxAPIMaxRetriesExceeded, ErrorSavingCounterCreds
from b2b.consumer.crs.hx.request import loadcart


class TestHXWrapperConfirmBooking(SimpleTestCase):
    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_api_failure(self, hxbm_mock):
        hxbm_mock.return_value.confirm_booking.side_effect = HxApiFailed('test-api',
                                                                         'test-error-message')
        with self.assertRaises(CrsApiFailed):
            HXWrapper.confirm_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_system_in_maint_window(self, hxbm_mock):
        hxbm_mock.return_value.confirm_booking.side_effect = HxSystemUnderMaintenance('test-api',
                                                                                      'test-error-message')
        with self.assertRaises(CrsInMaintenanceWindow):
            HXWrapper.confirm_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_auth_failure(self, hxbm_mock):
        hxbm_mock.return_value.confirm_booking.side_effect = HxAuthenticationFailure('test-api',
                                                                                     'test-error-message')
        with self.assertRaises(CrsAuthenticationFailure):
            HXWrapper.confirm_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_error_while_saving_couter_creds(self, hxbm_mock):
        hxbm_mock.return_value.confirm_booking.side_effect = ErrorSavingCounterCreds('test-hotel')
        with self.assertRaises(CrsAuthenticationFailure):
            HXWrapper.confirm_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_unknown_error_on_hx_response_parsing_error(self, hxbm_mock):
        hxbm_mock.return_value.confirm_booking.side_effect = HxResponseParsingError('test-api',
                                                                                    'test-error-message')
        with self.assertRaises(CrsUnknownError):
            HXWrapper.confirm_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_unknown_error_on_generic_exception(self, hxbm_mock):
        hxbm_mock.return_value.confirm_booking.side_effect = Exception('test-error-message')

        with self.assertRaises(CrsUnknownError):
            HXWrapper.confirm_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_unknown_error_on_unknown_hx_api_group(self, hxbm_mock):
        hxbm_mock.return_value.confirm_booking.side_effect = UnknownHxAPIGroup('test-api',
                                                                               'test-group')
        with self.assertRaises(CrsUnknownError):
            HXWrapper.confirm_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_max_retries_exceeded(self, hxbm_mock):
        creds = AccessCreds(key="test-key", secret='test-secret')
        loadcart_req = loadcart()
        loadcart_req.set_access_creds(creds)

        hxbm_mock.return_value.confirm_booking.side_effect = HxAPIMaxRetriesExceeded(loadcart_req, 3)
        with self.assertRaises(MaxRetriesExceeded):
            HXWrapper.confirm_booking('B2B-UNKN0WN')


class TestHXWrapperCancelBooking(SimpleTestCase):
    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_api_failure(self, hxbm_mock):
        hxbm_mock.return_value.cancel_booking.side_effect = HxApiFailed('test-api',
                                                                        'test-error-message')
        with self.assertRaises(CrsApiFailed):
            HXWrapper.cancel_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_system_in_maint_window(self, hxbm_mock):
        hxbm_mock.return_value.cancel_booking.side_effect = HxSystemUnderMaintenance('test-api',
                                                                                     'test-error-message')
        with self.assertRaises(CrsInMaintenanceWindow):
            HXWrapper.cancel_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_auth_failure(self, hxbm_mock):
        hxbm_mock.return_value.cancel_booking.side_effect = HxAuthenticationFailure('test-api',
                                                                                    'test-error-message')
        with self.assertRaises(CrsAuthenticationFailure):
            HXWrapper.cancel_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_error_while_saving_couter_creds(self, hxbm_mock):
        hxbm_mock.return_value.confirm_booking.side_effect = ErrorSavingCounterCreds('test-hotel')
        with self.assertRaises(CrsAuthenticationFailure):
            HXWrapper.confirm_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_unknown_error_on_hx_response_parsing_error(self, hxbm_mock):
        hxbm_mock.return_value.cancel_booking.side_effect = HxResponseParsingError('test-api',
                                                                                   'test-error-message')
        with self.assertRaises(CrsUnknownError):
            HXWrapper.cancel_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_unknown_error_on_generic_exception(self, hxbm_mock):
        hxbm_mock.return_value.cancel_booking.side_effect = Exception('test-error-message')

        with self.assertRaises(CrsUnknownError):
            HXWrapper.cancel_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_unknown_error_on_unknown_hx_api_group(self, hxbm_mock):
        hxbm_mock.return_value.cancel_booking.side_effect = UnknownHxAPIGroup('test-api',
                                                                              'test-group')
        with self.assertRaises(CrsUnknownError):
            HXWrapper.cancel_booking('B2B-UNKN0WN')

    @patch('b2b.consumer.crs.hx_impl.HxBookingManager')
    def test_max_retries_exceeded(self, hxbm_mock):
        creds = AccessCreds(key="test-key", secret='test-secret')
        loadcart_req = loadcart()
        loadcart_req.set_access_creds(creds)

        hxbm_mock.return_value.cancel_booking.side_effect = HxAPIMaxRetriesExceeded(loadcart_req, 3)
        with self.assertRaises(MaxRetriesExceeded):
            HXWrapper.cancel_booking('B2B-UNKN0WN')
