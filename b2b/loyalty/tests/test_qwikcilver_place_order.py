import pytest
from mock import patch, Mock

from loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order import \
    QwikcilverGiftCardOrder
from loyalty.dto.order_config_dto import OrderConfigDTO
from loyalty.dto.qwikcilver_gift_card_dto import PlaceOrderDTO
from loyalty.exceptions import TotalGiftCardLimitExceeds, GiftCardQuantityExceeds, InvalidQwikcilverItemPrice


class TestQwikcilverPlaceOrder(object):

    def test_get_total_amount(self):
        products = [{
            "product_id": "24",
            "price": 200,
            "qty": "2",
            "gift_message": "test message for gift"
        }]

        total = QwikcilverGiftCardOrder._get_total_amount(products)
        assert total == 400

    @patch("loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.QwikcilverProducts")
    @patch("loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.TreeboCatelogItem")
    @patch(
        "loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.TreeboCatelogProduct")
    def test_catalog_to_qs(self, TreeboCatelogProduct, TreeboCatelogItem, QwikcilverProducts):
        # setup
        TreeboCatelogProduct.objects.get().id = 1
        TreeboCatelogItem.objects.get().points = 500
        QwikcilverProducts.objects.get().qs_product_id = 432

        item = {
            "item_id": 12,
            "item_name": "Flipkart Test Gift Card",
            "item_quantity": 10
        }

        product_array = QwikcilverGiftCardOrder._catalog_item_to_qs_product(item)
        assert product_array == {'price': 500, 'product_id': 432, 'qty': 10}

    # TODO: Need to mock TreeboCatelogItem.DoesNotExist for this testcase
    # @patch("loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.TreeboCatelogItem")
    # def test_catalog_to_qs_with_bad_catalog_id(self, TreeboCatelogItem):
    #     item = {
    #         "item_id": 1212332,
    #         "item_name": "Flipkart Test Gift Card",
    #         "item_quantity": 10
    #     }
    #
    #     with pytest.raises(InvalidCatalogItem) as e_info:
    #         QwikcilverGiftCardOrder._catalog_item_to_qs_product(item)

    # TODO: Need to mock TreeboCatelogItem.DoesNotExist for this testcase

    # @patch("loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.TreeboCatelogItem")
    # def test_catalog_to_qs_with_bad_treebo_catalog_item_id(self, TreeboCatelogItem):
    #     item = {
    #         "item_id": 1212332,
    #         "item_name": "Flipkart Test Gift Card",
    #         "item_quantity": 10
    #     }
    #
    #     TreeboCatelogItem.objects.get().side_effect = TreeboCatelogItem.DoesNotExist
    #
    #     with pytest.raises(InvalidCatalogItem):
    #         QwikcilverGiftCardOrder._catalog_item_to_qs_product(item)

    @patch("loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.TreeboCatelogItem")
    @patch("loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.QwikcilverProducts")
    @patch(
        "loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.TreeboCatelogProduct")
    def test_catalog_to_qs_with_invalid_item_price(self, TreeboCatelogProduct, QwikcilverProducts, TreeboCatelogItem):
        qs_product_id = 24
        points = 234
        TreeboCatelogProduct.objects.get().id = 1
        QwikcilverProducts.objects.get.return_value = Mock(name="Flipkart Gift Card",
                                                           qs_product_id=qs_product_id,
                                                           description='',
                                                           short_description='',
                                                           min_custom_price='100',
                                                           max_custom_price='1000',
                                                           custom_denominations='100,200,300',
                                                           price_type='slab',
                                                           catelogitem_id=1,
                                                           image_url='')

        TreeboCatelogItem.objects.get().points = points

        item = {
            "item_id": 1,
            "item_name": "Flipkart Test Gift Card",
            "item_quantity": 10
        }
        with pytest.raises(InvalidQwikcilverItemPrice):
            QwikcilverGiftCardOrder._catalog_item_to_qs_product(item)

    @patch("loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.QwikcilverConfig")
    def test_verify_config_validity_true(self, QwikcilverConfig):
        QwikcilverConfig().get_config.return_value = OrderConfigDTO(data=dict(
            max_item_in_cart=3,
            product_max_qty_allow=10,
            store_id=10
        ))
        test_payment_object = {
            "payment_method": [{"po_number": "1672637263278", "amount_to_redem": "200", "method": "SVC"}],
            "refno": "11-AB-00024k563617s121",
            "products": [
                {"gift_message": "test message for gift-anubhav", "price": 200, "product_id": "24", "qty": "1"}],
            "shipping": {"city": "bangalore", "postcode": "560076", "firstname": "Anubhav",
                         "line_1": "address details1", "lastname": "Shrivastava", "region": "Karnataka",
                         "line_2": "address details 2", "country_id": "IN", "email": "<EMAIL>",
                         "telephone": "8867710045"
                         },
            "billing": {"city": "bangalore", "postcode": "560076", "firstname": "Anubhav", "line_1": "address details1",
                        "lastname": "S", "region": "Karnataka", "line_2": "address details 2", "country_id": "IN",
                        "email": "<EMAIL>", "telephone": "8867710045"}
        }

        place_order_dto = PlaceOrderDTO(data=test_payment_object)
        QwikcilverGiftCardOrder._verify_config_validity(place_order_dto)

    @patch("loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.QwikcilverConfig")
    def test_verify_config_validity_max_items_in_cart_exceeded(self, QwikcilverConfig):
        QwikcilverConfig().get_config.return_value = OrderConfigDTO(data=dict(
            max_item_in_cart=2,
            product_max_qty_allow=10,
            store_id=10
        ))
        test_payment_object = {
            "payment_method": [{"po_number": "1672637263278", "amount_to_redem": "200", "method": "SVC"}],
            "refno": "11-AB-00024k563617s121",
            "products": [
                {"gift_message": "test message for gift-anubhav", "price": 200, "product_id": "24", "qty": "1"},
                {"gift_message": "test message for gift-anubhav", "price": 200, "product_id": "24", "qty": "1"},
                {"gift_message": "test message for gift-anubhav", "price": 200, "product_id": "24", "qty": "1"},
                {"gift_message": "test message for gift-anubhav", "price": 200, "product_id": "24", "qty": "1"}
            ],
            "shipping": {"city": "bangalore", "postcode": "560076", "firstname": "Anubhav",
                         "line_1": "address details1", "lastname": "Shrivastava", "region": "Karnataka",
                         "line_2": "address details 2", "country_id": "IN", "email": "<EMAIL>",
                         "telephone": "8867710045"
                         },
            "billing": {"city": "bangalore", "postcode": "560076", "firstname": "Anubhav", "line_1": "address details1",
                        "lastname": "S", "region": "Karnataka", "line_2": "address details 2", "country_id": "IN",
                        "email": "<EMAIL>", "telephone": "8867710045"}
        }

        place_order_dto = PlaceOrderDTO(data=test_payment_object)
        with pytest.raises(TotalGiftCardLimitExceeds):
            QwikcilverGiftCardOrder._verify_config_validity(place_order_dto)

    @patch("loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.qwikcilver_gift_card_order.QwikcilverConfig")
    def test_verify_config_validity_product_qty_max_allowed_exceeded(self, QwikcilverConfig):
        QwikcilverConfig().get_config.return_value = OrderConfigDTO(data=dict(
            max_item_in_cart=3,
            product_max_qty_allow=1,
            store_id=10
        ))
        test_payment_object = {
            "payment_method": [{"po_number": "1672637263278", "amount_to_redem": "200", "method": "SVC"}],
            "refno": "11-AB-00024k563617s121",
            "products": [
                {"gift_message": "test message for gift-anubhav", "price": 200, "product_id": "24", "qty": "2"},
            ],
            "shipping": {"city": "bangalore", "postcode": "560076", "firstname": "Anubhav",
                         "line_1": "address details1", "lastname": "Shrivastava", "region": "Karnataka",
                         "line_2": "address details 2", "country_id": "IN", "email": "<EMAIL>",
                         "telephone": "8867710045"
                         },
            "billing": {"city": "bangalore", "postcode": "560076", "firstname": "Anubhav", "line_1": "address details1",
                        "lastname": "S", "region": "Karnataka", "line_2": "address details 2", "country_id": "IN",
                        "email": "<EMAIL>", "telephone": "8867710045"}
        }

        place_order_dto = PlaceOrderDTO(data=test_payment_object)
        with pytest.raises(GiftCardQuantityExceeds):
            QwikcilverGiftCardOrder._verify_config_validity(place_order_dto)

    def test_check_price_validity_against_qwikcilver_product_info_slab_price_type(self):
        qs_product = Mock(name="Flipkart Gift Card",
                          qs_product_id='24',
                          description='',
                          short_description='',
                          min_custom_price='100',
                          max_custom_price='1000',
                          custom_denominations='100,200 ,300 ',
                          price_type='slab',
                          catelogitem_id=1,
                          image_url='')
        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 100);
        assert result == True

        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 1000);
        assert result == True

        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 101);
        assert result == False

        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 400);
        assert result == False

        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 300);
        assert result == True

    def test_check_price_validity_against_qwikcilver_product_info_range_price_type(self):
        qs_product = Mock(name="Flipkart Gift Card",
                          qs_product_id='24',
                          description='',
                          short_description='',
                          min_custom_price='100',
                          max_custom_price='1000',
                          price_type='range',
                          catelogitem_id=1,
                          image_url='')

        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 99);
        assert result == False

        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 1001);
        assert result == False

        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 100);
        assert result == True

        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 1000);
        assert result == True

        result = QwikcilverGiftCardOrder._check_price_validity_against_qwikcilver_product_info(qs_product, 450);
        assert result == True
