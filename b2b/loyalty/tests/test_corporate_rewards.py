from datetime import datetime, timedelta

from django.test import SimpleTestCase
from mock import patch, Mock

from loyalty.consumer.loyalty.treebo_rewards.dto.loyalty_earn_dto import LoyaltyEarnDTO
from loyalty.consumer.loyalty.treebo_rewards.treebo_corporate_rewards import TreeboCorpRewards


class TestTreeboCorpRewards(SimpleTestCase):

    @patch('loyalty.consumer.loyalty.treebo_rewards.treebo_corporate_rewards.TreeboCorpRewards')
    @patch('requests.post')
    @patch('common.auth.auth_service.AuthorizationService')
    @patch('treebo_rewards.rewards')
    def test_earn_loyalty(self, treebo_rewards_package_mock, loyalty_auth_mock, request_mock, treebo_corp_rewards_mock):

        curr_date = datetime.now().date()
        loyalty_earn_dto = LoyaltyEarnDTO(data=dict(
            email="<EMAIL>",
            expires_on=(curr_date + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'),
            points_amount=1000,
            points_type="points"
        ))

        mock_json = {
            "data": {
                "id": 2873
            }
        }

        mock_resp = Mock()
        mock_resp.status_code = 200
        mock_resp.json = Mock(
            return_value=mock_json
        )

        request_mock.return_value = mock_resp

        mock_rewards_json = {
            'rewards': {
                'transaction_id': 1108,
                'rewards_status': 'COMPLETED'
            }
        }

        # backend_mock = Mock()
        # loyalty_auth_mock.get_auth_id.return_value = backend_mock

        mock_rewards_resp = Mock()

        mock_rewards_resp.credit_points = Mock()
        # mock_rewards_resp.status_code = 200
        mock_rewards_resp.json = Mock(
            return_value=mock_rewards_json
        )

        treebo_rewards_package_mock.return_value = mock_rewards_resp

        tcr = TreeboCorpRewards()
        points = tcr.earn(loyalty_earn_dto)

        # email = "<EMAIL>"
        # backend_mock.get_auth_id.assert_called_with(email)

        self.assertIn('rewards', points)
        self.assertEqual(points['rewards']['rewards_status'], 'COMPLETED')
