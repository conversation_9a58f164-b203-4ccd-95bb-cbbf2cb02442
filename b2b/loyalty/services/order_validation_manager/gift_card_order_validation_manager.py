import logging

from loyalty.dto.order_config_dto import OrderConfigDTO
from loyalty.exceptions import ZeroGiftCardInCart, TotalGiftCardLimitExceeds, GiftCardQuantityExceeds
from loyalty.models import TreeboCatelogItem
from loyalty.models import TreeboCatelogProduct
from loyalty.services.order_validation_manager.order_validation_manager import OrderValidationManager

logger = logging.getLogger(__name__)


class GiftCardOrderValidationManager(OrderValidationManager):
    def __init__(self):
        super(GiftCardOrderValidationManager, self).__init__()

    def order_validate(self, gift_card_order_data, legal_entity_id):

        catelog_config_dto = self.catelog_config.get_config()

        assert isinstance(catelog_config_dto, OrderConfigDTO)

        catelog_config = catelog_config_dto.data

        logger.info(
            "Validating gift card order for legal entity {legal_entity_id} for data {data}. Catalog config "
            "{catelog_config}".format(legal_entity_id=legal_entity_id, data=gift_card_order_data, catelog_config=catelog_config))

        if len(gift_card_order_data['products']) == 0:
            raise ZeroGiftCardInCart()

        order_amount = 0

        for product in gift_card_order_data['products']:

            if len(product['product_items']) > catelog_config['max_item_in_cart']:
                raise TotalGiftCardLimitExceeds(catelog_config['max_item_in_cart'])

            for item in product['product_items']:
                if item['item_quantity'] > catelog_config['product_max_qty_allow']:
                    raise GiftCardQuantityExceeds(catelog_config['product_max_qty_allow'])

                try:
                    item = TreeboCatelogItem.active_items.get(id=item['item_id'])
                    order_amount += item.points
                except TreeboCatelogItem.DoesNotExist:
                    logger.info("Catelog item does not exist for item {item_name} and id {id}".format(
                        item_name=item['item_name'], id=item['item_id']))
                    raise
            try:
                product = TreeboCatelogProduct.active_products.get(id=product['product_id'])
            except TreeboCatelogProduct.DoesNotExist:
                logger.info("Catelog product does not exist for product {product_name} and id {id}".format(
                    product_name=product['product_name'], id=product['product_id']))
                raise

        self.sufficient_loyalty_balance(order_amount, legal_entity_id)
