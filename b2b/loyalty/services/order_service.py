import logging

from b2b.domain.services.exceptions import PaymentFailed
from b2b.payments.services.payments import PaymentService
from b2b.payments.models import Payment
from common.auth.auth_service import AuthorizationService
from loyalty.exceptions import LoyaltyDisabled
from loyalty.services.order_manager import get_order_manager
from b2b.models import LegalEntity

logger = logging.getLogger(__name__)


class OrderService(object):
    def __init__(self, order_type, legal_entity_id):
        self.order_manager = get_order_manager(order_type)()
        self.payment_service = PaymentService()
        self.auth_service = AuthorizationService()
        legal_entity = LegalEntity.objects.get(legal_entity_id=legal_entity_id)
        if not legal_entity.is_loyalty_enabled():
            logger.info("Loyalty disabled for legal entity {legal_entity_id}".format(legal_entity_id=legal_entity_id))
            raise LoyaltyDisabled(legal_entity_id)

    def create_order(self, order_data, legal_entity_id, user):
        logger.info(
            "Create loyalty order with order_data {data}, legal_entity_id {legal_entity_id} by user {user}".format(data=order_data,
                                                                                                   legal_entity_id=legal_entity_id,
                                                                                                   user=user.email))
        order_id = self.order_manager.initiate_booking(order_data, legal_entity_id, user)
        return order_id

    def verify_otp(self, verify_otp_data, legal_entity_id):
        logger.info("Verify otp with data {data} for legal entity {legal_entity_id}".format(data=verify_otp_data, legal_entity_id=legal_entity_id))

        self.order_manager.is_voucher_code_used(verify_otp_data['order_id'], legal_entity_id, verify_otp_data['voucher_code'])
        self.order_manager.mark_voucher_code_verified(verify_otp_data['voucher_code'], verify_otp_data['order_id'])

    def confirm_order(self, confirm_order_data, legal_entity_id, user):
        logger.info("Confirm loyalty order with data {data} for legal entity {legal_entity_id} by user {user}".format(
            data=confirm_order_data, legal_entity_id=legal_entity_id, user=user.email))

        order_id = confirm_order_data['order_id']
        voucher_code = confirm_order_data['voucher_code']

        self.order_manager.confirm_order(order_id, voucher_code, legal_entity_id, user)

    def confirm_order_item(self, confirm_order_data, legal_entity_id, user):
        logger.info("Confirm loyalty order with data {data} for legal entity {legal_entity_id} by user {user}".format(
            data=confirm_order_data, legal_entity_id=legal_entity_id, user=user.email))

        try:
            self.order_manager.confirm_order_item(confirm_order_data, legal_entity_id, user)
        except PaymentFailed as e:
            logger.info("Payment failed for order_id {order_id}".format(order_id=confirm_order_data['order_id']))
            raise
        except Exception as e:
            logger.info(
                "Exception occurred while payment for order {order_id}. Error {err}".format(
                    order_id=confirm_order_data['order_id'], err=str(e)))
            raise

    def refund(self, order_id, amount=None):
        logger.info("Loyalty points refund for order {order_id}".format(order_id=order_id))
        payment = Payment.objects.get(entity_id=order_id, entity_type=Payment.Type.LOYALTY)
        refund_details = self.order_manager.refund(payment.order_id, amount)
        return refund_details

    def cancel_order(self, order_id):
        logger.info("Cancelling order {order_id}".format(order_id=order_id))
        self.order_manager.cancel(order_id)
