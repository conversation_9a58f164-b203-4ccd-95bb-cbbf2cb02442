import abc
import hmac
import json
from hashlib import sha1
import time

from datetime import datetime

import logging
from django.db import transaction
from django.conf import settings

from b2b import constants
from common.utils.slack import send_slack_notif
from loyalty import constants as LoyaltyConstants
from b2b.domain.services import NotificationService
from b2b.domain.services.exceptions import UnknownEntity, PaymentFailed, RefundFailed
from b2b.payments.services.payments import PaymentService
from b2b.dto.pay import PayRequestDTO
from b2b.dto.refund import RefundRequestDTO
from b2b.models import Corporate
from b2b.payments.models import Payment
from common.auth.auth_service import AuthorizationService
from loyalty.consumer.catelog_config import get_catelog_config_backend
from loyalty.exceptions import InvalidOrderType, OrderDoesNotExist, InvalidVoucherCode, OrderAlreadyConfirmed
from loyalty.models.loyalty_order import LoyaltyOrder
from loyalty.models.loyalty_order_voucher_code import LoyaltyOrderVoucherCode
from random import randint
from codecs import encode

from loyalty.utils.utils import get_legal_entity_email_id
from b2b.models import LegalEntity

logger = logging.getLogger(__name__)


class OrderManager(object, metaclass=abc.ABCMeta):

    def __init__(self):
        self.catelog_config = get_catelog_config_backend(settings.SERVICE_CONFIG['catelog_config'])()
        self.voucher_code_backend = None
        self.notif_service = NotificationService()
        self.payment_service = PaymentService()
        self.auth_service = AuthorizationService()
        self.order = None
        self.order_validation_manager = None

    @abc.abstractmethod
    def initiate_booking(self, order_data, legal_entity_id, user):
        pass

    def pay(self, legal_entity_id, order_id, amount, event_type, retry=None):
        legal_entity_email_id = get_legal_entity_email_id(legal_entity_id)
        pg_meta = dict(
            user_id=self.auth_service.get_auth_id(legal_entity_email_id),
            wallet_type=settings.LOYALTY['wallet_type'],
            event_type=event_type,
            cf_one=order_id,
            cf_two=self._create_cf_two(retry)
        )
        data = dict(amount=amount,
                    receipt=order_id,
                    gateway=settings.TREEBO_WALLET_GATEWAY,
                    currency='INR',
                    pg_meta=pg_meta)
        try:
            payment_dto = self.payment_service.pay(PayRequestDTO(data=data))
            return payment_dto.data['order_id']
        except PaymentFailed as e:
            msg = 'Payment failed for order {order_id} for data {data}, message {message}'.format(
                order_id=order_id,
                data=data,
                message=str(e))
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)

            logger.info("Payment failed for order_id {order_id}".format(order_id=order_id))
            raise
        except Exception as e:
            msg = 'Payment failed for order {order_id} for data {data}, message {message}'.format(
                order_id=order_id,
                data=data,
                message=str(e))
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
            logger.info(
                "Exception occurred while payment for order {order_id}. Error {err}".format(order_id=order_id,
                                                                                            err=str(e)))
            raise

    def _create_cf_two(self, retry):
        cf_two = dict(
            retry=retry
        )

        return json.dumps(cf_two)

    def refund(self, refund_order_id, amount=None):
        data = None
        try:
            payment = Payment.objects.get(order_id=refund_order_id)
            refund_amount = amount or payment.amount
            data = dict(order_id=payment.order_id,
                        amount=refund_amount,
                        gateway=payment.gateway)
            logger.info("Refunding for the order {order_id}. Data {data}".format(order_id=refund_order_id, data=data))
            payment_dto = self.payment_service.refund(RefundRequestDTO(data=data), payment.entity_id)
            payment.amount = refund_amount
            payment.status = constants.Payments.REFUNDED
            payment.save()

        except Payment.DoesNotExist as e:
            msg = 'Payment failed for order {order_id} for data {data}, message {message}'.format(
                order_id=refund_order_id,
                data=data,
                message=str(e))
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
            logger.info(
                'Payment does not exist for refund_order_id {refund_order_id}'.format(refund_order_id=refund_order_id))
            raise
        except RefundFailed as e:
            msg = 'Payment failed for order {order_id} for data {data}, message {message}'.format(
                order_id=refund_order_id,
                data=data,
                message=str(e))
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
            raise
        return payment_dto

    def cancel(self, order_id):
        try:
            order = LoyaltyOrder.objects.get(order_id=order_id)
            order.status = LoyaltyOrder.Status.CANCELLED
            order.save()
        except LoyaltyOrder.DoesNotExist:
            raise

    @abc.abstractmethod
    def confirm_order(self, order_id, voucher_code, corp_id, user):
        pass

    @abc.abstractmethod
    def get_order_data(self, order_id, corp_id):
        pass

    @abc.abstractmethod
    @transaction.atomic()
    def generate_order(self, order_data, corp_id, user):
        pass

    @transaction.atomic()
    def mark_status_confirmed(self, voucher_code, order_id):
        self.mark_voucher_code_verified(voucher_code, order_id)
        self.mark_order_confirmed(order_id)

    def is_voucher_code_used(self, order_id, legal_entity_id, voucher_code):
        orders = LoyaltyOrder.objects.filter(order_id=order_id, legal_entity_id=legal_entity_id).prefetch_related('voucher_code')
        if len(orders) == 0:
            raise OrderDoesNotExist(order_id, legal_entity_id)

        try:
            voucher = orders[0].voucher_code.get(status=LoyaltyOrderVoucherCode.GENERATED)
        except LoyaltyOrderVoucherCode.DoesNotExist:
            raise InvalidVoucherCode(voucher_code)

        if voucher.voucher_code != voucher_code:
            raise InvalidVoucherCode(voucher_code)

    def get_voucher_code(self):
        return randint(1000, 9999)

    def send_voucher_code(self, legal_entity_id, order_data, user):
        voucher_code = self.get_voucher_code()
        self.save_voucher_code(voucher_code)
        voucher_code_notif_id = self.voucher_code_backend.send_voucher_code(legal_entity_id, voucher_code, order_data, user)
        self.order.set_attr(LoyaltyConstants.LoyaltyOrderAttributes.OTP_NOTIF_ID, voucher_code_notif_id)

    def save_voucher_code(self, voucher_code):
        order_voucher_code = LoyaltyOrderVoucherCode.objects.create(order=self.order,
                                                                    voucher_code=voucher_code,
                                                                    status=LoyaltyOrderVoucherCode.GENERATED)
        order_voucher_code.save()

    def is_voucher_verified(self, order_id, voucher_code):
        try:
            LoyaltyOrderVoucherCode.objects.get(voucher_code=voucher_code, order__order_id=order_id,
                                                status=LoyaltyOrderVoucherCode.VERIFIED)
        except LoyaltyOrderVoucherCode.DoesNotExist:
            raise

    def mark_voucher_code_verified(self, voucher_code, order_id):
        try:
            voucher = LoyaltyOrderVoucherCode.objects.get(voucher_code=voucher_code, order__order_id=order_id)
            voucher.status = LoyaltyOrderVoucherCode.VERIFIED
            voucher.save()
        except LoyaltyOrderVoucherCode.DoesNotExist:
            raise InvalidVoucherCode(voucher_code)

    def mark_order_confirmed(self, order_id):
        try:
            voucher = LoyaltyOrder.objects.get(order_id=order_id)
            voucher.status = LoyaltyOrder.Status.CONFIRMED
            voucher.save()
        except LoyaltyOrderVoucherCode.DoesNotExist:
            raise

    def create_new_order(self, order_data, legal_entity_id, user):
        try:
            LegalEntity.objects.get(legal_entity_id__iexact=legal_entity_id)

        except LegalEntity.DoesNotExist:
            raise UnknownEntity('legal entity', legal_entity_id)

        order_id = self.generate_order_id(order_data)
        order_type = self.get_order_type(order_data.get("type"))
        self.order = LoyaltyOrder.objects.create(order_id=order_id, legal_entity_id=legal_entity_id,
                                                 user_id=user.id,
                                                 order_type=order_type,
                                                 amount=order_data.get('order_amount', 0),
                                                 status=LoyaltyOrder.Status.INITIATED)
        self.order.save()

    def get_order_type(self, order_type):
        if order_type in (LoyaltyOrder.Type.GIFT_CARD, LoyaltyOrder.Type.ROOM_NIGHT):
            return order_type
        logger.info("Invalid order_type {order_type}".format(order_type=order_type))

        raise InvalidOrderType(order_type)

    def get_order_amount(self, order_id):
        try:
            return LoyaltyOrder.objects.get(order_id=order_id).amount
        except LoyaltyOrder.DoesNotExist:
            raise

    def validate_order(self, order_id):
        try:
            order = LoyaltyOrder.objects.get(order_id=order_id)
            if order.status == LoyaltyOrder.Status.CONFIRMED:
                raise OrderAlreadyConfirmed(order_id)

        except (LoyaltyOrder.DoesNotExist, OrderAlreadyConfirmed):
            raise

    def generate_order_id(self, order_data):
        # length of the fragment to choose from the digest
        dflen = 8

        # sha1 digest of the order json
        # uniqueness will be further accentuated by current timestamp
        signature = hmac.new(encode(str(time.time() * 1000)), encode(json.dumps(order_data)), sha1).hexdigest()

        # choose a random fragment out of the digest (caveat: increases collision probability)
        pos = randint(0, len(signature) - dflen)

        today = datetime.today().strftime('%y%m%d')

        # reduce the chances of collision by embedding today's date within the digest fragment
        code = ''.join([''.join(t) for tup in
                        zip(list(zip(*[iter(signature[pos: pos + dflen])] * 2)),
                            list(zip(*[iter(today)] * 2)) + [''] * int((dflen - len(today)) / 2))
                        for t in tup])

        return 'LOY-{code}'.format(code=code.upper())
