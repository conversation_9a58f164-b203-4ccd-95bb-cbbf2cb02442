import json
from collections import defaultdict

import logging

import sentry_sdk
from django.db import transaction
from django.conf import settings

from common.utils.slack import send_slack_notif
from loyalty.consumer.gift_card_order import get_gift_card_order_backend
from loyalty.consumer.voucher_code.loyalty_order_voucher_code import LoyaltyVoucherCode
from loyalty.exceptions import CatalogProductDoesNotExist, OrderDoesNotExist, ItemCountExceeds, \
    QwikcilverSpendCallFailed, \
    QwikcilverOrderStatusNotCompleted, QwikcilverBookingFailed, ProductUnAvailableOnQwikcilver
from loyalty.models import LoyaltyOrderItem
from loyalty.models import TreeboCatelogItem
from loyalty.models import TreeboCatelogProduct
from loyalty.models.loyalty_order import LoyaltyOrder
from b2b.domain.services.exceptions import InvalidDTO

from loyalty.services.order_manager.order_manager import OrderManager
from loyalty.services.order_validation_manager.gift_card_order_validation_manager import GiftCardOrderValidationManager

from loyalty.dto.gift_card_order_dto import ProductListDto

from loyalty.utils.utils import get_wallet_points_to_rupee_conversion
from b2b import constants

logger = logging.getLogger(__name__)


class GiftCardOrderManager(OrderManager):
    def __init__(self):
        super(GiftCardOrderManager, self).__init__()
        self.order_validation_manager = GiftCardOrderValidationManager()
        self.gift_card_order_backend = get_gift_card_order_backend(settings.SERVICE_CONFIG['gift_card_order'])()
        self.voucher_code_backend = LoyaltyVoucherCode(LoyaltyOrder.Type.GIFT_CARD)

    def initiate_booking(self, order_data, legal_entity_id, user):
        logger.info("Initiate gift card order for  legal entity {legal_entity_id} bu user {email}. "
                    "Data {data}".format(legal_entity_id=legal_entity_id, data=order_data, email=user.email))

        self.generate_order(order_data, legal_entity_id, user)
        self.order_validation_manager.order_validate(order_data, legal_entity_id)
        self.order_validation_manager.loyalty_redemption_per_day_limit(legal_entity_id, self.order.amount)
        self.send_voucher_code(legal_entity_id, order_data['products'], user)
        return self.order.order_id

    @transaction.atomic()
    def generate_order(self, order_data, legal_entity_id, user):
        self.create_new_order(order_data, legal_entity_id, user)
        self.create_order_item(order_data)

    def confirm_order(self, order_id, voucher_code, legal_entity_id, user):
        logger.info(
            "Gift card confirm order for legal entity {legal_entity_id} with order_id {order_id},  voucher_code {voucher_code} and user {email}".format(
                legal_entity_id=legal_entity_id, order_id=order_id, voucher_code=voucher_code, email=user.email))

        try:
            self.order = LoyaltyOrder.objects.get(order_id=order_id)
        except LoyaltyOrder.DoesNotExist:
            raise

        self.order_validation_manager.loyalty_redemption_per_day_limit(legal_entity_id, self.order.amount)

        self.is_voucher_code_used(order_id, legal_entity_id, voucher_code)
        self.validate_order(order_id)
        order_amount = self.get_order_amount(order_id)
        order_data = self.get_order_data(order_id, legal_entity_id)

        gift_card_order_data_dto = ProductListDto(data=order_data)
        if not gift_card_order_data_dto.is_valid():
            raise InvalidDTO(gift_card_order_data_dto)

        payment_id = self.pay(legal_entity_id, order_id, order_amount, LoyaltyOrder.Type.GIFT_CARD)
        try:
            self.gift_card_order_backend.place_order(gift_card_order_data_dto, legal_entity_id, order_id, user)
        except (Exception, QwikcilverSpendCallFailed, QwikcilverOrderStatusNotCompleted, QwikcilverBookingFailed,
                ProductUnAvailableOnQwikcilver, InvalidDTO) as e:
            sentry_sdk.capture_exception(e)
            logger.info(
                "Error occurred while placing order {order_id}. Error {err}".format(order_id=order_id, err=str(e)))
            self.refund(payment_id)
            raise
        self.mark_status_confirmed(voucher_code, order_id)

    def confirm_order_item(self, order_data, legal_entity_id, user):
        logger.info("Confirm gift card order item for legal entity {legal_entity_id} with order data {order_data}".format(
            order_data=order_data, legal_entity_id=legal_entity_id))
        try:
            self.order = LoyaltyOrder.objects.get(order_id=order_data['order_id'])
        except LoyaltyOrder.DoesNotExist:
            raise

        self.order_validation_manager.loyalty_redemption_per_day_limit(legal_entity_id, self.order.amount)

        self.is_voucher_verified(order_data['order_id'], order_data['voucher_code'])
        self.validate_order_item_data(order_data, legal_entity_id)
        points = self.get_items_point(order_data)

        gift_card_order_data_dto = ProductListDto(data=order_data)
        if not gift_card_order_data_dto.is_valid():
            raise InvalidDTO(gift_card_order_data_dto)

        total_amount = get_wallet_points_to_rupee_conversion(points)
        logger.info("Paying amount for the order {order_id}. Data {data}".format(order_id=order_data['order_id'],
                                                                                 data=order_data))

        retry = gift_card_order_data_dto.data['retry']
        payment_id = self.pay(legal_entity_id, order_data['order_id'], total_amount, LoyaltyOrder.Type.GIFT_CARD, retry)

        try:
            self.gift_card_order_backend.place_order(gift_card_order_data_dto, legal_entity_id, order_data['order_id'], user)
        except (QwikcilverSpendCallFailed, QwikcilverOrderStatusNotCompleted, QwikcilverBookingFailed,
                ProductUnAvailableOnQwikcilver, InvalidDTO, Exception) as e:
            msg = 'Confirm order item failed for order {order_id} for data {data}, message {message}'.format(
                order_id=order_data['order_id'],
                data=order_data,
                message=str(e))
            sentry_sdk.capture_exception(e)
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception = False)
            logger.info(
                "Error occurred while placing order {order_id}. Error {err}. Data {data}. Amount {amount}".format(
                    order_id=order_data['order_id'], err=str(e), data=order_data, amount=total_amount))
            self.refund(payment_id)
            raise
        self.mark_order_item_confirmed(order_data, legal_entity_id)

    def validate_order_item_data(self, order_data, legal_entity_id):
        orders = LoyaltyOrder.objects.filter(order_id=order_data['order_id'], legal_entity_id=legal_entity_id).prefetch_related(
            'order_item')
        if len(orders) == 0:
            raise OrderDoesNotExist(order_data['order_id'], legal_entity_id)
        for product in order_data['products']:
            for item in product['product_items']:
                try:
                    catalog_item = orders[0].order_item.get(item_id=item['item_id'])
                    if item['item_quantity'] + catalog_item.confirmed > catalog_item.count:
                        raise ItemCountExceeds(item['item_id'], item['item_quantity'])
                except LoyaltyOrderItem.DoesNotExist:
                    raise

    @transaction.atomic()
    def mark_order_item_confirmed(self, order_data, legal_entity_id):
        order_id = order_data['order_id']
        orders = LoyaltyOrder.objects.filter(order_id=order_id, legal_entity_id=legal_entity_id).prefetch_related('order_item')
        if len(orders) == 0:
            raise OrderDoesNotExist(order_id, legal_entity_id)

        for product in order_data['products']:
            for item in product['product_items']:
                try:
                    catalog_item = orders[0].order_item.get(item_id=item['item_id'])
                    confirmed_item = catalog_item.confirmed
                    total_count = catalog_item.count
                    if item['item_quantity'] + confirmed_item > total_count:
                        raise ItemCountExceeds(item['item_id'], item['item_quantity'])

                    catalog_item.confirmed = item['item_quantity'] + confirmed_item
                    catalog_item.save()
                except LoyaltyOrderItem.DoesNotExist:
                    raise

    def get_items_point(self, products):
        points = 0
        for product in products['products']:
            for item in product['product_items']:
                try:
                    catalog_item = TreeboCatelogItem.objects.get(id=item['item_id'])
                    points += catalog_item.points * item['item_quantity']
                except TreeboCatelogItem.DoesNotExist:
                    raise

        return points

    def get_order_data(self, order_id, legal_entity_id):
        orders = LoyaltyOrder.objects.filter(order_id=order_id, legal_entity_id=legal_entity_id).prefetch_related('order_item')
        if len(orders) == 0:
            raise OrderDoesNotExist(order_id, legal_entity_id)

        items = orders[0].order_item.all()
        item_data = defaultdict(list)
        id_name_map = dict()
        for item in items:
            try:
                catalog_item = TreeboCatelogItem.objects.select_related('product').get(id=item.item_id)
                item_data[catalog_item.product.id].append(dict(
                    item_id=item.item_id,
                    item_name=catalog_item.name,
                    item_quantity=item.count,
                    product_name=catalog_item.product.name
                ))
                id_name_map[catalog_item.product.id] = catalog_item.product.name

            except TreeboCatelogItem.DoesNotExist:
                raise

        result = [{'product_id': product_id, 'product_name': id_name_map[product_id], 'product_items': items} for
                  product_id, items, in list(item_data.items())]
        return {'products': result}

    def create_order_item(self, order_data):
        order_type = self.order.order_type
        points = 0
        try:
            if order_type == LoyaltyOrder.Type.GIFT_CARD:
                for product in order_data.get('products'):
                    catelog_product = TreeboCatelogProduct.active_products.filter(
                        id=product['product_id']).prefetch_related('product_item')
                    if len(catelog_product) == 0:
                        raise CatalogProductDoesNotExist(product['product_id'])

                    for item in product['product_items']:
                        catelog_item = catelog_product[0].product_item.get(id=item['item_id'])
                        loyalty_order_item = LoyaltyOrderItem.objects.create(order=self.order, item=catelog_item,
                                                                             count=item['item_quantity'])
                        loyalty_order_item.save()
                        points += catelog_item.points * item['item_quantity']

            total_amount = get_wallet_points_to_rupee_conversion(points)
            self.order.amount = total_amount
            self.order.save()
        except (TreeboCatelogProduct.DoesNotExist, TreeboCatelogItem.DoesNotExist):
            raise

    def update_loyalty_order_status(self, order_id):
        orders = LoyaltyOrder.objects.filter(order_id=order_id).prefetch_related('order_item')
        order = orders[0]
        order_items = order.order_item.all()
        total_requested = 0
        total_confirmed = 0
        for item in order_items:
            total_requested += item.count
            total_confirmed += item.confirmed

        if total_confirmed == 0:
            order_status = LoyaltyOrder.Status.FAILED
        elif total_confirmed == total_requested:
            order_status = LoyaltyOrder.Status.CONFIRMED
        else:
            order_status = LoyaltyOrder.Status.PARTIALLY_CONFIRMED

        order.status = order_status
        order.save()
