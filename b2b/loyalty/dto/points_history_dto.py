# -*- coding: utf-8 -*-
from django.conf import settings
from rest_framework import serializers

from common.rounded_decimal_field import RoundedDecimalField


class _GiftCardDTO(serializers.Serializer):
    name = serializers.CharField()
    image_url = serializers.CharField()
    cardnumber = serializers.CharField()
    price = serializers.CharField()
    expiry_date = serializers.CharField(required=False, allow_null=True , allow_blank= True)
    pin = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    description=serializers.CharField(required=False, allow_null=True, allow_blank=True)
    terms_and_condition=serializers.CharField(required=False, allow_null=True, allow_blank=True)
    tnc_link=serializers.CharField(required=False, allow_null=True, allow_blank=True)
    powered_by=serializers.CharField(required=False, allow_null=True, allow_blank=True)

class _PointsHistoryDictDTO(serializers.Serializer):
    rewards_type = serializers.CharField(max_length=50, required=False, allow_null=True)
    reference_id = serializers.CharField(max_length=50, required=False, allow_null=True)
    expires_on = serializers.DateTimeField(required=False, allow_null=True)
    created_on = serializers.DateTimeField()
    amount = RoundedDecimalField(max_digits=20, decimal_places=2)
    type = serializers.CharField()
    used_on = serializers.DateTimeField(required=False, allow_null=True)
    base_amount = RoundedDecimalField(max_digits=20, decimal_places=2)
    confirmed_amount = RoundedDecimalField(max_digits=20, decimal_places=2)
    gift_cards = _GiftCardDTO(many=True, required=False)
    tcr_multiplier = serializers.DecimalField(max_digits=10, decimal_places=2, required=False , allow_null=True)


class WalletStatementDTO(serializers.Serializer):
    statement = serializers.ListField(child=_PointsHistoryDictDTO())
    total_records = serializers.IntegerField(required=True)