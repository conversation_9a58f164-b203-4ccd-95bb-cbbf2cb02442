from django.conf.urls import url

from loyalty.api.catelog import <PERSON>elogAP<PERSON>
from loyalty.api.confirm_gift_card_order import ConfirmGiftCardOrderAPI
from loyalty.api.confirm_gift_card_order_item import ConfirmGiftCard<PERSON>temOrderAPI
from loyalty.api.create_gift_card_order import CreateGiftCard<PERSON>rder<PERSON><PERSON>
from loyalty.api.create_room_night_order import CreateRoom<PERSON><PERSON><PERSON>rder<PERSON><PERSON>
from loyalty.api.downgrade_corporate_level import DowngradeCorporateLevel
from loyalty.api.earn import EarnAPI
from loyalty.api.loyalty_manage import LoyaltyManageAPI
from loyalty.api.loyalty_points import LoyaltyPointsAPI
from loyalty.api.verify_otp import VerifyOT<PERSON>PI
from loyalty.api.send_gift_card_email import SendGiftCardEmail
from loyalty.api.wallet_history import WalletHistory
from loyalty.api.get_gift_card_detail import GiftCardDetail
from loyalty.api.quickcilver_product_details import GetQwikcilverProductDetail

urlpatterns = [
    url(r'^loyalty-manage/', LoyaltyManageAPI.as_view(), name='loyalty_manage'),
    url(r'^earn/$', EarnAPI.as_view(), name='earn'),
    url(r'^get-loyalty-points/$', LoyaltyPointsAPI.as_view(), name='loyalty-points'),
    url(r'get-catelog/$', CatelogAPI.as_view(), name='catelog-list'),
    url(r'send-gift-card-email/$', SendGiftCardEmail.as_view(), name='send-gift-card-email'),
    url(r'create-gift-card-order/$', CreateGiftCardOrderAPI.as_view(), name='create-gift-card-order'),
    url(r'create-room-night-order/$', CreateRoomNightOrderAPI.as_view(), name='create-room-night-order'),
    url(r'verify-otp/$', VerifyOTPAPI.as_view(), name='verify-otp'),
    url(r'confirm-gift-card-order/$', ConfirmGiftCardOrderAPI.as_view(), name='confirm-gift-card-order'),
    url(r'confirm-gift-card-order-item/$', ConfirmGiftCardItemOrderAPI.as_view(), name='confirm-gift-card-order-item'),
    url(r'wallet-history/$', WalletHistory.as_view(), name='wallet-history'),
    url(r'gift-card-detail/$', GiftCardDetail.as_view(), name='gift-card-detail'),
    url(r'downgrade-corporate-level/$', DowngradeCorporateLevel.as_view(), name='downgrade-level'),
    url(r'get_qwikcilver_product_detail/$', GetQwikcilverProductDetail.as_view(),
        name='get-qwikcilver-product-detail')
]
