import logging

from b2b.models import AccountAdmin, LegalEntity
from rest_framework import status
from rest_framework.authentication import BasicAuthentication
from rest_framework.response import Response
from treebo_rewards.exceptions import UnableToFetchBalance

from b2b.api import CsrfExemptSessionAuthentication
from b2b.api.dashboard.api import DashboardAPI
from b2b.api.dashboard.authorization import DashboardAuthorization
from loyalty.api.api_error_response import APIErrorResponse
from loyalty.dto.loyalty_points_dto import LoyaltyPointsDTO
from loyalty.exceptions import InvalidDTO, LoyaltyDisabled
from loyalty.services.loyalty_service import LoyaltyService

logger = logging.getLogger(__name__)


class LoyaltyPointsAPI(DashboardAPI):
    permission_classes = (DashboardAuthorization,)
    authentication_classes = (BasicAuthentication, CsrfExemptSessionAuthentication)

    def get(self, request):
        legal_entity_id = request.query_params['legal_entity_id']
        try:
            logger.info("Getting loyalty points for legal entity {legal_entity_id}".format(legal_entity_id=legal_entity_id))
            loyalty_svc = LoyaltyService(legal_entity_id, request.user)
            result = loyalty_svc.get_points(legal_entity_id)
            points_dto = LoyaltyPointsDTO(data=result)
            if not points_dto.is_valid():
                raise InvalidDTO(points_dto)
            return Response(data=points_dto.data, status=status.HTTP_200_OK)
        except LoyaltyDisabled as e:
            logger.info(
                "Loyalty is disabled for the legal entity {legal_entity_id}. Error {err}".format(legal_entity_id=legal_entity_id, err=str(e)))
            return APIErrorResponse.loyalty_disabled()
        except UnableToFetchBalance as e:
            logger.info(
                "Wallet does not exist for legal entity {legal_entity_id}. Error {err}".format(legal_entity_id=legal_entity_id, err=str(e)))
            return APIErrorResponse.wallet_not_available(str(e))
        except InvalidDTO as e:
            logger.info("Invalid LoyaltyPointsDTO. Error {err}".format(err=str(e)))
            return APIErrorResponse.error_response(str(e), status.HTTP_400_BAD_REQUEST)
        except AccountAdmin.DoesNotExist as e:
            logger.info("Error occurred while getting loyalty points for the user. Error {err}".format(err=str(e)))
            return APIErrorResponse.error_response(str(e), status.HTTP_400_BAD_REQUEST)
        except LegalEntity.DoesNotExist as e:
            logger.info("Error occurred while getting loyalty points for the user. Error {err}".format(err=str(e)))
            return APIErrorResponse.error_response(str(e), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.info("Error occurred while getting loyalty points. Error {err}".format(err=str(e)))
            return APIErrorResponse.error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
