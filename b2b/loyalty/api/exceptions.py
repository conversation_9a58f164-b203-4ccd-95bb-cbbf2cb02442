# -*- coding: utf-8 -*-


class LoyaltyEarnDTOValidationError(Exception):
    def __init__(self, message):
        super(LoyaltyEarnDTOValidationError, self).__init__(message)


class CorporateAuthorizationError(Exception):
    """
    raised when user does not belog to the corporate
    """
    def __init__(self, message):
        super(CorporateAuthorizationError, self).__init__(message)


class GroupAuthorizationError(Exception):
    """
    raised when user does not belong to the dashboard group
    """
    def __init__(self, message):
        super(GroupAuthorizationError, self).__init__(message)
