# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('loyalty', '0026_delete_orderconfig'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderConfig',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('namespace', models.Char<PERSON>ield(max_length=20)),
                ('key', models.Char<PERSON>ield(max_length=100)),
                ('value', models.Char<PERSON>ield(max_length=1000)),
            ],
            options={
                'abstract': False,
                'default_permissions': ('add', 'change', 'read', 'delete'),
            },
        ),
        migrations.AlterUniqueTogether(
            name='orderconfig',
            unique_together=set([('namespace', 'key')]),
        ),
    ]
