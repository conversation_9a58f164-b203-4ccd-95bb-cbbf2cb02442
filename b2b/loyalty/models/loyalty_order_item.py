from b2b.models.mixins import DefaultPermissions
from django.db import models
from djutil.models import TimeStampedModel

from loyalty.models import TreeboCatelogItem
from loyalty.models.loyalty_order import LoyaltyOrder


class LoyaltyOrderItem(TimeStampedModel, DefaultPermissions):
    order = models.ForeignKey(LoyaltyOrder, related_name="order_item", on_delete=models.PROTECT)
    item = models.ForeignKey(TreeboCatelogItem, related_name="catalog_item", on_delete=models.PROTECT)
    count = models.IntegerField()
    confirmed = models.IntegerField(default=0)
