from b2b.models.mixins import DefaultPermissions
from django.db import models
from djutil.models import TimeStampedModel

from loyalty.models import LoyaltyOrder


class LoyaltyOrderAttributes(TimeStampedModel, DefaultPermissions):
    order = models.ForeignKey(LoyaltyOrder, related_name="loyalty_attr", on_delete=models.CASCADE)
    key = models.CharField(max_length=250)
    value = models.CharField(max_length=250)
