from loyalty.consumer.catelog_config.catelog_config_interface import BaseCatelogConfig
from loyalty.exceptions import InvalidDTO
from loyalty.models import OrderConfig
from loyalty.dto.order_config_dto import OrderConfigDTO


class TreeboCatelogConfig(BaseCatelogConfig):
    def get_config(self):
        configs = {config.key: config.value for config in
                   OrderConfig.objects.filter(namespace=OrderConfig.Namespace.TREEBO,
                                              key__in=('product_max_qty_allow', 'max_item_in_cart'))}

        order_config_dto = OrderConfigDTO(data=configs)

        if not order_config_dto.is_valid():
            raise InvalidDTO(order_config_dto)

        return order_config_dto
