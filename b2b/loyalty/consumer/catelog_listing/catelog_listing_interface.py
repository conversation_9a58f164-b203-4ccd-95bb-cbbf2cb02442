import abc


class BaseCatelogListing(object, metaclass=abc.ABCMeta):

    name = None

    @classmethod
    def get_name(cls):
        return cls.name

    @abc.abstractmethod
    def get_all_products(self):
        raise Exception(self.name, "get_all_products not implemented")

    @abc.abstractmethod
    def get_all_categories(self):
        raise Exception(self.name, "get_all_categories not implemented")

    @abc.abstractmethod
    def get_products_by_category(self, category):
        raise Exception(self.name, "get_products_by_category not implemented")

    @abc.abstractmethod
    def catelog_config(self):
        raise Exception(self.name, "catelog_config not implemented")
