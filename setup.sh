#!/bin/bash

set -e
set -x

ENV="$1"
export tenant_service_url="${6}"
VERSION="$2"
APP="$3"
CONFIG_BRANCH="$4"
TARGET="$5"
#ENV_REPO_SERVICE_NAME="$6"
export CLUSTER_IDENTIFIER="$7"
export regions="$8"
export AWS_SECRET_PREFIX="$9"

echo "Using env : $ENV"
export VERSION="$2"
export APP="$3"

export HOME=/opt/$APP
export SERVICE=$APP

cd $HOME

create_dir_if_doesnt_exist() {
        DIR_NAME=$1
        if [ ! -d $DIR_NAME ];
        then echo "creating directory at: $DIR_NAME";
        mkdir -pv $DIR_NAME;
        fi;
    }

TREEBO_TENANT_ID="treebo"
allTenants=()

function loadActiveTenants {
  # Read Tenant Ids from TenantGateway
  echo "Loading active tenants"
  if [ "$ENV" == "staging" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")

  elif [ "$ENV" == "production" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")
  fi

  allTenants=($(echo "$active_tenants" | jq -r '.data[].tenant_id'))
  echo $nonTreeboTenants
}

loadActiveTenants

echo "Tenants loaded: ${allTenants[@]}"
echo "Deploying $APP app on $ENV environment"

declare -a routing_keys=("0" "1" "2" "3" "4" "5" "6" "7" "8" "9")



if [ "$ENV" = "staging" ]; then
    export req_file=./requirements/staging.txt
    source /opt/$APP/docker/env_files/build_env/staging
    export ENV_FILE=/opt/$APP/docker/env_files/docker_env/staging.env
    create_dir_if_doesnt_exist $HOST_LOG_ROOT
    if [ $TARGET = "app" ]; then
      echo "Running tenant_gateway_app_service container"
      docker-compose -f /opt/$APP/docker/docker_compose/stag-compose.yml up -d tenant_gateway_app_service
      for tenant_id in ${allTenants[@]}; do
        export TENANT_ID=$tenant_id
        docker-compose -f /opt/$APP/docker/docker_compose/stag-compose.yml -p tenant_gateway_${TENANT_ID} up -d catalog_sync_worker
      done

    elif [ $TARGET = "workers" ]; then
      for tenant_id in ${allTenants[@]}; do
        export TENANT_ID=$tenant_id
        # Run worker container on a different port for all tenants
        docker-compose -f /opt/$APP/docker/docker_compose/stag-compose.yml -p tenant_gateway_${TENANT_ID} up -d booking_sync_worker
        docker-compose -f /opt/$APP/docker/docker_compose/stag-compose.yml -p tenant_gateway_${TENANT_ID} up -d crs_consumer_worker
        docker-compose -f /opt/$APP/docker/docker_compose/stag-compose.yml -p tenant_gateway_${TENANT_ID} up -d rate_sync_worker
        for routing_key in `seq 0 $SU_ROUTING_MODULAR_KEY`; do
          export ROUTING_KEY=$routing_key
          docker-compose -f /opt/$APP/docker/docker_compose/stag-compose.yml -p tenant_gateway_${TENANT_ID}_${ROUTING_KEY} up -d su_booking_consumer
          done
      done
    fi

elif [ "$ENV" = "production" ]; then
    export req_file=./requirements/production.txt
    source /opt/$APP/docker/env_files/build_env/production
    export ENV_FILE=/opt/$APP/docker/env_files/docker_env/production.env
    create_dir_if_doesnt_exist $HOST_LOG_ROOT
    if [ $TARGET = "app" ]; then
      echo "Running tenant_gateway_app_service container"
      docker-compose -f /opt/$APP/docker/docker_compose/prod-compose.yml up -d tenant_gateway_app_service
      
    elif [ $TARGET = "workers" ]; then
      for tenant_id in ${allTenants[@]}; do
        export TENANT_ID=$tenant_id
        # Run worker container on a different port for all tenants
        docker-compose -f /opt/$APP/docker/docker_compose/prod-compose.yml -p tenant_gateway_${TENANT_ID} up -d booking_sync_worker
        docker-compose -f /opt/$APP/docker/docker_compose/prod-compose.yml -p tenant_gateway_${TENANT_ID} up -d crs_consumer_worker
        docker-compose -f /opt/$APP/docker/docker_compose/prod-compose.yml -p tenant_gateway_${TENANT_ID} up -d rate_sync_worker
        docker-compose -f /opt/$APP/docker/docker_compose/prod-compose.yml -p tenant_gateway_${TENANT_ID} up -d catalog_sync_worker
        for routing_key in `seq 0 $SU_ROUTING_MODULAR_KEY`; do
          export ROUTING_KEY=$routing_key
          docker-compose -f /opt/$APP/docker/docker_compose/prod-compose.yml -p tenant_gateway_${TENANT_ID}_${ROUTING_KEY} up -d su_booking_consumer
          done
      done
    fi
fi
