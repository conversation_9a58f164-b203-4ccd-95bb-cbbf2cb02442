import json

from decorators import set_hotel_context
from sentry_sdk.integrations.serverless import serverless_function

from object_registry import inject
from prometheus.common.decorators import consumer_middleware
from prometheus.core.globals import consumer_context
from prometheus.reporting.in_touch_reports.reservation_report.service import (
    InTouchReservationReportingService,
)
from prometheus.wsgi import app
from ths_common.constants.catalog_constants import NightAuditStatus


@serverless_function
@consumer_middleware
@set_hotel_context()
@inject(in_touch_reservation_report_service=InTouchReservationReportingService)
def generate_reservation_report(
    in_touch_reservation_report_service, business_date, hotel_aggregate=None
):
    in_touch_reservation_report_service.generate_csv_report(
        business_date, hotel_aggregate
    )


def lambda_handler(event, context):
    print(f"Event: \n{event}")

    if 'Records' not in event or event['Records'][0]['EventSource'] != 'aws:sns':
        print("Report Lambda can be triggerred only using SNS.")
        return

    message = json.loads(event['Records'][0]['Sns']['Message'])

    if message['status'] != NightAuditStatus.COMPLETED.value:
        print("Reservation report can be generated only for completed night audits.")
        return

    with app.app_context():
        consumer_context.set_tenant_id(message['tenant_id'])
        consumer_context.hotel_id = message['hotel_id']
        generate_reservation_report(business_date=message['business_date'])

    print('Reservation report generation is complete!!')
